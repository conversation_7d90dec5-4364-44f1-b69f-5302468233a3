# Trino SQL语言与函数体系

## 概述

Trino 作为 ANSI SQL 兼容的查询引擎，提供了丰富的 SQL 语言特性和函数体系。本报告深入分析 Trino 的 SQL 支持能力、数据类型系统、内置函数库、窗口函数、聚合函数等核心语言特性，为架构师提供全面的 SQL 功能参考。

## 1. SQL 标准兼容性

### 1.1 ANSI SQL 兼容性

#### 1.1.1 标准支持级别

Trino 支持 ANSI SQL 标准的核心特性：

| SQL 标准 | 支持级别 | 主要特性 |
|----------|----------|----------|
| SQL-92 | 完全支持 | 基础查询、JOIN、子查询 |
| SQL-99 | 大部分支持 | 窗口函数、CTE、CASE 表达式 |
| SQL-2003 | 部分支持 | XML 数据类型、标准化函数 |
| SQL-2006 | 部分支持 | 数组操作、多维数组 |
| SQL-2008 | 部分支持 | MERGE 语句、触发器 |
| SQL-2011 | 部分支持 | 时间窗口、序列生成 |

#### 1.1.2 语言特性支持

**核心查询特性：**
- SELECT、FROM、WHERE、GROUP BY、HAVING、ORDER BY
- 内连接、外连接、交叉连接、自连接
- 子查询、相关子查询、EXISTS、IN
- UNION、INTERSECT、EXCEPT 集合操作
- 公共表表达式（CTE）和递归查询

**高级特性：**
- 窗口函数和分析函数
- 数组和 MAP 数据类型操作
- JSON 数据处理
- 正则表达式支持
- 用户定义函数（UDF）

### 1.2 SQL 语句分类

#### 1.2.1 全局可用语句

**会话管理：**
```sql
-- 设置会话属性
SET SESSION query_max_run_time = '1h';
SET SESSION join_distribution_type = 'BROADCAST';

-- 查看会话信息
SHOW SESSION;
SHOW SESSION LIKE 'query%';

-- 重置会话属性
RESET SESSION query_max_run_time;
```

**查询分析：**
```sql
-- 查看执行计划
EXPLAIN SELECT * FROM orders WHERE orderdate > DATE '2023-01-01';

-- 分析查询性能
EXPLAIN (TYPE DISTRIBUTED) 
SELECT o.orderkey, c.name 
FROM orders o JOIN customer c ON o.custkey = c.custkey;

-- 查看查询统计
EXPLAIN (TYPE IO) 
SELECT COUNT(*) FROM lineitem WHERE shipdate > DATE '2023-01-01';
```

#### 1.2.2 读操作支持

**基础查询：**
```sql
-- 标准 SELECT 查询
SELECT 
    orderkey,
    custkey,
    totalprice,
    orderdate
FROM orders 
WHERE orderdate BETWEEN DATE '2023-01-01' AND DATE '2023-12-31'
ORDER BY totalprice DESC
LIMIT 100;

-- 复杂聚合查询
SELECT 
    EXTRACT(YEAR FROM orderdate) AS year,
    EXTRACT(MONTH FROM orderdate) AS month,
    COUNT(*) AS order_count,
    SUM(totalprice) AS total_revenue,
    AVG(totalprice) AS avg_order_value
FROM orders
GROUP BY EXTRACT(YEAR FROM orderdate), EXTRACT(MONTH FROM orderdate)
HAVING COUNT(*) > 1000
ORDER BY year, month;
```

**窗口函数查询：**
```sql
-- 排名和分析函数
SELECT 
    orderkey,
    custkey,
    totalprice,
    ROW_NUMBER() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS order_rank,
    RANK() OVER (ORDER BY totalprice DESC) AS global_rank,
    LAG(totalprice) OVER (PARTITION BY custkey ORDER BY orderdate) AS prev_order_value,
    SUM(totalprice) OVER (PARTITION BY custkey) AS customer_total
FROM orders;
```

#### 1.2.3 写操作支持

**数据插入：**
```sql
-- 插入数据
INSERT INTO customer_summary 
SELECT 
    custkey,
    name,
    COUNT(*) AS order_count,
    SUM(totalprice) AS total_spent
FROM customer c
JOIN orders o ON c.custkey = o.custkey
GROUP BY custkey, name;

-- 条件插入
INSERT INTO high_value_customers
SELECT custkey, name, phone
FROM customer 
WHERE custkey IN (
    SELECT custkey 
    FROM orders 
    GROUP BY custkey 
    HAVING SUM(totalprice) > 100000
);
```

**数据更新和删除：**
```sql
-- 更新数据（连接器支持）
UPDATE customer 
SET phone = '******-0123' 
WHERE custkey = 12345;

-- 删除数据（连接器支持）
DELETE FROM orders 
WHERE orderdate < DATE '2020-01-01';
```

## 2. 数据类型系统

### 2.1 基础数据类型

#### 2.1.1 数值类型

**整数类型：**
```sql
-- TINYINT: 8位有符号整数 (-128 到 127)
SELECT CAST(100 AS TINYINT);

-- SMALLINT: 16位有符号整数 (-32,768 到 32,767)
SELECT CAST(30000 AS SMALLINT);

-- INTEGER/INT: 32位有符号整数
SELECT CAST(2147483647 AS INTEGER);

-- BIGINT: 64位有符号整数
SELECT CAST(9223372036854775807 AS BIGINT);
```

**浮点类型：**
```sql
-- REAL: 32位浮点数
SELECT CAST(3.14159 AS REAL);

-- DOUBLE: 64位浮点数
SELECT CAST(3.141592653589793 AS DOUBLE);

-- DECIMAL: 精确小数
SELECT CAST(123.456 AS DECIMAL(10,3));
SELECT CAST(999999.999999 AS DECIMAL(15,6));
```

#### 2.1.2 字符串类型

**字符串操作：**
```sql
-- VARCHAR: 可变长度字符串
SELECT CAST('Hello World' AS VARCHAR(50));

-- CHAR: 固定长度字符串
SELECT CAST('ABC' AS CHAR(10)); -- 结果: 'ABC       '

-- 字符串函数
SELECT 
    LENGTH('Hello World') AS str_length,
    UPPER('hello') AS uppercase,
    LOWER('WORLD') AS lowercase,
    SUBSTR('Hello World', 7, 5) AS substring,
    CONCAT('Hello', ' ', 'World') AS concatenated;
```

#### 2.1.3 日期时间类型

**日期时间操作：**
```sql
-- DATE: 日期类型
SELECT DATE '2023-12-25' AS christmas;

-- TIME: 时间类型
SELECT TIME '14:30:00' AS afternoon;

-- TIMESTAMP: 时间戳类型
SELECT TIMESTAMP '2023-12-25 14:30:00' AS christmas_afternoon;

-- TIMESTAMP WITH TIME ZONE: 带时区的时间戳
SELECT TIMESTAMP '2023-12-25 14:30:00 UTC' AS utc_time;

-- 日期时间计算
SELECT 
    CURRENT_DATE AS today,
    CURRENT_TIMESTAMP AS now,
    DATE_ADD('day', 30, CURRENT_DATE) AS thirty_days_later,
    DATE_DIFF('day', DATE '2023-01-01', CURRENT_DATE) AS days_since_new_year;
```

### 2.2 复杂数据类型

#### 2.2.1 数组类型

**数组操作：**
```sql
-- 创建数组
SELECT ARRAY[1, 2, 3, 4, 5] AS numbers;
SELECT ARRAY['apple', 'banana', 'cherry'] AS fruits;

-- 数组函数
SELECT 
    ARRAY[1, 2, 3, 4, 5] AS original_array,
    CARDINALITY(ARRAY[1, 2, 3, 4, 5]) AS array_size,
    ELEMENT_AT(ARRAY[1, 2, 3, 4, 5], 3) AS third_element,
    SLICE(ARRAY[1, 2, 3, 4, 5], 2, 3) AS slice_result,
    ARRAY_JOIN(ARRAY['a', 'b', 'c'], ',') AS joined_string;

-- 数组聚合
SELECT 
    custkey,
    ARRAY_AGG(orderkey) AS order_keys,
    ARRAY_AGG(totalprice ORDER BY orderdate) AS price_timeline
FROM orders
GROUP BY custkey
LIMIT 10;
```

#### 2.2.2 MAP 类型

**MAP 操作：**
```sql
-- 创建 MAP
SELECT MAP(ARRAY['key1', 'key2'], ARRAY['value1', 'value2']) AS sample_map;

-- MAP 函数
WITH map_data AS (
    SELECT MAP(ARRAY['name', 'age', 'city'], ARRAY['John', '30', 'NYC']) AS person_info
)
SELECT 
    person_info,
    MAP_KEYS(person_info) AS keys,
    MAP_VALUES(person_info) AS values,
    person_info['name'] AS person_name,
    CARDINALITY(person_info) AS map_size
FROM map_data;

-- MAP 聚合
SELECT 
    custkey,
    MAP_AGG(orderkey, totalprice) AS order_price_map
FROM orders
GROUP BY custkey
LIMIT 10;
```

#### 2.2.3 ROW 类型

**ROW 操作：**
```sql
-- 创建 ROW
SELECT ROW('John', 30, 'Engineer') AS person;
SELECT ROW(1, 'Apple', 2.50) AS product;

-- ROW 字段访问
WITH row_data AS (
    SELECT ROW('John Doe', 30, 'Engineer') AS person_info
)
SELECT 
    person_info,
    person_info.field0 AS name,
    person_info.field1 AS age,
    person_info.field2 AS job
FROM row_data;

-- 命名 ROW 类型
SELECT CAST(ROW('John', 30) AS ROW(name VARCHAR, age INTEGER)) AS named_person;
```

### 2.3 JSON 数据处理

#### 2.3.1 JSON 函数

**JSON 解析和提取：**
```sql
-- JSON 数据示例
WITH json_data AS (
    SELECT '{"name": "John", "age": 30, "skills": ["Java", "SQL", "Python"]}' AS json_str
)
SELECT 
    json_str,
    JSON_EXTRACT(json_str, '$.name') AS name,
    JSON_EXTRACT(json_str, '$.age') AS age,
    JSON_EXTRACT(json_str, '$.skills') AS skills,
    JSON_EXTRACT_SCALAR(json_str, '$.name') AS name_scalar,
    JSON_SIZE(json_str, '$.skills') AS skills_count
FROM json_data;
```

**JSON 数组处理：**
```sql
-- JSON 数组操作
WITH json_array_data AS (
    SELECT '[{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]' AS json_array
)
SELECT 
    json_array,
    JSON_EXTRACT(json_array, '$[0].name') AS first_name,
    JSON_EXTRACT(json_array, '$[1].id') AS second_id,
    JSON_SIZE(json_array) AS array_length
FROM json_array_data;
```

## 3. 内置函数库

### 3.1 数学函数

#### 3.1.1 基础数学函数

**算术函数：**
```sql
SELECT 
    ABS(-15) AS absolute_value,
    CEIL(4.3) AS ceiling,
    FLOOR(4.7) AS floor_value,
    ROUND(4.567, 2) AS rounded,
    TRUNCATE(4.567, 1) AS truncated,
    MOD(17, 5) AS modulo,
    POWER(2, 10) AS power_result,
    SQRT(16) AS square_root;
```

**三角函数：**
```sql
SELECT 
    PI() AS pi_value,
    SIN(PI()/2) AS sine,
    COS(0) AS cosine,
    TAN(PI()/4) AS tangent,
    ASIN(1) AS arcsine,
    ACOS(1) AS arccosine,
    ATAN(1) AS arctangent,
    DEGREES(PI()) AS pi_in_degrees,
    RADIANS(180) AS degrees_to_radians;
```

#### 3.1.2 统计函数

**统计计算：**
```sql
-- 基础统计
SELECT 
    COUNT(*) AS total_orders,
    COUNT(DISTINCT custkey) AS unique_customers,
    SUM(totalprice) AS total_revenue,
    AVG(totalprice) AS average_order_value,
    MIN(totalprice) AS min_order,
    MAX(totalprice) AS max_order,
    STDDEV(totalprice) AS standard_deviation,
    VARIANCE(totalprice) AS variance_value
FROM orders;

-- 百分位数计算
SELECT 
    APPROX_PERCENTILE(totalprice, 0.5) AS median_price,
    APPROX_PERCENTILE(totalprice, 0.25) AS q1_price,
    APPROX_PERCENTILE(totalprice, 0.75) AS q3_price,
    APPROX_PERCENTILE(totalprice, 0.95) AS p95_price
FROM orders;
```

### 3.2 字符串函数

#### 3.2.1 字符串操作

**基础字符串函数：**
```sql
SELECT 
    LENGTH('Hello World') AS str_length,
    CHAR_LENGTH('Hello 世界') AS char_count,
    UPPER('hello world') AS uppercase,
    LOWER('HELLO WORLD') AS lowercase,
    INITCAP('hello world') AS title_case,
    REVERSE('Hello') AS reversed,
    REPEAT('ABC', 3) AS repeated;
```

**字符串截取和拼接：**
```sql
SELECT 
    SUBSTR('Hello World', 7) AS substring_from,
    SUBSTR('Hello World', 1, 5) AS substring_range,
    LEFT('Hello World', 5) AS left_chars,
    RIGHT('Hello World', 5) AS right_chars,
    CONCAT('Hello', ' ', 'World') AS concatenated,
    'Hello' || ' ' || 'World' AS pipe_concat;
```

#### 3.2.2 字符串搜索和替换

**搜索函数：**
```sql
SELECT 
    POSITION('World' IN 'Hello World') AS position_result,
    STRPOS('Hello World', 'World') AS strpos_result,
    STARTS_WITH('Hello World', 'Hello') AS starts_with_result,
    ENDS_WITH('Hello World', 'World') AS ends_with_result;
```

**替换函数：**
```sql
SELECT 
    REPLACE('Hello World', 'World', 'Universe') AS replaced,
    TRANSLATE('Hello', 'el', 'XY') AS translated,
    TRIM('  Hello World  ') AS trimmed,
    LTRIM('  Hello World') AS left_trimmed,
    RTRIM('Hello World  ') AS right_trimmed;
```

### 3.3 日期时间函数

#### 3.3.1 日期时间提取

**时间组件提取：**
```sql
SELECT 
    EXTRACT(YEAR FROM CURRENT_TIMESTAMP) AS current_year,
    EXTRACT(MONTH FROM CURRENT_TIMESTAMP) AS current_month,
    EXTRACT(DAY FROM CURRENT_TIMESTAMP) AS current_day,
    EXTRACT(HOUR FROM CURRENT_TIMESTAMP) AS current_hour,
    EXTRACT(MINUTE FROM CURRENT_TIMESTAMP) AS current_minute,
    EXTRACT(SECOND FROM CURRENT_TIMESTAMP) AS current_second,
    EXTRACT(DOW FROM CURRENT_DATE) AS day_of_week,
    EXTRACT(DOY FROM CURRENT_DATE) AS day_of_year;
```

#### 3.3.2 日期时间计算

**日期算术：**
```sql
SELECT 
    CURRENT_DATE AS today,
    DATE_ADD('day', 30, CURRENT_DATE) AS thirty_days_later,
    DATE_ADD('month', 3, CURRENT_DATE) AS three_months_later,
    DATE_ADD('year', 1, CURRENT_DATE) AS next_year,
    DATE_DIFF('day', DATE '2023-01-01', CURRENT_DATE) AS days_since_new_year,
    DATE_DIFF('month', DATE '2023-01-01', CURRENT_DATE) AS months_since_new_year;
```

**时间格式化：**
```sql
SELECT 
    DATE_FORMAT(CURRENT_TIMESTAMP, '%Y-%m-%d') AS formatted_date,
    DATE_FORMAT(CURRENT_TIMESTAMP, '%Y-%m-%d %H:%i:%s') AS formatted_datetime,
    DATE_PARSE('2023-12-25', '%Y-%m-%d') AS parsed_date,
    FROM_UNIXTIME(1640995200) AS from_unix_timestamp,
    TO_UNIXTIME(CURRENT_TIMESTAMP) AS to_unix_timestamp;
```

## 4. 窗口函数

### 4.1 窗口函数基础

#### 4.1.1 窗口函数语法

**基本语法结构：**
```sql
function_name([arguments]) OVER (
    [PARTITION BY partition_expression, ...]
    [ORDER BY sort_expression [ASC|DESC], ...]
    [frame_clause]
)
```

**窗口规范示例：**
```sql
-- 基础窗口函数
SELECT
    orderkey,
    custkey,
    totalprice,
    -- 简单排名
    ROW_NUMBER() OVER (ORDER BY totalprice DESC) AS row_num,
    -- 分区排名
    RANK() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS customer_rank,
    -- 密集排名
    DENSE_RANK() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS dense_rank
FROM orders;
```

#### 4.1.2 窗口框架

**框架类型：**
```sql
-- ROWS 框架：基于物理行数
SELECT
    orderkey,
    totalprice,
    -- 当前行和前2行
    SUM(totalprice) OVER (
        ORDER BY orderdate
        ROWS BETWEEN 2 PRECEDING AND CURRENT ROW
    ) AS rolling_sum_3,
    -- 当前行和前后各1行
    AVG(totalprice) OVER (
        ORDER BY orderdate
        ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING
    ) AS moving_avg_3
FROM orders;

-- RANGE 框架：基于值范围
SELECT
    orderkey,
    orderdate,
    totalprice,
    -- 过去30天的订单总额
    SUM(totalprice) OVER (
        ORDER BY orderdate
        RANGE BETWEEN INTERVAL '30' DAY PRECEDING AND CURRENT ROW
    ) AS sum_last_30_days
FROM orders;
```

### 4.2 排名函数

#### 4.2.1 基础排名函数

**排名函数对比：**
```sql
SELECT
    custkey,
    orderkey,
    totalprice,
    -- ROW_NUMBER: 连续唯一排名
    ROW_NUMBER() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS row_number,
    -- RANK: 相同值相同排名，后续排名跳跃
    RANK() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS rank_value,
    -- DENSE_RANK: 相同值相同排名，后续排名连续
    DENSE_RANK() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS dense_rank_value,
    -- PERCENT_RANK: 百分比排名
    PERCENT_RANK() OVER (PARTITION BY custkey ORDER BY totalprice DESC) AS percent_rank
FROM orders
WHERE custkey IN (1, 2, 3);
```

#### 4.2.2 分位数函数

**分位数计算：**
```sql
SELECT
    custkey,
    orderkey,
    totalprice,
    -- 累积分布
    CUME_DIST() OVER (PARTITION BY custkey ORDER BY totalprice) AS cumulative_dist,
    -- 分桶
    NTILE(4) OVER (PARTITION BY custkey ORDER BY totalprice) AS quartile,
    NTILE(10) OVER (PARTITION BY custkey ORDER BY totalprice) AS decile
FROM orders
WHERE custkey IN (1, 2, 3);
```

### 4.3 分析函数

#### 4.3.1 偏移函数

**LAG 和 LEAD 函数：**
```sql
SELECT
    custkey,
    orderkey,
    orderdate,
    totalprice,
    -- 前一个订单
    LAG(totalprice) OVER (PARTITION BY custkey ORDER BY orderdate) AS prev_order_price,
    LAG(orderdate) OVER (PARTITION BY custkey ORDER BY orderdate) AS prev_order_date,
    -- 后一个订单
    LEAD(totalprice) OVER (PARTITION BY custkey ORDER BY orderdate) AS next_order_price,
    -- 计算订单间隔
    DATE_DIFF('day',
        LAG(orderdate) OVER (PARTITION BY custkey ORDER BY orderdate),
        orderdate
    ) AS days_since_last_order
FROM orders
WHERE custkey IN (1, 2, 3)
ORDER BY custkey, orderdate;
```

#### 4.3.2 首尾值函数

**FIRST_VALUE 和 LAST_VALUE：**
```sql
SELECT
    custkey,
    orderkey,
    orderdate,
    totalprice,
    -- 第一个订单
    FIRST_VALUE(totalprice) OVER (
        PARTITION BY custkey
        ORDER BY orderdate
        ROWS UNBOUNDED PRECEDING
    ) AS first_order_price,
    -- 最后一个订单
    LAST_VALUE(totalprice) OVER (
        PARTITION BY custkey
        ORDER BY orderdate
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) AS last_order_price,
    -- 最高订单金额
    MAX(totalprice) OVER (PARTITION BY custkey) AS max_order_price
FROM orders
WHERE custkey IN (1, 2, 3)
ORDER BY custkey, orderdate;
```

### 4.4 聚合窗口函数

#### 4.4.1 累积聚合

**累积计算：**
```sql
SELECT
    custkey,
    orderkey,
    orderdate,
    totalprice,
    -- 累积订单数
    COUNT(*) OVER (
        PARTITION BY custkey
        ORDER BY orderdate
        ROWS UNBOUNDED PRECEDING
    ) AS cumulative_order_count,
    -- 累积金额
    SUM(totalprice) OVER (
        PARTITION BY custkey
        ORDER BY orderdate
        ROWS UNBOUNDED PRECEDING
    ) AS cumulative_amount,
    -- 累积平均
    AVG(totalprice) OVER (
        PARTITION BY custkey
        ORDER BY orderdate
        ROWS UNBOUNDED PRECEDING
    ) AS cumulative_avg
FROM orders
WHERE custkey IN (1, 2, 3)
ORDER BY custkey, orderdate;
```

#### 4.4.2 滑动窗口聚合

**移动平均和趋势分析：**
```sql
SELECT
    orderdate,
    daily_orders,
    daily_revenue,
    -- 7天移动平均
    AVG(daily_orders) OVER (
        ORDER BY orderdate
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) AS ma_7_orders,
    AVG(daily_revenue) OVER (
        ORDER BY orderdate
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) AS ma_7_revenue,
    -- 30天移动平均
    AVG(daily_revenue) OVER (
        ORDER BY orderdate
        ROWS BETWEEN 29 PRECEDING AND CURRENT ROW
    ) AS ma_30_revenue
FROM (
    SELECT
        orderdate,
        COUNT(*) AS daily_orders,
        SUM(totalprice) AS daily_revenue
    FROM orders
    GROUP BY orderdate
) daily_stats
ORDER BY orderdate;
```

## 5. 聚合函数

### 5.1 基础聚合函数

#### 5.1.1 标准聚合函数

**基本聚合：**
```sql
SELECT
    custkey,
    -- 计数聚合
    COUNT(*) AS total_orders,
    COUNT(DISTINCT orderkey) AS unique_orders,
    COUNT(comment) AS orders_with_comments,
    -- 数值聚合
    SUM(totalprice) AS total_spent,
    AVG(totalprice) AS avg_order_value,
    MIN(totalprice) AS min_order,
    MAX(totalprice) AS max_order,
    -- 统计聚合
    STDDEV(totalprice) AS price_stddev,
    VARIANCE(totalprice) AS price_variance
FROM orders
GROUP BY custkey
HAVING COUNT(*) > 5
ORDER BY total_spent DESC;
```

#### 5.1.2 近似聚合函数

**近似计算：**
```sql
SELECT
    -- 近似去重计数
    APPROX_DISTINCT(custkey) AS approx_unique_customers,
    -- 近似百分位数
    APPROX_PERCENTILE(totalprice, 0.5) AS median_price,
    APPROX_PERCENTILE(totalprice, 0.95) AS p95_price,
    APPROX_PERCENTILE(totalprice, ARRAY[0.25, 0.5, 0.75, 0.95]) AS quartiles,
    -- 近似最频值
    APPROX_MOST_FREQUENT(3, custkey, 10) AS top_customers
FROM orders;
```

### 5.2 高级聚合函数

#### 5.2.1 数组聚合

**数组聚合函数：**
```sql
SELECT
    custkey,
    -- 数组聚合
    ARRAY_AGG(orderkey) AS order_keys,
    ARRAY_AGG(totalprice ORDER BY orderdate) AS price_timeline,
    ARRAY_AGG(DISTINCT EXTRACT(YEAR FROM orderdate)) AS order_years,
    -- MAP 聚合
    MAP_AGG(orderkey, totalprice) AS order_price_map,
    -- 多重聚合
    MULTIMAP_AGG(EXTRACT(YEAR FROM orderdate), totalprice) AS yearly_prices
FROM orders
WHERE custkey IN (1, 2, 3)
GROUP BY custkey;
```

#### 5.2.2 统计聚合

**高级统计函数：**
```sql
SELECT
    custkey,
    COUNT(*) AS order_count,
    -- 几何平均
    GEOMETRIC_MEAN(totalprice) AS geometric_avg,
    -- 调和平均
    1.0 / AVG(1.0 / totalprice) AS harmonic_mean,
    -- 偏度和峰度
    SKEWNESS(totalprice) AS price_skewness,
    KURTOSIS(totalprice) AS price_kurtosis,
    -- 相关系数（需要两个变量）
    CORR(totalprice, EXTRACT(DOY FROM orderdate)) AS price_day_correlation
FROM orders
GROUP BY custkey
HAVING COUNT(*) > 10;
```

### 5.3 条件聚合

#### 5.3.1 CASE 表达式聚合

**条件聚合：**
```sql
SELECT
    custkey,
    COUNT(*) AS total_orders,
    -- 条件计数
    COUNT(CASE WHEN totalprice > 10000 THEN 1 END) AS high_value_orders,
    COUNT(CASE WHEN totalprice < 1000 THEN 1 END) AS low_value_orders,
    -- 条件求和
    SUM(CASE WHEN totalprice > 10000 THEN totalprice ELSE 0 END) AS high_value_revenue,
    SUM(CASE WHEN EXTRACT(MONTH FROM orderdate) IN (11, 12) THEN totalprice ELSE 0 END) AS holiday_revenue,
    -- 条件平均
    AVG(CASE WHEN totalprice > 1000 THEN totalprice END) AS avg_significant_order
FROM orders
GROUP BY custkey;
```

#### 5.3.2 FILTER 子句

**FILTER 聚合：**
```sql
SELECT
    custkey,
    COUNT(*) AS total_orders,
    -- 使用 FILTER 子句
    COUNT(*) FILTER (WHERE totalprice > 10000) AS high_value_orders,
    SUM(totalprice) FILTER (WHERE EXTRACT(YEAR FROM orderdate) = 2023) AS revenue_2023,
    AVG(totalprice) FILTER (WHERE totalprice BETWEEN 1000 AND 10000) AS avg_medium_orders,
    -- 多条件过滤
    COUNT(*) FILTER (WHERE totalprice > 5000 AND EXTRACT(MONTH FROM orderdate) IN (11, 12)) AS holiday_big_orders
FROM orders
GROUP BY custkey;
```

## 6. 正则表达式

### 6.1 正则表达式函数

#### 6.1.1 模式匹配

**基础正则表达式：**
```sql
-- 正则表达式匹配
SELECT
    name,
    phone,
    -- 检查是否匹配模式
    REGEXP_LIKE(phone, '^\+1-\d{3}-\d{3}-\d{4}$') AS valid_us_phone,
    REGEXP_LIKE(name, '^[A-Z][a-z]+ [A-Z][a-z]+$') AS proper_name_format,
    -- 提取匹配的部分
    REGEXP_EXTRACT(phone, '\d{3}-\d{3}-\d{4}') AS phone_number_only,
    REGEXP_EXTRACT(name, '^(\w+)') AS first_name
FROM customer
WHERE REGEXP_LIKE(phone, '\+1-\d{3}-\d{3}-\d{4}');
```

#### 6.1.2 字符串替换

**正则表达式替换：**
```sql
SELECT
    name,
    phone,
    address,
    -- 替换模式
    REGEXP_REPLACE(phone, '[^\d]', '') AS digits_only,
    REGEXP_REPLACE(name, '(\w+)\s+(\w+)', '$2, $1') AS last_first_format,
    REGEXP_REPLACE(address, '\s+', ' ') AS normalized_address,
    -- 分割字符串
    REGEXP_SPLIT(address, ',\s*') AS address_parts
FROM customer
LIMIT 10;
```

### 6.2 文本分析

#### 6.2.1 数据清洗

**文本清洗示例：**
```sql
WITH raw_data AS (
    SELECT
        '  John   Doe  ' AS name,
        '******-123-4567 ext. 123' AS phone,
        '<EMAIL>' AS email
)
SELECT
    -- 清理姓名
    REGEXP_REPLACE(TRIM(name), '\s+', ' ') AS clean_name,
    -- 提取电话号码
    REGEXP_EXTRACT(phone, '\+?1?[-.\s]?\(?(\d{3})\)?[-.\s]?(\d{3})[-.\s]?(\d{4})') AS clean_phone,
    -- 标准化邮箱
    LOWER(TRIM(email)) AS clean_email,
    -- 验证邮箱格式
    REGEXP_LIKE(email, '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') AS valid_email
FROM raw_data;
```

#### 6.2.2 数据验证

**数据格式验证：**
```sql
SELECT
    custkey,
    name,
    phone,
    -- 验证各种格式
    CASE
        WHEN REGEXP_LIKE(phone, '^\+1-\d{3}-\d{3}-\d{4}$') THEN 'US Format'
        WHEN REGEXP_LIKE(phone, '^\(\d{3}\) \d{3}-\d{4}$') THEN 'US Parentheses'
        WHEN REGEXP_LIKE(phone, '^\d{3}\.\d{3}\.\d{4}$') THEN 'US Dots'
        ELSE 'Invalid Format'
    END AS phone_format,
    -- 提取区号
    REGEXP_EXTRACT(phone, '[\(\+1-]?(\d{3})[\)-]?') AS area_code
FROM customer
WHERE phone IS NOT NULL;
```

## 7. 用户定义函数

### 7.1 SQL 函数

#### 7.1.1 标量函数

**创建 SQL 函数：**
```sql
-- 创建简单的标量函数
CREATE FUNCTION calculate_tax(amount DOUBLE, rate DOUBLE)
RETURNS DOUBLE
RETURN amount * rate;

-- 使用函数
SELECT
    orderkey,
    totalprice,
    calculate_tax(totalprice, 0.08) AS tax_amount,
    totalprice + calculate_tax(totalprice, 0.08) AS total_with_tax
FROM orders
LIMIT 10;

-- 创建复杂的函数
CREATE FUNCTION customer_tier(total_spent DOUBLE)
RETURNS VARCHAR
RETURN CASE
    WHEN total_spent >= 100000 THEN 'Platinum'
    WHEN total_spent >= 50000 THEN 'Gold'
    WHEN total_spent >= 10000 THEN 'Silver'
    ELSE 'Bronze'
END;
```

#### 7.1.2 表函数

**创建表函数：**
```sql
-- 创建表值函数
CREATE FUNCTION top_customers(min_orders INTEGER)
RETURNS TABLE(custkey BIGINT, order_count BIGINT, total_spent DOUBLE)
RETURN
    SELECT
        custkey,
        COUNT(*) AS order_count,
        SUM(totalprice) AS total_spent
    FROM orders
    GROUP BY custkey
    HAVING COUNT(*) >= min_orders
    ORDER BY total_spent DESC;

-- 使用表函数
SELECT * FROM TABLE(top_customers(10));
```

### 7.2 Java UDF

#### 7.2.1 Java 函数示例

**Java UDF 实现：**
```java
// Java UDF 示例
public class StringFunctions {
    @ScalarFunction("reverse_words")
    @Description("Reverse the order of words in a string")
    @SqlType(StandardTypes.VARCHAR)
    public static Slice reverseWords(@SqlType(StandardTypes.VARCHAR) Slice input) {
        if (input == null) {
            return null;
        }

        String str = input.toStringUtf8();
        String[] words = str.split("\\s+");
        Collections.reverse(Arrays.asList(words));
        return Slices.utf8Slice(String.join(" ", words));
    }

    @ScalarFunction("word_count")
    @Description("Count the number of words in a string")
    @SqlType(StandardTypes.BIGINT)
    public static long wordCount(@SqlType(StandardTypes.VARCHAR) Slice input) {
        if (input == null) {
            return 0;
        }

        String str = input.toStringUtf8().trim();
        if (str.isEmpty()) {
            return 0;
        }

        return str.split("\\s+").length;
    }
}
```

**使用 Java UDF：**
```sql
-- 使用自定义 Java 函数
SELECT
    comment,
    reverse_words(comment) AS reversed_comment,
    word_count(comment) AS comment_word_count
FROM orders
WHERE comment IS NOT NULL
LIMIT 10;
```

## 8. 高级 SQL 特性

### 8.1 公共表表达式（CTE）

#### 8.1.1 基础 CTE

**简单 CTE：**
```sql
-- 基础 CTE 示例
WITH customer_stats AS (
    SELECT
        custkey,
        COUNT(*) AS order_count,
        SUM(totalprice) AS total_spent,
        AVG(totalprice) AS avg_order_value
    FROM orders
    GROUP BY custkey
),
high_value_customers AS (
    SELECT custkey
    FROM customer_stats
    WHERE total_spent > 50000
)
SELECT
    c.name,
    cs.order_count,
    cs.total_spent,
    cs.avg_order_value
FROM customer_stats cs
JOIN customer c ON cs.custkey = c.custkey
JOIN high_value_customers hvc ON cs.custkey = hvc.custkey
ORDER BY cs.total_spent DESC;
```

#### 8.1.2 递归 CTE

**递归查询：**
```sql
-- 递归 CTE 示例（组织层次结构）
WITH RECURSIVE employee_hierarchy AS (
    -- 基础情况：顶级管理者
    SELECT
        employee_id,
        name,
        manager_id,
        1 AS level,
        CAST(name AS VARCHAR(1000)) AS path
    FROM employees
    WHERE manager_id IS NULL

    UNION ALL

    -- 递归情况：下级员工
    SELECT
        e.employee_id,
        e.name,
        e.manager_id,
        eh.level + 1,
        eh.path || ' -> ' || e.name
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
)
SELECT
    employee_id,
    name,
    level,
    path
FROM employee_hierarchy
ORDER BY level, name;
```

### 8.2 集合操作

#### 8.2.1 UNION 操作

**集合合并：**
```sql
-- UNION 示例
SELECT 'Q1' AS quarter, SUM(totalprice) AS revenue
FROM orders
WHERE EXTRACT(QUARTER FROM orderdate) = 1

UNION ALL

SELECT 'Q2' AS quarter, SUM(totalprice) AS revenue
FROM orders
WHERE EXTRACT(QUARTER FROM orderdate) = 2

UNION ALL

SELECT 'Q3' AS quarter, SUM(totalprice) AS revenue
FROM orders
WHERE EXTRACT(QUARTER FROM orderdate) = 3

UNION ALL

SELECT 'Q4' AS quarter, SUM(totalprice) AS revenue
FROM orders
WHERE EXTRACT(QUARTER FROM orderdate) = 4

ORDER BY quarter;
```

#### 8.2.2 INTERSECT 和 EXCEPT

**集合交集和差集：**
```sql
-- 找到既有大订单又有小订单的客户
SELECT custkey FROM orders WHERE totalprice > 10000
INTERSECT
SELECT custkey FROM orders WHERE totalprice < 1000;

-- 找到只有大订单没有小订单的客户
SELECT custkey FROM orders WHERE totalprice > 10000
EXCEPT
SELECT custkey FROM orders WHERE totalprice < 1000;
```

## 总结

Trino 的 SQL 语言和函数体系为数据分析提供了强大而灵活的工具集。其主要特点包括：

**语言特性优势：**
- **ANSI SQL 兼容**：标准 SQL 语法，易于迁移和集成
- **丰富的数据类型**：支持复杂数据结构和 JSON 处理
- **强大的函数库**：涵盖数学、字符串、日期时间等各个领域
- **高级分析功能**：窗口函数、聚合函数、正则表达式
- **扩展能力**：支持用户定义函数和自定义插件

**架构师考虑因素：**
- **性能优化**：合理使用窗口函数和聚合函数
- **数据类型选择**：根据业务需求选择合适的数据类型
- **函数使用策略**：平衡功能需求和执行效率
- **UDF 开发**：扩展 Trino 功能以满足特定业务需求
- **SQL 优化**：利用 Trino 的查询优化器特性

在实际应用中，需要根据具体的数据分析场景和性能要求，合理选择和组合这些 SQL 特性，以实现高效的数据处理和分析。
