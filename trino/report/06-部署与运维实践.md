# Trino 部署与运维实践

## 概述

生产环境的 Trino 部署需要综合考虑硬件配置、网络架构、安全策略、监控体系等多个方面。本报告基于企业级部署经验，深入分析 Trino 的部署架构、配置优化、监控运维、故障排除和容量规划等关键实践。

## 1. 部署架构设计

### 1.1 集群架构模式

#### 1.1.1 标准部署架构

```
┌─────────────────────────────────────────────────────────┐
│                    Load Balancer                       │
│                  (HAProxy/Nginx)                       │
├─────────────────────────────────────────────────────────┤
│  Coordinator 1  │  Coordinator 2  │  Coordinator 3     │
│   (Active)      │   (Standby)     │   (Standby)        │
├─────────────────────────────────────────────────────────┤
│  Worker 1  │  Worker 2  │  Worker 3  │  ...  │ Worker N │
├─────────────────────────────────────────────────────────┤
│              External Dependencies                      │
│  • Metastore (HMS/Glue)                                │
│  • Object Storage (S3/HDFS/Azure)                      │
│  • Authentication (LDAP/OAuth)                         │
│  • Monitoring (Prometheus/Grafana)                     │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 高可用架构

**Coordinator 高可用：**
- 多个 Coordinator 实例
- 负载均衡器健康检查
- 自动故障转移
- 会话状态外部化

**Worker 弹性扩展：**
- 动态添加/移除 Worker
- 自动服务发现
- 负载均衡分配

#### 1.1.3 容器化部署

**<PERSON><PERSON>netes 部署架构：**
```yaml
# Coordinator Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trino-coordinator
spec:
  replicas: 3
  selector:
    matchLabels:
      app: trino-coordinator
  template:
    metadata:
      labels:
        app: trino-coordinator
    spec:
      containers:
      - name: trino
        image: trinodb/trino:476
        ports:
        - containerPort: 8080
        env:
        - name: TRINO_NODE_TYPE
          value: "coordinator"
        volumeMounts:
        - name: config
          mountPath: /etc/trino
        resources:
          requests:
            memory: "8Gi"
            cpu: "2"
          limits:
            memory: "16Gi"
            cpu: "4"
```

### 1.2 硬件配置规划

#### 1.2.1 Coordinator 节点

**推荐配置：**
- **CPU**: 16-32 核心，高频率处理器
- **内存**: 64-128GB RAM
- **存储**: SSD，500GB+ 用于日志和临时文件
- **网络**: 10Gbps 网卡

**配置原理：**
- Coordinator 负责查询规划和协调
- 内存用于查询计划缓存和元数据
- CPU 用于查询解析和优化
- 网络带宽影响任务调度效率

#### 1.2.2 Worker 节点

**推荐配置：**
- **CPU**: 32-64 核心
- **内存**: 128-512GB RAM
- **存储**: NVMe SSD，1TB+ 用于 Spill
- **网络**: 25Gbps 网卡

**配置考虑：**
- 内存是最关键资源
- CPU 核心数影响并行度
- 存储用于内存溢出
- 网络带宽影响数据传输

#### 1.2.3 存储配置

**本地存储：**
```bash
# 磁盘配置
/data/trino/spill    # Spill 目录，NVMe SSD
/data/trino/logs     # 日志目录
/data/trino/tmp      # 临时文件目录
```

**网络存储：**
- 对象存储（S3/Azure/GCS）
- 分布式文件系统（HDFS）
- 网络文件系统（NFS）

## 2. 安装与配置

### 2.1 基础安装

#### 2.1.1 系统准备

**操作系统要求：**
```bash
# CentOS/RHEL 8+
sudo yum update -y
sudo yum install -y java-17-openjdk-devel

# Ubuntu 20.04+
sudo apt update
sudo apt install -y openjdk-17-jdk

# 创建 Trino 用户
sudo useradd -r -s /bin/false trino
sudo mkdir -p /opt/trino /data/trino
sudo chown -R trino:trino /opt/trino /data/trino
```

**网络配置：**
```bash
# 配置主机名解析
echo "************ trino-coordinator" >> /etc/hosts
echo "************ trino-worker-1" >> /etc/hosts
echo "************ trino-worker-2" >> /etc/hosts

# 防火墙配置
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=8443/tcp
sudo firewall-cmd --reload
```

#### 2.1.2 软件安装

**下载和安装：**
```bash
# 下载 Trino
cd /opt/trino
wget https://repo1.maven.org/maven2/io/trino/trino-server/476/trino-server-476.tar.gz
tar -xzf trino-server-476.tar.gz
ln -s trino-server-476 current

# 创建配置目录
mkdir -p /opt/trino/current/etc/catalog
mkdir -p /data/trino/{logs,spill,tmp}
```

### 2.2 配置文件

#### 2.2.1 Coordinator 配置

**config.properties：**
```properties
# 节点配置
coordinator=true
node-scheduler.include-coordinator=false
http-server.http.port=8080
query.max-memory=50GB
query.max-memory-per-node=8GB

# 发现服务
discovery.uri=http://trino-coordinator:8080

# 资源管理
query.max-concurrent-queries=500
query.max-queued-queries=1000

# 性能优化
task.concurrency=16
task.max-worker-threads=200
task.min-drivers=16

# Spill 配置
spill-enabled=true
spiller-spill-path=/data/trino/spill
spiller-max-used-space-threshold=0.9

# 安全配置
http-server.https.enabled=true
http-server.https.port=8443
http-server.https.keystore.path=/etc/trino/keystore.jks
http-server.https.keystore.key=changeit

# 内部通信
internal-communication.shared-secret=/etc/trino/shared-secret.txt
```

**node.properties：**
```properties
node.environment=production
node.id=coordinator-1
node.data-dir=/data/trino
```

**jvm.config：**
```properties
-server
-Xmx64G
-XX:+UseG1GC
-XX:G1HeapRegionSize=32M
-XX:+ExplicitGCInvokesConcurrent
-XX:+HeapDumpOnOutOfMemoryError
-XX:+ExitOnOutOfMemoryError
-XX:ReservedCodeCacheSize=512M
-XX:PerMethodRecompilationCutoff=10000
-XX:PerBytecodeRecompilationCutoff=10000
-Djdk.attach.allowAttachSelf=true
-Djdk.nio.maxCachedBufferSize=2000000
-XX:+UnlockDiagnosticVMOptions
-XX:+UseAESCTRIntrinsics
```

#### 2.2.2 Worker 配置

**config.properties：**
```properties
# 节点配置
coordinator=false
http-server.http.port=8080
query.max-memory-per-node=100GB

# 发现服务
discovery.uri=http://trino-coordinator:8080

# 性能优化
task.concurrency=32
task.max-worker-threads=400
task.min-drivers=32

# Spill 配置
spill-enabled=true
spiller-spill-path=/data/trino/spill
spiller-max-used-space-threshold=0.9

# 内部通信
internal-communication.shared-secret=/etc/trino/shared-secret.txt
```

**node.properties：**
```properties
node.environment=production
node.id=worker-1
node.data-dir=/data/trino
```

**jvm.config：**
```properties
-server
-Xmx400G
-XX:+UseG1GC
-XX:G1HeapRegionSize=32M
-XX:+ExplicitGCInvokesConcurrent
-XX:+HeapDumpOnOutOfMemoryError
-XX:+ExitOnOutOfMemoryError
-XX:ReservedCodeCacheSize=512M
-XX:PerMethodRecompilationCutoff=10000
-XX:PerBytecodeRecompilationCutoff=10000
-Djdk.attach.allowAttachSelf=true
-Djdk.nio.maxCachedBufferSize=2000000
-XX:+UnlockDiagnosticVMOptions
-XX:+UseAESCTRIntrinsics
```

### 2.3 Catalog 配置

#### 2.3.1 Hive Catalog

**hive.properties：**
```properties
connector.name=hive
hive.metastore.uri=thrift://hive-metastore:9083

# S3 配置
hive.s3.aws-access-key=AKIAIOSFODNN7EXAMPLE
hive.s3.aws-secret-key=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
hive.s3.endpoint=s3.amazonaws.com
hive.s3.ssl.enabled=true

# 性能优化
hive.max-split-size=128MB
hive.max-initial-splits=200
hive.split-loader-concurrency=4

# 压缩配置
hive.compression-codec=GZIP
hive.force-local-scheduling=true

# 统计信息
hive.collect-column-statistics-on-write=true
hive.metastore-cache-ttl=4h
```

#### 2.3.2 Iceberg Catalog

**iceberg.properties：**
```properties
connector.name=iceberg
iceberg.catalog.type=hive_metastore
hive.metastore.uri=thrift://hive-metastore:9083

# 文件格式
iceberg.file-format=PARQUET
iceberg.compression-codec=GZIP

# 性能优化
iceberg.target-max-file-size=1GB
iceberg.split-size=64MB

# 表属性
iceberg.table-statistics-enabled=true
iceberg.extended-statistics.enabled=true
```

## 3. 启动与管理

### 3.1 服务管理

#### 3.1.1 Systemd 服务

**创建服务文件：**
```bash
# /etc/systemd/system/trino.service
[Unit]
Description=Trino Server
After=network.target

[Service]
Type=forking
User=trino
Group=trino
ExecStart=/opt/trino/current/bin/launcher start
ExecStop=/opt/trino/current/bin/launcher stop
ExecReload=/opt/trino/current/bin/launcher restart
PIDFile=/data/trino/var/run/launcher.pid
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**服务操作：**
```bash
# 启用服务
sudo systemctl enable trino
sudo systemctl start trino

# 查看状态
sudo systemctl status trino

# 查看日志
sudo journalctl -u trino -f
```

#### 3.1.2 Docker 部署

**Dockerfile：**
```dockerfile
FROM trinodb/trino:476

# 复制配置文件
COPY etc/ /etc/trino/

# 设置权限
USER trino

# 暴露端口
EXPOSE 8080 8443

# 启动命令
CMD ["/usr/lib/trino/bin/run-trino"]
```

**Docker Compose：**
```yaml
version: '3.8'
services:
  trino-coordinator:
    image: trinodb/trino:476
    ports:
      - "8080:8080"
      - "8443:8443"
    volumes:
      - ./etc:/etc/trino:ro
      - trino-data:/data/trino
    environment:
      - TRINO_NODE_TYPE=coordinator
    networks:
      - trino-network

  trino-worker:
    image: trinodb/trino:476
    volumes:
      - ./etc:/etc/trino:ro
      - trino-data:/data/trino
    environment:
      - TRINO_NODE_TYPE=worker
    networks:
      - trino-network
    depends_on:
      - trino-coordinator
    deploy:
      replicas: 3

volumes:
  trino-data:

networks:
  trino-network:
    driver: bridge
```

### 3.2 集群管理

#### 3.2.1 节点管理

**添加 Worker 节点：**
```bash
# 1. 准备新节点
# 2. 安装 Trino
# 3. 配置 Worker 配置文件
# 4. 启动服务
sudo systemctl start trino

# 验证节点加入
curl http://trino-coordinator:8080/v1/node | jq '.[] | {nodeId, uri, nodeVersion}'
```

**移除 Worker 节点：**
```bash
# 1. 停止接收新任务
curl -X PUT http://trino-coordinator:8080/v1/node/{nodeId}/shutdown

# 2. 等待任务完成
# 3. 停止服务
sudo systemctl stop trino
```

#### 3.2.2 配置更新

**滚动更新策略：**
```bash
#!/bin/bash
# rolling-update.sh

NODES=("worker-1" "worker-2" "worker-3")

for node in "${NODES[@]}"; do
    echo "Updating $node..."

    # 停止节点接收新任务
    ssh $node "curl -X PUT http://localhost:8080/v1/node/shutdown"

    # 等待任务完成
    while [[ $(ssh $node "curl -s http://localhost:8080/v1/info" | jq -r '.activeWorkers') -gt 0 ]]; do
        echo "Waiting for tasks to complete on $node..."
        sleep 30
    done

    # 更新配置
    scp new-config.properties $node:/opt/trino/current/etc/

    # 重启服务
    ssh $node "sudo systemctl restart trino"

    # 验证节点健康
    while ! ssh $node "curl -s http://localhost:8080/v1/info" > /dev/null; do
        echo "Waiting for $node to start..."
        sleep 10
    done

    echo "$node updated successfully"
done
```

## 4. 性能调优

### 4.1 内存配置优化

#### 4.1.1 JVM 内存配置

**G1GC 调优：**
```properties
# jvm.config
-XX:+UseG1GC
-XX:G1HeapRegionSize=32M
-XX:MaxGCPauseMillis=200
-XX:G1NewSizePercent=20
-XX:G1MaxNewSizePercent=40
-XX:G1HeapWastePercent=5
-XX:G1MixedGCCountTarget=8
-XX:InitiatingHeapOccupancyPercent=70
```

**内存分配策略：**
- **Coordinator**: 总内存的 10-15%
- **Worker**: 总内存的 80-85%
- **系统预留**: 总内存的 5-10%

#### 4.1.2 查询内存配置

**内存限制配置：**
```properties
# config.properties
query.max-memory=500GB
query.max-memory-per-node=50GB
query.max-total-memory-per-node=60GB

# 内存管理
memory.heap-headroom-per-node=8GB
query.low-memory-killer.delay=5m
query.low-memory-killer.policy=total-reservation-on-blocked-nodes
```

### 4.2 并发配置优化

#### 4.2.1 任务并发配置

**任务级别配置：**
```properties
# 任务并发
task.concurrency=32
task.max-worker-threads=400
task.min-drivers=32

# 分片处理
task.max-split-concurrency=256
task.split-concurrency-adjustment-interval=100ms

# 网络配置
task.max-partial-aggregation-memory=32MB
task.operator-pre-allocated-memory=16MB
```

#### 4.2.2 查询并发控制

**并发限制：**
```properties
# 查询并发
query.max-concurrent-queries=1000
query.max-queued-queries=5000

# 资源组配置
resource-groups.configuration-manager=file
resource-groups.config-file=etc/resource-groups.json
```

**资源组配置示例：**
```json
{
  "rootGroups": [
    {
      "name": "global",
      "softMemoryLimit": "80%",
      "hardConcurrencyLimit": 1000,
      "maxQueued": 5000,
      "subGroups": [
        {
          "name": "adhoc",
          "softMemoryLimit": "30%",
          "hardConcurrencyLimit": 50,
          "maxQueued": 100
        },
        {
          "name": "pipeline",
          "softMemoryLimit": "50%",
          "hardConcurrencyLimit": 200,
          "maxQueued": 500
        }
      ]
    }
  ],
  "selectors": [
    {
      "user": ".*",
      "group": "global.adhoc"
    },
    {
      "source": "pipeline.*",
      "group": "global.pipeline"
    }
  ]
}
```

### 4.3 存储优化

#### 4.3.1 Spill 配置

**Spill 优化：**
```properties
# Spill 配置
spill-enabled=true
spiller-spill-path=/data/trino/spill
spiller-max-used-space-threshold=0.9
spiller-threads=4

# 压缩配置
spiller-compression-enabled=true
spiller-compression-codec=LZ4

# 加密配置
spiller-encryption-enabled=true
```

#### 4.3.2 连接器优化

**Hive 连接器优化：**
```properties
# 分片优化
hive.max-split-size=256MB
hive.max-initial-splits=200
hive.split-loader-concurrency=8

# 缓存优化
hive.metastore-cache-ttl=4h
hive.metastore-refresh-interval=1m

# 统计信息
hive.collect-column-statistics-on-write=true
hive.partition-statistics-sample-size=100
```

## 5. 监控与告警

### 5.1 监控体系架构

#### 5.1.1 监控组件

```
┌─────────────────────────────────────────────────────────┐
│                    Grafana Dashboard                    │
├─────────────────────────────────────────────────────────┤
│                    Prometheus                           │
├─────────────────────────────────────────────────────────┤
│  JMX Exporter  │  Node Exporter  │  Custom Metrics     │
├─────────────────────────────────────────────────────────┤
│  Trino Cluster │  OS Metrics     │  Application Logs   │
└─────────────────────────────────────────────────────────┘
```

#### 5.1.2 JMX 监控配置

**启用 JMX：**
```properties
# config.properties
jmx.rmiregistry.port=9080
jmx.rmiserver.port=9081
```

**JMX Exporter 配置：**
```yaml
# jmx_exporter.yml
rules:
  - pattern: 'trino.execution<name=QueryManager><>RunningQueries'
    name: trino_running_queries
    type: GAUGE

  - pattern: 'trino.execution<name=QueryManager><>QueuedQueries'
    name: trino_queued_queries
    type: GAUGE

  - pattern: 'trino.memory<name=ClusterMemoryManager><>ClusterMemoryBytes'
    name: trino_cluster_memory_bytes
    type: GAUGE

  - pattern: 'java.lang<type=Memory><HeapMemoryUsage>used'
    name: jvm_heap_memory_used_bytes
    type: GAUGE
```

### 5.2 关键指标监控

#### 5.2.1 集群级别指标

**集群健康指标：**
- 活跃节点数量
- 集群总内存使用率
- 网络吞吐量
- 磁盘 I/O 使用率

**查询性能指标：**
- 运行中查询数量
- 排队查询数量
- 查询完成率
- 平均查询执行时间

#### 5.2.2 节点级别指标

**系统资源指标：**
- CPU 使用率
- 内存使用率
- 磁盘使用率
- 网络带宽使用

**JVM 指标：**
- 堆内存使用
- GC 频率和时间
- 线程数量
- 类加载数量

### 5.3 Prometheus 配置

#### 5.3.1 Prometheus 配置

**prometheus.yml：**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "trino_alerts.yml"

scrape_configs:
  - job_name: 'trino-coordinator'
    static_configs:
      - targets: ['trino-coordinator:9080']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'trino-workers'
    static_configs:
      - targets:
        - 'trino-worker-1:9080'
        - 'trino-worker-2:9080'
        - 'trino-worker-3:9080'
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - 'trino-coordinator:9100'
        - 'trino-worker-1:9100'
        - 'trino-worker-2:9100'
        - 'trino-worker-3:9100'

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 5.3.2 告警规则

**trino_alerts.yml：**
```yaml
groups:
- name: trino_cluster
  rules:
  - alert: TrinoCoordinatorDown
    expr: up{job="trino-coordinator"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Trino Coordinator is down"
      description: "Trino Coordinator has been down for more than 1 minute"

  - alert: TrinoWorkerDown
    expr: up{job="trino-workers"} == 0
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Trino Worker is down"
      description: "Trino Worker {{ $labels.instance }} has been down for more than 2 minutes"

  - alert: TrinoHighMemoryUsage
    expr: trino_cluster_memory_bytes / trino_cluster_memory_total_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Trino cluster memory usage is high"
      description: "Trino cluster memory usage is {{ $value | humanizePercentage }}"

  - alert: TrinoHighQueuedQueries
    expr: trino_queued_queries > 100
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "High number of queued queries"
      description: "{{ $value }} queries are currently queued"

  - alert: TrinoLongRunningQuery
    expr: trino_query_execution_time_seconds > 3600
    for: 0m
    labels:
      severity: info
    annotations:
      summary: "Long running query detected"
      description: "Query {{ $labels.query_id }} has been running for more than 1 hour"
```

### 5.4 Grafana 仪表板

#### 5.4.1 集群概览仪表板

**关键面板：**
- 集群状态总览
- 节点健康状态
- 查询执行统计
- 资源使用趋势

#### 5.4.2 查询性能仪表板

**性能指标：**
- 查询吞吐量
- 查询延迟分布
- 失败查询统计
- 资源消耗分析

### 5.5 日志管理

#### 5.5.1 日志配置

**log.properties：**
```properties
# 根日志级别
io.trino=INFO

# 查询日志
io.trino.event.query=DEBUG
io.trino.execution.QueryManager=DEBUG

# 连接器日志
io.trino.plugin.hive=INFO
io.trino.plugin.iceberg=INFO

# 安全日志
io.trino.security=INFO

# 性能日志
io.trino.execution.TaskManager=INFO
io.trino.memory=INFO
```

#### 5.5.2 日志聚合

**ELK Stack 配置：**
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/trino/logs/*.log
  fields:
    service: trino
    environment: production
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "trino-logs-%{+yyyy.MM.dd}"
```

## 6. 故障排除

### 6.1 常见问题诊断

#### 6.1.1 内存相关问题

**OutOfMemoryError 诊断：**
```bash
# 检查堆转储
jhat -port 7000 /data/trino/heap-dump.hprof

# 分析内存使用
jmap -histo <pid> | head -20

# 检查 GC 日志
grep "Full GC" /data/trino/logs/gc.log
```

**内存泄漏排查：**
- 检查查询复杂度
- 分析数据倾斜
- 验证连接器配置
- 监控内存增长趋势

#### 6.1.2 性能问题诊断

**查询性能分析：**
```sql
-- 查看运行中的查询
SELECT
    query_id,
    state,
    user_name,
    source,
    query,
    created,
    execution_start_time,
    end_time
FROM system.runtime.queries
WHERE state = 'RUNNING'
ORDER BY created DESC;

-- 分析查询统计
SELECT
    query_id,
    cpu_time,
    wall_time,
    queued_time,
    peak_memory_bytes,
    processed_rows,
    processed_bytes
FROM system.runtime.queries
WHERE end_time > current_timestamp - interval '1' hour
ORDER BY cpu_time DESC
LIMIT 10;
```

**慢查询优化：**
- 检查执行计划
- 分析数据分布
- 优化 JOIN 顺序
- 调整分区策略

#### 6.1.3 连接问题诊断

**网络连接检查：**
```bash
# 检查端口连通性
telnet trino-coordinator 8080

# 检查 DNS 解析
nslookup trino-coordinator

# 检查防火墙规则
iptables -L -n | grep 8080
```

**服务发现问题：**
```bash
# 检查节点注册
curl http://trino-coordinator:8080/v1/node

# 检查集群状态
curl http://trino-coordinator:8080/v1/cluster
```

### 6.2 故障恢复

#### 6.2.1 Coordinator 故障恢复

**自动故障转移：**
```bash
#!/bin/bash
# coordinator-failover.sh

PRIMARY_COORDINATOR="trino-coordinator-1"
BACKUP_COORDINATOR="trino-coordinator-2"
LOAD_BALANCER_CONFIG="/etc/haproxy/haproxy.cfg"

# 检查主 Coordinator 健康状态
if ! curl -f http://$PRIMARY_COORDINATOR:8080/v1/info > /dev/null 2>&1; then
    echo "Primary coordinator is down, switching to backup..."

    # 更新负载均衡器配置
    sed -i "s/server primary $PRIMARY_COORDINATOR:8080 check/server primary $PRIMARY_COORDINATOR:8080 check backup/" $LOAD_BALANCER_CONFIG
    sed -i "s/server backup $BACKUP_COORDINATOR:8080 check backup/server backup $BACKUP_COORDINATOR:8080 check/" $LOAD_BALANCER_CONFIG

    # 重新加载负载均衡器
    systemctl reload haproxy

    echo "Failover completed"
fi
```

#### 6.2.2 Worker 故障恢复

**Worker 自动重启：**
```bash
#!/bin/bash
# worker-health-check.sh

WORKER_NODES=("worker-1" "worker-2" "worker-3")

for worker in "${WORKER_NODES[@]}"; do
    if ! curl -f http://$worker:8080/v1/info > /dev/null 2>&1; then
        echo "Worker $worker is down, attempting restart..."

        ssh $worker "sudo systemctl restart trino"

        # 等待服务启动
        sleep 30

        if curl -f http://$worker:8080/v1/info > /dev/null 2>&1; then
            echo "Worker $worker restarted successfully"
        else
            echo "Failed to restart worker $worker, manual intervention required"
            # 发送告警
            curl -X POST http://alertmanager:9093/api/v1/alerts \
                -H "Content-Type: application/json" \
                -d '[{
                    "labels": {
                        "alertname": "WorkerRestartFailed",
                        "instance": "'$worker'",
                        "severity": "critical"
                    },
                    "annotations": {
                        "summary": "Failed to restart Trino worker",
                        "description": "Worker '$worker' failed to restart automatically"
                    }
                }]'
        fi
    fi
done
```

### 6.3 数据恢复

#### 6.3.1 查询状态恢复

**查询重试机制：**
```properties
# config.properties
query.retry-policy=TASK
query.retry-attempts=3
query.retry-initial-delay=10s
query.retry-max-delay=1m
query.retry-delay-scale-factor=2.0

# 任务重试
task.retry-policy=TASK
task.retry-attempts=3
task.retry-initial-delay=5s
```

#### 6.3.2 元数据恢复

**Metastore 备份恢复：**
```bash
#!/bin/bash
# metastore-backup.sh

BACKUP_DIR="/backup/metastore"
MYSQL_HOST="metastore-db"
MYSQL_USER="hive"
MYSQL_PASSWORD="password"
DATABASE="metastore"

# 创建备份
mysqldump -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD $DATABASE > $BACKUP_DIR/metastore_$(date +%Y%m%d_%H%M%S).sql

# 保留最近30天的备份
find $BACKUP_DIR -name "metastore_*.sql" -mtime +30 -delete

# 恢复备份（如需要）
# mysql -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD $DATABASE < $BACKUP_DIR/metastore_backup.sql
```

## 7. 容量规划

### 7.1 资源需求评估

#### 7.1.1 计算资源规划

**CPU 需求计算：**
```
总 CPU 需求 = 并发查询数 × 平均 CPU 使用率 × 安全系数
安全系数 = 1.5-2.0（考虑峰值负载）

示例：
- 并发查询：100
- 平均 CPU 使用率：4 核/查询
- 安全系数：1.5
- 总需求：100 × 4 × 1.5 = 600 核
```

**内存需求计算：**
```
总内存需求 = 并发查询数 × 平均内存使用 × 安全系数

示例：
- 并发查询：100
- 平均内存使用：8GB/查询
- 安全系数：1.5
- 总需求：100 × 8 × 1.5 = 1200GB
```

#### 7.1.2 存储需求规划

**Spill 存储计算：**
```
Spill 存储 = 总内存 × Spill 比例 × 压缩比

示例：
- 总内存：1200GB
- Spill 比例：20%
- 压缩比：0.3
- Spill 存储：1200 × 0.2 × 0.3 = 72GB/节点
```

### 7.2 扩容策略

#### 7.2.1 水平扩容

**Worker 节点扩容：**
```bash
#!/bin/bash
# scale-workers.sh

CURRENT_WORKERS=$(curl -s http://trino-coordinator:8080/v1/node | jq '. | length')
TARGET_WORKERS=$1

if [ $TARGET_WORKERS -gt $CURRENT_WORKERS ]; then
    WORKERS_TO_ADD=$((TARGET_WORKERS - CURRENT_WORKERS))
    echo "Adding $WORKERS_TO_ADD worker nodes..."

    for i in $(seq 1 $WORKERS_TO_ADD); do
        NEW_WORKER_ID=$((CURRENT_WORKERS + i))

        # 启动新的 Worker 实例
        docker run -d \
            --name trino-worker-$NEW_WORKER_ID \
            --network trino-network \
            -v ./etc:/etc/trino:ro \
            -e TRINO_NODE_TYPE=worker \
            -e NODE_ID=worker-$NEW_WORKER_ID \
            trinodb/trino:476

        echo "Started worker-$NEW_WORKER_ID"
    done
fi
```

#### 7.2.2 垂直扩容

**内存扩容配置：**
```properties
# 更新 JVM 配置
-Xmx800G  # 从 400G 扩容到 800G

# 更新查询内存限制
query.max-memory-per-node=200GB  # 从 100GB 扩容到 200GB
```

### 7.3 性能基准测试

#### 7.3.1 TPC-H 基准测试

**测试环境准备：**
```sql
-- 创建 TPC-H 测试数据
CREATE SCHEMA tpch_sf1000;

-- 生成测试数据（1TB 规模）
CREATE TABLE tpch_sf1000.lineitem AS
SELECT * FROM tpch.sf1000.lineitem;

-- 收集统计信息
ANALYZE TABLE tpch_sf1000.lineitem;
```

**性能测试脚本：**
```bash
#!/bin/bash
# tpch-benchmark.sh

QUERIES_DIR="tpch-queries"
RESULTS_DIR="benchmark-results"
SCALE_FACTOR="1000"

mkdir -p $RESULTS_DIR

for query_file in $QUERIES_DIR/q*.sql; do
    query_name=$(basename $query_file .sql)
    echo "Running $query_name..."

    start_time=$(date +%s.%N)

    trino --server https://trino-coordinator:8443 \
          --catalog tpch \
          --schema sf$SCALE_FACTOR \
          --file $query_file \
          --output-format CSV \
          > $RESULTS_DIR/${query_name}_result.csv

    end_time=$(date +%s.%N)
    execution_time=$(echo "$end_time - $start_time" | bc)

    echo "$query_name,$execution_time" >> $RESULTS_DIR/benchmark_summary.csv
    echo "$query_name completed in ${execution_time}s"
done
```

#### 7.3.2 性能指标分析

**关键性能指标：**
- **吞吐量**: 查询/小时
- **延迟**: P50, P95, P99 响应时间
- **资源利用率**: CPU, 内存, 网络, 磁盘
- **并发能力**: 最大并发查询数

## 8. 运维最佳实践

### 8.1 部署最佳实践

#### 8.1.1 环境隔离

**多环境部署：**
```
开发环境：
- 单节点部署
- 最小资源配置
- 快速迭代测试

测试环境：
- 小规模集群
- 生产配置验证
- 性能基准测试

生产环境：
- 高可用集群
- 完整监控体系
- 严格变更控制
```

#### 8.1.2 配置管理

**配置版本控制：**
```bash
# Git 管理配置文件
git init /etc/trino-config
cd /etc/trino-config

# 添加配置文件
git add config.properties node.properties jvm.config
git commit -m "Initial Trino configuration"

# 配置变更追踪
git log --oneline config.properties
```

### 8.2 运维自动化

#### 8.2.1 部署自动化

**Ansible Playbook：**
```yaml
# trino-deployment.yml
---
- hosts: trino_coordinators
  become: yes
  vars:
    trino_version: "476"
    trino_home: "/opt/trino"
    trino_data: "/data/trino"

  tasks:
    - name: Create trino user
      user:
        name: trino
        system: yes
        shell: /bin/false

    - name: Create directories
      file:
        path: "{{ item }}"
        state: directory
        owner: trino
        group: trino
      loop:
        - "{{ trino_home }}"
        - "{{ trino_data }}"
        - "{{ trino_data }}/logs"
        - "{{ trino_data }}/spill"

    - name: Download Trino
      get_url:
        url: "https://repo1.maven.org/maven2/io/trino/trino-server/{{ trino_version }}/trino-server-{{ trino_version }}.tar.gz"
        dest: "/tmp/trino-server-{{ trino_version }}.tar.gz"

    - name: Extract Trino
      unarchive:
        src: "/tmp/trino-server-{{ trino_version }}.tar.gz"
        dest: "{{ trino_home }}"
        owner: trino
        group: trino
        remote_src: yes

    - name: Create symlink
      file:
        src: "{{ trino_home }}/trino-server-{{ trino_version }}"
        dest: "{{ trino_home }}/current"
        state: link

    - name: Copy configuration files
      template:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        owner: trino
        group: trino
      loop:
        - { src: "config.properties.j2", dest: "{{ trino_home }}/current/etc/config.properties" }
        - { src: "node.properties.j2", dest: "{{ trino_home }}/current/etc/node.properties" }
        - { src: "jvm.config.j2", dest: "{{ trino_home }}/current/etc/jvm.config" }
      notify: restart trino

    - name: Install systemd service
      template:
        src: trino.service.j2
        dest: /etc/systemd/system/trino.service
      notify:
        - reload systemd
        - restart trino

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: yes

    - name: restart trino
      systemd:
        name: trino
        state: restarted
        enabled: yes
```

#### 8.2.2 监控自动化

**健康检查脚本：**
```bash
#!/bin/bash
# health-check.sh

COORDINATOR="trino-coordinator:8080"
WORKERS=("worker-1:8080" "worker-2:8080" "worker-3:8080")
ALERT_WEBHOOK="http://alertmanager:9093/api/v1/alerts"

# 检查 Coordinator
if ! curl -f http://$COORDINATOR/v1/info > /dev/null 2>&1; then
    echo "CRITICAL: Coordinator is down"
    # 发送告警
    curl -X POST $ALERT_WEBHOOK \
        -H "Content-Type: application/json" \
        -d '[{
            "labels": {
                "alertname": "CoordinatorDown",
                "severity": "critical"
            },
            "annotations": {
                "summary": "Trino Coordinator is down"
            }
        }]'
    exit 1
fi

# 检查 Workers
for worker in "${WORKERS[@]}"; do
    if ! curl -f http://$worker/v1/info > /dev/null 2>&1; then
        echo "WARNING: Worker $worker is down"
    fi
done

# 检查查询队列
QUEUED_QUERIES=$(curl -s http://$COORDINATOR/v1/cluster | jq '.queuedQueries')
if [ $QUEUED_QUERIES -gt 100 ]; then
    echo "WARNING: High number of queued queries: $QUEUED_QUERIES"
fi

echo "Health check completed"
```

### 8.3 安全运维

#### 8.3.1 访问控制

**运维权限管理：**
```json
{
  "catalogs": [
    {
      "group": "trino_admins",
      "catalog": ".*",
      "allow": "all"
    },
    {
      "group": "trino_operators",
      "catalog": "system",
      "allow": "read-only"
    }
  ],
  "systemInformation": [
    {
      "group": "trino_admins",
      "allow": "read,write"
    },
    {
      "group": "trino_operators",
      "allow": "read"
    }
  ]
}
```

#### 8.3.2 审计日志

**运维操作审计：**
```bash
#!/bin/bash
# audit-log.sh

OPERATION=$1
USER=$(whoami)
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
AUDIT_LOG="/var/log/trino/operations.log"

echo "[$TIMESTAMP] User: $USER, Operation: $OPERATION" >> $AUDIT_LOG

# 示例使用
# ./audit-log.sh "Restarted Trino service"
# ./audit-log.sh "Updated configuration"
```

## 总结

Trino 的部署与运维是一个系统性工程，需要综合考虑架构设计、性能优化、监控告警、故障处理等多个方面。

对于架构师而言，关键的运维考虑因素包括：
- **架构设计**：高可用、可扩展的集群架构
- **资源规划**：基于业务需求的容量规划
- **性能优化**：内存、并发、存储的全面优化
- **监控体系**：完善的指标监控和告警机制
- **故障处理**：自动化的故障检测和恢复
- **运维自动化**：减少人工干预，提高运维效率

在实际部署中，需要根据具体的业务场景、数据规模和性能要求，制定相应的部署策略和运维方案，确保 Trino 集群的稳定性和高性能。
