# Trino 开发者指南

## 概述

Trino 提供了强大的插件架构和扩展机制，允许开发者通过 SPI（Service Provider Interface）接口扩展其功能。本报告深入分析 Trino 的开发框架、插件开发、连接器开发、自定义函数开发等核心开发技术，为架构师提供全面的扩展开发指南。

## 1. SPI 架构概览

### 1.1 SPI 设计原理

#### 1.1.1 插件架构

Trino 采用插件化架构，通过 SPI 接口实现功能扩展：

```
┌─────────────────────────────────────────────────────────┐
│                    Trino Core                           │
├─────────────────────────────────────────────────────────┤
│                    SPI Layer                            │
├─────────────────────────────────────────────────────────┤
│  Connector  │  Function  │  Type  │  Security  │ Event │
│   Plugin    │   Plugin   │ Plugin │   Plugin   │Plugin │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 SPI 接口分类

**核心 SPI 接口：**
- **Plugin**: 插件入口点
- **ConnectorFactory**: 连接器工厂
- **ConnectorMetadata**: 元数据管理
- **ConnectorSplitManager**: 数据分片管理
- **ConnectorRecordSetProvider**: 数据读取
- **ConnectorPageSourceProvider**: 页面数据源

**扩展 SPI 接口：**
- **Type**: 自定义数据类型
- **Function**: 自定义函数
- **SystemAccessControl**: 访问控制
- **EventListener**: 事件监听
- **PasswordAuthenticator**: 认证器

### 1.2 插件开发基础

#### 1.2.1 Maven 项目结构

**基础项目结构：**
```
my-trino-plugin/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/example/plugin/
│       │       ├── MyPlugin.java
│       │       ├── MyConnectorFactory.java
│       │       └── MyConnector.java
│       └── resources/
│           └── META-INF/
│               └── services/
│                   └── io.trino.spi.Plugin
```

**Maven 依赖配置：**
```xml
<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>my-trino-plugin</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <trino.version>476</trino.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.trino</groupId>
            <artifactId>trino-spi</artifactId>
            <version>${trino.version}</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.2</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
            </plugin>
        </plugins>
    </build>
</project>
```

#### 1.2.2 插件入口点

**Plugin 接口实现：**
```java
package com.example.plugin;

import io.trino.spi.Plugin;
import io.trino.spi.connector.ConnectorFactory;
import java.util.List;

public class MyPlugin implements Plugin {
    @Override
    public Iterable<ConnectorFactory> getConnectorFactories() {
        return List.of(new MyConnectorFactory());
    }
    
    @Override
    public Iterable<Class<?>> getFunctions() {
        return List.of(MyCustomFunctions.class);
    }
    
    @Override
    public Iterable<Type> getTypes() {
        return List.of(new MyCustomType());
    }
}
```

**服务注册文件：**
```
# src/main/resources/META-INF/services/io.trino.spi.Plugin
com.example.plugin.MyPlugin
```

## 2. 连接器开发

### 2.1 连接器架构

#### 2.1.1 连接器组件

**核心组件关系：**
```java
ConnectorFactory
    ↓
Connector
    ├── ConnectorMetadata
    ├── ConnectorSplitManager
    ├── ConnectorRecordSetProvider
    └── ConnectorPageSourceProvider
```

#### 2.1.2 ConnectorFactory 实现

**连接器工厂：**
```java
package com.example.plugin;

import io.trino.spi.connector.Connector;
import io.trino.spi.connector.ConnectorContext;
import io.trino.spi.connector.ConnectorFactory;
import java.util.Map;

public class MyConnectorFactory implements ConnectorFactory {
    @Override
    public String getName() {
        return "my-connector";
    }

    @Override
    public Connector create(String catalogName, Map<String, String> config, ConnectorContext context) {
        return new MyConnector(catalogName, config, context);
    }
}
```

### 2.2 元数据管理

#### 2.2.1 ConnectorMetadata 实现

**基础元数据实现：**
```java
package com.example.plugin;

import io.trino.spi.connector.*;
import io.trino.spi.type.Type;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class MyConnectorMetadata implements ConnectorMetadata {
    
    @Override
    public List<String> listSchemaNames(ConnectorSession session) {
        // 返回 schema 列表
        return List.of("default", "test");
    }

    @Override
    public List<SchemaTableName> listTables(ConnectorSession session, Optional<String> schemaName) {
        // 返回表列表
        if (schemaName.isPresent()) {
            return getTablesForSchema(schemaName.get());
        }
        return getAllTables();
    }

    @Override
    public ConnectorTableHandle getTableHandle(ConnectorSession session, SchemaTableName tableName) {
        // 返回表句柄
        if (tableExists(tableName)) {
            return new MyTableHandle(tableName);
        }
        return null;
    }

    @Override
    public ConnectorTableMetadata getTableMetadata(ConnectorSession session, ConnectorTableHandle table) {
        MyTableHandle tableHandle = (MyTableHandle) table;
        List<ColumnMetadata> columns = getTableColumns(tableHandle.getTableName());
        return new ConnectorTableMetadata(tableHandle.getTableName(), columns);
    }

    @Override
    public Map<String, ColumnHandle> getColumnHandles(ConnectorSession session, ConnectorTableHandle tableHandle) {
        MyTableHandle myTableHandle = (MyTableHandle) tableHandle;
        return getColumnHandlesForTable(myTableHandle.getTableName());
    }

    @Override
    public ColumnMetadata getColumnMetadata(ConnectorSession session, ConnectorTableHandle tableHandle, ColumnHandle columnHandle) {
        MyColumnHandle myColumnHandle = (MyColumnHandle) columnHandle;
        return new ColumnMetadata(myColumnHandle.getName(), myColumnHandle.getType());
    }

    private List<SchemaTableName> getTablesForSchema(String schemaName) {
        // 实现获取指定 schema 的表列表
        return List.of();
    }

    private List<SchemaTableName> getAllTables() {
        // 实现获取所有表列表
        return List.of();
    }

    private boolean tableExists(SchemaTableName tableName) {
        // 实现表存在性检查
        return true;
    }

    private List<ColumnMetadata> getTableColumns(SchemaTableName tableName) {
        // 实现获取表列信息
        return List.of();
    }

    private Map<String, ColumnHandle> getColumnHandlesForTable(SchemaTableName tableName) {
        // 实现获取列句柄映射
        return Map.of();
    }
}
```

#### 2.2.2 表和列句柄

**表句柄实现：**
```java
package com.example.plugin;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.trino.spi.connector.ConnectorTableHandle;
import io.trino.spi.connector.SchemaTableName;

public class MyTableHandle implements ConnectorTableHandle {
    private final SchemaTableName tableName;

    @JsonCreator
    public MyTableHandle(@JsonProperty("tableName") SchemaTableName tableName) {
        this.tableName = tableName;
    }

    @JsonProperty
    public SchemaTableName getTableName() {
        return tableName;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MyTableHandle that = (MyTableHandle) obj;
        return tableName.equals(that.tableName);
    }

    @Override
    public int hashCode() {
        return tableName.hashCode();
    }

    @Override
    public String toString() {
        return "MyTableHandle{" + "tableName=" + tableName + '}';
    }
}
```

**列句柄实现：**
```java
package com.example.plugin;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.trino.spi.connector.ColumnHandle;
import io.trino.spi.type.Type;

public class MyColumnHandle implements ColumnHandle {
    private final String name;
    private final Type type;
    private final int ordinalPosition;

    @JsonCreator
    public MyColumnHandle(
            @JsonProperty("name") String name,
            @JsonProperty("type") Type type,
            @JsonProperty("ordinalPosition") int ordinalPosition) {
        this.name = name;
        this.type = type;
        this.ordinalPosition = ordinalPosition;
    }

    @JsonProperty
    public String getName() {
        return name;
    }

    @JsonProperty
    public Type getType() {
        return type;
    }

    @JsonProperty
    public int getOrdinalPosition() {
        return ordinalPosition;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MyColumnHandle that = (MyColumnHandle) obj;
        return ordinalPosition == that.ordinalPosition &&
               name.equals(that.name) &&
               type.equals(that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, type, ordinalPosition);
    }

    @Override
    public String toString() {
        return "MyColumnHandle{" +
               "name='" + name + '\'' +
               ", type=" + type +
               ", ordinalPosition=" + ordinalPosition +
               '}';
    }
}
```

### 2.3 数据分片管理

#### 2.3.1 SplitManager 实现

**分片管理器：**
```java
package com.example.plugin;

import io.trino.spi.connector.*;
import java.util.List;

public class MySplitManager implements ConnectorSplitManager {
    
    @Override
    public ConnectorSplitSource getSplits(
            ConnectorTransactionHandle transaction,
            ConnectorSession session,
            ConnectorTableHandle table,
            DynamicFilter dynamicFilter,
            Constraint constraint) {
        
        MyTableHandle tableHandle = (MyTableHandle) table;
        List<ConnectorSplit> splits = generateSplits(tableHandle, constraint);
        
        return new FixedSplitSource(splits);
    }

    private List<ConnectorSplit> generateSplits(MyTableHandle tableHandle, Constraint constraint) {
        // 根据表和约束条件生成数据分片
        // 这里可以实现分区裁剪、谓词下推等优化
        
        List<ConnectorSplit> splits = new ArrayList<>();
        
        // 示例：为每个数据文件创建一个分片
        List<String> dataFiles = getDataFiles(tableHandle);
        for (String dataFile : dataFiles) {
            if (shouldIncludeFile(dataFile, constraint)) {
                splits.add(new MySplit(dataFile, tableHandle.getTableName()));
            }
        }
        
        return splits;
    }

    private List<String> getDataFiles(MyTableHandle tableHandle) {
        // 获取表对应的数据文件列表
        return List.of();
    }

    private boolean shouldIncludeFile(String dataFile, Constraint constraint) {
        // 根据约束条件判断是否包含该文件
        return true;
    }
}
```

#### 2.3.2 Split 实现

**数据分片：**
```java
package com.example.plugin;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.trino.spi.connector.ConnectorSplit;
import io.trino.spi.HostAddress;
import java.util.List;

public class MySplit implements ConnectorSplit {
    private final String dataFile;
    private final SchemaTableName tableName;
    private final List<HostAddress> addresses;

    @JsonCreator
    public MySplit(
            @JsonProperty("dataFile") String dataFile,
            @JsonProperty("tableName") SchemaTableName tableName) {
        this.dataFile = dataFile;
        this.tableName = tableName;
        this.addresses = getPreferredHosts(dataFile);
    }

    @JsonProperty
    public String getDataFile() {
        return dataFile;
    }

    @JsonProperty
    public SchemaTableName getTableName() {
        return tableName;
    }

    @Override
    public boolean isRemotelyAccessible() {
        return true;
    }

    @Override
    public List<HostAddress> getAddresses() {
        return addresses;
    }

    @Override
    public Object getInfo() {
        return this;
    }

    private List<HostAddress> getPreferredHosts(String dataFile) {
        // 返回数据文件的首选主机列表
        // 这有助于数据本地性优化
        return List.of();
    }
}

### 2.4 数据读取

#### 2.4.1 PageSourceProvider 实现

**页面数据源提供者：**
```java
package com.example.plugin;

import io.trino.spi.connector.*;
import java.util.List;

public class MyPageSourceProvider implements ConnectorPageSourceProvider {

    @Override
    public ConnectorPageSource createPageSource(
            ConnectorTransactionHandle transaction,
            ConnectorSession session,
            ConnectorSplit split,
            ConnectorTableHandle table,
            List<ColumnHandle> columns,
            DynamicFilter dynamicFilter) {

        MySplit mySplit = (MySplit) split;
        MyTableHandle tableHandle = (MyTableHandle) table;

        return new MyPageSource(mySplit, tableHandle, columns);
    }
}
```

#### 2.4.2 PageSource 实现

**页面数据源：**
```java
package com.example.plugin;

import io.trino.spi.Page;
import io.trino.spi.block.Block;
import io.trino.spi.block.BlockBuilder;
import io.trino.spi.connector.ConnectorPageSource;
import io.trino.spi.type.Type;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class MyPageSource implements ConnectorPageSource {
    private final MySplit split;
    private final MyTableHandle tableHandle;
    private final List<ColumnHandle> columns;
    private final List<Type> columnTypes;
    private boolean finished = false;

    public MyPageSource(MySplit split, MyTableHandle tableHandle, List<ColumnHandle> columns) {
        this.split = split;
        this.tableHandle = tableHandle;
        this.columns = columns;
        this.columnTypes = columns.stream()
                .map(column -> ((MyColumnHandle) column).getType())
                .collect(toList());
    }

    @Override
    public long getCompletedBytes() {
        return 0;
    }

    @Override
    public long getReadTimeNanos() {
        return 0;
    }

    @Override
    public boolean isFinished() {
        return finished;
    }

    @Override
    public Page getNextPage() {
        if (finished) {
            return null;
        }

        // 读取数据并构建页面
        List<List<Object>> rows = readDataFromSplit(split);
        if (rows.isEmpty()) {
            finished = true;
            return null;
        }

        Block[] blocks = new Block[columnTypes.size()];
        for (int i = 0; i < columnTypes.size(); i++) {
            blocks[i] = buildBlock(columnTypes.get(i), rows, i);
        }

        finished = true; // 简化示例，实际可能需要分批读取
        return new Page(rows.size(), blocks);
    }

    @Override
    public void close() {
        // 清理资源
    }

    private List<List<Object>> readDataFromSplit(MySplit split) {
        // 从分片读取数据
        // 这里可以实现各种数据源的读取逻辑
        return List.of();
    }

    private Block buildBlock(Type type, List<List<Object>> rows, int columnIndex) {
        BlockBuilder blockBuilder = type.createBlockBuilder(null, rows.size());
        for (List<Object> row : rows) {
            Object value = row.get(columnIndex);
            if (value == null) {
                blockBuilder.appendNull();
            } else {
                type.writeObject(blockBuilder, value);
            }
        }
        return blockBuilder.build();
    }
}
```

## 3. 自定义函数开发

### 3.1 SQL 用户定义函数

#### 3.1.1 SQL UDF 语法

**基础 SQL 函数：**
```sql
-- 创建标量函数
CREATE FUNCTION calculate_discount(price DOUBLE, discount_rate DOUBLE)
RETURNS DOUBLE
LANGUAGE SQL
DETERMINISTIC
RETURNS NULL ON NULL INPUT
COMMENT 'Calculate discounted price'
RETURN price * (1 - discount_rate);

-- 使用函数
SELECT
    product_name,
    original_price,
    calculate_discount(original_price, 0.15) AS discounted_price
FROM products;
```

**复杂 SQL 函数：**
```sql
-- 创建表值函数
CREATE FUNCTION top_selling_products(category_filter VARCHAR, limit_count INTEGER)
RETURNS TABLE(
    product_id BIGINT,
    product_name VARCHAR,
    total_sales DOUBLE,
    sales_rank INTEGER
)
LANGUAGE SQL
DETERMINISTIC
COMMENT 'Get top selling products by category'
RETURN
    SELECT
        p.product_id,
        p.product_name,
        SUM(s.sales_amount) AS total_sales,
        ROW_NUMBER() OVER (ORDER BY SUM(s.sales_amount) DESC) AS sales_rank
    FROM products p
    JOIN sales s ON p.product_id = s.product_id
    WHERE p.category = category_filter
    GROUP BY p.product_id, p.product_name
    ORDER BY total_sales DESC
    LIMIT limit_count;

-- 使用表值函数
SELECT * FROM TABLE(top_selling_products('Electronics', 10));
```

#### 3.1.2 函数属性

**函数特性配置：**
```sql
-- 确定性函数
CREATE FUNCTION hash_value(input VARCHAR)
RETURNS BIGINT
LANGUAGE SQL
DETERMINISTIC  -- 相同输入总是产生相同输出
RETURNS NULL ON NULL INPUT
RETURN HASH(input);

-- 非确定性函数
CREATE FUNCTION current_timestamp_millis()
RETURNS BIGINT
LANGUAGE SQL
NOT DETERMINISTIC  -- 每次调用可能产生不同结果
CALLED ON NULL INPUT
RETURN TO_UNIXTIME(CURRENT_TIMESTAMP) * 1000;

-- 安全上下文
CREATE FUNCTION get_user_data(user_id BIGINT)
RETURNS TABLE(user_name VARCHAR, email VARCHAR)
LANGUAGE SQL
SECURITY DEFINER  -- 使用定义者权限执行
COMMENT 'Get user data with elevated privileges'
RETURN
    SELECT user_name, email
    FROM sensitive_user_table
    WHERE id = user_id;
```

### 3.2 Java 用户定义函数

#### 3.2.1 标量函数

**Java 标量函数实现：**
```java
package com.example.functions;

import io.trino.spi.function.Description;
import io.trino.spi.function.ScalarFunction;
import io.trino.spi.function.SqlType;
import io.trino.spi.type.StandardTypes;
import io.airlift.slice.Slice;
import io.airlift.slice.Slices;

public class StringFunctions {

    @ScalarFunction("reverse_string")
    @Description("Reverse a string")
    @SqlType(StandardTypes.VARCHAR)
    public static Slice reverseString(@SqlType(StandardTypes.VARCHAR) Slice input) {
        if (input == null) {
            return null;
        }

        String str = input.toStringUtf8();
        String reversed = new StringBuilder(str).reverse().toString();
        return Slices.utf8Slice(reversed);
    }

    @ScalarFunction("word_count")
    @Description("Count words in a string")
    @SqlType(StandardTypes.BIGINT)
    public static long wordCount(@SqlType(StandardTypes.VARCHAR) Slice input) {
        if (input == null) {
            return 0;
        }

        String str = input.toStringUtf8().trim();
        if (str.isEmpty()) {
            return 0;
        }

        return str.split("\\s+").length;
    }

    @ScalarFunction("extract_domain")
    @Description("Extract domain from email address")
    @SqlType(StandardTypes.VARCHAR)
    public static Slice extractDomain(@SqlType(StandardTypes.VARCHAR) Slice email) {
        if (email == null) {
            return null;
        }

        String emailStr = email.toStringUtf8();
        int atIndex = emailStr.lastIndexOf('@');
        if (atIndex == -1 || atIndex == emailStr.length() - 1) {
            return null;
        }

        String domain = emailStr.substring(atIndex + 1);
        return Slices.utf8Slice(domain);
    }
}
```

#### 3.2.2 聚合函数

**Java 聚合函数实现：**
```java
package com.example.functions;

import io.trino.spi.function.AggregationFunction;
import io.trino.spi.function.CombineFunction;
import io.trino.spi.function.InputFunction;
import io.trino.spi.function.OutputFunction;
import io.trino.spi.function.SqlType;
import io.trino.spi.type.StandardTypes;
import io.trino.spi.block.BlockBuilder;

@AggregationFunction("geometric_mean")
public class GeometricMeanFunction {

    @InputFunction
    public static void input(GeometricMeanState state, @SqlType(StandardTypes.DOUBLE) double value) {
        if (value > 0) {
            state.addValue(Math.log(value));
        }
    }

    @CombineFunction
    public static void combine(GeometricMeanState state, GeometricMeanState otherState) {
        state.merge(otherState);
    }

    @OutputFunction(StandardTypes.DOUBLE)
    public static void output(GeometricMeanState state, BlockBuilder out) {
        if (state.getCount() == 0) {
            out.appendNull();
        } else {
            double result = Math.exp(state.getSum() / state.getCount());
            out.writeDouble(result);
        }
    }

    public static class GeometricMeanState {
        private double sum = 0.0;
        private long count = 0;

        public void addValue(double logValue) {
            sum += logValue;
            count++;
        }

        public void merge(GeometricMeanState other) {
            sum += other.sum;
            count += other.count;
        }

        public double getSum() {
            return sum;
        }

        public long getCount() {
            return count;
        }
    }
}
```

#### 3.2.3 窗口函数

**Java 窗口函数实现：**
```java
package com.example.functions;

import io.trino.spi.function.WindowFunction;
import io.trino.spi.function.Description;
import io.trino.spi.function.SqlType;
import io.trino.spi.type.StandardTypes;
import io.trino.spi.block.BlockBuilder;

@WindowFunction("moving_average")
@Description("Calculate moving average over a window")
public class MovingAverageFunction implements WindowFunction {

    @Override
    public void processRow(
            BlockBuilder output,
            int frameStart,
            int frameEnd,
            int currentPosition) {

        double sum = 0.0;
        int count = 0;

        for (int i = frameStart; i <= frameEnd; i++) {
            if (!isNull(i)) {
                sum += getValue(i);
                count++;
            }
        }

        if (count > 0) {
            output.writeDouble(sum / count);
        } else {
            output.appendNull();
        }
    }

    private boolean isNull(int position) {
        // 检查指定位置的值是否为 null
        return false;
    }

    private double getValue(int position) {
        // 获取指定位置的值
        return 0.0;
    }
}
```

### 3.3 Python 用户定义函数

#### 3.3.1 Python UDF 基础

**Python 函数示例：**
```python
# python_functions.py
import json
import re
from typing import Optional

def extract_json_field(json_str: str, field_path: str) -> Optional[str]:
    """Extract field from JSON string using dot notation path"""
    if not json_str:
        return None

    try:
        data = json.loads(json_str)
        fields = field_path.split('.')

        current = data
        for field in fields:
            if isinstance(current, dict) and field in current:
                current = current[field]
            else:
                return None

        return str(current) if current is not None else None
    except (json.JSONDecodeError, AttributeError):
        return None

def validate_email(email: str) -> bool:
    """Validate email address format"""
    if not email:
        return False

    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two points using Haversine formula"""
    import math

    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))

    # Radius of earth in kilometers
    r = 6371

    return c * r
```

#### 3.3.2 Python UDF 注册

**注册 Python 函数：**
```sql
-- 注册 Python 函数
CREATE FUNCTION extract_json_field(json_str VARCHAR, field_path VARCHAR)
RETURNS VARCHAR
LANGUAGE PYTHON
WITH (
    handler = 'python_functions.extract_json_field'
);

CREATE FUNCTION validate_email(email VARCHAR)
RETURNS BOOLEAN
LANGUAGE PYTHON
WITH (
    handler = 'python_functions.validate_email'
);

CREATE FUNCTION calculate_distance(lat1 DOUBLE, lon1 DOUBLE, lat2 DOUBLE, lon2 DOUBLE)
RETURNS DOUBLE
LANGUAGE PYTHON
WITH (
    handler = 'python_functions.calculate_distance'
);

-- 使用 Python 函数
SELECT
    user_id,
    extract_json_field(user_profile, 'preferences.language') AS preferred_language,
    validate_email(email) AS is_valid_email,
    calculate_distance(home_lat, home_lon, work_lat, work_lon) AS commute_distance
FROM users;
```

## 4. 自定义数据类型

### 4.1 Type 接口实现

#### 4.1.1 基础类型实现

**自定义数据类型：**
```java
package com.example.types;

import io.trino.spi.type.AbstractType;
import io.trino.spi.type.TypeSignature;
import io.trino.spi.block.Block;
import io.trino.spi.block.BlockBuilder;
import io.airlift.slice.Slice;

public class IpAddressType extends AbstractType {
    public static final IpAddressType IP_ADDRESS = new IpAddressType();
    public static final String NAME = "ipaddress";

    private IpAddressType() {
        super(new TypeSignature(NAME), Slice.class);
    }

    @Override
    public Object getObjectValue(ConnectorSession session, Block block, int position) {
        if (block.isNull(position)) {
            return null;
        }
        return block.getSlice(position, 0, block.getSliceLength(position)).toStringUtf8();
    }

    @Override
    public void writeSlice(BlockBuilder blockBuilder, Slice value) {
        blockBuilder.writeBytes(value, 0, value.length()).closeEntry();
    }

    @Override
    public void writeObject(BlockBuilder blockBuilder, Object value) {
        if (value instanceof String) {
            writeSlice(blockBuilder, Slices.utf8Slice((String) value));
        } else {
            throw new IllegalArgumentException("Expected String, got " + value.getClass().getName());
        }
    }

    @Override
    public Slice getSlice(Block block, int position) {
        return block.getSlice(position, 0, block.getSliceLength(position));
    }

    @Override
    public void appendTo(Block block, int position, BlockBuilder blockBuilder) {
        if (block.isNull(position)) {
            blockBuilder.appendNull();
        } else {
            blockBuilder.writeBytes(block, position, 0, block.getSliceLength(position)).closeEntry();
        }
    }

    @Override
    public boolean equalTo(Block leftBlock, int leftPosition, Block rightBlock, int rightPosition) {
        Slice leftSlice = getSlice(leftBlock, leftPosition);
        Slice rightSlice = getSlice(rightBlock, rightPosition);
        return leftSlice.equals(rightSlice);
    }

    @Override
    public long hash(Block block, int position) {
        return getSlice(block, position).hashCode();
    }

    @Override
    public int compareTo(Block leftBlock, int leftPosition, Block rightBlock, int rightPosition) {
        Slice leftSlice = getSlice(leftBlock, leftPosition);
        Slice rightSlice = getSlice(rightBlock, rightPosition);
        return leftSlice.compareTo(rightSlice);
    }

    @Override
    public String getDisplayName() {
        return NAME;
    }
}
```

#### 4.1.2 参数化类型

**参数化类型实现：**
```java
package com.example.types;

import io.trino.spi.type.ParametricType;
import io.trino.spi.type.Type;
import io.trino.spi.type.TypeManager;
import io.trino.spi.type.TypeParameter;
import java.util.List;

public class FixedLengthStringType implements ParametricType {
    public static final FixedLengthStringType FIXED_LENGTH_STRING = new FixedLengthStringType();

    @Override
    public String getName() {
        return "fixed_string";
    }

    @Override
    public Type createType(TypeManager typeManager, List<TypeParameter> parameters) {
        if (parameters.size() != 1) {
            throw new IllegalArgumentException("Expected exactly one parameter, got " + parameters.size());
        }

        TypeParameter parameter = parameters.get(0);
        if (!parameter.isLongLiteral()) {
            throw new IllegalArgumentException("Expected length parameter to be a number");
        }

        long length = parameter.getLongLiteral();
        if (length < 0 || length > Integer.MAX_VALUE) {
            throw new IllegalArgumentException("Invalid length: " + length);
        }

        return new FixedLengthStringTypeImpl((int) length);
    }
}
```

## 5. 事件监听器

### 5.1 EventListener 实现

#### 5.1.1 查询事件监听

**事件监听器实现：**
```java
package com.example.listeners;

import io.trino.spi.eventlistener.EventListener;
import io.trino.spi.eventlistener.QueryCompletedEvent;
import io.trino.spi.eventlistener.QueryCreatedEvent;
import io.trino.spi.eventlistener.SplitCompletedEvent;

public class MyEventListener implements EventListener {

    @Override
    public void queryCreated(QueryCreatedEvent queryCreatedEvent) {
        // 查询创建事件处理
        String queryId = queryCreatedEvent.getMetadata().getQueryId();
        String query = queryCreatedEvent.getMetadata().getQuery();
        String user = queryCreatedEvent.getContext().getUser();

        logQueryCreated(queryId, user, query);
    }

    @Override
    public void queryCompleted(QueryCompletedEvent queryCompletedEvent) {
        // 查询完成事件处理
        String queryId = queryCompletedEvent.getMetadata().getQueryId();
        String queryState = queryCompletedEvent.getMetadata().getQueryState();
        long executionTimeMs = queryCompletedEvent.getStatistics().getWallTime().toMillis();

        logQueryCompleted(queryId, queryState, executionTimeMs);

        // 记录性能指标
        recordQueryMetrics(queryCompletedEvent);
    }

    @Override
    public void splitCompleted(SplitCompletedEvent splitCompletedEvent) {
        // 分片完成事件处理
        String queryId = splitCompletedEvent.getQueryId();
        long cpuTimeMs = splitCompletedEvent.getStatistics().getCpuTime().toMillis();
        long wallTimeMs = splitCompletedEvent.getStatistics().getWallTime().toMillis();

        logSplitCompleted(queryId, cpuTimeMs, wallTimeMs);
    }

    private void logQueryCreated(String queryId, String user, String query) {
        // 实现查询创建日志记录
        System.out.printf("Query created: %s by %s: %s%n", queryId, user, query);
    }

    private void logQueryCompleted(String queryId, String state, long executionTimeMs) {
        // 实现查询完成日志记录
        System.out.printf("Query completed: %s, state: %s, time: %dms%n",
                         queryId, state, executionTimeMs);
    }

    private void logSplitCompleted(String queryId, long cpuTimeMs, long wallTimeMs) {
        // 实现分片完成日志记录
        System.out.printf("Split completed for query: %s, CPU: %dms, Wall: %dms%n",
                         queryId, cpuTimeMs, wallTimeMs);
    }

    private void recordQueryMetrics(QueryCompletedEvent event) {
        // 记录查询性能指标到监控系统
        // 例如：Prometheus、InfluxDB 等
    }
}
```

#### 5.1.2 EventListenerFactory 实现

**事件监听器工厂：**
```java
package com.example.listeners;

import io.trino.spi.eventlistener.EventListener;
import io.trino.spi.eventlistener.EventListenerFactory;
import java.util.Map;

public class MyEventListenerFactory implements EventListenerFactory {

    @Override
    public String getName() {
        return "my-event-listener";
    }

    @Override
    public EventListener create(Map<String, String> config) {
        // 根据配置创建事件监听器
        boolean enableQueryLogging = Boolean.parseBoolean(
            config.getOrDefault("query.logging.enabled", "true"));
        boolean enableMetrics = Boolean.parseBoolean(
            config.getOrDefault("metrics.enabled", "false"));

        return new MyEventListener(enableQueryLogging, enableMetrics);
    }
}
```

## 6. 访问控制

### 6.1 SystemAccessControl 实现

#### 6.1.1 基础访问控制

**系统访问控制实现：**
```java
package com.example.security;

import io.trino.spi.security.SystemAccessControl;
import io.trino.spi.security.Identity;
import io.trino.spi.security.AccessDeniedException;
import io.trino.spi.connector.CatalogSchemaName;
import io.trino.spi.connector.SchemaTableName;
import java.util.Set;

public class MySystemAccessControl implements SystemAccessControl {

    @Override
    public void checkCanSetUser(Identity identity, String userName) {
        // 检查是否可以设置用户
        if (!identity.getUser().equals(userName) && !isAdmin(identity)) {
            throw new AccessDeniedException("Cannot set user to " + userName);
        }
    }

    @Override
    public void checkCanAccessCatalog(Identity identity, String catalogName) {
        // 检查是否可以访问目录
        if (!hasAccessToCatalog(identity, catalogName)) {
            throw new AccessDeniedException("Access denied to catalog " + catalogName);
        }
    }

    @Override
    public Set<String> filterCatalogs(Identity identity, Set<String> catalogs) {
        // 过滤用户可访问的目录
        return catalogs.stream()
                .filter(catalog -> hasAccessToCatalog(identity, catalog))
                .collect(toSet());
    }

    @Override
    public void checkCanCreateSchema(Identity identity, CatalogSchemaName schema) {
        // 检查是否可以创建 schema
        if (!hasSchemaCreatePermission(identity, schema)) {
            throw new AccessDeniedException("Cannot create schema " + schema);
        }
    }

    @Override
    public void checkCanDropSchema(Identity identity, CatalogSchemaName schema) {
        // 检查是否可以删除 schema
        if (!hasSchemaDropPermission(identity, schema)) {
            throw new AccessDeniedException("Cannot drop schema " + schema);
        }
    }

    @Override
    public void checkCanSelectFromColumns(Identity identity, SchemaTableName table, Set<String> columns) {
        // 检查是否可以从指定列选择数据
        if (!hasSelectPermission(identity, table, columns)) {
            throw new AccessDeniedException("Cannot select from " + table);
        }
    }

    @Override
    public void checkCanInsertIntoTable(Identity identity, SchemaTableName table) {
        // 检查是否可以插入数据
        if (!hasInsertPermission(identity, table)) {
            throw new AccessDeniedException("Cannot insert into " + table);
        }
    }

    @Override
    public void checkCanDeleteFromTable(Identity identity, SchemaTableName table) {
        // 检查是否可以删除数据
        if (!hasDeletePermission(identity, table)) {
            throw new AccessDeniedException("Cannot delete from " + table);
        }
    }

    private boolean isAdmin(Identity identity) {
        // 检查用户是否为管理员
        return identity.getGroups().contains("admin");
    }

    private boolean hasAccessToCatalog(Identity identity, String catalogName) {
        // 检查用户是否有访问目录的权限
        return true; // 简化实现
    }

    private boolean hasSchemaCreatePermission(Identity identity, CatalogSchemaName schema) {
        // 检查用户是否有创建 schema 的权限
        return isAdmin(identity) || identity.getGroups().contains("schema_admin");
    }

    private boolean hasSchemaDropPermission(Identity identity, CatalogSchemaName schema) {
        // 检查用户是否有删除 schema 的权限
        return isAdmin(identity);
    }

    private boolean hasSelectPermission(Identity identity, SchemaTableName table, Set<String> columns) {
        // 检查用户是否有查询权限
        return true; // 简化实现
    }

    private boolean hasInsertPermission(Identity identity, SchemaTableName table) {
        // 检查用户是否有插入权限
        return !identity.getGroups().contains("readonly");
    }

    private boolean hasDeletePermission(Identity identity, SchemaTableName table) {
        // 检查用户是否有删除权限
        return isAdmin(identity) || identity.getGroups().contains("data_admin");
    }
}
```

#### 6.1.2 SystemAccessControlFactory 实现

**访问控制工厂：**
```java
package com.example.security;

import io.trino.spi.security.SystemAccessControl;
import io.trino.spi.security.SystemAccessControlFactory;
import java.util.Map;

public class MySystemAccessControlFactory implements SystemAccessControlFactory {

    @Override
    public String getName() {
        return "my-access-control";
    }

    @Override
    public SystemAccessControl create(Map<String, String> config) {
        // 根据配置创建访问控制实例
        String configFile = config.get("config.file");
        boolean strictMode = Boolean.parseBoolean(
            config.getOrDefault("strict.mode", "false"));

        return new MySystemAccessControl(configFile, strictMode);
    }
}

## 7. 插件部署与测试

### 7.1 插件构建

#### 7.1.1 Maven 构建配置

**完整的 pom.xml：**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>my-trino-plugin</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <trino.version>476</trino.version>
        <jackson.version>2.15.2</jackson.version>
        <guava.version>32.1.2-jre</guava.version>
    </properties>

    <dependencies>
        <!-- Trino SPI -->
        <dependency>
            <groupId>io.trino</groupId>
            <artifactId>trino-spi</artifactId>
            <version>${trino.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Guava utilities -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>io.trino</groupId>
            <artifactId>trino-testing</artifactId>
            <version>${trino.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.24.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```

#### 7.1.2 插件部署

**部署步骤：**
```bash
# 1. 构建插件
mvn clean package

# 2. 创建插件目录
mkdir -p /usr/lib/trino/plugin/my-connector

# 3. 复制插件 JAR 文件
cp target/my-trino-plugin-1.0.0.jar /usr/lib/trino/plugin/my-connector/

# 4. 创建目录配置文件
cat > /etc/trino/catalog/my-catalog.properties << EOF
connector.name=my-connector
# 其他连接器特定配置
EOF

# 5. 重启 Trino 服务
systemctl restart trino
```

### 7.2 插件测试

#### 7.2.1 单元测试

**连接器测试：**
```java
package com.example.plugin;

import io.trino.spi.connector.ConnectorSession;
import io.trino.spi.connector.SchemaTableName;
import io.trino.testing.TestingConnectorSession;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.assertj.core.api.Assertions.assertThat;

public class MyConnectorMetadataTest {
    private MyConnectorMetadata metadata;
    private ConnectorSession session;

    @BeforeEach
    public void setUp() {
        metadata = new MyConnectorMetadata();
        session = TestingConnectorSession.builder().build();
    }

    @Test
    public void testListSchemaNames() {
        List<String> schemas = metadata.listSchemaNames(session);
        assertThat(schemas).contains("default", "test");
    }

    @Test
    public void testGetTableHandle() {
        SchemaTableName tableName = new SchemaTableName("default", "test_table");
        ConnectorTableHandle handle = metadata.getTableHandle(session, tableName);
        assertThat(handle).isNotNull();
        assertThat(handle).isInstanceOf(MyTableHandle.class);
    }

    @Test
    public void testGetTableMetadata() {
        SchemaTableName tableName = new SchemaTableName("default", "test_table");
        MyTableHandle tableHandle = new MyTableHandle(tableName);

        ConnectorTableMetadata tableMetadata = metadata.getTableMetadata(session, tableHandle);
        assertThat(tableMetadata.getTable()).isEqualTo(tableName);
        assertThat(tableMetadata.getColumns()).isNotEmpty();
    }
}
```

#### 7.2.2 集成测试

**查询测试：**
```java
package com.example.plugin;

import io.trino.testing.DistributedQueryRunner;
import io.trino.testing.QueryRunner;
import io.trino.tests.AbstractTestQueryFramework;
import org.junit.jupiter.api.Test;

public class MyConnectorQueryTest extends AbstractTestQueryFramework {

    @Override
    protected QueryRunner createQueryRunner() throws Exception {
        DistributedQueryRunner queryRunner = DistributedQueryRunner.builder(
                TestingConnectorSession.builder().build())
                .setNodeCount(1)
                .build();

        queryRunner.installPlugin(new MyPlugin());
        queryRunner.createCatalog("my_catalog", "my-connector", Map.of());

        return queryRunner;
    }

    @Test
    public void testSelectFromTable() {
        assertQuery("SELECT count(*) FROM my_catalog.default.test_table", "VALUES 100");
    }

    @Test
    public void testTableMetadata() {
        assertQuery(
            "SELECT column_name, data_type FROM information_schema.columns " +
            "WHERE table_schema = 'default' AND table_name = 'test_table'",
            "VALUES ('id', 'bigint'), ('name', 'varchar')"
        );
    }

    @Test
    public void testCustomFunction() {
        assertQuery("SELECT reverse_string('hello')", "VALUES 'olleh'");
    }
}
```

#### 7.2.3 性能测试

**基准测试：**
```java
package com.example.plugin;

import io.trino.testing.DistributedQueryRunner;
import org.openjdk.jmh.annotations.*;
import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
public class MyConnectorBenchmark {

    private DistributedQueryRunner queryRunner;

    @Setup
    public void setup() throws Exception {
        queryRunner = DistributedQueryRunner.builder(
                TestingConnectorSession.builder().build())
                .setNodeCount(1)
                .build();

        queryRunner.installPlugin(new MyPlugin());
        queryRunner.createCatalog("benchmark", "my-connector", Map.of());
    }

    @TearDown
    public void tearDown() {
        queryRunner.close();
    }

    @Benchmark
    public void benchmarkSimpleSelect() {
        queryRunner.execute("SELECT count(*) FROM benchmark.default.large_table");
    }

    @Benchmark
    public void benchmarkComplexQuery() {
        queryRunner.execute(
            "SELECT category, avg(price), count(*) " +
            "FROM benchmark.default.products " +
            "GROUP BY category " +
            "ORDER BY avg(price) DESC"
        );
    }

    @Benchmark
    public void benchmarkCustomFunction() {
        queryRunner.execute(
            "SELECT word_count(description) " +
            "FROM benchmark.default.products " +
            "WHERE description IS NOT NULL"
        );
    }
}
```

## 8. 最佳实践

### 8.1 开发最佳实践

#### 8.1.1 代码组织

**项目结构建议：**
```
src/main/java/com/example/plugin/
├── MyPlugin.java                    # 插件入口
├── connector/
│   ├── MyConnectorFactory.java     # 连接器工厂
│   ├── MyConnector.java             # 连接器实现
│   ├── MyConnectorMetadata.java     # 元数据管理
│   ├── MySplitManager.java          # 分片管理
│   ├── MyPageSourceProvider.java    # 数据源提供者
│   └── MyPageSource.java            # 页面数据源
├── handles/
│   ├── MyTableHandle.java           # 表句柄
│   ├── MyColumnHandle.java          # 列句柄
│   └── MySplit.java                 # 数据分片
├── functions/
│   ├── StringFunctions.java         # 字符串函数
│   ├── MathFunctions.java           # 数学函数
│   └── AggregateFunctions.java      # 聚合函数
├── types/
│   └── CustomTypes.java             # 自定义类型
├── security/
│   └── MyAccessControl.java         # 访问控制
└── config/
    └── MyConnectorConfig.java       # 配置类
```

#### 8.1.2 性能优化

**优化策略：**
```java
// 1. 谓词下推
@Override
public ConnectorTableHandle applyFilter(
        ConnectorSession session,
        ConnectorTableHandle table,
        Constraint constraint) {

    MyTableHandle tableHandle = (MyTableHandle) table;
    TupleDomain<ColumnHandle> predicate = constraint.getSummary();

    // 将谓词转换为数据源特定的过滤条件
    Map<String, Object> filters = convertPredicateToFilters(predicate);

    return new MyTableHandle(
        tableHandle.getTableName(),
        tableHandle.getColumns(),
        filters  // 添加过滤条件
    );
}

// 2. 投影下推
@Override
public Optional<ProjectionApplicationResult<ConnectorTableHandle>> applyProjection(
        ConnectorSession session,
        ConnectorTableHandle handle,
        List<ConnectorExpression> projections,
        Map<String, ColumnHandle> assignments) {

    // 只读取需要的列
    List<ColumnHandle> projectedColumns = extractProjectedColumns(projections, assignments);

    MyTableHandle tableHandle = (MyTableHandle) handle;
    MyTableHandle newHandle = new MyTableHandle(
        tableHandle.getTableName(),
        projectedColumns,
        tableHandle.getFilters()
    );

    return Optional.of(new ProjectionApplicationResult<>(newHandle, projections, assignments));
}

// 3. 限制下推
@Override
public Optional<LimitApplicationResult<ConnectorTableHandle>> applyLimit(
        ConnectorSession session,
        ConnectorTableHandle handle,
        long limit) {

    MyTableHandle tableHandle = (MyTableHandle) handle;
    MyTableHandle newHandle = new MyTableHandle(
        tableHandle.getTableName(),
        tableHandle.getColumns(),
        tableHandle.getFilters(),
        OptionalLong.of(limit)  // 添加限制条件
    );

    return Optional.of(new LimitApplicationResult<>(newHandle, true, false));
}
```

#### 8.1.3 错误处理

**异常处理策略：**
```java
public class MyConnectorMetadata implements ConnectorMetadata {

    @Override
    public ConnectorTableHandle getTableHandle(ConnectorSession session, SchemaTableName tableName) {
        try {
            if (!tableExists(tableName)) {
                return null;  // 表不存在返回 null
            }
            return new MyTableHandle(tableName);
        } catch (Exception e) {
            throw new TrinoException(GENERIC_INTERNAL_ERROR,
                "Failed to get table handle for " + tableName, e);
        }
    }

    @Override
    public List<SchemaTableName> listTables(ConnectorSession session, Optional<String> schemaName) {
        try {
            return doListTables(schemaName);
        } catch (DataSourceException e) {
            throw new TrinoException(GENERIC_EXTERNAL_ERROR,
                "Failed to list tables in schema " + schemaName.orElse("all"), e);
        } catch (Exception e) {
            throw new TrinoException(GENERIC_INTERNAL_ERROR,
                "Unexpected error listing tables", e);
        }
    }

    private List<SchemaTableName> doListTables(Optional<String> schemaName) {
        // 实际的表列表获取逻辑
        return List.of();
    }
}
```

### 8.2 部署最佳实践

#### 8.2.1 配置管理

**配置类设计：**
```java
package com.example.plugin.config;

import io.airlift.configuration.Config;
import io.airlift.configuration.ConfigDescription;
import io.airlift.units.Duration;
import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;

public class MyConnectorConfig {
    private String connectionUrl;
    private String username;
    private String password;
    private Duration connectionTimeout = new Duration(30, TimeUnit.SECONDS);
    private int maxConnections = 100;
    private boolean enableMetrics = true;

    @NotNull
    public String getConnectionUrl() {
        return connectionUrl;
    }

    @Config("connection-url")
    @ConfigDescription("Database connection URL")
    public MyConnectorConfig setConnectionUrl(String connectionUrl) {
        this.connectionUrl = connectionUrl;
        return this;
    }

    public String getUsername() {
        return username;
    }

    @Config("connection-user")
    @ConfigDescription("Database username")
    public MyConnectorConfig setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getPassword() {
        return password;
    }

    @Config("connection-password")
    @ConfigDescription("Database password")
    public MyConnectorConfig setPassword(String password) {
        this.password = password;
        return this;
    }

    public Duration getConnectionTimeout() {
        return connectionTimeout;
    }

    @Config("connection-timeout")
    @ConfigDescription("Connection timeout")
    public MyConnectorConfig setConnectionTimeout(Duration connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
        return this;
    }

    public int getMaxConnections() {
        return maxConnections;
    }

    @Config("max-connections")
    @ConfigDescription("Maximum number of connections")
    public MyConnectorConfig setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
        return this;
    }

    public boolean isEnableMetrics() {
        return enableMetrics;
    }

    @Config("enable-metrics")
    @ConfigDescription("Enable metrics collection")
    public MyConnectorConfig setEnableMetrics(boolean enableMetrics) {
        this.enableMetrics = enableMetrics;
        return this;
    }
}
```

#### 8.2.2 监控集成

**指标收集：**
```java
package com.example.plugin.metrics;

import io.airlift.stats.CounterStat;
import io.airlift.stats.TimeStat;
import org.weakref.jmx.Managed;
import org.weakref.jmx.Nested;

public class MyConnectorMetrics {
    private final CounterStat queriesExecuted = new CounterStat();
    private final CounterStat queriesFailed = new CounterStat();
    private final TimeStat queryExecutionTime = new TimeStat();
    private final CounterStat rowsRead = new CounterStat();
    private final CounterStat bytesRead = new CounterStat();

    @Managed
    @Nested
    public CounterStat getQueriesExecuted() {
        return queriesExecuted;
    }

    @Managed
    @Nested
    public CounterStat getQueriesFailed() {
        return queriesFailed;
    }

    @Managed
    @Nested
    public TimeStat getQueryExecutionTime() {
        return queryExecutionTime;
    }

    @Managed
    @Nested
    public CounterStat getRowsRead() {
        return rowsRead;
    }

    @Managed
    @Nested
    public CounterStat getBytesRead() {
        return bytesRead;
    }

    public void recordQueryExecution(long executionTimeNanos) {
        queriesExecuted.update(1);
        queryExecutionTime.add(executionTimeNanos, TimeUnit.NANOSECONDS);
    }

    public void recordQueryFailure() {
        queriesFailed.update(1);
    }

    public void recordDataRead(long rows, long bytes) {
        rowsRead.update(rows);
        bytesRead.update(bytes);
    }
}
```

## 总结

Trino 的开发者生态系统为扩展和定制提供了强大的支持。通过 SPI 接口，开发者可以：

**核心开发能力：**
- **连接器开发**：集成各种数据源和存储系统
- **函数扩展**：SQL、Java、Python 多语言函数支持
- **类型系统**：自定义数据类型和参数化类型
- **安全集成**：访问控制和认证机制扩展
- **监控集成**：事件监听和指标收集

**架构师考虑因素：**
- **扩展策略**：根据业务需求选择合适的扩展点
- **性能优化**：实现谓词下推、投影下推等优化
- **兼容性管理**：确保插件与 Trino 版本兼容
- **测试策略**：单元测试、集成测试、性能测试
- **部署管理**：配置管理、监控集成、错误处理

在实际开发中，需要深入理解 Trino 的架构原理，合理设计插件接口，并遵循最佳实践，以构建高性能、可维护的扩展组件。
```
```
