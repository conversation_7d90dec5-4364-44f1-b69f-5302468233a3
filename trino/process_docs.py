#!/usr/bin/env python3
"""
Trino文档处理脚本
功能：
1. 整理HTML文档格式
2. 翻译文档内容
3. 重命名翻译后的文件
"""

import os
import re
import json
import time
from pathlib import Path
from bs4 import BeautifulSoup
import requests
from typing import List, Dict, Optional

class TrinoDocProcessor:
    def __init__(self, docs_dir: str):
        self.docs_dir = Path(docs_dir)
        self.current_dir = self.docs_dir / "trino.io" / "docs" / "current"
        self.processed_count = 0
        
    def find_html_files(self) -> List[Path]:
        """查找所有HTML文件"""
        html_files = []
        for file_path in self.current_dir.rglob("*.html"):
            # 跳过一些不需要翻译的文件
            if any(skip in str(file_path) for skip in ['search.html', 'genindex.html']):
                continue
            html_files.append(file_path)
        return sorted(html_files)
    
    def clean_html_content(self, html_content: str) -> BeautifulSoup:
        """清理和整理HTML内容，返回BeautifulSoup对象"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # 不移除脚本和样式，保持原始结构
        # 只清理文本内容中的多余空白
        for element in soup.find_all(text=True):
            if element.parent.name not in ['script', 'style', 'code', 'pre']:
                cleaned_text = re.sub(r'\s+', ' ', element.strip())
                if cleaned_text != element:
                    element.replace_with(cleaned_text)

        return soup
    
    def extract_text_for_translation(self, soup: BeautifulSoup) -> List[Dict]:
        """提取需要翻译的文本内容"""
        texts_to_translate = []

        # 提取标题、段落、列表项等文本内容，但避免代码块
        for tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li', 'td', 'th', 'dt', 'dd']):
            # 跳过代码相关的标签
            if tag.find_parent(['code', 'pre', 'script', 'style']):
                continue

            # 获取直接文本内容（不包括子标签的文本）
            direct_text = ''.join(tag.find_all(text=True, recursive=False)).strip()

            if direct_text and len(direct_text) > 3:
                texts_to_translate.append({
                    'original': direct_text,
                    'tag': tag.name,
                    'element': tag,
                    'text_nodes': tag.find_all(text=True, recursive=False)
                })

        return texts_to_translate
    
    def translate_text_batch(self, texts: List[str]) -> List[str]:
        """批量翻译文本"""
        translations = []

        # 详细的技术术语映射
        term_mapping = {
            # 核心概念
            'Trino': 'Trino',
            'SQL': 'SQL',
            'connector': '连接器',
            'connectors': '连接器',
            'query': '查询',
            'queries': '查询',
            'database': '数据库',
            'databases': '数据库',
            'table': '表',
            'tables': '表',
            'schema': '模式',
            'schemas': '模式',
            'catalog': '目录',
            'catalogs': '目录',
            'cluster': '集群',
            'clusters': '集群',
            'coordinator': '协调器',
            'coordinators': '协调器',
            'worker': '工作节点',
            'workers': '工作节点',
            'node': '节点',
            'nodes': '节点',

            # 配置和部署
            'configuration': '配置',
            'configurations': '配置',
            'installation': '安装',
            'deployment': '部署',
            'setup': '设置',
            'properties': '属性',
            'property': '属性',
            'parameter': '参数',
            'parameters': '参数',
            'option': '选项',
            'options': '选项',
            'setting': '设置',
            'settings': '设置',

            # 安全相关
            'security': '安全',
            'authentication': '认证',
            'authorization': '授权',
            'access control': '访问控制',
            'permission': '权限',
            'permissions': '权限',
            'role': '角色',
            'roles': '角色',
            'user': '用户',
            'users': '用户',
            'group': '组',
            'groups': '组',

            # 功能和操作
            'function': '函数',
            'functions': '函数',
            'operator': '操作符',
            'operators': '操作符',
            'expression': '表达式',
            'expressions': '表达式',
            'statement': '语句',
            'statements': '语句',
            'command': '命令',
            'commands': '命令',

            # 数据类型
            'data type': '数据类型',
            'data types': '数据类型',
            'integer': '整数',
            'string': '字符串',
            'boolean': '布尔值',
            'decimal': '十进制数',
            'timestamp': '时间戳',
            'date': '日期',
            'time': '时间',
            'array': '数组',
            'map': '映射',
            'row': '行',
            'column': '列',
            'columns': '列',

            # 性能和优化
            'performance': '性能',
            'optimization': '优化',
            'optimizer': '优化器',
            'execution': '执行',
            'planning': '规划',
            'planner': '规划器',
            'statistics': '统计信息',
            'cost': '成本',
            'memory': '内存',
            'CPU': 'CPU',
            'disk': '磁盘',
            'network': '网络',
            'throughput': '吞吐量',
            'latency': '延迟',

            # 监控和故障排除
            'monitoring': '监控',
            'troubleshooting': '故障排除',
            'debugging': '调试',
            'logging': '日志记录',
            'metrics': '指标',
            'event': '事件',
            'events': '事件',
            'error': '错误',
            'errors': '错误',
            'warning': '警告',
            'warnings': '警告',
            'exception': '异常',
            'exceptions': '异常',

            # 常用词汇
            'overview': '概述',
            'introduction': '介绍',
            'getting started': '入门指南',
            'tutorial': '教程',
            'example': '示例',
            'examples': '示例',
            'documentation': '文档',
            'reference': '参考',
            'guide': '指南',
            'manual': '手册',
            'specification': '规范',
            'requirements': '要求',
            'prerequisites': '先决条件',
            'limitations': '限制',
            'notes': '注意事项',
            'tips': '提示',
            'best practices': '最佳实践',
            'recommendations': '建议'
        }

        for text in texts:
            # 跳过代码块和特殊标记
            if any(marker in text for marker in ['```', '`', 'SELECT', 'FROM', 'WHERE', 'CREATE', 'ALTER', 'DROP']):
                translations.append(text)
                continue

            # 跳过纯数字或很短的文本
            if len(text.strip()) < 3 or text.strip().isdigit():
                translations.append(text)
                continue

            translated = text

            # 应用术语映射
            for en_term, zh_term in term_mapping.items():
                # 使用单词边界匹配，避免部分匹配
                pattern = r'\b' + re.escape(en_term) + r'\b'
                translated = re.sub(pattern, zh_term, translated, flags=re.IGNORECASE)

            # 如果没有任何翻译，保持原文（可能是代码或专有名词）
            translations.append(translated)

        return translations
    
    def process_single_file(self, file_path: Path) -> bool:
        """处理单个HTML文件"""
        try:
            print(f"处理文件: {file_path.relative_to(self.docs_dir)}")

            # 读取原始文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # 清理HTML内容，获取BeautifulSoup对象
            soup = self.clean_html_content(original_content)

            # 提取需要翻译的文本
            texts_to_translate = self.extract_text_for_translation(soup)

            if not texts_to_translate:
                print(f"  跳过文件（无需翻译的内容）: {file_path.name}")
                return True

            # 批量翻译
            original_texts = [item['original'] for item in texts_to_translate]
            translated_texts = self.translate_text_batch(original_texts)

            # 替换翻译后的内容 - 只替换文本节点，保持HTML结构
            for i, item in enumerate(texts_to_translate):
                if i < len(translated_texts) and translated_texts[i] != item['original']:
                    # 替换文本节点
                    for text_node in item['text_nodes']:
                        if text_node.strip() == item['original'].strip():
                            text_node.replace_with(translated_texts[i])
                            break

            # 生成翻译后的文件名
            original_name = file_path.name
            if original_name.endswith('.html'):
                translated_name = original_name[:-5] + '_zh.html'
            else:
                translated_name = original_name + '_zh'

            translated_path = file_path.parent / translated_name

            # 保存翻译后的文件，保持原始格式
            with open(translated_path, 'w', encoding='utf-8') as f:
                f.write(str(soup))

            print(f"  翻译完成: {translated_name}")
            self.processed_count += 1
            return True

        except Exception as e:
            print(f"  处理文件失败 {file_path}: {e}")
            return False
    
    def process_all_files(self, limit: Optional[int] = None):
        """处理所有HTML文件"""
        html_files = self.find_html_files()
        total_files = len(html_files)

        if limit:
            html_files = html_files[:limit]
            print(f"找到 {total_files} 个HTML文件，处理前 {limit} 个")
        else:
            print(f"找到 {total_files} 个HTML文件需要处理")

        for i, file_path in enumerate(html_files, 1):
            print(f"\n进度: {i}/{len(html_files)}")
            success = self.process_single_file(file_path)

            if not success:
                print(f"处理失败: {file_path}")

            # 添加小延迟避免过快处理
            time.sleep(0.1)

        print(f"\n处理完成！成功处理了 {self.processed_count} 个文件")

def main():
    docs_dir = "/Users/<USER>/Downloads/trino/html"
    processor = TrinoDocProcessor(docs_dir)
    # 先处理前5个文件作为测试
    processor.process_all_files(limit=5)

if __name__ == "__main__":
    main()
