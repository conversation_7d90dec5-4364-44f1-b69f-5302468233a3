<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Client protocol properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-client-protocol.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="HTTP server properties" href="properties-http-server.html" />
    <link rel="prev" title="General properties" href="properties-general.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-client-protocol.html#admin/properties-client-protocol" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Client protocol properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Client protocol </label>
    
      <a href="properties-client-protocol.html#" class="md-nav__link md-nav__link--active">Client protocol</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-protocol-properties" class="md-nav__link">Spooling protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-shared-secret-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.shared-secret-key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-retrieval-mode" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.retrieval-mode</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-zstd-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+zstd.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-lz4-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+lz4.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-compression-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.compression.threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-initial-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.initial-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-max-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.max-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-rows" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-rows</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-file-system-properties" class="md-nav__link">Spooling file system properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-azure-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.azure.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-s3-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.s3.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-gcs-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.gcs.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-direct-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.direct.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-encryption" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.encryption</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-explicit-ack" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.explicit-ack</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-batch-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.batch-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#shared-protocol-properties" class="md-nav__link">Shared protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-length-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.length-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-min-gain" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.min-gain</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Client protocol </label>
    
      <a href="properties-client-protocol.html#" class="md-nav__link md-nav__link--active">Client protocol</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-protocol-properties" class="md-nav__link">Spooling protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-shared-secret-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.shared-secret-key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-retrieval-mode" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.retrieval-mode</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-zstd-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+zstd.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-lz4-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+lz4.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-compression-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.compression.threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-initial-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.initial-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-max-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.max-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-rows" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-rows</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-file-system-properties" class="md-nav__link">Spooling file system properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-azure-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.azure.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-s3-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.s3.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-gcs-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.gcs.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-direct-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.direct.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-encryption" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.encryption</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-explicit-ack" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.explicit-ack</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-batch-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.batch-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#shared-protocol-properties" class="md-nav__link">Shared protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-length-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.length-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-min-gain" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.min-gain</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-protocol-properties" class="md-nav__link">Spooling protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-shared-secret-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.shared-secret-key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-retrieval-mode" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.retrieval-mode</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-zstd-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+zstd.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-json-lz4-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+lz4.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-encoding-compression-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.compression.threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-initial-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.initial-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-max-segment-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.max-segment-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-rows" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-rows</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-spooling-inlining-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#spooling-file-system-properties" class="md-nav__link">Spooling file system properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-azure-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.azure.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-s3-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.s3.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-gcs-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.gcs.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-direct-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.direct.ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-encryption" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.encryption</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-explicit-ack" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.explicit-ack</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#fs-segment-pruning-batch-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.batch-size</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#shared-protocol-properties" class="md-nav__link">Shared protocol properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-length-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.length-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-min-gain" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.min-gain</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="client-protocol-properties">
<h1 id="admin-properties-client-protocol--page-root">Client protocol properties<a class="headerlink" href="properties-client-protocol.html#admin-properties-client-protocol--page-root" title="Link to this heading">#</a></h1>
<p>The following sections provide a reference for all properties related to the
<a class="reference internal" href="../client/client-protocol.html"><span class="doc std std-doc">client protocol</span></a>.</p>
<section id="spooling-protocol-properties">
<span id="prop-protocol-spooling"></span><h2 id="spooling-protocol-properties">Spooling protocol properties<a class="headerlink" href="properties-client-protocol.html#spooling-protocol-properties" title="Link to this heading">#</a></h2>
<p>The following properties are related to the <a class="reference internal" href="../client/client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a>.</p>
<section id="protocol-spooling-enabled">
<h3 id="protocol-spooling-enabled"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_enabled</span></code></p></li>
</ul>
<p>Enable the support for the client <a class="reference internal" href="../client/client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a>. The protocol is used if
client drivers and applications request usage, otherwise the direct protocol is
used automatically.</p>
</section>
<section id="protocol-spooling-shared-secret-key">
<h3 id="protocol-spooling-shared-secret-key"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.shared-secret-key</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-shared-secret-key" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>A required 256 bit, base64-encoded secret key used to secure spooled metadata
exchanged with the client. Create a suitable value with the following command:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>openssl<span class="w"> </span>rand<span class="w"> </span>-base64<span class="w"> </span><span class="m">32</span>
</pre></div>
</div>
</section>
<section id="protocol-spooling-retrieval-mode">
<h3 id="protocol-spooling-retrieval-mode"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.retrieval-mode</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-retrieval-mode" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">STORAGE</span></code></p></li>
</ul>
<p>Determines how the client retrieves the segment. Following are possible values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">STORAGE</span></code> - client accesses the storage directly with the pre-signed URI. Uses
one client HTTP request per data segment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">COORDINATOR_STORAGE_REDIRECT</span></code> - client first accesses the coordinator, which
redirects the client to the storage with the pre-signed URI. Uses two client
HTTP requests per data segment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">COORDINATOR_PROXY</span></code> - client accesses the coordinator and gets data segment
through it. Uses one client HTTP request per data segment, but requires a
coordinator HTTP request to the storage.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">WORKER_PROXY</span></code> - client accesses the coordinator, which redirects to an
available worker node. It fetches the data from the storage and provides it
to the client. Uses two client HTTP requests, and requires a worker request to
the storage.</p></li>
</ul>
</section>
<section id="protocol-spooling-encoding-json-enabled">
<h3 id="protocol-spooling-encoding-json-enabled"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-encoding-json-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Activate support for using uncompressed JSON encoding for spooled segments.</p>
</section>
<section id="protocol-spooling-encoding-json-zstd-enabled">
<h3 id="protocol-spooling-encoding-json-zstd-enabled"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+zstd.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-encoding-json-zstd-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Activate support for using JSON encoding with Zstandard compression for spooled
segments.</p>
</section>
<section id="protocol-spooling-encoding-json-lz4-enabled">
<h3 id="protocol-spooling-encoding-json-lz4-enabled"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.json+lz4.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-encoding-json-lz4-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Activate support for using JSON encoding with LZ4 compression for spooled
segments.</p>
</section>
<section id="protocol-spooling-encoding-compression-threshold">
<h3 id="protocol-spooling-encoding-compression-threshold"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.encoding.compression.threshold</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-encoding-compression-threshold" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8KB</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1KB</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">4MB</span></code></p></li>
</ul>
<p>Threshold for enabling compression with larger segments.</p>
</section>
<section id="protocol-spooling-initial-segment-size">
<h3 id="protocol-spooling-initial-segment-size"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.initial-segment-size</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-initial-segment-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8MB</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1KB</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">128MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_initial_segment_size</span></code></p></li>
</ul>
<p>Initial size of the spooled segments.</p>
</section>
<section id="protocol-spooling-max-segment-size">
<h3 id="protocol-spooling-max-segment-size"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.max-segment-size</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-max-segment-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">16MB</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1KB</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">128MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_max_segment_size</span></code></p></li>
</ul>
<p>Maximum size for each spooled segment.</p>
</section>
<section id="protocol-spooling-inlining-enabled">
<h3 id="protocol-spooling-inlining-enabled"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-inlining-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_inlining_enabled</span></code></p></li>
</ul>
<p>Allow spooled protocol to inline initial rows to decrease time to return the
first row.</p>
</section>
<section id="protocol-spooling-inlining-max-rows">
<h3 id="protocol-spooling-inlining-max-rows"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-rows</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-inlining-max-rows" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1000</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">1000000</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_inlining_max_rows</span></code></p></li>
</ul>
<p>Maximum number of rows to inline per worker.</p>
</section>
<section id="protocol-spooling-inlining-max-size">
<h3 id="protocol-spooling-inlining-max-size"><code class="docutils literal notranslate"><span class="pre">protocol.spooling.inlining.max-size</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-spooling-inlining-max-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">128kB</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1KB</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">1MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">spooling_inlining_max_size</span></code></p></li>
</ul>
<p>Maximum size of rows to inline per worker.</p>
</section>
</section>
<section id="spooling-file-system-properties">
<span id="prop-spooling-file-system"></span><h2 id="spooling-file-system-properties">Spooling file system properties<a class="headerlink" href="properties-client-protocol.html#spooling-file-system-properties" title="Link to this heading">#</a></h2>
<p>The following properties are used to configure the object storage used with the
<a class="reference internal" href="../client/client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a>.</p>
<section id="fs-azure-enabled">
<h3 id="fs-azure-enabled"><code class="docutils literal notranslate"><span class="pre">fs.azure.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#fs-azure-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Activate <a class="reference internal" href="../object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a> for spooling segments.</p>
</section>
<section id="fs-s3-enabled">
<h3 id="fs-s3-enabled"><code class="docutils literal notranslate"><span class="pre">fs.s3.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#fs-s3-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Activate <a class="reference internal" href="../object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a> for spooling segments.</p>
</section>
<section id="fs-gcs-enabled">
<h3 id="fs-gcs-enabled"><code class="docutils literal notranslate"><span class="pre">fs.gcs.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#fs-gcs-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Activate <a class="reference internal" href="../object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a> for spooling segments.</p>
</section>
<section id="fs-location">
<h3 id="fs-location"><code class="docutils literal notranslate"><span class="pre">fs.location</span></code><a class="headerlink" href="properties-client-protocol.html#fs-location" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>The object storage location to use for spooling segments. Must be accessible by
the coordinator and all workers. With the <code class="docutils literal notranslate"><span class="pre">protocol.spooling.retrieval-mode</span></code>
retrieval modes <code class="docutils literal notranslate"><span class="pre">STORAGE</span></code> and <code class="docutils literal notranslate"><span class="pre">COORDINATOR_STORAGE_REDIRECT</span></code> the location must
also be accessible by all clients. Valid location values vary by object storage
type, and typically follow a pattern of <code class="docutils literal notranslate"><span class="pre">scheme://bucketName/path/</span></code>.</p>
<p>Examples:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">s3://my-spooling-bucket/my-segments/</span></code></p></li>
</ul>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>The specified object storage location must not be used for spooling for another
Trino cluster or any object storage catalog. When using the same object storage
for multiple services, you must use separate locations for each one. For
example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">s3://my-spooling-bucket/my-segments/cluster1-spooling</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">s3://my-spooling-bucket/my-segments/cluster2-spooling</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">s3://my-spooling-bucket/my-segments/iceberg-catalog</span></code></p></li>
</ul>
</div>
</section>
<section id="fs-segment-ttl">
<h3 id="fs-segment-ttl"><code class="docutils literal notranslate"><span class="pre">fs.segment.ttl</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-ttl" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">12h</span></code></p></li>
</ul>
<p>Maximum available time for the client to retrieve spooled segment before it
expires and is pruned.</p>
</section>
<section id="fs-segment-direct-ttl">
<h3 id="fs-segment-direct-ttl"><code class="docutils literal notranslate"><span class="pre">fs.segment.direct.ttl</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-direct-ttl" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1h</span></code></p></li>
</ul>
<p>Maximum available time for the client to retrieve spooled segment using the
pre-signed URI.</p>
</section>
<section id="fs-segment-encryption">
<h3 id="fs-segment-encryption"><code class="docutils literal notranslate"><span class="pre">fs.segment.encryption</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-encryption" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Encrypt segments with ephemeral keys using Server-Side Encryption with Customer
key (SSE-C).</p>
</section>
<section id="fs-segment-explicit-ack">
<h3 id="fs-segment-explicit-ack"><code class="docutils literal notranslate"><span class="pre">fs.segment.explicit-ack</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-explicit-ack" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Activate pruning of segments on client acknowledgment of a successful read of
each segment.</p>
</section>
<section id="fs-segment-pruning-enabled">
<h3 id="fs-segment-pruning-enabled"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.enabled</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-pruning-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Activate periodic pruning of expired segments.</p>
</section>
<section id="fs-segment-pruning-interval">
<h3 id="fs-segment-pruning-interval"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.interval</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-pruning-interval" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">5m</span></code></p></li>
</ul>
<p>Interval to prune expired segments.</p>
</section>
<section id="fs-segment-pruning-batch-size">
<h3 id="fs-segment-pruning-batch-size"><code class="docutils literal notranslate"><span class="pre">fs.segment.pruning.batch-size</span></code><a class="headerlink" href="properties-client-protocol.html#fs-segment-pruning-batch-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> integer</p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">250</span></code></p></li>
</ul>
<p>Number of expired segments to prune as a single batch operation.</p>
</section>
</section>
<section id="shared-protocol-properties">
<span id="prop-protocol-shared"></span><h2 id="shared-protocol-properties">Shared protocol properties<a class="headerlink" href="properties-client-protocol.html#shared-protocol-properties" title="Link to this heading">#</a></h2>
<p>The following properties are related to the <a class="reference internal" href="../client/client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a> and the
<a class="reference internal" href="../client/client-protocol.html#protocol-direct"><span class="std std-ref">Direct protocol</span></a>, formerly named the V1 protocol.</p>
<section id="protocol-v1-prepared-statement-compression-length-threshold">
<h3 id="protocol-v1-prepared-statement-compression-length-threshold"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.length-threshold</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-length-threshold" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">2048</span></code></p></li>
</ul>
<p>Prepared statements that are submitted to Trino for processing, and are longer
than the value of this property, are compressed for transport via the HTTP
header to improve handling, and to avoid failures due to hitting HTTP header
size limits.</p>
</section>
<section id="protocol-v1-prepared-statement-compression-min-gain">
<h3 id="protocol-v1-prepared-statement-compression-min-gain"><code class="docutils literal notranslate"><span class="pre">protocol.v1.prepared-statement-compression.min-gain</span></code><a class="headerlink" href="properties-client-protocol.html#protocol-v1-prepared-statement-compression-min-gain" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">512</span></code></p></li>
</ul>
<p>Prepared statement compression is not applied if the size gain is less than the
configured value. Smaller statements do not benefit from compression, and are
left uncompressed.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-general.html" title="General properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> General properties </span>
              </div>
            </a>
          
          
            <a href="properties-http-server.html" title="HTTP server properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> HTTP server properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>