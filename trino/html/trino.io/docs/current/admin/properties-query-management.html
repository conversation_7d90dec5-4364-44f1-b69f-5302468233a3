<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Query management properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-query-management.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Catalog management properties" href="properties-catalog.html" />
    <link rel="prev" title="Resource management properties" href="properties-resource-management.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-query-management.html#admin/properties-query-management" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Query management properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Query management </label>
    
      <a href="properties-query-management.html#" class="md-nav__link md-nav__link--active">Query management</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-query-management.html#query-client-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.client.timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-execution-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.execution-policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-determine-partition-count-for-write-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.determine-partition-count-for-write-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count-for-write" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count-for-write</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-writer-task-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-writer-task-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#task-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-execution-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-execution-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-planning-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-planning-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-run-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-run-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-scan-physical-bytes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-scan-physical-bytes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-stage-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-stage-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-expire-age" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-enable-adaptive-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-guaranteed-splits-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.guaranteed-splits-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-error-duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-error-duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-request-size-headroom" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.request-size-headroom</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-info-url-template" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.info-url-template</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#retry-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">retry-policy</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Query management </label>
    
      <a href="properties-query-management.html#" class="md-nav__link md-nav__link--active">Query management</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-query-management.html#query-client-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.client.timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-execution-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.execution-policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-determine-partition-count-for-write-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.determine-partition-count-for-write-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count-for-write" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count-for-write</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-writer-task-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-writer-task-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#task-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-execution-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-execution-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-planning-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-planning-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-run-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-run-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-scan-physical-bytes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-scan-physical-bytes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-stage-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-stage-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-expire-age" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-enable-adaptive-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-guaranteed-splits-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.guaranteed-splits-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-error-duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-error-duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-request-size-headroom" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.request-size-headroom</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-info-url-template" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.info-url-template</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#retry-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">retry-policy</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-query-management.html#query-client-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.client.timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-execution-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.execution-policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-determine-partition-count-for-write-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.determine-partition-count-for-write-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-hash-partition-count-for-write" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count-for-write</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-writer-task-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-writer-task-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#task-low-memory-killer-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.low-memory-killer.policy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-execution-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-execution-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-planning-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-planning-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-run-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-run-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-scan-physical-bytes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-scan-physical-bytes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-stage-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-stage-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-min-expire-age" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-enable-adaptive-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-guaranteed-splits-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.guaranteed-splits-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-error-duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-error-duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-max-request-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-request-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-remote-task-request-size-headroom" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.remote-task.request-size-headroom</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#query-info-url-template" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query.info-url-template</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-query-management.html#retry-policy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">retry-policy</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="query-management-properties">
<h1 id="admin-properties-query-management--page-root">Query management properties<a class="headerlink" href="properties-query-management.html#admin-properties-query-management--page-root" title="Link to this heading">#</a></h1>
<section id="query-client-timeout">
<h2 id="query-client-timeout"><code class="docutils literal notranslate"><span class="pre">query.client.timeout</span></code><a class="headerlink" href="properties-query-management.html#query-client-timeout" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">5m</span></code></p></li>
</ul>
<p>Configures how long the cluster runs without contact from the client
application, such as the CLI, before it abandons and cancels its work.</p>
</section>
<section id="query-execution-policy">
<h2 id="query-execution-policy"><code class="docutils literal notranslate"><span class="pre">query.execution-policy</span></code><a class="headerlink" href="properties-query-management.html#query-execution-policy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">phased</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">execution_policy</span></code></p></li>
</ul>
<p>Configures the algorithm to organize the processing of all the
stages of a query. You can use the following execution policies:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">phased</span></code> schedules stages in a sequence to avoid blockages because of
inter-stage dependencies. This policy maximizes cluster resource utilization
and provides the lowest query wall time.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">all-at-once</span></code> schedules all the stages of a query at one time. As a
result, cluster resource utilization is initially high, but inter-stage
dependencies typically prevent full processing and cause longer queue times
which increases the query wall time overall.</p></li>
</ul>
</section>
<section id="query-determine-partition-count-for-write-enabled">
<h2 id="query-determine-partition-count-for-write-enabled"><code class="docutils literal notranslate"><span class="pre">query.determine-partition-count-for-write-enabled</span></code><a class="headerlink" href="properties-query-management.html#query-determine-partition-count-for-write-enabled" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">determine_partition_count_for_write_enabled</span></code></p></li>
</ul>
<p>Enables determining the number of partitions based on amount of data read and processed by the
query for write queries.</p>
</section>
<section id="query-max-hash-partition-count">
<h2 id="query-max-hash-partition-count"><code class="docutils literal notranslate"><span class="pre">query.max-hash-partition-count</span></code><a class="headerlink" href="properties-query-management.html#query-max-hash-partition-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">max_hash_partition_count</span></code></p></li>
</ul>
<p>The maximum number of partitions to use for processing distributed operations, such as
joins, aggregations, partitioned window functions and others.</p>
</section>
<section id="query-min-hash-partition-count">
<h2 id="query-min-hash-partition-count"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count</span></code><a class="headerlink" href="properties-query-management.html#query-min-hash-partition-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">4</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">min_hash_partition_count</span></code></p></li>
</ul>
<p>The minimum number of partitions to use for processing distributed operations, such as
joins, aggregations, partitioned window functions and others.</p>
</section>
<section id="query-min-hash-partition-count-for-write">
<h2 id="query-min-hash-partition-count-for-write"><code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count-for-write</span></code><a class="headerlink" href="properties-query-management.html#query-min-hash-partition-count-for-write" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">50</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">min_hash_partition_count_for_write</span></code></p></li>
</ul>
<p>The minimum number of partitions to use for processing distributed operations in write queries,
such as joins, aggregations, partitioned window functions and others.</p>
</section>
<section id="query-max-writer-task-count">
<h2 id="query-max-writer-task-count"><code class="docutils literal notranslate"><span class="pre">query.max-writer-task-count</span></code><a class="headerlink" href="properties-query-management.html#query-max-writer-task-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">max_writer_task_count</span></code></p></li>
</ul>
<p>The maximum number of tasks that will take part in writing data during
<code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span> <span class="pre">SELECT</span></code> and <code class="docutils literal notranslate"><span class="pre">EXECUTE</span></code> queries.
The limit is only applicable when <code class="docutils literal notranslate"><span class="pre">redistribute-writes</span></code> or <code class="docutils literal notranslate"><span class="pre">scale-writers</span></code> is enabled.</p>
</section>
<section id="query-low-memory-killer-policy">
<h2 id="query-low-memory-killer-policy"><code class="docutils literal notranslate"><span class="pre">query.low-memory-killer.policy</span></code><a class="headerlink" href="properties-query-management.html#query-low-memory-killer-policy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">total-reservation-on-blocked-nodes</span></code></p></li>
</ul>
<p>Configures the behavior to handle killing running queries in the event of low
memory availability. Supports the following values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">none</span></code> - Do not kill any queries in the event of low memory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">total-reservation</span></code> - Kill the query currently using the most total memory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">total-reservation-on-blocked-nodes</span></code> - Kill the query currently using the
most memory specifically on nodes that are now out of memory.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only applies for queries with task level retries disabled (<code class="docutils literal notranslate"><span class="pre">retry-policy</span></code> set to <code class="docutils literal notranslate"><span class="pre">NONE</span></code> or <code class="docutils literal notranslate"><span class="pre">QUERY</span></code>)</p>
</div>
</section>
<section id="task-low-memory-killer-policy">
<h2 id="task-low-memory-killer-policy"><code class="docutils literal notranslate"><span class="pre">task.low-memory-killer.policy</span></code><a class="headerlink" href="properties-query-management.html#task-low-memory-killer-policy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">total-reservation-on-blocked-nodes</span></code></p></li>
</ul>
<p>Configures the behavior to handle killing running tasks in the event of low
memory availability. Supports the following values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">none</span></code> - Do not kill any tasks in the event of low memory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">total-reservation-on-blocked-nodes</span></code> - Kill the tasks that are part of the queries
which have task retries enabled and are currently using the most memory specifically
on nodes that are now out of memory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">least-waste</span></code> - Kill the tasks that are part of the queries
which have task retries enabled and use significant amount of memory on nodes
which are now out of memory. This policy avoids killing tasks which are already
executing for a long time, so significant amount of work is not wasted.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only applies for queries with task level retries enabled (<code class="docutils literal notranslate"><span class="pre">retry-policy=TASK</span></code>)</p>
</div>
</section>
<section id="query-max-execution-time">
<h2 id="query-max-execution-time"><code class="docutils literal notranslate"><span class="pre">query.max-execution-time</span></code><a class="headerlink" href="properties-query-management.html#query-max-execution-time" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100d</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">query_max_execution_time</span></code></p></li>
</ul>
<p>The maximum allowed time for a query to be actively executing on the
cluster, before it is terminated. Compared to the run time below, execution
time does not include analysis, query planning or wait times in a queue.</p>
</section>
<section id="query-max-length">
<h2 id="query-max-length"><code class="docutils literal notranslate"><span class="pre">query.max-length</span></code><a class="headerlink" href="properties-query-management.html#query-max-length" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1,000,000</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">1,000,000,000</span></code></p></li>
</ul>
<p>The maximum number of characters allowed for the SQL query text. Longer queries
are not processed, and terminated with error <code class="docutils literal notranslate"><span class="pre">QUERY_TEXT_TOO_LARGE</span></code>.</p>
</section>
<section id="query-max-planning-time">
<h2 id="query-max-planning-time"><code class="docutils literal notranslate"><span class="pre">query.max-planning-time</span></code><a class="headerlink" href="properties-query-management.html#query-max-planning-time" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10m</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">query_max_planning_time</span></code></p></li>
</ul>
<p>The maximum allowed time for a query to be actively planning the execution.
After this period the coordinator will make its best effort to stop the
query. Note that some operations in planning phase are not easily cancellable
and may not terminate immediately.</p>
</section>
<section id="query-max-run-time">
<h2 id="query-max-run-time"><code class="docutils literal notranslate"><span class="pre">query.max-run-time</span></code><a class="headerlink" href="properties-query-management.html#query-max-run-time" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100d</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">query_max_run_time</span></code></p></li>
</ul>
<p>The maximum allowed time for a query to be processed on the cluster, before
it is terminated. The time includes time for analysis and planning, but also
time spent in a queue waiting, so essentially this is the time allowed for a
query to exist since creation.</p>
</section>
<section id="query-max-scan-physical-bytes">
<h2 id="query-max-scan-physical-bytes"><code class="docutils literal notranslate"><span class="pre">query.max-scan-physical-bytes</span></code><a class="headerlink" href="properties-query-management.html#query-max-scan-physical-bytes" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">query_max_scan_physical_bytes</span></code></p></li>
</ul>
<p>The maximum number of bytes that can be scanned by a query during its execution.
When this limit is reached, query processing is terminated to prevent excessive
resource usage.</p>
</section>
<section id="query-max-stage-count">
<h2 id="query-max-stage-count"><code class="docutils literal notranslate"><span class="pre">query.max-stage-count</span></code><a class="headerlink" href="properties-query-management.html#query-max-stage-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">150</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
</ul>
<p>The maximum number of stages allowed to be generated per query. If a query
generates more stages than this it will get killed with error
<code class="docutils literal notranslate"><span class="pre">QUERY_HAS_TOO_MANY_STAGES</span></code>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Setting this to a high value can cause queries with a large number of
stages to introduce instability in the cluster causing unrelated queries
to get killed with <code class="docutils literal notranslate"><span class="pre">REMOTE_TASK_ERROR</span></code> and the message
<code class="docutils literal notranslate"><span class="pre">Max</span> <span class="pre">requests</span> <span class="pre">queued</span> <span class="pre">per</span> <span class="pre">destination</span> <span class="pre">exceeded</span> <span class="pre">for</span> <span class="pre">HttpDestination</span> <span class="pre">...</span></code></p>
</div>
</section>
<section id="query-max-history">
<h2 id="query-max-history"><code class="docutils literal notranslate"><span class="pre">query.max-history</span></code><a class="headerlink" href="properties-query-management.html#query-max-history" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100</span></code></p></li>
</ul>
<p>The maximum number of queries to keep in the query history to provide statistics
and other information, and make the data available in the
<a class="reference internal" href="web-interface.html"><span class="doc std std-doc">Web UI</span></a>. If this amount is reached, queries are removed based
on age.</p>
<p>To store query events and therefore information about more queries in an
external system you must use <a class="reference internal" href="../admin.html#admin-event-listeners"><span class="std std-ref">an event listener</span></a>.</p>
</section>
<section id="query-min-expire-age">
<h2 id="query-min-expire-age"><code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code><a class="headerlink" href="properties-query-management.html#query-min-expire-age" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">15m</span></code></p></li>
</ul>
<p>The minimal age of a query in the history before it is expired. An expired query
is removed from the query history buffer and no longer available in the
<a class="reference internal" href="web-interface.html"><span class="doc std std-doc">Web UI</span></a>.</p>
<p>To store query events and therefore information about more queries in an
external system you must use <a class="reference internal" href="../admin.html#admin-event-listeners"><span class="std std-ref">an event listener</span></a>.</p>
</section>
<section id="query-remote-task-enable-adaptive-request-size">
<h2 id="query-remote-task-enable-adaptive-request-size"><code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code><a class="headerlink" href="properties-query-management.html#query-remote-task-enable-adaptive-request-size" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">remote_task_adaptive_update_request_size_enabled</span></code></p></li>
</ul>
<p>Enables dynamically splitting up server requests sent by tasks, which can
prevent out-of-memory errors for large schemas. The default settings are
optimized for typical usage and should only be modified by advanced users
working with extremely large tables.</p>
</section>
<section id="query-remote-task-guaranteed-splits-per-task">
<h2 id="query-remote-task-guaranteed-splits-per-task"><code class="docutils literal notranslate"><span class="pre">query.remote-task.guaranteed-splits-per-task</span></code><a class="headerlink" href="properties-query-management.html#query-remote-task-guaranteed-splits-per-task" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">3</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">remote_task_guaranteed_splits_per_request</span></code></p></li>
</ul>
<p>The minimum number of splits that should be assigned to each remote task to
ensure that each task has a minimum amount of work to perform. Requires
<code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code> to be enabled.</p>
</section>
<section id="query-remote-task-max-error-duration">
<h2 id="query-remote-task-max-error-duration"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-error-duration</span></code><a class="headerlink" href="properties-query-management.html#query-remote-task-max-error-duration" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1m</span></code></p></li>
</ul>
<p>Timeout value for remote tasks that fail to communicate with the coordinator. If
the coordinator is unable to receive updates from a remote task before this
value is reached, the coordinator treats the task as failed.</p>
</section>
<section id="query-remote-task-max-request-size">
<h2 id="query-remote-task-max-request-size"><code class="docutils literal notranslate"><span class="pre">query.remote-task.max-request-size</span></code><a class="headerlink" href="properties-query-management.html#query-remote-task-max-request-size" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">remote_task_max_request_size</span></code></p></li>
</ul>
<p>The maximum size of a single request made by a remote task. Requires
<code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code> to be enabled.</p>
</section>
<section id="query-remote-task-request-size-headroom">
<h2 id="query-remote-task-request-size-headroom"><code class="docutils literal notranslate"><span class="pre">query.remote-task.request-size-headroom</span></code><a class="headerlink" href="properties-query-management.html#query-remote-task-request-size-headroom" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">2MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">remote_task_request_size_headroom</span></code></p></li>
</ul>
<p>Determines the amount of headroom that should be allocated beyond the size of
the request data. Requires <code class="docutils literal notranslate"><span class="pre">query.remote-task.enable-adaptive-request-size</span></code> to
be enabled.</p>
</section>
<section id="query-info-url-template">
<h2 id="query-info-url-template"><code class="docutils literal notranslate"><span class="pre">query.info-url-template</span></code><a class="headerlink" href="properties-query-management.html#query-info-url-template" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">(URL</span> <span class="pre">of</span> <span class="pre">the</span> <span class="pre">query</span> <span class="pre">info</span> <span class="pre">page</span> <span class="pre">on</span> <span class="pre">the</span> <span class="pre">coordinator)</span></code></p></li>
</ul>
<p>Configure redirection of clients to an alternative location for query
information. The URL must contain a query id placeholder <code class="docutils literal notranslate"><span class="pre">${QUERY_ID}</span></code>.</p>
<p>For example <code class="docutils literal notranslate"><span class="pre">https://example.com/query/${QUERY_ID}</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">${QUERY_ID}</span></code> gets replaced with the actual query’s id.</p>
</section>
<section id="retry-policy">
<h2 id="retry-policy"><code class="docutils literal notranslate"><span class="pre">retry-policy</span></code><a class="headerlink" href="properties-query-management.html#retry-policy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></li>
</ul>
<p>The <a class="reference internal" href="fault-tolerant-execution.html#fte-retry-policy"><span class="std std-ref">retry policy</span></a> to use for
<a class="reference internal" href="fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a>. Supports the following values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code> - Disable fault-tolerant execution.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TASK</span></code> - Retry individual tasks within a query in the event of failure.
Requires configuration of an <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-manager"><span class="std std-ref">exchange manager</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">QUERY</span></code> - Retry the whole query in the event of failure.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-resource-management.html" title="Resource management properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Resource management properties </span>
              </div>
            </a>
          
          
            <a href="properties-catalog.html" title="Catalog management properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Catalog management properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>