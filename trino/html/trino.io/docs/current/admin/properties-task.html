<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Task properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-task.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Write partitioning properties" href="properties-write-partitioning.html" />
    <link rel="prev" title="Exchange properties" href="properties-exchange.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-task.html#admin/properties-task" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Task properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Task </label>
    
      <a href="properties-task.html#" class="md-nav__link md-nav__link--active">Task</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-task.html#task-concurrency" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.concurrency</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-response-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-response-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-timeout-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-timeout-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-info-update-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.info-update-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-partial-aggregation-memory" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-partial-aggregation-memory</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-worker-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-worker-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-scale-writers-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.scale-writers.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-warning-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-warning-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-detection-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-detection-interval</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Task </label>
    
      <a href="properties-task.html#" class="md-nav__link md-nav__link--active">Task</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-task.html#task-concurrency" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.concurrency</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-response-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-response-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-timeout-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-timeout-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-info-update-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.info-update-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-partial-aggregation-memory" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-partial-aggregation-memory</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-worker-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-worker-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-scale-writers-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.scale-writers.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-warning-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-warning-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-detection-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-detection-interval</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-task.html#task-concurrency" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.concurrency</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-response-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-response-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-http-timeout-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.http-timeout-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-info-update-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.info-update-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-partial-aggregation-memory" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-partial-aggregation-memory</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-worker-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-worker-threads</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-drivers-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-drivers-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-scale-writers-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.scale-writers.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-min-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.min-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-max-writer-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.max-writer-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-warning-threshold" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-warning-threshold</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-task.html#task-interrupt-stuck-split-tasks-detection-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-detection-interval</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="task-properties">
<h1 id="admin-properties-task--page-root">Task properties<a class="headerlink" href="properties-task.html#admin-properties-task--page-root" title="Link to this heading">#</a></h1>
<section id="task-concurrency">
<h2 id="task-concurrency"><code class="docutils literal notranslate"><span class="pre">task.concurrency</span></code><a class="headerlink" href="properties-task.html#task-concurrency" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Restrictions:</strong> Must be a power of two</p></li>
<li><p><strong>Default value:</strong> The number of physical CPUs of the node, with a minimum
value of 2 and a maximum of 32. Defaults to 8 in
<a class="reference internal" href="fault-tolerant-execution.html"><span class="doc std std-doc">Fault-tolerant execution</span></a> mode.</p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">task_concurrency</span></code></p></li>
</ul>
<p>Default local concurrency for parallel operators, such as joins and aggregations.
This value should be adjusted up or down based on the query concurrency and worker
resource utilization. Lower values are better for clusters that run many queries
concurrently, because the cluster is already utilized by all the running
queries, so adding more concurrency results in slow-downs due to context
switching and other overhead. Higher values are better for clusters that only run
one or a few queries at a time.</p>
</section>
<section id="task-http-response-threads">
<h2 id="task-http-response-threads"><code class="docutils literal notranslate"><span class="pre">task.http-response-threads</span></code><a class="headerlink" href="properties-task.html#task-http-response-threads" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100</span></code></p></li>
</ul>
<p>Maximum number of threads that may be created to handle HTTP responses. Threads are
created on demand and are cleaned up when idle, thus there is no overhead to a large
value, if the number of requests to be handled is small. More threads may be helpful
on clusters with a high number of concurrent queries, or on clusters with hundreds
or thousands of workers.</p>
</section>
<section id="task-http-timeout-threads">
<h2 id="task-http-timeout-threads"><code class="docutils literal notranslate"><span class="pre">task.http-timeout-threads</span></code><a class="headerlink" href="properties-task.html#task-http-timeout-threads" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">3</span></code></p></li>
</ul>
<p>Number of threads used to handle timeouts when generating HTTP responses. This value
should be increased if all the threads are frequently in use. This can be monitored
via the <code class="docutils literal notranslate"><span class="pre">trino.server:name=AsyncHttpExecutionMBean:TimeoutExecutor</span></code>
JMX object. If <code class="docutils literal notranslate"><span class="pre">ActiveCount</span></code> is always the same as <code class="docutils literal notranslate"><span class="pre">PoolSize</span></code>, increase the
number of threads.</p>
</section>
<section id="task-info-update-interval">
<h2 id="task-info-update-interval"><code class="docutils literal notranslate"><span class="pre">task.info-update-interval</span></code><a class="headerlink" href="properties-task.html#task-info-update-interval" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1ms</span></code></p></li>
<li><p><strong>Maximum value:</strong> <code class="docutils literal notranslate"><span class="pre">10s</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">3s</span></code></p></li>
</ul>
<p>Controls staleness of task information, which is used in scheduling. Larger values
can reduce coordinator CPU load, but may result in suboptimal split scheduling.</p>
</section>
<section id="task-max-drivers-per-task">
<h2 id="task-max-drivers-per-task"><code class="docutils literal notranslate"><span class="pre">task.max-drivers-per-task</span></code><a class="headerlink" href="properties-task.html#task-max-drivers-per-task" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Default Value:</strong> <code class="docutils literal notranslate"><span class="pre">2147483647</span></code></p></li>
</ul>
<p>Controls the maximum number of drivers a task runs concurrently. Setting this value
reduces the likelihood that a task uses too many drivers and can improve concurrent query
performance. This can lead to resource waste if it runs too few concurrent queries.</p>
</section>
<section id="task-max-partial-aggregation-memory">
<h2 id="task-max-partial-aggregation-memory"><code class="docutils literal notranslate"><span class="pre">task.max-partial-aggregation-memory</span></code><a class="headerlink" href="properties-task.html#task-max-partial-aggregation-memory" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">16MB</span></code></p></li>
</ul>
<p>Maximum size of partial aggregation results for distributed aggregations. Increasing this
value can result in less network transfer and lower CPU utilization, by allowing more
groups to be kept locally before being flushed, at the cost of additional memory usage.</p>
</section>
<section id="task-max-worker-threads">
<h2 id="task-max-worker-threads"><code class="docutils literal notranslate"><span class="pre">task.max-worker-threads</span></code><a class="headerlink" href="properties-task.html#task-max-worker-threads" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> (Node CPUs * 2)</p></li>
</ul>
<p>Sets the number of threads used by workers to process splits. Increasing this number
can improve throughput, if worker CPU utilization is low and all the threads are in use,
but it causes increased heap space usage. Setting the value too high may cause a drop
in performance due to a context switching. The number of active threads is available
via the <code class="docutils literal notranslate"><span class="pre">RunningSplits</span></code> property of the
<code class="docutils literal notranslate"><span class="pre">trino.execution.executor:name=TaskExecutor.RunningSplits</span></code> JMX object.</p>
</section>
<section id="task-min-drivers">
<h2 id="task-min-drivers"><code class="docutils literal notranslate"><span class="pre">task.min-drivers</span></code><a class="headerlink" href="properties-task.html#task-min-drivers" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> (<code class="docutils literal notranslate"><span class="pre">task.max-worker-threads</span></code> * 2)</p></li>
</ul>
<p>The target number of running leaf splits on a worker. This is a minimum value because
each leaf task is guaranteed at least <code class="docutils literal notranslate"><span class="pre">3</span></code> running splits. Non-leaf tasks are also
guaranteed to run in order to prevent deadlocks. A lower value may improve responsiveness
for new tasks, but can result in underutilized resources. A higher value can increase
resource utilization, but uses additional memory.</p>
</section>
<section id="task-min-drivers-per-task">
<h2 id="task-min-drivers-per-task"><code class="docutils literal notranslate"><span class="pre">task.min-drivers-per-task</span></code><a class="headerlink" href="properties-task.html#task-min-drivers-per-task" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Default Value:</strong> <code class="docutils literal notranslate"><span class="pre">3</span></code></p></li>
</ul>
<p>The minimum number of drivers guaranteed to run concurrently for a single task given
the task has remaining splits to process.</p>
</section>
<section id="task-scale-writers-enabled">
<h2 id="task-scale-writers-enabled"><code class="docutils literal notranslate"><span class="pre">task.scale-writers.enabled</span></code><a class="headerlink" href="properties-task.html#task-scale-writers-enabled" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Description:</strong> see details at <a class="reference internal" href="properties-writer-scaling.html#prop-task-scale-writers"><span class="std std-ref">task.scale-writers.enabled</span></a></p></li>
</ul>
</section>
<section id="task-min-writer-count">
<span id="prop-task-min-writer-count"></span><h2 id="task-min-writer-count"><code class="docutils literal notranslate"><span class="pre">task.min-writer-count</span></code><a class="headerlink" href="properties-task.html#task-min-writer-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">task_min_writer_count</span></code></p></li>
</ul>
<p>The number of concurrent writer threads per worker per query when
<a class="reference internal" href="properties-write-partitioning.html#preferred-write-partitioning"><span class="std std-ref">preferred partitioning</span></a> and
<a class="reference internal" href="properties-writer-scaling.html#prop-task-scale-writers"><span class="std std-ref">task writer scaling</span></a> are not used. Increasing this value may
increase write speed, especially when a query is not I/O bound and can take advantage of
additional CPU for parallel writes.</p>
<p>Some connectors can be bottlenecked on the CPU when writing due to compression or other factors.
Setting this too high may cause the cluster to become overloaded due to excessive resource
utilization. Especially when the engine is inserting into a partitioned table without using
<a class="reference internal" href="properties-write-partitioning.html#preferred-write-partitioning"><span class="std std-ref">preferred partitioning</span></a>. In such case, each writer thread
could write to all partitions. This can lead to out of memory error since writing to a partition
allocates a certain amount of memory for buffering.</p>
</section>
<section id="task-max-writer-count">
<span id="prop-task-max-writer-count"></span><h2 id="task-max-writer-count"><code class="docutils literal notranslate"><span class="pre">task.max-writer-count</span></code><a class="headerlink" href="properties-task.html#task-max-writer-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Restrictions:</strong> Must be a power of two</p></li>
<li><p><strong>Default value:</strong> The number of physical CPUs of the node, with a minimum value of 2 and a maximum of 64</p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">task_max_writer_count</span></code></p></li>
</ul>
<p>The number of concurrent writer threads per worker per query when either
<a class="reference internal" href="properties-writer-scaling.html#prop-task-scale-writers"><span class="std std-ref">task writer scaling</span></a> or
<a class="reference internal" href="properties-write-partitioning.html#preferred-write-partitioning"><span class="std std-ref">preferred partitioning</span></a> is used. Increasing this value may
increase write speed, especially when a query is not I/O bound and can take advantage of additional
CPU for parallel writes. Some connectors can be bottlenecked on CPU when writing due to compression
or other factors. Setting this too high may cause the cluster to become overloaded due to excessive
resource utilization.</p>
</section>
<section id="task-interrupt-stuck-split-tasks-enabled">
<h2 id="task-interrupt-stuck-split-tasks-enabled"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-enabled</span></code><a class="headerlink" href="properties-task.html#task-interrupt-stuck-split-tasks-enabled" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Enables Trino detecting and failing tasks containing splits that have been stuck. Can be
specified by <code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code> and
<code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-detection-interval</span></code>. Only applies to threads that
are blocked by the third-party Joni regular expression library.</p>
</section>
<section id="task-interrupt-stuck-split-tasks-warning-threshold">
<h2 id="task-interrupt-stuck-split-tasks-warning-threshold"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-warning-threshold</span></code><a class="headerlink" href="properties-task.html#task-interrupt-stuck-split-tasks-warning-threshold" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1m</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10m</span></code></p></li>
</ul>
<p>Print out call stacks at <code class="docutils literal notranslate"><span class="pre">/v1/maxActiveSplits</span></code> endpoint and generate JMX metrics
for splits running longer than the threshold.</p>
</section>
<section id="task-interrupt-stuck-split-tasks-timeout">
<h2 id="task-interrupt-stuck-split-tasks-timeout"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code><a class="headerlink" href="properties-task.html#task-interrupt-stuck-split-tasks-timeout" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">3m</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10m</span></code></p></li>
</ul>
<p>The length of time Trino waits for a blocked split processing thread before failing the
task. Only applies to threads that are blocked by the third-party Joni regular
expression library.</p>
</section>
<section id="task-interrupt-stuck-split-tasks-detection-interval">
<h2 id="task-interrupt-stuck-split-tasks-detection-interval"><code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-detection-interval</span></code><a class="headerlink" href="properties-task.html#task-interrupt-stuck-split-tasks-detection-interval" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1m</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">2m</span></code></p></li>
</ul>
<p>The interval of Trino checks for splits that have processing time exceeding
<code class="docutils literal notranslate"><span class="pre">task.interrupt-stuck-split-tasks-timeout</span></code>. Only applies to threads that are blocked
by the third-party Joni regular expression library.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-exchange.html" title="Exchange properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Exchange properties </span>
              </div>
            </a>
          
          
            <a href="properties-write-partitioning.html" title="Write partitioning properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Write partitioning properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>