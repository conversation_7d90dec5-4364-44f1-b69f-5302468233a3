<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Optimizer properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-optimizer.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Logging properties" href="properties-logging.html" />
    <link rel="prev" title="Node scheduler properties" href="properties-node-scheduler.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-optimizer.html#admin/properties-optimizer" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Optimizer properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Optimizer </label>
    
      <a href="properties-optimizer.html#" class="md-nav__link md-nav__link--active">Optimizer</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-dictionary-aggregation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.dictionary-aggregation</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-metadata-queries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-metadata-queries</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-distinct-aggregations-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.distinct-aggregations-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-aggregation-through-outer-join" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-aggregation-through-outer-join</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-table-write-through-union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-table-write-through-union</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-filter-into-values-max-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-filter-into-values-max-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-reordering-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-reordering-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-max-reordered-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.max-reordered-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-duplicate-insensitive-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-duplicate-insensitive-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-exact-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-exact-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-table-scan-node-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-table-scan-node-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.table-scan-node-partitioning-min-bucket-to-task-ratio</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-colocated-joins-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.colocated-joins-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-filter-conjunction-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.filter-conjunction-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-multi-clause-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-multi-clause-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-non-estimatable-predicate-approximation-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.non-estimatable-predicate-approximation.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-partitioned-build-min-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-partitioned-build-min-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-size-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-size-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-rows-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-rows-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-cost-based-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-cost-based-partitioning</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Optimizer </label>
    
      <a href="properties-optimizer.html#" class="md-nav__link md-nav__link--active">Optimizer</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-dictionary-aggregation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.dictionary-aggregation</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-metadata-queries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-metadata-queries</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-distinct-aggregations-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.distinct-aggregations-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-aggregation-through-outer-join" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-aggregation-through-outer-join</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-table-write-through-union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-table-write-through-union</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-filter-into-values-max-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-filter-into-values-max-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-reordering-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-reordering-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-max-reordered-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.max-reordered-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-duplicate-insensitive-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-duplicate-insensitive-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-exact-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-exact-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-table-scan-node-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-table-scan-node-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.table-scan-node-partitioning-min-bucket-to-task-ratio</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-colocated-joins-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.colocated-joins-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-filter-conjunction-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.filter-conjunction-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-multi-clause-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-multi-clause-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-non-estimatable-predicate-approximation-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.non-estimatable-predicate-approximation.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-partitioned-build-min-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-partitioned-build-min-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-size-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-size-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-rows-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-rows-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-cost-based-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-cost-based-partitioning</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-dictionary-aggregation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.dictionary-aggregation</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-metadata-queries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-metadata-queries</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-distinct-aggregations-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.distinct-aggregations-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-aggregation-through-outer-join" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-aggregation-through-outer-join</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-table-write-through-union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-table-write-through-union</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-push-filter-into-values-max-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.push-filter-into-values-max-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-reordering-strategy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-reordering-strategy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-max-reordered-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.max-reordered-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-optimize-duplicate-insensitive-joins" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-duplicate-insensitive-joins</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-exact-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-exact-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-table-scan-node-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-table-scan-node-partitioning</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.table-scan-node-partitioning-min-bucket-to-task-ratio</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-colocated-joins-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.colocated-joins-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-filter-conjunction-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.filter-conjunction-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-multi-clause-independence-factor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-multi-clause-independence-factor</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-non-estimatable-predicate-approximation-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.non-estimatable-predicate-approximation.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-join-partitioned-build-min-row-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.join-partitioned-build-min-row-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-size-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-size-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-min-input-rows-per-task" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-rows-per-task</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-optimizer.html#optimizer-use-cost-based-partitioning" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">optimizer.use-cost-based-partitioning</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="optimizer-properties">
<h1 id="admin-properties-optimizer--page-root">Optimizer properties<a class="headerlink" href="properties-optimizer.html#admin-properties-optimizer--page-root" title="Link to this heading">#</a></h1>
<section id="optimizer-dictionary-aggregation">
<h2 id="optimizer-dictionary-aggregation"><code class="docutils literal notranslate"><span class="pre">optimizer.dictionary-aggregation</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-dictionary-aggregation" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">dictionary_aggregation</span></code></p></li>
</ul>
<p>Enables optimization for aggregations on dictionaries.</p>
</section>
<section id="optimizer-optimize-metadata-queries">
<h2 id="optimizer-optimize-metadata-queries"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-metadata-queries</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-optimize-metadata-queries" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">optimize_metadata_queries</span></code></p></li>
</ul>
<p>Enable optimization of some aggregations by using values that are stored as metadata.
This allows Trino to execute some simple queries in constant time. Currently, this
optimization applies to <code class="docutils literal notranslate"><span class="pre">max</span></code>, <code class="docutils literal notranslate"><span class="pre">min</span></code> and <code class="docutils literal notranslate"><span class="pre">approx_distinct</span></code> of partition
keys and other aggregation insensitive to the cardinality of the input,including
<code class="docutils literal notranslate"><span class="pre">DISTINCT</span></code> aggregates. Using this may speed up some queries significantly.</p>
<p>The main drawback is that it can produce incorrect results, if the connector returns
partition keys for partitions that have no rows. In particular, the Hive connector
can return empty partitions, if they were created by other systems. Trino cannot
create them.</p>
</section>
<section id="optimizer-distinct-aggregations-strategy">
<h2 id="optimizer-distinct-aggregations-strategy"><code class="docutils literal notranslate"><span class="pre">optimizer.distinct-aggregations-strategy</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-distinct-aggregations-strategy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Allowed values:</strong> <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code>, <code class="docutils literal notranslate"><span class="pre">MARK_DISTINCT</span></code>, <code class="docutils literal notranslate"><span class="pre">SINGLE_STEP</span></code>, <code class="docutils literal notranslate"><span class="pre">PRE_AGGREGATE</span></code>, <code class="docutils literal notranslate"><span class="pre">SPLIT_TO_SUBQUERIES</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">distinct_aggregations_strategy</span></code></p></li>
</ul>
<p>The strategy to use for multiple distinct aggregations.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">SINGLE_STEP</span></code> Computes distinct aggregations in single-step without any pre-aggregations.
This strategy will perform poorly if the number of distinct grouping keys is small.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MARK_DISTINCT</span></code> uses <code class="docutils literal notranslate"><span class="pre">MarkDistinct</span></code> for multiple distinct aggregations or for mix of distinct and non-distinct aggregations.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PRE_AGGREGATE</span></code> Computes distinct aggregations using a combination of aggregation and pre-aggregation steps.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SPLIT_TO_SUBQUERIES</span></code> Splits the aggregation input to independent sub-queries, where each subquery computes single distinct aggregation thus improving parallelism</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> chooses the strategy automatically.</p></li>
</ul>
<p>Single-step strategy is preferred. However, for cases with limited concurrency due to
a small number of distinct grouping keys, it will choose an alternative strategy
based on input data statistics.</p>
</section>
<section id="optimizer-push-aggregation-through-outer-join">
<h2 id="optimizer-push-aggregation-through-outer-join"><code class="docutils literal notranslate"><span class="pre">optimizer.push-aggregation-through-outer-join</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-push-aggregation-through-outer-join" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">push_aggregation_through_outer_join</span></code></p></li>
</ul>
<p>When an aggregation is above an outer join and all columns from the outer side of the join
are in the grouping clause, the aggregation is pushed below the outer join. This optimization
is particularly useful for correlated scalar subqueries, which get rewritten to an aggregation
over an outer join. For example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">item</span><span class="w"> </span><span class="n">i</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">i</span><span class="p">.</span><span class="n">i_current_price</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="p">(</span>
<span class="w">        </span><span class="k">SELECT</span><span class="w"> </span><span class="k">AVG</span><span class="p">(</span><span class="n">j</span><span class="p">.</span><span class="n">i_current_price</span><span class="p">)</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">item</span><span class="w"> </span><span class="n">j</span>
<span class="w">            </span><span class="k">WHERE</span><span class="w"> </span><span class="n">i</span><span class="p">.</span><span class="n">i_category</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">j</span><span class="p">.</span><span class="n">i_category</span><span class="p">);</span>
</pre></div>
</div>
<p>Enabling this optimization can substantially speed up queries by reducing the
amount of data that needs to be processed by the join. However, it may slow down
some queries that have very selective joins.</p>
</section>
<section id="optimizer-push-table-write-through-union">
<h2 id="optimizer-push-table-write-through-union"><code class="docutils literal notranslate"><span class="pre">optimizer.push-table-write-through-union</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-push-table-write-through-union" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">push_table_write_through_union</span></code></p></li>
</ul>
<p>Parallelize writes when using <code class="docutils literal notranslate"><span class="pre">UNION</span> <span class="pre">ALL</span></code> in queries that write data. This improves the
speed of writing output tables in <code class="docutils literal notranslate"><span class="pre">UNION</span> <span class="pre">ALL</span></code> queries, because these writes do not require
additional synchronization when collecting results. Enabling this optimization can improve
<code class="docutils literal notranslate"><span class="pre">UNION</span> <span class="pre">ALL</span></code> speed, when write speed is not yet saturated. However, it may slow down queries
in an already heavily loaded system.</p>
</section>
<section id="optimizer-push-filter-into-values-max-row-count">
<h2 id="optimizer-push-filter-into-values-max-row-count"><code class="docutils literal notranslate"><span class="pre">optimizer.push-filter-into-values-max-row-count</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-push-filter-into-values-max-row-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">100</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">0</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">push_filter_into_values_max_row_count</span></code></p></li>
</ul>
<p>The number of rows in <a class="reference internal" href="../sql/values.html"><span class="doc std std-doc">VALUES</span></a> below which the planner evaluates a filter
on top of <code class="docutils literal notranslate"><span class="pre">VALUES</span></code> to optimize the query plan.</p>
</section>
<section id="optimizer-join-reordering-strategy">
<h2 id="optimizer-join-reordering-strategy"><code class="docutils literal notranslate"><span class="pre">optimizer.join-reordering-strategy</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-join-reordering-strategy" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Allowed values:</strong> <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code>, <code class="docutils literal notranslate"><span class="pre">ELIMINATE_CROSS_JOINS</span></code>, <code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">join_reordering_strategy</span></code></p></li>
</ul>
<p>The join reordering strategy to use.  <code class="docutils literal notranslate"><span class="pre">NONE</span></code> maintains the order the tables are listed in the
query.  <code class="docutils literal notranslate"><span class="pre">ELIMINATE_CROSS_JOINS</span></code> reorders joins to eliminate cross joins, where possible, and
otherwise maintains the original query order. When reordering joins, it also strives to maintain the
original table order as much as possible. <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> enumerates possible orders, and uses
statistics-based cost estimation to determine the least cost order. If stats are not available, or if
for any reason a cost could not be computed, the <code class="docutils literal notranslate"><span class="pre">ELIMINATE_CROSS_JOINS</span></code> strategy is used.</p>
</section>
<section id="optimizer-max-reordered-joins">
<h2 id="optimizer-max-reordered-joins"><code class="docutils literal notranslate"><span class="pre">optimizer.max-reordered-joins</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-max-reordered-joins" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">max_reordered_joins</span></code></p></li>
</ul>
<p>When optimizer.join-reordering-strategy is set to cost-based, this property determines
the maximum number of joins that can be reordered at once.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The number of possible join orders scales factorially with the number of
relations, so increasing this value can cause serious performance issues.</p>
</div>
</section>
<section id="optimizer-optimize-duplicate-insensitive-joins">
<h2 id="optimizer-optimize-duplicate-insensitive-joins"><code class="docutils literal notranslate"><span class="pre">optimizer.optimize-duplicate-insensitive-joins</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-optimize-duplicate-insensitive-joins" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">optimize_duplicate_insensitive_joins</span></code></p></li>
</ul>
<p>Reduces number of rows produced by joins when optimizer detects that duplicated
join output rows can be skipped.</p>
</section>
<section id="optimizer-use-exact-partitioning">
<h2 id="optimizer-use-exact-partitioning"><code class="docutils literal notranslate"><span class="pre">optimizer.use-exact-partitioning</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-use-exact-partitioning" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">use_exact_partitioning</span></code></p></li>
</ul>
<p>Re-partition data unless the partitioning of the upstream
<a class="reference internal" href="../overview/concepts.html#trino-concept-stage"><span class="std std-ref">stage</span></a> exactly matches what the downstream stage
expects.</p>
</section>
<section id="optimizer-use-table-scan-node-partitioning">
<h2 id="optimizer-use-table-scan-node-partitioning"><code class="docutils literal notranslate"><span class="pre">optimizer.use-table-scan-node-partitioning</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-use-table-scan-node-partitioning" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">use_table_scan_node_partitioning</span></code></p></li>
</ul>
<p>Use connector provided table node partitioning when reading tables.
For example, table node partitioning corresponds to Hive table buckets.
When set to <code class="docutils literal notranslate"><span class="pre">true</span></code> and minimal partition to task ratio is matched or exceeded,
each table partition is read by a separate worker. The minimal ratio is defined in
<code class="docutils literal notranslate"><span class="pre">optimizer.table-scan-node-partitioning-min-bucket-to-task-ratio</span></code>.</p>
<p>Partition reader assignments are distributed across workers for
parallel processing. Use of table scan node partitioning can improve
query performance by reducing query complexity. For example,
cluster wide data reshuffling might not be needed when processing an aggregation query.
However, query parallelism might be reduced when partition count is
low compared to number of workers.</p>
</section>
<section id="optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio">
<h2 id="optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio"><code class="docutils literal notranslate"><span class="pre">optimizer.table-scan-node-partitioning-min-bucket-to-task-ratio</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-table-scan-node-partitioning-min-bucket-to-task-ratio" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-double"><span class="std std-ref">double</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">0.5</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">table_scan_node_partitioning_min_bucket_to_task_ratio</span></code></p></li>
</ul>
<p>Specifies minimal bucket to task ratio that has to be matched or exceeded in order
to use table scan node partitioning. When the table bucket count is small
compared to the number of workers, then the table scan is distributed across
all workers for improved parallelism.</p>
</section>
<section id="optimizer-colocated-joins-enabled">
<h2 id="optimizer-colocated-joins-enabled"><code class="docutils literal notranslate"><span class="pre">optimizer.colocated-joins-enabled</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-colocated-joins-enabled" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">colocated_join</span></code></p></li>
</ul>
<p>Use co-located joins when both sides of a join have the same table partitioning on the join keys
and the conditions for <code class="docutils literal notranslate"><span class="pre">optimizer.use-table-scan-node-partitioning</span></code> are met.
For example, a join on bucketed Hive tables with matching bucketing schemes can
avoid exchanging data between workers using a co-located join to improve query performance.</p>
</section>
<section id="optimizer-filter-conjunction-independence-factor">
<h2 id="optimizer-filter-conjunction-independence-factor"><code class="docutils literal notranslate"><span class="pre">optimizer.filter-conjunction-independence-factor</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-filter-conjunction-independence-factor" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-double"><span class="std std-ref">double</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">0.75</span></code></p></li>
<li><p><strong>Min allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">0</span></code></p></li>
<li><p><strong>Max allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">filter_conjunction_independence_factor</span></code></p></li>
</ul>
<p>Scales the strength of independence assumption for estimating the selectivity of
the conjunction of multiple predicates. Lower values for this property will produce
more conservative estimates by assuming a greater degree of correlation between the
columns of the predicates in a conjunction. A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> results in the
optimizer assuming that the columns of the predicates are fully correlated and only
the most selective predicate drives the selectivity of a conjunction of predicates.</p>
</section>
<section id="optimizer-join-multi-clause-independence-factor">
<h2 id="optimizer-join-multi-clause-independence-factor"><code class="docutils literal notranslate"><span class="pre">optimizer.join-multi-clause-independence-factor</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-join-multi-clause-independence-factor" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-double"><span class="std std-ref">double</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">0.25</span></code></p></li>
<li><p><strong>Min allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">0</span></code></p></li>
<li><p><strong>Max allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">join_multi_clause_independence_factor</span></code></p></li>
</ul>
<p>Scales the strength of independence assumption for estimating the output of a
multi-clause join. Lower values for this property will produce more
conservative estimates by assuming a greater degree of correlation between the
columns of the clauses in a join. A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> results in the optimizer
assuming that the columns of the join clauses are fully correlated and only
the most selective clause drives the selectivity of the join.</p>
</section>
<section id="optimizer-non-estimatable-predicate-approximation-enabled">
<h2 id="optimizer-non-estimatable-predicate-approximation-enabled"><code class="docutils literal notranslate"><span class="pre">optimizer.non-estimatable-predicate-approximation.enabled</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-non-estimatable-predicate-approximation-enabled" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">non_estimatable_predicate_approximation_enabled</span></code></p></li>
</ul>
<p>Enables approximation of the output row count of filters whose costs cannot be
accurately estimated even with complete statistics. This allows the optimizer to
produce more efficient plans in the presence of filters which were previously
not estimated.</p>
</section>
<section id="optimizer-join-partitioned-build-min-row-count">
<h2 id="optimizer-join-partitioned-build-min-row-count"><code class="docutils literal notranslate"><span class="pre">optimizer.join-partitioned-build-min-row-count</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-join-partitioned-build-min-row-count" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1000000</span></code></p></li>
<li><p><strong>Min allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">0</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">join_partitioned_build_min_row_count</span></code></p></li>
</ul>
<p>The minimum number of join build side rows required to use partitioned join lookup.
If the build side of a join is estimated to be smaller than the configured threshold,
single threaded join lookup is used to improve join performance.
A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> disables this optimization.</p>
</section>
<section id="optimizer-min-input-size-per-task">
<h2 id="optimizer-min-input-size-per-task"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-size-per-task</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-min-input-size-per-task" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">5GB</span></code></p></li>
<li><p><strong>Min allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">0MB</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">min_input_size_per_task</span></code></p></li>
</ul>
<p>The minimum input size required per task. This will help optimizer to determine hash
partition count for joins and aggregations. Limiting hash partition count for small queries
increases concurrency on large clusters where multiple small queries are running concurrently.
The estimated value will always be between <code class="docutils literal notranslate"><span class="pre">min_hash_partition_count</span></code> and
<code class="docutils literal notranslate"><span class="pre">max_hash_partition_count</span></code> session property.
A value of <code class="docutils literal notranslate"><span class="pre">0MB</span></code> disables this optimization.</p>
</section>
<section id="optimizer-min-input-rows-per-task">
<h2 id="optimizer-min-input-rows-per-task"><code class="docutils literal notranslate"><span class="pre">optimizer.min-input-rows-per-task</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-min-input-rows-per-task" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10000000</span></code></p></li>
<li><p><strong>Min allowed value:</strong> <code class="docutils literal notranslate"><span class="pre">0</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">min_input_rows_per_task</span></code></p></li>
</ul>
<p>The minimum number of input rows required per task. This will help optimizer to determine hash
partition count for joins and aggregations. Limiting hash partition count for small queries
increases concurrency on large clusters where multiple small queries are running concurrently.
The estimated value will always be between <code class="docutils literal notranslate"><span class="pre">min_hash_partition_count</span></code> and
<code class="docutils literal notranslate"><span class="pre">max_hash_partition_count</span></code> session property.
A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> disables this optimization.</p>
</section>
<section id="optimizer-use-cost-based-partitioning">
<h2 id="optimizer-use-cost-based-partitioning"><code class="docutils literal notranslate"><span class="pre">optimizer.use-cost-based-partitioning</span></code><a class="headerlink" href="properties-optimizer.html#optimizer-use-cost-based-partitioning" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p><strong>Session property:</strong> <code class="docutils literal notranslate"><span class="pre">use_cost_based_partitioning</span></code></p></li>
</ul>
<p>When enabled the cost based optimizer is used to determine if repartitioning the output of an
already partitioned stage is necessary.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-node-scheduler.html" title="Node scheduler properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Node scheduler properties </span>
              </div>
            </a>
          
          
            <a href="properties-logging.html" title="Logging properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Logging properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>