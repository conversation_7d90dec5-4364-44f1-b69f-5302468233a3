<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>OpenLineage event listener &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="event-listeners-openlineage.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Query optimizer" href="../optimizer.html" />
    <link rel="prev" title="MySQL event listener" href="event-listeners-mysql.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="event-listeners-openlineage.html#admin/event-listeners-openlineage" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> OpenLineage event listener </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> OpenLineage event listener </label>
    
      <a href="event-listeners-openlineage.html#" class="md-nav__link md-nav__link--active">OpenLineage event listener</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#rationale" class="md-nav__link">Rationale</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#available-trino-facets" class="md-nav__link">Available Trino Facets</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-metadata" class="md-nav__link">Trino Metadata</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-query-context" class="md-nav__link">Trino Query Context</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-query-statistics" class="md-nav__link">Trino Query Statistics</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#supported-transport-types" class="md-nav__link">Supported Transport Types</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#custom-http-headers" class="md-nav__link">Custom HTTP headers</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#custom-url-params" class="md-nav__link">Custom URL Params</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#rationale" class="md-nav__link">Rationale</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#available-trino-facets" class="md-nav__link">Available Trino Facets</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-metadata" class="md-nav__link">Trino Metadata</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-query-context" class="md-nav__link">Trino Query Context</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#trino-query-statistics" class="md-nav__link">Trino Query Statistics</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#supported-transport-types" class="md-nav__link">Supported Transport Types</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#custom-http-headers" class="md-nav__link">Custom HTTP headers</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-openlineage.html#custom-url-params" class="md-nav__link">Custom URL Params</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="openlineage-event-listener">
<h1 id="admin-event-listeners-openlineage--page-root">OpenLineage event listener<a class="headerlink" href="event-listeners-openlineage.html#admin-event-listeners-openlineage--page-root" title="Link to this heading">#</a></h1>
<p>The OpenLineage event listener plugin allows streaming of lineage information,
encoded in
JSON format aligned with OpenLineage specification, to an external, OpenLineage
compatible API, by POSTing them
to a specified URI.</p>
<section id="rationale">
<h2 id="rationale">Rationale<a class="headerlink" href="event-listeners-openlineage.html#rationale" title="Link to this heading">#</a></h2>
<p>This event listener is aiming to capture every query that creates or modifies
Trino tables and transform it into lineage
information. Linage can be understood as relationship/flow between data/tables.
OpenLineage is a widely used open-source
standard for capturing lineage information from variety of system including (but
not limited to) Spark, Airflow, Flink.</p>
<table id="id2">
<caption><span class="caption-text">Trino Query attributes mapping to OpenLineage attributes</span><a class="headerlink" href="event-listeners-openlineage.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino</p></th>
<th class="head"><p>OpenLineage</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">{UUID(Query</span> <span class="pre">Id)}</span></code></p></td>
<td><p>Run ID</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">{queryCreatedEvent.getCreateTime()}</span> <span class="pre">or</span> <span class="pre">{queryCompletedEvent.getEndTime()}</span> </code></p></td>
<td><p>Run Event Time</p></td>
</tr>
<tr class="row-even"><td><p>Query Id</p></td>
<td><p>Job Facet Name</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino://</span> <span class="pre">+</span> <span class="pre">{openlineage-event-listener.trino.uri.getHost()}</span> <span class="pre">+</span> <span class="pre">":"</span> <span class="pre">+</span> <span class="pre">{openlineage-event-listener.trino.uri.getPort()}</span></code></p></td>
<td><p>Job Facet Namespace (default, can be overridden)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">{schema}.{table}</span></code></p></td>
<td><p>Dataset Name</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino://</span> <span class="pre">+</span> <span class="pre">{openlineage-event-listener.trino.uri.getHost()}</span> <span class="pre">+</span> <span class="pre">":"</span> <span class="pre">+</span> <span class="pre">{openlineage-event-listener.trino.uri.getPort()}</span></code></p></td>
<td><p>Dataset Namespace</p></td>
</tr>
</tbody>
</table>
<section id="available-trino-facets">
<span id="trino-facets"></span><h3 id="available-trino-facets">Available Trino Facets<a class="headerlink" href="event-listeners-openlineage.html#available-trino-facets" title="Link to this heading">#</a></h3>
<section id="trino-metadata">
<h4 id="trino-metadata">Trino Metadata<a class="headerlink" href="event-listeners-openlineage.html#trino-metadata" title="Link to this heading">#</a></h4>
<p>Facet containing properties (if present):</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">queryPlan</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">transactionId</span></code> - transaction id used for query processing</p></li>
</ul>
<p>related to query based on which OpenLineage Run Event was generated.</p>
<p>Available in both <code class="docutils literal notranslate"><span class="pre">Start</span></code> and <code class="docutils literal notranslate"><span class="pre">Complete/Fail</span></code> OpenLineage events.</p>
<p>If you want to disable this facet, add <code class="docutils literal notranslate"><span class="pre">trino_metadata</span></code> to
<code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.disabled-facets</span></code>.</p>
</section>
<section id="trino-query-context">
<h4 id="trino-query-context">Trino Query Context<a class="headerlink" href="event-listeners-openlineage.html#trino-query-context" title="Link to this heading">#</a></h4>
<p>Facet containing properties:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">serverVersion</span></code> - version of Trino server that was used to process the query</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">environment</span></code> - inherited from <code class="docutils literal notranslate"><span class="pre">node.environment</span></code> of <a class="reference internal" href="../installation/deployment.html#node-properties"><span class="std std-ref">Node properties</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">queryType</span></code> - one of query types configured via
<code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.trino.include-query-types</span></code></p></li>
</ul>
<p>related to query based on which OpenLineage Run Event was generated.</p>
<p>Available in both <code class="docutils literal notranslate"><span class="pre">Start</span></code> and <code class="docutils literal notranslate"><span class="pre">Complete/Fail</span></code> OpenLineage events.</p>
<p>If you want to disable this facet, add <code class="docutils literal notranslate"><span class="pre">trino_query_context</span></code> to
<code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.disabled-facets</span></code>.</p>
</section>
<section id="trino-query-statistics">
<h4 id="trino-query-statistics">Trino Query Statistics<a class="headerlink" href="event-listeners-openlineage.html#trino-query-statistics" title="Link to this heading">#</a></h4>
<p>Facet containing full contents of query statistics of completed. Available only
in OpenLineage <code class="docutils literal notranslate"><span class="pre">Complete/Fail</span></code> events.</p>
<p>If you want to disable this facet, add <code class="docutils literal notranslate"><span class="pre">trino_query_statistics</span></code> to
<code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.disabled-facets</span></code>.</p>
</section>
</section>
</section>
<section id="requirements">
<span id="openlineage-event-listener-requirements"></span><h2 id="requirements">Requirements<a class="headerlink" href="event-listeners-openlineage.html#requirements" title="Link to this heading">#</a></h2>
<p>You need to perform the following steps:</p>
<ul class="simple">
<li><p>Provide an HTTP/S service that accepts POST events with a JSON body and is
compatible with the OpenLineage API format.</p></li>
<li><p>Configure <code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.transport.url</span></code> in the event listener
properties file with the URI of the service</p></li>
<li><p>Configure <code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.trino.uri</span></code> so proper OpenLineage job
namespace is render within produced events. Needs to be proper uri with scheme,
host and port (otherwise plugin will fail to start).</p></li>
<li><p>Configure what events to send as detailed
in <a class="reference internal" href="event-listeners-openlineage.html#openlineage-event-listener-configuration"><span class="std std-ref">Configuration</span></a></p></li>
</ul>
</section>
<section id="configuration">
<span id="openlineage-event-listener-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="event-listeners-openlineage.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the OpenLineage event listener, create an event listener properties
file in <code class="docutils literal notranslate"><span class="pre">etc</span></code> named <code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.properties</span></code> with the following
contents as an example of minimal required configuration:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">event-listener.name</span><span class="o">=</span><span class="s">openlineage</span>
<span class="na">openlineage-event-listener.trino.uri</span><span class="o">=</span><span class="s">&lt;Address of your Trino coordinator&gt;</span>
</pre></div>
</div>
<p>Add <code class="docutils literal notranslate"><span class="pre">etc/openlineage-event-listener.properties</span></code> to <code class="docutils literal notranslate"><span class="pre">event-listener.config-files</span></code>
in <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">event-listener.config-files</span><span class="o">=</span><span class="s">etc/openlineage-event-listener.properties,...</span>
</pre></div>
</div>
<table id="id3">
<caption><span class="caption-text">OpenLineage event listener configuration properties</span><a class="headerlink" href="event-listeners-openlineage.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 40%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>openlineage-event-listener.transport.type</p></td>
<td><p>Type of transport to use when emitting lineage information.
See <a class="reference internal" href="event-listeners-openlineage.html#supported-transport-types"><span class="std std-ref">Supported Transport Types</span></a> for list of available options with
descriptions.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CONSOLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>openlineage-event-listener.trino.uri</p></td>
<td><p>Required Trino URL with host and port. Used to render Job Namespace in OpenLineage.</p></td>
<td><p>None.</p></td>
</tr>
<tr class="row-even"><td><p>openlineage-event-listener.trino.include-query-types</p></td>
<td><p>Which types of queries should be taken into account when emitting lineage
information. List of values split by comma. Each value must be
matching <code class="docutils literal notranslate"><span class="pre">io.trino.spi.resourcegroups.QueryType</span></code> enum. Query types not
included here are filtered out.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DELETE,INSERT,MERGE,UPDATE,ALTER_TABLE_EXECUTE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>openlineage-event-listener.disabled-facets</p></td>
<td><p>Which <a class="reference internal" href="event-listeners-openlineage.html#trino-facets"><span class="std std-ref">Available Trino Facets</span></a> should be not included in final OpenLineage event.
Allowed values: <code class="docutils literal notranslate"><span class="pre">trino_metadata</span></code>, <code class="docutils literal notranslate"><span class="pre">trino_query_context</span></code>,
<code class="docutils literal notranslate"><span class="pre">trino_query_statistics</span></code>.</p></td>
<td><p>None.</p></td>
</tr>
<tr class="row-even"><td><p>openlineage-event-listener.namespace</p></td>
<td><p>Custom namespace to be used for Job <code class="docutils literal notranslate"><span class="pre">namespace</span></code> attribute. If blank will
default to Dataset Namespace.</p></td>
<td><p>None.</p></td>
</tr>
</tbody>
</table>
<section id="supported-transport-types">
<span id="id1"></span><h3 id="supported-transport-types">Supported Transport Types<a class="headerlink" href="event-listeners-openlineage.html#supported-transport-types" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CONSOLE</span></code> - sends OpenLineage JSON event to Trino coordinator standard output.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HTTP</span></code> - sends OpenLineage JSON event to OpenLineage compatible HTTP endpoint.</p></li>
</ul>
<table id="id4">
<caption><span class="caption-text">OpenLineage <code class="docutils literal notranslate"><span class="pre">HTTP</span></code> Transport Configuration properties</span><a class="headerlink" href="event-listeners-openlineage.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 40%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>openlineage-event-listener.transport.url</p></td>
<td><p>URL of OpenLineage . Required if <code class="docutils literal notranslate"><span class="pre">HTTP</span></code> transport is configured.</p></td>
<td><p>None.</p></td>
</tr>
<tr class="row-odd"><td><p>openlineage-event-listener.transport.endpoint</p></td>
<td><p>Custom path for OpenLineage compatible endpoint. If configured, there
cannot be any custom path within
<code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.transport.url</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">/api/v1</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p>openlineage-event-listener.transport.api-key</p></td>
<td><p>API key (string value) used to authenticate with the service.
at <code class="docutils literal notranslate"><span class="pre">openlineage-event-listener.transport.url</span></code>.</p></td>
<td><p>None.</p></td>
</tr>
<tr class="row-odd"><td><p>openlineage-event-listener.transport.timeout</p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">Timeout</span></a> when making HTTP Requests.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5000ms</span></code></p></td>
</tr>
<tr class="row-even"><td><p>openlineage-event-listener.transport.headers</p></td>
<td><p>List of custom HTTP headers to be sent along with the events. See
<a class="reference internal" href="event-listeners-openlineage.html#openlineage-event-listener-custom-headers"><span class="std std-ref">Custom HTTP headers</span></a> for more details.</p></td>
<td><p>Empty</p></td>
</tr>
<tr class="row-odd"><td><p>openlineage-event-listener.transport.url-params</p></td>
<td><p>List of custom url params to be added to final HTTP Request. See
<a class="reference internal" href="event-listeners-openlineage.html#openlineage-event-listener-custom-url-params"><span class="std std-ref">Custom URL Params</span></a> for more details.</p></td>
<td><p>Empty</p></td>
</tr>
</tbody>
</table>
</section>
<section id="custom-http-headers">
<span id="openlineage-event-listener-custom-headers"></span><h3 id="custom-http-headers">Custom HTTP headers<a class="headerlink" href="event-listeners-openlineage.html#custom-http-headers" title="Link to this heading">#</a></h3>
<p>Providing custom HTTP headers is a useful mechanism for sending metadata along
with event messages.</p>
<p>Providing headers follows the pattern of <code class="docutils literal notranslate"><span class="pre">key:value</span></code> pairs separated by commas:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>openlineage-event-listener.transport.headers="Header-Name-1:header value 1,Header-Value-2:header value 2,..."
</pre></div>
</div>
<p>If you need to use a comma(<code class="docutils literal notranslate"><span class="pre">,</span></code>) or colon(<code class="docutils literal notranslate"><span class="pre">:</span></code>) in a header name or value,
escape it using a backslash (<code class="docutils literal notranslate"><span class="pre">\</span></code>).</p>
<p>Keep in mind that these are static, so they can not carry information
taken from the event itself.</p>
</section>
<section id="custom-url-params">
<span id="openlineage-event-listener-custom-url-params"></span><h3 id="custom-url-params">Custom URL Params<a class="headerlink" href="event-listeners-openlineage.html#custom-url-params" title="Link to this heading">#</a></h3>
<p>Providing additional URL Params included in final HTTP Request.</p>
<p>Providing url params follows the pattern of <code class="docutils literal notranslate"><span class="pre">key:value</span></code> pairs separated by commas:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>openlineage-event-listener.transport.url-params="Param-Name-1:param value 1,Param-Value-2:param value 2,..."
</pre></div>
</div>
<p>Keep in mind that these are static, so they can not carry information
taken from the event itself.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="event-listeners-mysql.html" title="MySQL event listener"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> MySQL event listener </span>
              </div>
            </a>
          
          
            <a href="../optimizer.html" title="Query optimizer"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Query optimizer </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>