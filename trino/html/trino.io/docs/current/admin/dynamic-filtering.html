<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Dynamic filtering &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="dynamic-filtering.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Graceful shutdown" href="graceful-shutdown.html" />
    <link rel="prev" title="Distributed sort" href="dist-sort.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="dynamic-filtering.html#admin/dynamic-filtering" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Dynamic filtering </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Dynamic filtering </label>
    
      <a href="dynamic-filtering.html#" class="md-nav__link md-nav__link--active">Dynamic filtering</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="dynamic-filtering.html#analysis-and-confirmation" class="md-nav__link">Analysis and confirmation</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#dynamic-filter-collection-thresholds" class="md-nav__link">Dynamic filter collection thresholds</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#dimension-tables-layout" class="md-nav__link">Dimension tables layout</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#limitations" class="md-nav__link">Limitations</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="dynamic-filtering.html#analysis-and-confirmation" class="md-nav__link">Analysis and confirmation</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#dynamic-filter-collection-thresholds" class="md-nav__link">Dynamic filter collection thresholds</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#dimension-tables-layout" class="md-nav__link">Dimension tables layout</a>
        </li>
        <li class="md-nav__item"><a href="dynamic-filtering.html#limitations" class="md-nav__link">Limitations</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="dynamic-filtering">
<h1 id="admin-dynamic-filtering--page-root">Dynamic filtering<a class="headerlink" href="dynamic-filtering.html#admin-dynamic-filtering--page-root" title="Link to this heading">#</a></h1>
<p>Dynamic filtering optimizations significantly improve the performance of queries
with selective joins by avoiding reading of data that would be filtered by join condition.</p>
<p>Consider the following query which captures a common pattern of a fact table <code class="docutils literal notranslate"><span class="pre">store_sales</span></code>
joined with a filtered dimension table <code class="docutils literal notranslate"><span class="pre">date_dim</span></code>:</p>
<blockquote>
<div><p>SELECT count(*)
FROM store_sales
JOIN date_dim ON store_sales.ss_sold_date_sk = date_dim.d_date_sk
WHERE d_following_holiday=’Y’ AND d_year = 2000;</p>
</div></blockquote>
<p>Without dynamic filtering, Trino pushes predicates for the dimension table to the
table scan on <code class="docutils literal notranslate"><span class="pre">date_dim</span></code>, and it scans all the data in the fact table since there
are no filters on <code class="docutils literal notranslate"><span class="pre">store_sales</span></code> in the query. The join operator ends up throwing away
most of the probe-side rows as the join criteria is highly selective.</p>
<p>When dynamic filtering is enabled, Trino collects candidate values for join condition
from the processed dimension table on the right side of join. In the case of broadcast joins,
the runtime predicates generated from this collection are pushed into the local table scan
on the left side of the join running on the same worker.</p>
<p>Additionally, these runtime predicates are communicated to the coordinator over the network
so that dynamic filtering can also be performed on the coordinator during enumeration of
table scan splits.</p>
<p>For example, in the case of the Hive connector, dynamic filters are used
to skip loading of partitions which don’t match the join criteria.
This is known as <strong>dynamic partition pruning</strong>.</p>
<p>After completing the collection of dynamic filters, the coordinator also distributes them
to worker nodes over the network for partitioned joins. This allows push down of dynamic
filters from partitioned joins into the table scans on the left side of that join.</p>
<p>The results of dynamic filtering optimization can include the following benefits:</p>
<ul class="simple">
<li><p>improved overall query performance</p></li>
<li><p>reduced network traffic between Trino and the data source</p></li>
<li><p>reduced load on the remote data source</p></li>
</ul>
<p>Dynamic filtering is enabled by default. It can be disabled by setting either the
<code class="docutils literal notranslate"><span class="pre">enable-dynamic-filtering</span></code> configuration property, or the session property
<code class="docutils literal notranslate"><span class="pre">enable_dynamic_filtering</span></code> to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<p>Support for push down of dynamic filters is specific to each connector,
and the relevant underlying database or storage system. The documentation for
specific connectors with support for dynamic filtering includes further details,
for example the <a class="reference internal" href="../connector/hive.html#hive-dynamic-filtering"><span class="std std-ref">Hive connector</span></a>
or the <a class="reference internal" href="../connector/memory.html#memory-dynamic-filtering"><span class="std std-ref">Memory connector</span></a>.</p>
<section id="analysis-and-confirmation">
<h2 id="analysis-and-confirmation">Analysis and confirmation<a class="headerlink" href="dynamic-filtering.html#analysis-and-confirmation" title="Link to this heading">#</a></h2>
<p>Dynamic filtering depends on a number of factors:</p>
<ul class="simple">
<li><p>Planner support for dynamic filtering for a given join operation in Trino.
Currently inner and right joins with <code class="docutils literal notranslate"><span class="pre">=</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;=</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;=</span></code> or
<code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code> join conditions, and
semi-joins with <code class="docutils literal notranslate"><span class="pre">IN</span></code> conditions are supported.</p></li>
<li><p>Connector support for utilizing dynamic filters pushed into the table scan at runtime.
For example, the Hive connector can push dynamic filters into ORC and Parquet readers
to perform stripe or row-group pruning.</p></li>
<li><p>Connector support for utilizing dynamic filters at the splits enumeration stage.</p></li>
<li><p>Size of right (build) side of the join.</p></li>
</ul>
<p>You can take a closer look at the <a class="reference internal" href="../sql/explain.html"><span class="doc">EXPLAIN plan</span></a> of the query
to analyze if the planner is adding dynamic filters to a specific query’s plan.
For example, the explain plan for the above query can be obtained by running
the following statement:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">store_sales</span>
<span class="k">JOIN</span><span class="w"> </span><span class="n">date_dim</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">store_sales</span><span class="p">.</span><span class="n">ss_sold_date_sk</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">date_dim</span><span class="p">.</span><span class="n">d_date_sk</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">d_following_holiday</span><span class="o">=</span><span class="s1">'Y'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">d_year</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2000</span><span class="p">;</span>
</pre></div>
</div>
<p>The explain plan for this query shows <code class="docutils literal notranslate"><span class="pre">dynamicFilterAssignments</span></code> in the
<code class="docutils literal notranslate"><span class="pre">InnerJoin</span></code> node with dynamic filter <code class="docutils literal notranslate"><span class="pre">df_370</span></code> collected from build symbol <code class="docutils literal notranslate"><span class="pre">d_date_sk</span></code>.
You can also see the <code class="docutils literal notranslate"><span class="pre">dynamicFilter</span></code> predicate as part of the Hive <code class="docutils literal notranslate"><span class="pre">ScanFilterProject</span></code>
operator where <code class="docutils literal notranslate"><span class="pre">df_370</span></code> is associated with probe symbol <code class="docutils literal notranslate"><span class="pre">ss_sold_date_sk</span></code>.
This shows you that the planner is successful in pushing dynamic filters
down to the connector in the query plan.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>...

Fragment 1 [SOURCE]
    Output layout: [count_3]
    Output partitioning: SINGLE []
    Aggregate(PARTIAL)
    │   Layout: [count_3:bigint]
    │   count_3 := count(*)
    └─ InnerJoin[(""ss_sold_date_sk"" = ""d_date_sk"")][$hashvalue, $hashvalue_4]
       │   Layout: []
       │   Estimates: {rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}
       │   Distribution: REPLICATED
       │   dynamicFilterAssignments = {d_date_sk -&gt; #df_370}
       ├─ ScanFilterProject[table = hive:default:store_sales, grouped = false, filterPredicate = true, dynamicFilters = {""ss_sold_date_sk"" = #df_370}]
       │      Layout: [ss_sold_date_sk:bigint, $hashvalue:bigint]
       │      Estimates: {rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}/{rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}/{rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}
       │      $hashvalue := combine_hash(bigint '0', COALESCE(""$operator$hash_code""(""ss_sold_date_sk""), 0))
       │      ss_sold_date_sk := ss_sold_date_sk:bigint:REGULAR
       └─ LocalExchange[HASH][$hashvalue_4] (""d_date_sk"")
          │   Layout: [d_date_sk:bigint, $hashvalue_4:bigint]
          │   Estimates: {rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}
          └─ RemoteSource[2]
                 Layout: [d_date_sk:bigint, $hashvalue_5:bigint]

Fragment 2 [SOURCE]
    Output layout: [d_date_sk, $hashvalue_6]
    Output partitioning: BROADCAST []
    ScanFilterProject[table = hive:default:date_dim, grouped = false, filterPredicate = ((""d_following_holiday"" = CAST('Y' AS char(1))) AND (""d_year"" = 2000))]
        Layout: [d_date_sk:bigint, $hashvalue_6:bigint]
        Estimates: {rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}/{rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}/{rows: 0 (0B), cpu: 0, memory: 0B, network: 0B}
        $hashvalue_6 := combine_hash(bigint '0', COALESCE(""$operator$hash_code""(""d_date_sk""), 0))
        d_following_holiday := d_following_holiday:char(1):REGULAR
        d_date_sk := d_date_sk:bigint:REGULAR
        d_year := d_year:int:REGULAR
</pre></div>
</div>
<p>During execution of a query with dynamic filters, Trino populates statistics
about dynamic filters in the QueryInfo JSON available through the
<a class="reference internal" href="web-interface.html"><span class="doc">Web UI</span></a>.
In the <code class="docutils literal notranslate"><span class="pre">queryStats</span></code> section, statistics about dynamic filters collected
by the coordinator can be found in the <code class="docutils literal notranslate"><span class="pre">dynamicFiltersStats</span></code> structure.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>"dynamicFiltersStats" : {
      "dynamicFilterDomainStats" : [ {
        "dynamicFilterId" : "df_370",
        "simplifiedDomain" : "[ SortedRangeSet[type=bigint, ranges=3, {[2451546], ..., [2451905]}] ]",
        "collectionDuration" : "2.34s"
      } ],
      "lazyDynamicFilters" : 1,
      "replicatedDynamicFilters" : 1,
      "totalDynamicFilters" : 1,
      "dynamicFiltersCompleted" : 1
}
</pre></div>
</div>
<p>Push down of dynamic filters into a table scan on the worker nodes can be
verified by looking at the operator statistics for that table scan.
<code class="docutils literal notranslate"><span class="pre">dynamicFilterSplitsProcessed</span></code> records the number of splits
processed after a dynamic filter is pushed down to the table scan.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>"operatorType" : "ScanFilterAndProjectOperator",
"totalDrivers" : 1,
"addInputCalls" : 762,
"addInputWall" : "0.00ns",
"addInputCpu" : "0.00ns",
"physicalInputDataSize" : "0B",
"physicalInputPositions" : 28800991,
"inputPositions" : 28800991,
"dynamicFilterSplitsProcessed" : 1,
</pre></div>
</div>
<p>Dynamic filters are reported as a part of the
<a class="reference internal" href="../sql/explain-analyze.html"><span class="doc">EXPLAIN ANALYZE plan</span></a> in the statistics for
<code class="docutils literal notranslate"><span class="pre">ScanFilterProject</span></code> nodes.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>...

 └─ InnerJoin[("ss_sold_date_sk" = "d_date_sk")][$hashvalue, $hashvalue_4]
    │   Layout: []
    │   Estimates: {rows: 11859 (0B), cpu: 8.84M, memory: 3.19kB, network: 3.19kB}
    │   CPU: 78.00ms (30.00%), Scheduled: 295.00ms (47.05%), Output: 296 rows (0B)
    │   Left (probe) Input avg.: 120527.00 rows, Input std.dev.: 0.00%
    │   Right (build) Input avg.: 0.19 rows, Input std.dev.: 208.17%
    │   Distribution: REPLICATED
    │   dynamicFilterAssignments = {d_date_sk -&gt; #df_370}
    ├─ ScanFilterProject[table = hive:default:store_sales, grouped = false, filterPredicate = true, dynamicFilters = {"ss_sold_date_sk" = #df_370}]
    │      Layout: [ss_sold_date_sk:bigint, $hashvalue:bigint]
    │      Estimates: {rows: 120527 (2.03MB), cpu: 1017.64k, memory: 0B, network: 0B}/{rows: 120527 (2.03MB), cpu: 1.99M, memory: 0B, network: 0B}/{rows: 120527 (2.03MB), cpu: 4.02M, memory: 0B, network: 0B}
    │      CPU: 49.00ms (18.85%), Scheduled: 123.00ms (19.62%), Output: 120527 rows (2.07MB)
    │      Input avg.: 120527.00 rows, Input std.dev.: 0.00%
    │      $hashvalue := combine_hash(bigint '0', COALESCE("$operator$hash_code"("ss_sold_date_sk"), 0))
    │      ss_sold_date_sk := ss_sold_date_sk:bigint:REGULAR
    │      Input: 120527 rows (1.03MB), Filtered: 0.00%
    │      Dynamic filters:
    │          - df_370, [ SortedRangeSet[type=bigint, ranges=3, {[2451546], ..., [2451905]}] ], collection time=2.34s
    |
...
</pre></div>
</div>
</section>
<section id="dynamic-filter-collection-thresholds">
<h2 id="dynamic-filter-collection-thresholds">Dynamic filter collection thresholds<a class="headerlink" href="dynamic-filtering.html#dynamic-filter-collection-thresholds" title="Link to this heading">#</a></h2>
<p>In order for dynamic filtering to work, the smaller dimension table
needs to be chosen as a join’s build side. The cost-based optimizer can automatically
do this using table statistics provided by connectors. Therefore, it is recommended
to keep <a class="reference internal" href="../optimizer/statistics.html"><span class="doc">table statistics</span></a> up to date and rely on the
CBO to correctly choose the smaller table on the build side of join.</p>
<p>Collection of values of the join key columns from the build side for
dynamic filtering may incur additional CPU overhead during query execution.
Therefore, to limit the overhead of collecting dynamic filters
to the cases where the join operator is likely to be selective,
Trino defines thresholds on the size of dynamic filters collected from build side tasks.
Collection of dynamic filters for joins with large build sides can be enabled
using the <code class="docutils literal notranslate"><span class="pre">enable-large-dynamic-filters</span></code> configuration property or the
<code class="docutils literal notranslate"><span class="pre">enable_large_dynamic_filters</span></code> session property.</p>
<p>When large dynamic filters are enabled, limits on the size of dynamic filters can
be configured using the configuration properties
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large.max-distinct-values-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large.max-size-per-driver</span></code> ,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large.range-row-limit-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large-partitioned.max-distinct-values-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large-partitioned.max-size-per-driver</span></code> and
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large-partitioned.range-row-limit-per-driver</span></code>.</p>
<p>Similarly, limits for dynamic filters when <code class="docutils literal notranslate"><span class="pre">enable-large-dynamic-filters</span></code>
is not enabled can be configured using configuration properties like
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small.max-distinct-values-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small.max-size-per-driver</span></code> ,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small.range-row-limit-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small-partitioned.max-distinct-values-per-driver</span></code>,
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small-partitioned.max-size-per-driver</span></code> and
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small-partitioned.range-row-limit-per-driver</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large.*</span></code> and <code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small.*</span></code> limits are applied
when dynamic filters are collected before build side is partitioned on join
keys (when broadcast join is chosen or when fault-tolerant execution is enabled). The
<code class="docutils literal notranslate"><span class="pre">dynamic-filtering.large-partitioned.*</span></code> and <code class="docutils literal notranslate"><span class="pre">dynamic-filtering.small-partitioned.*</span></code>
limits are applied when dynamic filters are collected after build side is partitioned
on join keys (when partitioned join is chosen and fault-tolerant execution is disabled).</p>
<p>The properties based on <code class="docutils literal notranslate"><span class="pre">max-distinct-values-per-driver</span></code> and <code class="docutils literal notranslate"><span class="pre">max-size-per-driver</span></code>
define thresholds for the size up to which dynamic filters are collected in a
distinct values data structure. When the build side exceeds these thresholds,
Trino switches to collecting min and max values per column to reduce overhead.
This min-max filter has much lower granularity than the distinct values filter.
However, it may still be beneficial in filtering some data from the probe side,
especially when a range of values is selected from the build side of the join.
The limits for min-max filters collection are defined by the properties
based on <code class="docutils literal notranslate"><span class="pre">range-row-limit-per-driver</span></code>.</p>
</section>
<section id="dimension-tables-layout">
<h2 id="dimension-tables-layout">Dimension tables layout<a class="headerlink" href="dynamic-filtering.html#dimension-tables-layout" title="Link to this heading">#</a></h2>
<p>Dynamic filtering works best for dimension tables where
table keys are correlated with columns.</p>
<p>For example, a date dimension key column should be correlated with a date column,
so the table keys monotonically increase with date values.
An address dimension key can be composed of other columns such as
<code class="docutils literal notranslate"><span class="pre">COUNTRY-STATE-ZIP-ADDRESS_ID</span></code> with an example value of <code class="docutils literal notranslate"><span class="pre">US-NY-10001-1234</span></code>.
This usage allows dynamic filtering to succeed even with a large number
of selected rows from the dimension table.</p>
</section>
<section id="limitations">
<h2 id="limitations">Limitations<a class="headerlink" href="dynamic-filtering.html#limitations" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Min-max dynamic filter collection is not supported for <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">REAL</span></code> and unorderable data types.</p></li>
<li><p>Dynamic filtering is not supported for <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> and <code class="docutils literal notranslate"><span class="pre">REAL</span></code> data types when using <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code> predicate.</p></li>
<li><p>Dynamic filtering is supported when the join key contains a cast from the build key type to the
probe key type. Dynamic filtering is also supported in limited scenarios when there is an implicit
cast from the probe key type to the build key type. For example, dynamic filtering is supported when
the build side key is of <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> type and the probe side key is of <code class="docutils literal notranslate"><span class="pre">REAL</span></code> or <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> type.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="dist-sort.html" title="Distributed sort"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Distributed sort </span>
              </div>
            </a>
          
          
            <a href="graceful-shutdown.html" title="Graceful shutdown"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Graceful shutdown </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>