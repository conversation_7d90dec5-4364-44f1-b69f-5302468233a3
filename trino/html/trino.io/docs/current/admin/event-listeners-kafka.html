<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Kafka event listener &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="event-listeners-kafka.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="MySQL event listener" href="event-listeners-mysql.html" />
    <link rel="prev" title="HTTP event listener" href="event-listeners-http.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="event-listeners-kafka.html#admin/event-listeners-kafka" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Kafka event listener </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Kafka event listener </label>
    
      <a href="event-listeners-kafka.html#" class="md-nav__link md-nav__link--active">Kafka event listener</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="event-listeners-kafka.html#rationale" class="md-nav__link">Rationale</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-kafka.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-kafka.html#configuration" class="md-nav__link">Configuration</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="event-listeners-kafka.html#rationale" class="md-nav__link">Rationale</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-kafka.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="event-listeners-kafka.html#configuration" class="md-nav__link">Configuration</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="kafka-event-listener">
<h1 id="admin-event-listeners-kafka--page-root">Kafka event listener<a class="headerlink" href="event-listeners-kafka.html#admin-event-listeners-kafka--page-root" title="Link to this heading">#</a></h1>
<p>The Kafka event listener plugin allows streaming of query events to an external
Kafka-compatible system. The query history in the Kafka topic can then be
accessed directly in Kafka, via Trino in a catalog using the <a class="reference internal" href="../connector/kafka.html"><span class="doc std std-doc">Kafka
connector</span></a> or many downstream systems processing and storing
the data.</p>
<section id="rationale">
<h2 id="rationale">Rationale<a class="headerlink" href="event-listeners-kafka.html#rationale" title="Link to this heading">#</a></h2>
<p>This event listener is a first step to store the query history of your Trino
cluster. The query events can provide CPU and memory usage metrics, what data is
being accessed with resolution down to specific columns, and metadata about the
query processing.</p>
<p>Running the capture system separate from Trino reduces the performance impact
and avoids downtime for non-client-facing changes.</p>
</section>
<section id="requirements">
<span id="kafka-event-listener-requirements"></span><h2 id="requirements">Requirements<a class="headerlink" href="event-listeners-kafka.html#requirements" title="Link to this heading">#</a></h2>
<p>You need to perform the following steps:</p>
<ul class="simple">
<li><p>Provide a Kafka service that is network-accessible to Trino.</p></li>
<li><p>Configure <code class="docutils literal notranslate"><span class="pre">kafka-event-listener.broker-endpoints</span></code> in the event listener
properties file with the URI of the service</p></li>
<li><p>Configure what events to send as detailed
in <a class="reference internal" href="event-listeners-kafka.html#kafka-event-listener-configuration"><span class="std std-ref">Configuration</span></a></p></li>
</ul>
</section>
<section id="configuration">
<span id="kafka-event-listener-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="event-listeners-kafka.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Kafka event listener, create an event listener properties
file in <code class="docutils literal notranslate"><span class="pre">etc</span></code> named <code class="docutils literal notranslate"><span class="pre">kafka-event-listener.properties</span></code> with the following
contents as an example of a minimal required configuration:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">event-listener.name</span><span class="o">=</span><span class="s">kafka</span>
<span class="na">kafka-event-listener.broker-endpoints</span><span class="o">=</span><span class="s">kafka.example.com:9093</span>
<span class="na">kafka-event-listener.created-event.topic</span><span class="o">=</span><span class="s">query_create</span>
<span class="na">kafka-event-listener.completed-event.topic</span><span class="o">=</span><span class="s">query_complete</span>
<span class="na">kafka-event-listener.client-id</span><span class="o">=</span><span class="s">trino-example</span>
</pre></div>
</div>
<p>Add <code class="docutils literal notranslate"><span class="pre">etc/kafka-event-listener.properties</span></code> to <code class="docutils literal notranslate"><span class="pre">event-listener.config-files</span></code>
in <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">event-listener.config-files</span><span class="o">=</span><span class="s">etc/kafka-event-listener.properties,...</span>
</pre></div>
</div>
<p>In some cases, such as when using specialized authentication methods, it is
necessary to specify additional Kafka client properties in order to access
your Kafka cluster. To do so, add the <code class="docutils literal notranslate"><span class="pre">kafka-event-listener.config.resources</span></code>
property to reference your Kafka config files. Note that configs can be
overwritten if defined explicitly in <code class="docutils literal notranslate"><span class="pre">kafka-event-listener.properties</span></code>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">event-listener.name</span><span class="o">=</span><span class="s">kafka</span>
<span class="na">kafka-event-listener.broker-endpoints</span><span class="o">=</span><span class="s">kafka.example.com:9093</span>
<span class="na">kafka-event-listener.created-event.topic</span><span class="o">=</span><span class="s">query_create</span>
<span class="na">kafka-event-listener.completed-event.topic</span><span class="o">=</span><span class="s">query_complete</span>
<span class="na">kafka-event-listener.client-id</span><span class="o">=</span><span class="s">trino-example</span>
<span class="na">kafka.config.resources</span><span class="o">=</span><span class="s">/etc/kafka-configuration.properties</span>
</pre></div>
</div>
<p>The contents of <code class="docutils literal notranslate"><span class="pre">/etc/kafka-configuration.properties</span></code> can for example be:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">sasl.mechanism</span><span class="o">=</span><span class="s">SCRAM-SHA-512</span>
<span class="na">security.protocol</span><span class="o">=</span><span class="s">SASL_SSL</span>
<span class="na">sasl.jaas.config</span><span class="o">=</span><span class="s">org.apache.kafka.common.security.scram.ScramLoginModule required </span><span class="se">\</span>
<span class="w">  </span><span class="s">username="kafkaclient1" </span><span class="se">\</span>
<span class="w">  </span><span class="s">password="kafkaclient1-secret";</span>
</pre></div>
</div>
<p>Use the following properties for further configuration.</p>
<table id="id1">
<caption><span class="caption-text">Kafka event listener configuration properties</span><a class="headerlink" href="event-listeners-kafka.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 40%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.broker-endpoints</span></code></p></td>
<td><p>Comma-separated list of Kafka broker endpoints with URL and port, for
example <code class="docutils literal notranslate"><span class="pre">kafka-1.example.com:9093,kafka-2.example.com:9093</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.anonymization.enabled</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">Boolean</span></a> switch to enable anonymization of the event
data in Trino before it is sent to Kafka.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.client-id</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">String identifier</span></a> for the Trino cluster to allow
distinction in Kafka, if multiple Trino clusters send events to the same
Kafka system.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.publish-created-event</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">Boolean</span></a> switch to control publishing of query creation
events.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.created-event.topic</span></code></p></td>
<td><p>Name of the Kafka topic for the query creation event data.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.publish-split-completed-event</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">Boolean</span></a> switch to control publishing of
<a class="reference internal" href="../overview/concepts.html#trino-concept-splits"><span class="std std-ref">split</span></a> completion events.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.split-completed-event.topic</span></code></p></td>
<td><p>Name of the Kafka topic for the split completion event data.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.publish-completed-event</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">Boolean</span></a> switch to control publishing of query
completion events.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.completed-event.topic</span></code></p></td>
<td><p>Name of the Kafka topic for the query completion event data.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.excluded-fields</span></code></p></td>
<td><p>Comma-separated list of field names to exclude from the Kafka event, for
example <code class="docutils literal notranslate"><span class="pre">payload,user</span></code>. Values are replaced with null.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.request-timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> to complete a Kafka request. Minimum
value of <code class="docutils literal notranslate"><span class="pre">1ms</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.terminate-on-initialization-failure</span></code></p></td>
<td><p>Kafka publisher initialization can fail due to network issues reaching the
Kafka brokers. This <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a> switch controls whether to
throw an exception in such cases.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.env-var-prefix</span></code></p></td>
<td><p>When set, Kafka events are sent with additional metadata populated from
environment variables. For example, if the value is <code class="docutils literal notranslate"><span class="pre">TRINO_INSIGHTS_</span></code> and an
environment variable on the cluster is set at
<code class="docutils literal notranslate"><span class="pre">TRINO_INSIGHTS_CLUSTER_ID=foo</span></code>, then the Kafka payload metadata contains
<code class="docutils literal notranslate"><span class="pre">CLUSTER_ID=foo</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka-event-listener.config.resources</span></code></p></td>
<td><p>A comma-separated list of Kafka client configuration files. These files
must exist on the machines running Trino. Only specify this if absolutely
necessary to access Kafka. Example: <code class="docutils literal notranslate"><span class="pre">/etc/kafka-configuration.properties</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="event-listeners-http.html" title="HTTP event listener"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> HTTP event listener </span>
              </div>
            </a>
          
          
            <a href="event-listeners-mysql.html" title="MySQL event listener"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> MySQL event listener </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>