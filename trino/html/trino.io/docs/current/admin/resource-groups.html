<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Resource groups &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="resource-groups.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Session property managers" href="session-property-managers.html" />
    <link rel="prev" title="Spill to disk" href="spill.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="resource-groups.html#admin/resource-groups" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Resource groups </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Resource groups </label>
    
      <a href="resource-groups.html#" class="md-nav__link md-nav__link--active">Resource groups</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="resource-groups.html#file-resource-group-manager" class="md-nav__link">File resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#database-resource-group-manager" class="md-nav__link">Database resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#resource-group-properties" class="md-nav__link">Resource group properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="resource-groups.html#scheduling-weight-example" class="md-nav__link">Scheduling weight example</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#selector-rules" class="md-nav__link">Selector rules</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#global-properties" class="md-nav__link">Global properties</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#providing-selector-properties" class="md-nav__link">Providing selector properties</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#example" class="md-nav__link">Example</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="resource-groups.html#id1" class="md-nav__link">File resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#id2" class="md-nav__link">Database resource group manager</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="resource-groups.html#file-resource-group-manager" class="md-nav__link">File resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#database-resource-group-manager" class="md-nav__link">Database resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#resource-group-properties" class="md-nav__link">Resource group properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="resource-groups.html#scheduling-weight-example" class="md-nav__link">Scheduling weight example</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#selector-rules" class="md-nav__link">Selector rules</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#global-properties" class="md-nav__link">Global properties</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#providing-selector-properties" class="md-nav__link">Providing selector properties</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#example" class="md-nav__link">Example</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="resource-groups.html#id1" class="md-nav__link">File resource group manager</a>
        </li>
        <li class="md-nav__item"><a href="resource-groups.html#id2" class="md-nav__link">Database resource group manager</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="resource-groups">
<h1 id="admin-resource-groups--page-root">Resource groups<a class="headerlink" href="resource-groups.html#admin-resource-groups--page-root" title="Link to this heading">#</a></h1>
<p>Resource groups place limits on resource usage, and can enforce queueing policies on
queries that run within them, or divide their resources among sub-groups. A query
belongs to a single resource group, and consumes resources from that group (and its ancestors).
Except for the limit on queued queries, when a resource group runs out of a resource
it does not cause running queries to fail; instead new queries become queued.
A resource group may have sub-groups or may accept queries, but may not do both.</p>
<p>The resource groups and associated selection rules are configured by a manager, which is pluggable.</p>
<p>You can use a file-based or a database-based resource group manager:</p>
<ul class="simple">
<li><p>Add a file <code class="docutils literal notranslate"><span class="pre">etc/resource-groups.properties</span></code></p></li>
<li><p>Set the <code class="docutils literal notranslate"><span class="pre">resource-groups.configuration-manager</span></code> property to <code class="docutils literal notranslate"><span class="pre">file</span></code> or <code class="docutils literal notranslate"><span class="pre">db</span></code></p></li>
<li><p>Add further configuration properties for the desired manager.</p></li>
</ul>
<section id="file-resource-group-manager">
<h2 id="file-resource-group-manager">File resource group manager<a class="headerlink" href="resource-groups.html#file-resource-group-manager" title="Link to this heading">#</a></h2>
<p>The file resource group manager reads a JSON configuration file, specified with
<code class="docutils literal notranslate"><span class="pre">resource-groups.config-file</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>resource-groups.configuration-manager=file
resource-groups.config-file=etc/resource-groups.json
</pre></div>
</div>
<p>The path to the JSON file can be an absolute path, or a path relative to the Trino
data directory. The JSON file only needs to be present on the coordinator.</p>
</section>
<section id="database-resource-group-manager">
<span id="db-resource-group-manager"></span><h2 id="database-resource-group-manager">Database resource group manager<a class="headerlink" href="resource-groups.html#database-resource-group-manager" title="Link to this heading">#</a></h2>
<p>The database resource group manager loads the configuration from a relational database. The
supported databases are MySQL, PostgreSQL, and Oracle.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>resource-groups.configuration-manager=db
resource-groups.config-db-url=*******************************************
resource-groups.config-db-user=username
resource-groups.config-db-password=password
</pre></div>
</div>
<p>The resource group configuration must be populated through tables
<code class="docutils literal notranslate"><span class="pre">resource_groups_global_properties</span></code>, <code class="docutils literal notranslate"><span class="pre">resource_groups</span></code>, and
<code class="docutils literal notranslate"><span class="pre">selectors</span></code>. If any of the tables do not exist when Trino starts, they
will be created automatically.</p>
<p>The rules in the <code class="docutils literal notranslate"><span class="pre">selectors</span></code> table are processed in descending order of the
values in the <code class="docutils literal notranslate"><span class="pre">priority</span></code> field.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">resource_groups</span></code> table also contains an <code class="docutils literal notranslate"><span class="pre">environment</span></code> field which is
matched with the value contained in the <code class="docutils literal notranslate"><span class="pre">node.environment</span></code> property in
<a class="reference internal" href="../installation/deployment.html#node-properties"><span class="std std-ref">Node properties</span></a>. This allows the resource group configuration for different
Trino clusters to be stored in the same database if required.</p>
<p>The configuration is reloaded from the database every second, and the changes
are reflected automatically for incoming queries.</p>
<table id="id3">
<caption><span class="caption-text">Database resource group manager properties</span><a class="headerlink" href="resource-groups.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 50%"/>
<col style="width: 10%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.config-db-url</span></code></p></td>
<td><p>Database URL to load configuration from.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">none</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.config-db-user</span></code></p></td>
<td><p>Database user to connect with.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">none</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.config-db-password</span></code></p></td>
<td><p>Password for database user to connect with.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">none</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.max-refresh-interval</span></code></p></td>
<td><p>The maximum time period for which the cluster will continue to accept
queries after refresh failures, causing configuration to become stale.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1h</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.refresh-interval</span></code></p></td>
<td><p>How often the cluster reloads from the database</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">resource-groups.exact-match-selector-enabled</span></code></p></td>
<td><p>Setting this flag enables usage of an additional
<code class="docutils literal notranslate"><span class="pre">exact_match_source_selectors</span></code> table to configure resource group selection
rules defined exact name based matches for source, environment and query
type. By default, the rules are only loaded from the <code class="docutils literal notranslate"><span class="pre">selectors</span></code> table, with
a regex-based filter for <code class="docutils literal notranslate"><span class="pre">source</span></code>, among other filters.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="resource-group-properties">
<h2 id="resource-group-properties">Resource group properties<a class="headerlink" href="resource-groups.html#resource-group-properties" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> (required): name of the group. May be a template (see below).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">maxQueued</span></code> (required): maximum number of queued queries. Once this limit is reached
new queries are rejected.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">softConcurrencyLimit</span></code> (optional): number of concurrently running queries after which
new queries will only run if all peer resource groups below their soft limits are ineligible
or if all eligible peers are above soft limits.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hardConcurrencyLimit</span></code> (required): maximum number of running queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">softMemoryLimit</span></code> (optional): maximum amount of distributed memory this
group may use, before new queries become queued. May be specified as
an absolute value (i.e. <code class="docutils literal notranslate"><span class="pre">1GB</span></code>) or as a percentage (i.e. <code class="docutils literal notranslate"><span class="pre">10%</span></code>) of the cluster’s memory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">softCpuLimit</span></code> (optional): maximum amount of CPU time this
group may use in a period (see <code class="docutils literal notranslate"><span class="pre">cpuQuotaPeriod</span></code>), before a penalty is applied to
the maximum number of running queries. <code class="docutils literal notranslate"><span class="pre">hardCpuLimit</span></code> must also be specified.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hardCpuLimit</span></code> (optional): maximum amount of CPU time this
group may use in a period.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schedulingPolicy</span></code> (optional): specifies how queued queries are selected to run,
and how sub-groups become eligible to start their queries. May be one of three values:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">fair</span></code> (default): queued queries are processed first-in-first-out, and sub-groups
must take turns starting new queries, if they have any queued.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">weighted_fair</span></code>: sub-groups are selected based on their <code class="docutils literal notranslate"><span class="pre">schedulingWeight</span></code> and the number of
queries they are already running concurrently. The expected share of running queries for a
sub-group is computed based on the weights for all currently eligible sub-groups. The sub-group
with the least concurrency relative to its share is selected to start the next query.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">weighted</span></code>: queued queries are selected stochastically in proportion to their priority,
specified via the <code class="docutils literal notranslate"><span class="pre">query_priority</span></code> <a class="reference internal" href="../sql/set-session.html"><span class="doc">session property</span></a>. Sub groups are selected
to start new queries in proportion to their <code class="docutils literal notranslate"><span class="pre">schedulingWeight</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">query_priority</span></code>: all sub-groups must also be configured with <code class="docutils literal notranslate"><span class="pre">query_priority</span></code>.
Queued queries are selected strictly according to their priority.</p></li>
</ul>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">schedulingWeight</span></code> (optional): weight of this sub-group used in <code class="docutils literal notranslate"><span class="pre">weighted</span></code>
and the <code class="docutils literal notranslate"><span class="pre">weighted_fair</span></code> scheduling policy. Defaults to <code class="docutils literal notranslate"><span class="pre">1</span></code>. See
<a class="reference internal" href="resource-groups.html#scheduleweight-example"><span class="std std-ref">Scheduling weight example</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">jmxExport</span></code> (optional): If true, group statistics are exported to JMX for monitoring.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">subGroups</span></code> (optional): list of sub-groups.</p></li>
</ul>
<section id="scheduling-weight-example">
<span id="scheduleweight-example"></span><h3 id="scheduling-weight-example">Scheduling weight example<a class="headerlink" href="resource-groups.html#scheduling-weight-example" title="Link to this heading">#</a></h3>
<p>Schedule weighting is a method of assigning a priority to a resource. Sub-groups
with a higher scheduling weight are given higher priority. For example, to
ensure timely execution of scheduled pipelines queries, weight them higher than
adhoc queries.</p>
<p>In the following example, pipeline queries are weighted with a value of <code class="docutils literal notranslate"><span class="pre">350</span></code>,
which is higher than the adhoc queries that have a scheduling weight of <code class="docutils literal notranslate"><span class="pre">150</span></code>.
This means that approximately 70% (350 out of 500 queries) of your queries come
from the pipeline sub-group, and 30% (150 out of 500 queries) come from the adhoc
sub-group in a given timeframe. Alternatively, if you set each sub-group value to
<code class="docutils literal notranslate"><span class="pre">1</span></code>, the weight of the queries for the pipeline and adhoc sub-groups are split
evenly and each receive 50% of the queries in a given timeframe.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
  {
    "name": "pipeline",
    "schedulingWeight": 350,
  },
  {
    "name": "adhoc",
    "schedulingWeight": 150
  }
}
</pre></div>
</div>
</section>
</section>
<section id="selector-rules">
<h2 id="selector-rules">Selector rules<a class="headerlink" href="resource-groups.html#selector-rules" title="Link to this heading">#</a></h2>
<p>The selector rules for pattern matching use Java’s regular expression
capabilities. Java implements regular expressions through the <code class="docutils literal notranslate"><span class="pre">java.util.regex</span></code>
package. For more information, see the <a class="reference external" href="https://docs.oracle.com/en/java/javase/24/docs/api/java.base/java/util/regex/Pattern.html">Java
documentation</a>.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): Java regex to match against username.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">originalUser</span></code> (optional): Java regex to match against the <em>original</em> username,
i.e. before any changes to the session user. For example, if user “foo” runs
<code class="docutils literal notranslate"><span class="pre">SET</span> <span class="pre">SESSION</span> <span class="pre">AUTHORIZATION</span> <span class="pre">'bar'</span></code>, <code class="docutils literal notranslate"><span class="pre">originalUser</span></code> is “foo”, while <code class="docutils literal notranslate"><span class="pre">user</span></code> is “bar”.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">authenticatedUser</span></code> (optional): Java regex to match against the <em>authenticated</em> username,
which will always refer to the user that authenticated with the system, regardless of any
changes made to the session user.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">userGroup</span></code> (optional): Java regex to match against every user group the user belongs to.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">source</span></code> (optional): Java regex to match against source string.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">queryType</span></code> (optional): string to match against the type of the query submitted:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">SELECT</span></code>: <a class="reference internal" href="../sql/select.html"><span class="doc std std-doc">SELECT</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code>: <a class="reference internal" href="../sql/explain.html"><span class="doc std std-doc">EXPLAIN</span></a> queries, but not <a class="reference internal" href="../sql/explain-analyze.html"><span class="doc std std-doc">EXPLAIN
ANALYZE</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DESCRIBE</span></code>: <a class="reference internal" href="../sql/describe.html"><span class="doc std std-doc">DESCRIBE</span></a>, <a class="reference internal" href="../sql/describe-input.html"><span class="doc std std-doc">DESCRIBE
INPUT</span></a>, <a class="reference internal" href="../sql/describe-output.html"><span class="doc std std-doc">DESCRIBE OUTPUT</span></a>, and
<code class="docutils literal notranslate"><span class="pre">SHOW</span></code> queries such as <a class="reference internal" href="../sql/show-catalogs.html"><span class="doc std std-doc">SHOW CATALOGS</span></a>, <a class="reference internal" href="../sql/show-schemas.html"><span class="doc std std-doc">SHOW
SCHEMAS</span></a>, and <a class="reference internal" href="../sql/show-tables.html"><span class="doc std std-doc">SHOW TABLES</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INSERT</span></code>: <a class="reference internal" href="../sql/insert.html"><span class="doc std std-doc">INSERT</span></a>, <a class="reference internal" href="../sql/create-table-as.html"><span class="doc std std-doc">CREATE TABLE AS</span></a>,
and <a class="reference internal" href="../sql/refresh-materialized-view.html"><span class="doc std std-doc">REFRESH MATERIALIZED VIEW</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>: <a class="reference internal" href="../sql/update.html"><span class="doc std std-doc">UPDATE</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MERGE</span></code>: <a class="reference internal" href="../sql/merge.html"><span class="doc std std-doc">MERGE</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DELETE</span></code>: <a class="reference internal" href="../sql/delete.html"><span class="doc std std-doc">DELETE</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code>: <a class="reference internal" href="../sql/analyze.html"><span class="doc std std-doc">ANALYZE</span></a> queries.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DATA_DEFINITION</span></code>: Queries that affect the data definition. These include
<code class="docutils literal notranslate"><span class="pre">CREATE</span></code>, <code class="docutils literal notranslate"><span class="pre">ALTER</span></code>, and <code class="docutils literal notranslate"><span class="pre">DROP</span></code> statements for schemas, tables, views, and
materialized views, as well as statements that manage prepared statements,
privileges, sessions, and transactions.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ALTER_TABLE_EXECUTE</span></code>: Queries that execute table procedures with <a class="reference internal" href="../sql/alter-table.html#alter-table-execute"><span class="std std-ref">ALTER
TABLE EXECUTE</span></a>.</p></li>
</ul>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">clientTags</span></code> (optional): list of tags. To match, every tag in this list must be in the list of
client-provided tags associated with the query.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (required): the group these queries will run in.</p></li>
</ul>
<p>All rules within a single selector are combined using a logical <code class="docutils literal notranslate"><span class="pre">AND</span></code>. Therefore
all rules must match for a selector to be applied.</p>
<p>Selectors are processed sequentially and the first one that matches will be used.</p>
</section>
<section id="global-properties">
<h2 id="global-properties">Global properties<a class="headerlink" href="resource-groups.html#global-properties" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cpuQuotaPeriod</span></code> (optional): the period in which cpu quotas are enforced.</p></li>
</ul>
</section>
<section id="providing-selector-properties">
<h2 id="providing-selector-properties">Providing selector properties<a class="headerlink" href="resource-groups.html#providing-selector-properties" title="Link to this heading">#</a></h2>
<p>The source name can be set as follows:</p>
<ul class="simple">
<li><p>CLI: use the <code class="docutils literal notranslate"><span class="pre">--source</span></code> option.</p></li>
<li><p>JDBC driver when used in client apps: add the <code class="docutils literal notranslate"><span class="pre">source</span></code> property to the
connection configuration and set the value when using a Java application that
uses the JDBC Driver.</p></li>
<li><p>JDBC driver used with Java programs: add a property with the key <code class="docutils literal notranslate"><span class="pre">source</span></code>
and the value on the <code class="docutils literal notranslate"><span class="pre">Connection</span></code> instance as shown in <a class="reference internal" href="../client/jdbc.html#jdbc-java-connection"><span class="std std-ref">the example</span></a>.</p></li>
</ul>
<p>Client tags can be set as follows:</p>
<ul class="simple">
<li><p>CLI: use the <code class="docutils literal notranslate"><span class="pre">--client-tags</span></code> option.</p></li>
<li><p>JDBC driver when used in client apps: add the <code class="docutils literal notranslate"><span class="pre">clientTags</span></code> property to the
connection configuration and set the value when using a Java application that
uses the JDBC Driver.</p></li>
<li><p>JDBC driver used with Java programs: add a property with the key
<code class="docutils literal notranslate"><span class="pre">clientTags</span></code> and the value on the <code class="docutils literal notranslate"><span class="pre">Connection</span></code> instance as shown in
<a class="reference internal" href="../client/jdbc.html#jdbc-parameter-reference"><span class="std std-ref">the example</span></a>.</p></li>
</ul>
</section>
<section id="example">
<h2 id="example">Example<a class="headerlink" href="resource-groups.html#example" title="Link to this heading">#</a></h2>
<p>In the example configuration below, there are several resource groups, some of which are templates.
Templates allow administrators to construct resource group trees dynamically. For example, in
the <code class="docutils literal notranslate"><span class="pre">pipeline_${USER}</span></code> group, <code class="docutils literal notranslate"><span class="pre">${USER}</span></code> is expanded to the name of the user that submitted
the query. <code class="docutils literal notranslate"><span class="pre">${SOURCE}</span></code> is also supported, which is expanded to the source that submitted the
query. You may also use custom named variables in the regular expressions for <code class="docutils literal notranslate"><span class="pre">user</span></code>, <code class="docutils literal notranslate"><span class="pre">source</span></code>,
<code class="docutils literal notranslate"><span class="pre">originalUser</span></code>, and <code class="docutils literal notranslate"><span class="pre">authenticatedUser</span></code>.</p>
<p>There are six selectors, that define which queries run in which resource group:</p>
<ul class="simple">
<li><p>The first selector matches queries from <code class="docutils literal notranslate"><span class="pre">bob</span></code> and places them in the admin group.</p></li>
<li><p>The next selector matches queries with an <em>original</em> user of <code class="docutils literal notranslate"><span class="pre">bob</span></code>
and places them in the admin group.</p></li>
<li><p>The next selector matches queries with an <em>authenticated</em> user of <code class="docutils literal notranslate"><span class="pre">bob</span></code>
and places them in the admin group.</p></li>
<li><p>The next selector matches queries from <code class="docutils literal notranslate"><span class="pre">admin</span></code> user group and places them in the admin group.</p></li>
<li><p>The next selector matches all data definition (DDL) queries from a source name that includes <code class="docutils literal notranslate"><span class="pre">pipeline</span></code>
and places them in the <code class="docutils literal notranslate"><span class="pre">global.data_definition</span></code> group. This could help reduce queue times for this
class of queries, since they are expected to be fast.</p></li>
<li><p>The next selector matches queries from a source name that includes <code class="docutils literal notranslate"><span class="pre">pipeline</span></code>, and places them in a
dynamically-created per-user pipeline group under the <code class="docutils literal notranslate"><span class="pre">global.pipeline</span></code> group.</p></li>
<li><p>The next selector matches queries that come from BI tools which have a source matching the regular
expression <code class="docutils literal notranslate"><span class="pre">jdbc#(?&lt;toolname&gt;.*)</span></code> and have client provided tags that are a superset of <code class="docutils literal notranslate"><span class="pre">hipri</span></code>.
These are placed in a dynamically-created sub-group under the <code class="docutils literal notranslate"><span class="pre">global.adhoc</span></code> group.
The dynamic sub-groups are created based on the values of named variables <code class="docutils literal notranslate"><span class="pre">toolname</span></code> and <code class="docutils literal notranslate"><span class="pre">user</span></code>.
The values are derived from the source regular expression and the query user respectively.
Consider a query with a source <code class="docutils literal notranslate"><span class="pre">jdbc#powerfulbi</span></code>, user <code class="docutils literal notranslate"><span class="pre">kayla</span></code>, and client tags <code class="docutils literal notranslate"><span class="pre">hipri</span></code> and <code class="docutils literal notranslate"><span class="pre">fast</span></code>.
This query is routed to the <code class="docutils literal notranslate"><span class="pre">global.adhoc.bi-powerfulbi.kayla</span></code> resource group.</p></li>
<li><p>The last selector is a catch-all, which places all queries that have not yet been matched into a per-user
adhoc group.</p></li>
</ul>
<p>Together, these selectors implement the following policy:</p>
<ul class="simple">
<li><p>The user <code class="docutils literal notranslate"><span class="pre">bob</span></code> and any user belonging to user group <code class="docutils literal notranslate"><span class="pre">admin</span></code> is an admin and can run up to
50 concurrent queries. <code class="docutils literal notranslate"><span class="pre">bob</span></code> will be treated as an admin even if they have changed their session
user to a different user (i.e. via a <code class="docutils literal notranslate"><span class="pre">SET</span> <span class="pre">SESSION</span> <span class="pre">AUTHORIZATION</span></code> statement or the
<code class="docutils literal notranslate"><span class="pre">X-Trino-User</span></code> request header). Queries will be run based on user-provided priority.</p></li>
</ul>
<p>For the remaining users:</p>
<ul class="simple">
<li><p>No more than 100 total queries may run concurrently.</p></li>
<li><p>Up to 5 concurrent DDL queries with a source <code class="docutils literal notranslate"><span class="pre">pipeline</span></code> can run. Queries are run in FIFO order.</p></li>
<li><p>Non-DDL queries will run under the <code class="docutils literal notranslate"><span class="pre">global.pipeline</span></code> group, with a total concurrency of 45, and a per-user
concurrency of 5. Queries are run in FIFO order.</p></li>
<li><p>For BI tools, each tool can run up to 10 concurrent queries, and each user can run up to 3. If the total demand
exceeds the limit of 10, the user with the fewest running queries gets the next concurrency slot. This policy
results in fairness when under contention.</p></li>
<li><p>All remaining queries are placed into a per-user group under <code class="docutils literal notranslate"><span class="pre">global.adhoc.other</span></code> that behaves similarly.</p></li>
</ul>
<section id="id1">
<h3 id="id1">File resource group manager<a class="headerlink" href="resource-groups.html#id1" title="Link to this heading">#</a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"rootGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"global"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"80%"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schedulingPolicy"</span><span class="p">:</span><span class="w"> </span><span class="s2">"weighted"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"jmxExport"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"subGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"data_definition"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"schedulingWeight"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"adhoc"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"schedulingWeight"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"subGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">              </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"other"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"schedulingWeight"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"schedulingPolicy"</span><span class="p">:</span><span class="w"> </span><span class="s2">"weighted_fair"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"subGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">                </span><span class="p">{</span>
<span class="w">                  </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"${USER}"</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">              </span><span class="p">]</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">              </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bi-${toolname}"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"schedulingWeight"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"schedulingPolicy"</span><span class="p">:</span><span class="w"> </span><span class="s2">"weighted_fair"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"subGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">                </span><span class="p">{</span>
<span class="w">                  </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"${USER}"</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10%"</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">              </span><span class="p">]</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">]</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pipeline"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"80%"</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"schedulingWeight"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"jmxExport"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">          </span><span class="nt">"subGroups"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">              </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pipeline_${USER}"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"50%"</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">              </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">]</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"softMemoryLimit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"100%"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"hardConcurrencyLimit"</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"maxQueued"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schedulingPolicy"</span><span class="p">:</span><span class="w"> </span><span class="s2">"query_priority"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"jmxExport"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"selectors"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"originalUser"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"authenticatedUser"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"userGroup"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"source"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*pipeline.*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"queryType"</span><span class="p">:</span><span class="w"> </span><span class="s2">"DATA_DEFINITION"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"global.data_definition"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"source"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*pipeline.*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"global.pipeline.pipeline_${USER}"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"source"</span><span class="p">:</span><span class="w"> </span><span class="s2">"jdbc#(?&lt;toolname&gt;.*)"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"clientTags"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"hipri"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"global.adhoc.bi-${toolname}.${USER}"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"global.adhoc.other.${USER}"</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"cpuQuotaPeriod"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1h"</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="id2">
<h3 id="id2">Database resource group manager<a class="headerlink" href="resource-groups.html#id2" title="Link to this heading">#</a></h3>
<p>This example is for a MySQL database.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- global properties</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups_global_properties</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'cpu_quota_period'</span><span class="p">,</span><span class="w"> </span><span class="s1">'1h'</span><span class="p">);</span>

<span class="c1">-- Every row in resource_groups table indicates a resource group.</span>
<span class="c1">-- The enviroment name is 'test_environment', make sure it matches `node.environment` in your cluster.</span>
<span class="c1">-- The parent-child relationship is indicated by the ID in 'parent' column.</span>

<span class="c1">-- create a root group 'global' with NULL parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_policy</span><span class="p">,</span><span class="w"> </span><span class="n">jmx_export</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'global'</span><span class="p">,</span><span class="w"> </span><span class="s1">'80%'</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span><span class="w"> </span><span class="s1">'weighted'</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">);</span>

<span class="c1">-- get ID of 'global' group</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'global'</span><span class="p">;</span><span class="w">  </span><span class="c1">-- 1</span>
<span class="c1">-- create two new groups with 'global' as parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_weight</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'data_definition'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_weight</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'adhoc'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>

<span class="c1">-- get ID of 'adhoc' group</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'adhoc'</span><span class="p">;</span><span class="w">   </span><span class="c1">-- 3</span>
<span class="c1">-- create 'other' group with 'adhoc' as parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_weight</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_policy</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'other'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="s1">'weighted_fair'</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span>

<span class="c1">-- get ID of 'other' group</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'other'</span><span class="p">;</span><span class="w">  </span><span class="c1">-- 4</span>
<span class="c1">-- create '${USER}' group with 'other' as parent.</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'${USER}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">);</span>

<span class="c1">-- create 'bi-${toolname}' group with 'adhoc' as parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_weight</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_policy</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'bi-${toolname}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="s1">'weighted_fair'</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span>

<span class="c1">-- get ID of 'bi-${toolname}' group</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'bi-${toolname}'</span><span class="p">;</span><span class="w">  </span><span class="c1">-- 6</span>
<span class="c1">-- create '${USER}' group with 'bi-${toolname}' as parent. This indicates</span>
<span class="c1">-- nested group 'global.adhoc.bi-${toolname}.${USER}', and will have a</span>
<span class="c1">-- different ID than 'global.adhoc.other.${USER}' created above.</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w">  </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'${USER}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'10%'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">6</span><span class="p">);</span>

<span class="c1">-- create 'pipeline' group with 'global' as parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_weight</span><span class="p">,</span><span class="w"> </span><span class="n">jmx_export</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'pipeline'</span><span class="p">,</span><span class="w"> </span><span class="s1">'80%'</span><span class="p">,</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>

<span class="c1">-- get ID of 'pipeline' group</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'pipeline'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- 8</span>
<span class="c1">-- create 'pipeline_${USER}' group with 'pipeline' as parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w">  </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">parent</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'pipeline_${USER}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'50%'</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="mi">8</span><span class="p">);</span>

<span class="c1">-- create a root group 'admin' with NULL parent</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">soft_memory_limit</span><span class="p">,</span><span class="w"> </span><span class="n">hard_concurrency_limit</span><span class="p">,</span><span class="w"> </span><span class="n">max_queued</span><span class="p">,</span><span class="w"> </span><span class="n">scheduling_policy</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">jmx_export</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'admin'</span><span class="p">,</span><span class="w"> </span><span class="s1">'100%'</span><span class="p">,</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="s1">'query_priority'</span><span class="p">,</span><span class="w"> </span><span class="s1">'test_environment'</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">);</span>


<span class="c1">-- Selectors</span>

<span class="c1">-- use ID of 'admin' resource group for selector</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">user_regex</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">((</span><span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'admin'</span><span class="p">),</span><span class="w"> </span><span class="s1">'bob'</span><span class="p">,</span><span class="w"> </span><span class="mi">6</span><span class="p">);</span>

<span class="c1">-- use ID of 'admin' resource group for selector</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">user_group_regex</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">((</span><span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'admin'</span><span class="p">),</span><span class="w"> </span><span class="s1">'admin'</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">);</span>

<span class="c1">-- use ID of 'global.data_definition' resource group for selector</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">source_regex</span><span class="p">,</span><span class="w"> </span><span class="n">query_type</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">((</span><span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'data_definition'</span><span class="p">),</span><span class="w"> </span><span class="s1">'.*pipeline.*'</span><span class="p">,</span><span class="w"> </span><span class="s1">'DATA_DEFINITION'</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">);</span>

<span class="c1">-- use ID of 'global.pipeline.pipeline_${USER}' resource group for selector</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">source_regex</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">((</span><span class="k">SELECT</span><span class="w"> </span><span class="n">resource_group_id</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'pipeline_${USER}'</span><span class="p">),</span><span class="w"> </span><span class="s1">'.*pipeline.*'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span>

<span class="c1">-- get ID of 'global.adhoc.bi-${toolname}.${USER}' resource group by disambiguating group name using parent ID</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">resource_group_id</span><span class="w"> </span><span class="n">self_id</span><span class="p">,</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">resource_group_id</span><span class="w"> </span><span class="n">parent_id</span><span class="p">,</span><span class="w"> </span><span class="n">concat</span><span class="p">(</span><span class="n">B</span><span class="p">.</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="s1">'.'</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">name</span><span class="p">)</span><span class="w"> </span><span class="n">name_with_parent</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="n">A</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="n">B</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">parent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">resource_group_id</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'${USER}'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'bi-${toolname}'</span><span class="p">;</span>
<span class="c1">--  7 |         6 | bi-${toolname}.${USER}</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">source_regex</span><span class="p">,</span><span class="w"> </span><span class="n">client_tags</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="s1">'jdbc#(?&lt;toolname&gt;.*)'</span><span class="p">,</span><span class="w"> </span><span class="s1">'["hipri"]'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span>

<span class="c1">-- get ID of 'global.adhoc.other.${USER}' resource group for by disambiguating group name using parent ID</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">resource_group_id</span><span class="w"> </span><span class="n">self_id</span><span class="p">,</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">resource_group_id</span><span class="w"> </span><span class="n">parent_id</span><span class="p">,</span><span class="w"> </span><span class="n">concat</span><span class="p">(</span><span class="n">B</span><span class="p">.</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="s1">'.'</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">name</span><span class="p">)</span><span class="w"> </span><span class="n">name_with_parent</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="n">A</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">resource_groups</span><span class="w"> </span><span class="n">B</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">parent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">resource_group_id</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'${USER}'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'other'</span><span class="p">;</span>
<span class="c1">-- |       5 |         4 | other.${USER}    |</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">selectors</span><span class="w"> </span><span class="p">(</span><span class="n">resource_group_id</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="spill.html" title="Spill to disk"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Spill to disk </span>
              </div>
            </a>
          
          
            <a href="session-property-managers.html" title="Session property managers"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Session property managers </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>