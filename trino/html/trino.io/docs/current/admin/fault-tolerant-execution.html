<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Fault-tolerant execution &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="fault-tolerant-execution.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="HTTP event listener" href="event-listeners-http.html" />
    <link rel="prev" title="Graceful shutdown" href="graceful-shutdown.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="fault-tolerant-execution.html#admin/fault-tolerant-execution" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Fault-tolerant execution </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Fault-tolerant execution </label>
    
      <a href="fault-tolerant-execution.html#" class="md-nav__link md-nav__link--active">Fault-tolerant execution</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#retry-policy" class="md-nav__link">Retry policy</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#query" class="md-nav__link">QUERY</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#task" class="md-nav__link">TASK</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#encryption" class="md-nav__link">Encryption</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#advanced-configuration" class="md-nav__link">Advanced configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#retry-limits" class="md-nav__link">Retry limits</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#task-sizing" class="md-nav__link">Task sizing</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#node-allocation" class="md-nav__link">Node allocation</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#other-tuning" class="md-nav__link">Other tuning</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#exchange-manager" class="md-nav__link">Exchange manager</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#id1" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#aws-s3" class="md-nav__link">AWS S3</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#azure-blob-storage" class="md-nav__link">Azure Blob Storage</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#google-cloud-storage" class="md-nav__link">Google Cloud Storage</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#hdfs" class="md-nav__link">HDFS</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#local-filesystem-storage" class="md-nav__link">Local filesystem storage</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#adaptive-plan-optimizations" class="md-nav__link">Adaptive plan optimizations</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#retry-policy" class="md-nav__link">Retry policy</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#query" class="md-nav__link">QUERY</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#task" class="md-nav__link">TASK</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#encryption" class="md-nav__link">Encryption</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#advanced-configuration" class="md-nav__link">Advanced configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#retry-limits" class="md-nav__link">Retry limits</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#task-sizing" class="md-nav__link">Task sizing</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#node-allocation" class="md-nav__link">Node allocation</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#other-tuning" class="md-nav__link">Other tuning</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#exchange-manager" class="md-nav__link">Exchange manager</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#id1" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#aws-s3" class="md-nav__link">AWS S3</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#azure-blob-storage" class="md-nav__link">Azure Blob Storage</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#google-cloud-storage" class="md-nav__link">Google Cloud Storage</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#hdfs" class="md-nav__link">HDFS</a>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#local-filesystem-storage" class="md-nav__link">Local filesystem storage</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="fault-tolerant-execution.html#adaptive-plan-optimizations" class="md-nav__link">Adaptive plan optimizations</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="fault-tolerant-execution">
<h1 id="admin-fault-tolerant-execution--page-root">Fault-tolerant execution<a class="headerlink" href="fault-tolerant-execution.html#admin-fault-tolerant-execution--page-root" title="Link to this heading">#</a></h1>
<p>By default, if a Trino node lacks the resources to execute a task or
otherwise fails during query execution, the query fails and must be run again
manually. The longer the runtime of a query, the more likely it is to be
susceptible to such failures.</p>
<p>Fault-tolerant execution is a mechanism in Trino that enables a cluster to
mitigate query failures by retrying queries or their component tasks in
the event of failure. With fault-tolerant execution enabled, intermediate
exchange data is spooled and can be re-used by another worker in the event of a
worker outage or other fault during query execution.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Fault tolerance does not apply to broken queries or other user error. For
example, Trino does not spend resources retrying a query that fails because
its SQL cannot be parsed.</p>
<p>For a step-by-step guide explaining how to configure a Trino cluster with
fault-tolerant execution to improve query processing resilience, read
<a class="reference internal" href="../installation/query-resiliency.html"><span class="doc">Improve query processing resilience</span></a>.</p>
</div>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="fault-tolerant-execution.html#configuration" title="Link to this heading">#</a></h2>
<p>Fault-tolerant execution is turned off by default. To enable the feature, set the
<code class="docutils literal notranslate"><span class="pre">retry-policy</span></code> configuration property to either <code class="docutils literal notranslate"><span class="pre">QUERY</span></code> or <code class="docutils literal notranslate"><span class="pre">TASK</span></code>
depending on the desired <a class="reference internal" href="fault-tolerant-execution.html#fte-retry-policy"><span class="std std-ref">retry policy</span></a>.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">retry-policy</span><span class="o">=</span><span class="s">QUERY</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Setting <code class="docutils literal notranslate"><span class="pre">retry-policy</span></code> may cause queries to fail with connectors that do not
explicitly support fault-tolerant execution, resulting in a “This connector
does not support query retries” error message.</p>
<p>Support for fault-tolerant execution of SQL statements varies on a
per-connector basis, with more details in the documentation for each
connector. The following connectors support fault-tolerant execution:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../connector/bigquery.html#bigquery-fte-support"><span class="std std-ref">BigQuery connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/delta-lake.html#delta-lake-fte-support"><span class="std std-ref">Delta Lake connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/hive.html#hive-fte-support"><span class="std std-ref">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/iceberg.html#iceberg-fte-support"><span class="std std-ref">Iceberg connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/mariadb.html#mariadb-fte-support"><span class="std std-ref">MariaDB connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/mongodb.html#mongodb-fte-support"><span class="std std-ref">MongoDB connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/mysql.html#mysql-fte-support"><span class="std std-ref">MySQL connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/oracle.html#oracle-fte-support"><span class="std std-ref">Oracle connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/postgresql.html#postgresql-fte-support"><span class="std std-ref">PostgreSQL connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/redshift.html#redshift-fte-support"><span class="std std-ref">Redshift connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/sqlserver.html#sqlserver-fte-support"><span class="std std-ref">SQL Server connector</span></a></p></li>
</ul>
</div>
<p>The following configuration properties control the behavior of fault-tolerant
execution on a Trino cluster:</p>
<table id="id2">
<caption><span class="caption-text">Fault-tolerant execution configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 50%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">retry-policy</span></code></p></td>
<td><p>Configures what is retried in the event of failure, either <code class="docutils literal notranslate"><span class="pre">QUERY</span></code> to retry
the whole query, or <code class="docutils literal notranslate"><span class="pre">TASK</span></code> to retry tasks individually if they fail. See
<a class="reference internal" href="fault-tolerant-execution.html#fte-retry-policy"><span class="std std-ref">retry policy</span></a> for more information. Use the equivalent
session property <code class="docutils literal notranslate"><span class="pre">retry_policy</span></code> only on clusters configured for
fault-tolerant execution and typically only to deactivate with <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, since
switching between modes on a cluster is not tested.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.deduplication-buffer-size</span></code></p></td>
<td><p><a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">Data size</span></a> of the coordinator’s in-memory buffer used
by fault-tolerant execution to store output of query
<a class="reference internal" href="../overview/concepts.html#trino-concept-stage"><span class="std std-ref">stages</span></a>. If this buffer is filled during query
execution, the query fails with a “Exchange manager must be configured for
the failure recovery capabilities to be fully functional” error message unless an
<a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-manager"><span class="std std-ref">exchange manager</span></a> is configured.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">32MB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution.exchange-encryption-enabled</span></code></p></td>
<td><p>Enable encryption of spooling data, see <a class="reference internal" href="fault-tolerant-execution.html#fte-encryption"><span class="std std-ref">Encryption</span></a> for details.
Setting this property to false is not recommended if Trino processes sensitive data.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
<p>Find further related properties in <a class="reference internal" href="properties.html"><span class="doc std std-doc">Properties reference</span></a>, specifically in
<a class="reference internal" href="properties-resource-management.html"><span class="doc std std-doc">Resource management properties</span></a> and <a class="reference internal" href="properties-exchange.html"><span class="doc std std-doc">Exchange properties</span></a>.</p>
</section>
<section id="retry-policy">
<span id="fte-retry-policy"></span><h2 id="retry-policy">Retry policy<a class="headerlink" href="fault-tolerant-execution.html#retry-policy" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">retry-policy</span></code> configuration property, or the <code class="docutils literal notranslate"><span class="pre">retry_policy</span></code> session
property, designates whether Trino retries entire queries or a query’s
individual tasks in the event of failure.</p>
<section id="query">
<h3 id="query">QUERY<a class="headerlink" href="fault-tolerant-execution.html#query" title="Link to this heading">#</a></h3>
<p>A <code class="docutils literal notranslate"><span class="pre">QUERY</span></code> retry policy instructs Trino to automatically retry a query in the
event of an error occurring on a worker node. A <code class="docutils literal notranslate"><span class="pre">QUERY</span></code> retry policy is
recommended when the majority of the Trino cluster’s workload consists of many
small queries.</p>
<p>By default Trino does not implement fault tolerance for queries whose result set
exceeds 32MB in size, such as <a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a> statements that return a very
large data set to the user. This limit can be increased by modifying the
<code class="docutils literal notranslate"><span class="pre">exchange.deduplication-buffer-size</span></code> configuration property to be greater than
the default value of <code class="docutils literal notranslate"><span class="pre">32MB</span></code>, but this results in higher memory usage on the
coordinator.</p>
<p>To enable fault-tolerant execution on queries with a larger result set, it is
strongly recommended to configure an <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-manager"><span class="std std-ref">exchange manager</span></a> that utilizes external storage for spooled data and
therefore allows for storage of spilled data beyond the in-memory buffer size.</p>
</section>
<section id="task">
<h3 id="task">TASK<a class="headerlink" href="fault-tolerant-execution.html#task" title="Link to this heading">#</a></h3>
<p>A <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy instructs Trino to retry individual query <a class="reference internal" href="../overview/concepts.html#trino-concept-task"><span class="std std-ref">tasks</span></a> in the event of failure. You must configure an
<a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-manager"><span class="std std-ref">exchange manager</span></a> to use the task retry policy.
This policy is recommended when executing large batch queries, as the cluster
can more efficiently retry smaller tasks within the query rather than retry the
whole query.</p>
<p>When a cluster is configured with a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy, some relevant
configuration properties have their default values changed to follow best
practices for a fault-tolerant cluster. However, this automatic change does not
affect clusters that have these properties manually configured. If you have
any of the following properties configured in the <code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file on
a cluster with a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy, it is strongly recommended to set the
<code class="docutils literal notranslate"><span class="pre">task.low-memory-killer.policy</span></code>
<a class="reference internal" href="properties-query-management.html"><span class="doc">query management property</span></a> to
<code class="docutils literal notranslate"><span class="pre">total-reservation-on-blocked-nodes</span></code>, or queries may need to be manually killed
if the cluster runs out of memory.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy is best suited for large batch queries, but this
policy can result in higher latency for short-running queries executed in high
volume. As a best practice, it is recommended to run a dedicated cluster
with a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy for large batch queries, separate from another
cluster that handles short queries.</p>
</div>
</section>
</section>
<section id="encryption">
<span id="fte-encryption"></span><h2 id="encryption">Encryption<a class="headerlink" href="fault-tolerant-execution.html#encryption" title="Link to this heading">#</a></h2>
<p>Trino encrypts data before spooling it to storage. This prevents access to query
data by anyone besides the Trino cluster that wrote it, including administrators
of the storage system. A new encryption key is randomly generated for every
query, and the key is discarded once a query is completed.</p>
</section>
<section id="advanced-configuration">
<h2 id="advanced-configuration">Advanced configuration<a class="headerlink" href="fault-tolerant-execution.html#advanced-configuration" title="Link to this heading">#</a></h2>
<p>You can further configure fault-tolerant execution with the following
configuration properties. The default values for these properties should work
for most deployments, but you can change these values for testing or
troubleshooting purposes.</p>
<section id="retry-limits">
<h3 id="retry-limits">Retry limits<a class="headerlink" href="fault-tolerant-execution.html#retry-limits" title="Link to this heading">#</a></h3>
<p>The following configuration properties control the thresholds at which
queries/tasks are no longer retried in the event of repeated failures:</p>
<table id="id3">
<caption><span class="caption-text">Fault tolerance retry limit configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 23%"/>
<col style="width: 38%"/>
<col style="width: 15%"/>
<col style="width: 23%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
<th class="head"><p>Retry policy</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">query-retry-attempts</span></code></p></td>
<td><p>Maximum number of times Trino may attempt to retry a query before declaring
the query as failed.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">QUERY</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">task-retry-attempts-per-task</span></code></p></td>
<td><p>Maximum number of times Trino may attempt to retry a single task before
declaring the query as failed.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">retry-initial-delay</span></code></p></td>
<td><p>Minimum <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">time</span></a> that a failed query or task must wait
before it is retried. May be overridden with the <code class="docutils literal notranslate"><span class="pre">retry_initial_delay</span></code>
<a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">QUERY</span></code> and <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">retry-max-delay</span></code></p></td>
<td><p>Maximum <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">time</span></a> that a failed query or task must
wait before it is retried. Wait time is increased on each subsequent
failure. May be overridden with the <code class="docutils literal notranslate"><span class="pre">retry_max_delay</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1m</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">QUERY</span></code> and <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">retry-delay-scale-factor</span></code></p></td>
<td><p>Factor by which retry delay is increased on each query or task failure. May
be overridden with the <code class="docutils literal notranslate"><span class="pre">retry_delay_scale_factor</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2.0</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">QUERY</span></code> and <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="task-sizing">
<h3 id="task-sizing">Task sizing<a class="headerlink" href="fault-tolerant-execution.html#task-sizing" title="Link to this heading">#</a></h3>
<p>With a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy, it is important to manage the amount of data
processed in each task. If tasks are too small, the management of task
coordination can take more processing time and resources than executing the task
itself. If tasks are too large, then a single task may require more resources
than are available on any one node and therefore prevent the query from
completing.</p>
<p>Trino supports limited automatic task sizing. If issues are occurring
during fault-tolerant task execution, you can configure the following
configuration properties to manually control task sizing. These configuration
properties only apply to a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy.</p>
<table id="id4">
<caption><span class="caption-text">Task sizing configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 50%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-standard-split-size</span></code></p></td>
<td><p>Standard <a class="reference internal" href="../overview/concepts.html#trino-concept-splits"><span class="std std-ref">split</span></a> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a>
processed by tasks that read data from source tables. Value is interpreted
with split weight taken into account. If the weight of splits produced by a
catalog denotes that they are lighter or heavier than “standard” split, then
the number of splits processed by a single task is adjusted accordingly.</p>
<p>May be overridden for the current session with the
<code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_standard_split_size</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">64MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-max-task-split-count</span></code></p></td>
<td><p>Maximum number of <a class="reference internal" href="../overview/concepts.html#trino-concept-splits"><span class="std std-ref">splits</span></a> processed by a single task.
This value is not split weight-adjusted and serves as protection against
situations where catalogs report an incorrect split weight.</p>
<p>May be overridden for the current session with the
<code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_max_task_split_count</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">2048</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-compute-task-target-size-growth-period</span></code></p></td>
<td><p>The number of tasks created for any given non-writer stage of arbitrary
distribution before task size is increased.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">64</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-compute-task-target-size-growth-factor</span></code></p></td>
<td><p>Growth factor for adaptive sizing of non-writer tasks of arbitrary
distribution for fault-tolerant execution. Lower bound is 1.0. For every
task size increase, new task target size is old task target size multiplied
by this growth factor.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1.26</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-compute-task-target-size-min</span></code></p></td>
<td><p>Initial/minimum target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for non-writer
tasks of arbitrary distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">512MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-compute-task-target-size-max</span></code></p></td>
<td><p>Maximum target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for each non-writer
task of arbitrary distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">50GB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-write-task-target-size-growth-period</span></code></p></td>
<td><p>The number of tasks created for any given writer stage of arbitrary
distribution before task size is increased.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">64</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-write-task-target-size-growth-factor</span></code></p></td>
<td><p>Growth factor for adaptive sizing of writer tasks of arbitrary distribution
for fault-tolerant execution. Lower bound is 1.0. For every task size
increase, new task target size is old task target size multiplied by this
growth factor.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1.26</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-write-task-target-size-min</span></code></p></td>
<td><p>Initial/minimum target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for writer
tasks of arbitrary distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4GB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-arbitrary-distribution-write-task-target-size-max</span></code></p></td>
<td><p>Maximum target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for writer tasks of
arbitrary distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">50GB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-hash-distribution-compute-task-target-size</span></code></p></td>
<td><p>Target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for non-writer tasks of hash
distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">512MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-hash-distribution-write-task-target-size</span></code></p></td>
<td><p>Target input <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> of writer tasks of hash
distribution of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4GB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-hash-distribution-write-task-target-max-count</span></code></p></td>
<td><p>Soft upper bound on number of writer tasks in a stage of hash distribution
of fault-tolerant execution.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2000</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="node-allocation">
<h3 id="node-allocation">Node allocation<a class="headerlink" href="fault-tolerant-execution.html#node-allocation" title="Link to this heading">#</a></h3>
<p>With a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy, nodes are allocated to tasks based on available
memory and estimated memory usage. If task failure occurs due to exceeding
available memory on a node, the task is restarted with a request to allocate the
full node for its execution.</p>
<p>The initial task memory-requirements estimation is static and configured with
the <code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-task-memory</span></code> configuration property. This property only
applies to a <code class="docutils literal notranslate"><span class="pre">TASK</span></code> retry policy.</p>
<table id="id5">
<caption><span class="caption-text">Node allocation configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id5" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 50%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-task-memory</span></code></p></td>
<td><p>Initial task memory <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> estimation
used for bin-packing when allocating nodes for tasks. May be overridden
for the current session with the
<code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_task_memory</span></code>
<a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5GB</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="other-tuning">
<h3 id="other-tuning">Other tuning<a class="headerlink" href="fault-tolerant-execution.html#other-tuning" title="Link to this heading">#</a></h3>
<p>The following additional configuration property can be used to manage
fault-tolerant execution:</p>
<table id="id6">
<caption><span class="caption-text">Other fault-tolerant execution configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id6" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 23%"/>
<col style="width: 38%"/>
<col style="width: 15%"/>
<col style="width: 23%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
<th class="head"><p>Retry policy</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-task-descriptor-storage-max-memory</span></code></p></td>
<td><p>Maximum <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> of memory to be used to
store task descriptors for fault-tolerant queries on coordinator. Extra
memory is needed to be able to reschedule tasks in case of a failure.</p></td>
<td><p>(JVM heap size * 0.15)</p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-max-partition-count</span></code></p></td>
<td><p>Maximum number of partitions to use for distributed joins and aggregations,
similar in function to the <code class="docutils literal notranslate"><span class="pre">query.max-hash-partition-count</span></code> <a class="reference internal" href="properties-query-management.html"><span class="doc std std-doc">query
management property</span></a>. It is not
recommended to increase this property value higher than the default of <code class="docutils literal notranslate"><span class="pre">50</span></code>,
which may result in instability and poor performance. May be overridden for
the current session with the <code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_max_partition_count</span></code>
<a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">50</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-min-partition-count</span></code></p></td>
<td><p>Minimum number of partitions to use for distributed joins and aggregations,
similar in function to the <code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count</span></code> <a class="reference internal" href="properties-query-management.html"><span class="doc std std-doc">query
management property</span></a>. May be overridden
for the current session with the
<code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_min_partition_count</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fault-tolerant-execution-min-partition-count-for-write</span></code></p></td>
<td><p>Minimum number of partitions to use for distributed joins and aggregations
in write queries, similar in function to the
<code class="docutils literal notranslate"><span class="pre">query.min-hash-partition-count-for-write</span></code> <a class="reference internal" href="properties-query-management.html"><span class="doc std std-doc">query management
property</span></a>. May be overridden for the
current session with the
<code class="docutils literal notranslate"><span class="pre">fault_tolerant_execution_min_partition_count_for_write</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session
property</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">50</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">max-tasks-waiting-for-node-per-query</span></code></p></td>
<td><p>Allow for up to configured number of tasks to wait for node allocation
per query, before pausing scheduling for other tasks from this query.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">50</span></code></p></td>
<td><p>Only <code class="docutils literal notranslate"><span class="pre">TASK</span></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="exchange-manager">
<span id="fte-exchange-manager"></span><h2 id="exchange-manager">Exchange manager<a class="headerlink" href="fault-tolerant-execution.html#exchange-manager" title="Link to this heading">#</a></h2>
<p>Exchange spooling is responsible for storing and managing spooled data for
fault-tolerant execution. You can configure a filesystem-based exchange manager
that stores spooled data in a specified location, such as <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-aws-s3"><span class="std std-ref">AWS S3</span></a> and S3-compatible systems, <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-azure-blob"><span class="std std-ref">Azure Blob Storage</span></a>, <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-gcs"><span class="std std-ref">Google Cloud Storage</span></a>,
or <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-hdfs"><span class="std std-ref">HDFS</span></a>.</p>
<section id="id1">
<h3 id="id1">Configuration<a class="headerlink" href="fault-tolerant-execution.html#id1" title="Link to this heading">#</a></h3>
<p>To configure an exchange manager, create a new
<code class="docutils literal notranslate"><span class="pre">etc/exchange-manager.properties</span></code> configuration file on the coordinator and
all worker nodes. In this file, set the <code class="docutils literal notranslate"><span class="pre">exchange-manager.name</span></code> configuration
property to <code class="docutils literal notranslate"><span class="pre">filesystem</span></code> or <code class="docutils literal notranslate"><span class="pre">hdfs</span></code>, and set additional configuration properties as needed
for your storage solution.</p>
<p>The following table lists the available configuration properties for
<code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code>, their default values, and which file systems
the property may be configured for:</p>
<table id="id7">
<caption><span class="caption-text">Exchange manager configuration properties</span><a class="headerlink" href="fault-tolerant-execution.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 23%"/>
<col style="width: 38%"/>
<col style="width: 15%"/>
<col style="width: 23%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
<th class="head"><p>Supported filesystem</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.base-directories</span></code></p></td>
<td><p>Comma-separated list of URI locations that the exchange manager uses to
store spooling data.</p></td>
<td></td>
<td><p>Any</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.sink-buffer-pool-min-size</span></code></p></td>
<td><p>The minimum buffer pool size for an exchange sink. The larger the buffer
pool size, the larger the write parallelism and memory usage.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
<td><p>Any</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.sink-buffers-per-partition</span></code></p></td>
<td><p>The number of buffers per partition in the buffer pool. The larger the
buffer pool size, the larger the write parallelism and memory usage.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p>Any</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.sink-max-file-size</span></code></p></td>
<td><p>Max <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> of files written by exchange sinks.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1GB</span></code></p></td>
<td><p>Any</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.source-concurrent-readers</span></code></p></td>
<td><p>Number of concurrent readers to read from spooling storage. The larger the
number of concurrent readers, the larger the read parallelism and memory
usage.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>Any</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.aws-access-key</span></code></p></td>
<td><p>AWS access key to use. Required for a connection to AWS S3 and GCS, can be
ignored for other S3 storage systems.</p></td>
<td></td>
<td><p>AWS S3, GCS</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.aws-secret-key</span></code></p></td>
<td><p>AWS secret key to use. Required for a connection to AWS S3 and GCS, can be
ignored for other S3 storage systems.</p></td>
<td></td>
<td><p>AWS S3, GCS</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.iam-role</span></code></p></td>
<td><p>IAM role to assume.</p></td>
<td></td>
<td><p>AWS S3, GCS</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.external-id</span></code></p></td>
<td><p>External ID for the IAM role trust policy.</p></td>
<td></td>
<td><p>AWS S3, GCS</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.region</span></code></p></td>
<td><p>Region of the S3 bucket.</p></td>
<td></td>
<td><p>AWS S3, GCS</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.endpoint</span></code></p></td>
<td><p>S3 storage endpoint server if using an S3-compatible storage system that
is not AWS. If using AWS S3, this can be ignored unless HTTPS is required
by an AWS bucket policy. If TLS is required, then this property can be
set to an https endpoint such as <code class="docutils literal notranslate"><span class="pre">https://s3.us-east-1.amazonaws.com</span></code>.
Note that TLS is redundant due to <a class="reference internal" href="fault-tolerant-execution.html#fte-encryption"><span class="std std-ref">automatic encryption</span></a>.
If using GCS, set it to <code class="docutils literal notranslate"><span class="pre">https://storage.googleapis.com</span></code>.</p></td>
<td></td>
<td><p>Any S3-compatible storage</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.max-error-retries</span></code></p></td>
<td><p>Maximum number of times the exchange manager’s S3 client should retry
a request.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
<td><p>Any S3-compatible storage</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.path-style-access</span></code></p></td>
<td><p>Enables using <a class="reference external" href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/VirtualHosting.html#path-style-access">path-style access</a>
for all requests to S3.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
<td><p>Any S3-compatible storage</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.s3.upload.part-size</span></code></p></td>
<td><p>Part <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for S3 multi-part upload.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5MB</span></code></p></td>
<td><p>Any S3-compatible storage</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.gcs.json-key-file-path</span></code></p></td>
<td><p>Path to the JSON file that contains your Google Cloud Platform service
account key. Not to be set together with <code class="docutils literal notranslate"><span class="pre">exchange.gcs.json-key</span></code></p></td>
<td></td>
<td><p>GCS</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.gcs.json-key</span></code></p></td>
<td><p>Your Google Cloud Platform service account key in JSON format. Not to be set
together with <code class="docutils literal notranslate"><span class="pre">exchange.gcs.json-key-file-path</span></code></p></td>
<td></td>
<td><p>GCS</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.azure.endpoint</span></code></p></td>
<td><p>Azure blob endpoint used to access the spooling container. Not to be set
together with <code class="docutils literal notranslate"><span class="pre">exchange.azure.connection-string</span></code></p></td>
<td></td>
<td><p>Azure Blob Storage</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.azure.connection-string</span></code></p></td>
<td><p>Connection string used to access the spooling container. Not to be set
together with <code class="docutils literal notranslate"><span class="pre">exchange.azure.endpoint</span></code></p></td>
<td></td>
<td><p>Azure Blob Storage</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.azure.block-size</span></code></p></td>
<td><p>Block <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for Azure block blob parallel upload.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4MB</span></code></p></td>
<td><p>Azure Blob Storage</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.azure.max-error-retries</span></code></p></td>
<td><p>Maximum number of times the exchange manager’s Azure client should
retry a request.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
<td><p>Azure Blob Storage</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.hdfs.block-size</span></code></p></td>
<td><p>Block <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for HDFS storage.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4MB</span></code></p></td>
<td><p>HDFS</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">exchange.hdfs.skip-directory-scheme-validation</span></code></p></td>
<td><p>Skip directory scheme validation to support Hadoop-compatible file system.</p></td>
<td><p>false</p></td>
<td><p>HDFS</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hdfs.config.resources</span></code></p></td>
<td><p>Comma-separated list of paths to HDFS configuration files, for example
<code class="docutils literal notranslate"><span class="pre">/etc/hdfs-site.xml</span></code>. The files must exist on all nodes in the Trino
cluster.</p></td>
<td></td>
<td><p>HDFS</p></td>
</tr>
</tbody>
</table>
<p>To reduce the exchange manager’s overall I/O load, the
<a class="reference internal" href="properties-exchange.html#prop-exchange-compression-codec"><span class="std std-ref">exchange.compression-codec</span></a> configuration property defaults to <code class="docutils literal notranslate"><span class="pre">LZ4</span></code>. In
addition, <a class="reference internal" href="properties-general.html#file-compression"><span class="std std-ref">File compression and decompression</span></a> is automatically performed and some details can
be configured.</p>
<p>It is also recommended to configure a bucket lifecycle rule to automatically
expire abandoned objects in the event of a node crash.</p>
<section id="aws-s3">
<span id="fte-exchange-aws-s3"></span><h4 id="aws-s3">AWS S3<a class="headerlink" href="fault-tolerant-execution.html#aws-s3" title="Link to this heading">#</a></h4>
<p>The following example <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration specifies an
AWS S3 bucket as the spooling storage destination. Note that the destination
does not have to be in AWS, but can be any S3-compatible storage system. While
the exchange manager is designed to support S3-compatible storage systems, only
AWS S3 and MinIO are tested for compatibility. For other storage systems,
perform your own testing and consult your vendor for more information.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange-manager.name</span><span class="o">=</span><span class="s">filesystem</span>
<span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">s3://exchange-spooling-bucket</span>
<span class="na">exchange.s3.region</span><span class="o">=</span><span class="s">us-west-1</span>
<span class="na">exchange.s3.aws-access-key</span><span class="o">=</span><span class="s">example-access-key</span>
<span class="na">exchange.s3.aws-secret-key</span><span class="o">=</span><span class="s">example-secret-key</span>
</pre></div>
</div>
<p>You can configure multiple S3 buckets for the exchange manager to distribute
spooled data across buckets, reducing the I/O load on any one bucket. If a query
fails with the error message
“software.amazon.awssdk.services.s3.model.S3Exception: Please reduce your
request rate”, this indicates that the workload is I/O intensive, and you should
specify multiple S3 buckets in <code class="docutils literal notranslate"><span class="pre">exchange.base-directories</span></code> to balance the
load:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">s3://exchange-spooling-bucket-1,s3://exchange-spooling-bucket-2</span>
</pre></div>
</div>
</section>
<section id="azure-blob-storage">
<span id="fte-exchange-azure-blob"></span><h4 id="azure-blob-storage">Azure Blob Storage<a class="headerlink" href="fault-tolerant-execution.html#azure-blob-storage" title="Link to this heading">#</a></h4>
<p>The following example <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration specifies an
Azure Blob Storage container as the spooling storage destination. You must use
Azure Blob Storage, not Azure Data Lake Storage or any other hierarchical
storage option in Azure.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange-manager.name</span><span class="o">=</span><span class="s">filesystem</span>
<span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">abfs://container_name@account_name.dfs.core.windows.net</span>
<span class="na">exchange.azure.connection-string</span><span class="o">=</span><span class="s">connection-string</span>
</pre></div>
</div>
</section>
<section id="google-cloud-storage">
<span id="fte-exchange-gcs"></span><h4 id="google-cloud-storage">Google Cloud Storage<a class="headerlink" href="fault-tolerant-execution.html#google-cloud-storage" title="Link to this heading">#</a></h4>
<p>To enable exchange spooling on GCS in Trino, change the request endpoint to the
<code class="docutils literal notranslate"><span class="pre">https://storage.googleapis.com</span></code> Google storage URI, and configure your AWS
access/secret keys to use the GCS HMAC keys. If you deploy Trino on GCP, you
must either create a service account with access to your spooling bucket or
configure the key path to your GCS credential file.</p>
<p>For more information on GCS’s S3 compatibility, refer to the <a class="reference external" href="https://cloud.google.com/storage/docs/aws-simple-migration">Google Cloud
documentation on S3 migration</a>.</p>
<p>The following example <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration specifies a
GCS bucket as the spooling storage destination.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange-manager.name</span><span class="o">=</span><span class="s">filesystem</span>
<span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">gs://exchange-spooling-bucket</span>
<span class="na">exchange.s3.region</span><span class="o">=</span><span class="s">us-west-1</span>
<span class="na">exchange.s3.aws-access-key</span><span class="o">=</span><span class="s">example-access-key</span>
<span class="na">exchange.s3.aws-secret-key</span><span class="o">=</span><span class="s">example-secret-key</span>
<span class="na">exchange.s3.endpoint</span><span class="o">=</span><span class="s">https://storage.googleapis.com</span>
<span class="na">exchange.gcs.json-key-file-path</span><span class="o">=</span><span class="s">/path/to/gcs_keyfile.json</span>
</pre></div>
</div>
</section>
<section id="hdfs">
<span id="fte-exchange-hdfs"></span><h4 id="hdfs">HDFS<a class="headerlink" href="fault-tolerant-execution.html#hdfs" title="Link to this heading">#</a></h4>
<p>The following <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration example specifies HDFS
as the spooling storage destination.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange-manager.name</span><span class="o">=</span><span class="s">hdfs</span>
<span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">hadoop-master:9000/exchange-spooling-directory</span>
<span class="na">hdfs.config.resources</span><span class="o">=</span><span class="s">/usr/lib/hadoop/etc/hadoop/core-site.xml</span>
</pre></div>
</div>
<p>When you want use Hadoop-compatible file system as the spooling storage location,
you should enable <code class="docutils literal notranslate"><span class="pre">exchange.hdfs.skip-directory-scheme-validation</span></code> in <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code>
when configure <code class="docutils literal notranslate"><span class="pre">exchange.base-directories</span></code> with a specific scheme instead of <code class="docutils literal notranslate"><span class="pre">hdfs</span></code> and the following steps
may be necessary.</p>
<ol class="arabic simple">
<li><p>Configure the <code class="docutils literal notranslate"><span class="pre">AbstractFileSystem</span></code> implementation in <code class="docutils literal notranslate"><span class="pre">core-site.xml</span></code>.</p></li>
<li><p>Add the relevant client JAR files into the directory <code class="docutils literal notranslate"><span class="pre">${Trino_HOME}/plugin/exchange-hdfs</span></code>
on all Trino cluster nodes.</p></li>
</ol>
</section>
<section id="local-filesystem-storage">
<span id="fte-exchange-local-filesystem"></span><h4 id="local-filesystem-storage">Local filesystem storage<a class="headerlink" href="fault-tolerant-execution.html#local-filesystem-storage" title="Link to this heading">#</a></h4>
<p>The following example <code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration specifies a
local directory, <code class="docutils literal notranslate"><span class="pre">/tmp/trino-exchange-manager</span></code>, as the spooling storage
destination.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It is only recommended to use a local filesystem for exchange in standalone,
non-production clusters. A local directory can only be used for exchange in
a distributed cluster if the exchange directory is shared and accessible
from all nodes.</p>
</div>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">exchange-manager.name</span><span class="o">=</span><span class="s">filesystem</span>
<span class="na">exchange.base-directories</span><span class="o">=</span><span class="s">/tmp/trino-exchange-manager</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="adaptive-plan-optimizations">
<h2 id="adaptive-plan-optimizations">Adaptive plan optimizations<a class="headerlink" href="fault-tolerant-execution.html#adaptive-plan-optimizations" title="Link to this heading">#</a></h2>
<p>Fault-tolerant execution mode offers several adaptive plan
optimizations that adjust query execution plans dynamically based on
runtime statistics. For more information, see
<a class="reference internal" href="../optimizer/adaptive-plan-optimizations.html"><span class="doc std std-doc">Adaptive plan optimizations</span></a>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="graceful-shutdown.html" title="Graceful shutdown"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Graceful shutdown </span>
              </div>
            </a>
          
          
            <a href="event-listeners-http.html" title="HTTP event listener"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> HTTP event listener </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>