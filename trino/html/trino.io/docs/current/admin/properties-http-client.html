<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>HTTP client properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-http-client.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Spill to disk" href="spill.html" />
    <link rel="prev" title="Regular expression function properties" href="properties-regexp-function.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-http-client.html#admin/properties-http-client" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> HTTP client properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> HTTP client </label>
    
      <a href="properties-http-client.html#" class="md-nav__link md-nav__link--active">HTTP client</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-client.html#general-properties" class="md-nav__link">General properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-connect-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.connect-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#max-content-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">max-content-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-request-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.request-timeout</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#tls-and-security-properties" class="md-nav__link">TLS and security properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-included-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.included-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-hostname-verification" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.hostname-verification</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-path</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#proxy-properties" class="md-nav__link">Proxy properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-user" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.user</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-secure" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.secure</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-socks-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.socks-proxy</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#request-logging" class="md-nav__link">Request logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-compression-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.compression-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-flush-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.flush-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-queue-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.queue-size</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> HTTP client </label>
    
      <a href="properties-http-client.html#" class="md-nav__link md-nav__link--active">HTTP client</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-client.html#general-properties" class="md-nav__link">General properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-connect-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.connect-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#max-content-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">max-content-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-request-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.request-timeout</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#tls-and-security-properties" class="md-nav__link">TLS and security properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-included-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.included-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-hostname-verification" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.hostname-verification</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-path</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#proxy-properties" class="md-nav__link">Proxy properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-user" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.user</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-secure" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.secure</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-socks-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.socks-proxy</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#request-logging" class="md-nav__link">Request logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-compression-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.compression-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-flush-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.flush-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-queue-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.queue-size</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-client.html#general-properties" class="md-nav__link">General properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-connect-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.connect-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#max-content-length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">max-content-length</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-request-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.request-timeout</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#tls-and-security-properties" class="md-nav__link">TLS and security properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-included-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.included-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-https-hostname-verification" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.https.hostname-verification</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-key-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-trust-store-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-path</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#proxy-properties" class="md-nav__link">Proxy properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-user" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.user</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-http-proxy-secure" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.secure</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-socks-proxy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.socks-proxy</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#request-logging" class="md-nav__link">Request logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-compression-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.compression-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-flush-interval" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.flush-interval</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-history" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-history</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-max-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-client.html#http-client-log-queue-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-client.log.queue-size</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="http-client-properties">
<h1 id="admin-properties-http-client--page-root">HTTP client properties<a class="headerlink" href="properties-http-client.html#admin-properties-http-client--page-root" title="Link to this heading">#</a></h1>
<p>HTTP client properties allow you to configure the connection from Trino to
external services using HTTP.</p>
<p>The following properties can be used after adding the specific prefix to the
property. For example, for <a class="reference internal" href="../security/oauth2.html"><span class="doc std std-doc">OAuth 2.0 authentication</span></a>, you can enable HTTP for
interactions with the external OAuth 2.0 provider by adding the prefix
<code class="docutils literal notranslate"><span class="pre">oauth2-jwk</span></code> to the <code class="docutils literal notranslate"><span class="pre">http-client.connect-timeout</span></code> property, and increasing
the connection timeout to ten seconds by setting the value to <code class="docutils literal notranslate"><span class="pre">10</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">oauth2</span><span class="o">-</span><span class="n">jwk</span><span class="p">.</span><span class="n">http</span><span class="o">-</span><span class="n">client</span><span class="p">.</span><span class="k">connect</span><span class="o">-</span><span class="n">timeout</span><span class="o">=</span><span class="mi">10</span><span class="n">s</span>
</pre></div>
</div>
<p>The following prefixes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">oauth2-jwk</span></code> for <a class="reference internal" href="../security/oauth2.html"><span class="doc std std-doc">OAuth 2.0 authentication</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">jwk</span></code> for <a class="reference internal" href="../security/jwt.html"><span class="doc std std-doc">JWT authentication</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">exchange</span></code> to configure data transfer between Trino nodes in addition to
<a class="reference internal" href="properties-exchange.html"><span class="doc std std-doc">Exchange properties</span></a></p></li>
</ul>
<section id="general-properties">
<h2 id="general-properties">General properties<a class="headerlink" href="properties-http-client.html#general-properties" title="Link to this heading">#</a></h2>
<section id="http-client-connect-timeout">
<h3 id="http-client-connect-timeout"><code class="docutils literal notranslate"><span class="pre">http-client.connect-timeout</span></code><a class="headerlink" href="properties-http-client.html#http-client-connect-timeout" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">5s</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">0ms</span></code></p></li>
</ul>
<p>Timeout value for establishing the connection to the external service.</p>
</section>
<section id="max-content-length">
<h3 id="max-content-length"><code class="docutils literal notranslate"><span class="pre">max-content-length</span></code><a class="headerlink" href="properties-http-client.html#max-content-length" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">16MB</span></code></p></li>
</ul>
<p>Maximum content size for each HTTP request and response.</p>
</section>
<section id="http-client-request-timeout">
<h3 id="http-client-request-timeout"><code class="docutils literal notranslate"><span class="pre">http-client.request-timeout</span></code><a class="headerlink" href="properties-http-client.html#http-client-request-timeout" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">5m</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">0ms</span></code></p></li>
</ul>
<p>Timeout value for the overall request.</p>
</section>
</section>
<section id="tls-and-security-properties">
<h2 id="tls-and-security-properties">TLS and security properties<a class="headerlink" href="properties-http-client.html#tls-and-security-properties" title="Link to this heading">#</a></h2>
<section id="http-client-https-excluded-cipher">
<h3 id="http-client-https-excluded-cipher"><code class="docutils literal notranslate"><span class="pre">http-client.https.excluded-cipher</span></code><a class="headerlink" href="properties-http-client.html#http-client-https-excluded-cipher" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>A comma-separated list of regexes for the names of cipher algorithms to exclude.</p>
</section>
<section id="http-client-https-included-cipher">
<h3 id="http-client-https-included-cipher"><code class="docutils literal notranslate"><span class="pre">http-client.https.included-cipher</span></code><a class="headerlink" href="properties-http-client.html#http-client-https-included-cipher" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>A comma-separated list of regexes for the names of the cipher algorithms to use.</p>
</section>
<section id="http-client-https-hostname-verification">
<h3 id="http-client-https-hostname-verification"><code class="docutils literal notranslate"><span class="pre">http-client.https.hostname-verification</span></code><a class="headerlink" href="properties-http-client.html#http-client-https-hostname-verification" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Verify that the server hostname matches the server DNS name in the
SubjectAlternativeName (SAN) field of the certificate.</p>
</section>
<section id="http-client-key-store-password">
<h3 id="http-client-key-store-password"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-password</span></code><a class="headerlink" href="properties-http-client.html#http-client-key-store-password" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Password for the keystore.</p>
</section>
<section id="http-client-key-store-path">
<h3 id="http-client-key-store-path"><code class="docutils literal notranslate"><span class="pre">http-client.key-store-path</span></code><a class="headerlink" href="properties-http-client.html#http-client-key-store-path" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>File path on the server to the keystore file.</p>
</section>
<section id="http-client-secure-random-algorithm">
<h3 id="http-client-secure-random-algorithm"><code class="docutils literal notranslate"><span class="pre">http-client.secure-random-algorithm</span></code><a class="headerlink" href="properties-http-client.html#http-client-secure-random-algorithm" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Set the secure random algorithm for the connection. The default varies by
operating system. Algorithms are specified according to standard algorithm name
documentation.</p>
<p>Possible types include <code class="docutils literal notranslate"><span class="pre">NativePRNG</span></code>, <code class="docutils literal notranslate"><span class="pre">NativePRNGBlocking</span></code>,
<code class="docutils literal notranslate"><span class="pre">NativePRNGNonBlocking</span></code>, <code class="docutils literal notranslate"><span class="pre">PKCS11</span></code>, and <code class="docutils literal notranslate"><span class="pre">SHA1PRNG</span></code>.</p>
</section>
<section id="http-client-trust-store-password">
<h3 id="http-client-trust-store-password"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-password</span></code><a class="headerlink" href="properties-http-client.html#http-client-trust-store-password" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Password for the truststore.</p>
</section>
<section id="http-client-trust-store-path">
<h3 id="http-client-trust-store-path"><code class="docutils literal notranslate"><span class="pre">http-client.trust-store-path</span></code><a class="headerlink" href="properties-http-client.html#http-client-trust-store-path" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>File path on the server to the truststore file.</p>
</section>
</section>
<section id="proxy-properties">
<h2 id="proxy-properties">Proxy properties<a class="headerlink" href="properties-http-client.html#proxy-properties" title="Link to this heading">#</a></h2>
<section id="http-client-http-proxy">
<h3 id="http-client-http-proxy"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy</span></code><a class="headerlink" href="properties-http-client.html#http-client-http-proxy" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Host and port for an HTTP proxy with the format <code class="docutils literal notranslate"><span class="pre">example.net:8080</span></code>.</p>
</section>
<section id="http-client-http-proxy-user">
<h3 id="http-client-http-proxy-user"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.user</span></code><a class="headerlink" href="properties-http-client.html#http-client-http-proxy-user" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Username for basic authentication with the HTTP proxy.</p>
</section>
<section id="http-client-http-proxy-password">
<h3 id="http-client-http-proxy-password"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.password</span></code><a class="headerlink" href="properties-http-client.html#http-client-http-proxy-password" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Password for basic authentication with the HTTP proxy.</p>
</section>
<section id="http-client-http-proxy-secure">
<h3 id="http-client-http-proxy-secure"><code class="docutils literal notranslate"><span class="pre">http-client.http-proxy.secure</span></code><a class="headerlink" href="properties-http-client.html#http-client-http-proxy-secure" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Enable HTTPS for the proxy.</p>
</section>
<section id="http-client-socks-proxy">
<h3 id="http-client-socks-proxy"><code class="docutils literal notranslate"><span class="pre">http-client.socks-proxy</span></code><a class="headerlink" href="properties-http-client.html#http-client-socks-proxy" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Host and port for a SOCKS proxy.</p>
</section>
</section>
<section id="request-logging">
<h2 id="request-logging">Request logging<a class="headerlink" href="properties-http-client.html#request-logging" title="Link to this heading">#</a></h2>
<section id="http-client-log-compression-enabled">
<h3 id="http-client-log-compression-enabled"><code class="docutils literal notranslate"><span class="pre">http-client.log.compression-enabled</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-compression-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Enable log file compression. The client uses the <code class="docutils literal notranslate"><span class="pre">.gz</span></code> format for log files.</p>
</section>
<section id="http-client-log-enabled">
<h3 id="http-client-log-enabled"><code class="docutils literal notranslate"><span class="pre">http-client.log.enabled</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Enable logging of HTTP requests.</p>
</section>
<section id="http-client-log-flush-interval">
<h3 id="http-client-log-flush-interval"><code class="docutils literal notranslate"><span class="pre">http-client.log.flush-interval</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-flush-interval" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10s</span></code></p></li>
</ul>
<p>Frequency of flushing the log data to disk.</p>
</section>
<section id="http-client-log-max-history">
<h3 id="http-client-log-max-history"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-history</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-max-history" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">15</span></code></p></li>
</ul>
<p>Retention limit of log files in days. Files older than the <code class="docutils literal notranslate"><span class="pre">max-history</span></code> are
deleted when the HTTP client creates files for new logging periods.</p>
</section>
<section id="http-client-log-max-size">
<h3 id="http-client-log-max-size"><code class="docutils literal notranslate"><span class="pre">http-client.log.max-size</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-max-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1GB</span></code></p></li>
</ul>
<p>Maximum total size of all log files on disk.</p>
</section>
<section id="http-client-log-path">
<h3 id="http-client-log-path"><code class="docutils literal notranslate"><span class="pre">http-client.log.path</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-path" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">var/log/</span></code></p></li>
</ul>
<p>Sets the path of the log files. All log files are named <code class="docutils literal notranslate"><span class="pre">http-client.log</span></code>, and
have the prefix of the specific HTTP client added. For example,
<code class="docutils literal notranslate"><span class="pre">jwk-http-client.log</span></code>.</p>
</section>
<section id="http-client-log-queue-size">
<h3 id="http-client-log-queue-size"><code class="docutils literal notranslate"><span class="pre">http-client.log.queue-size</span></code><a class="headerlink" href="properties-http-client.html#http-client-log-queue-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10000</span></code></p></li>
<li><p><strong>Minimum value:</strong> <code class="docutils literal notranslate"><span class="pre">1</span></code></p></li>
</ul>
<p>Size of the HTTP client logging queue.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-regexp-function.html" title="Regular expression function properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Regular expression function properties </span>
              </div>
            </a>
          
          
            <a href="spill.html" title="Spill to disk"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Spill to disk </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>