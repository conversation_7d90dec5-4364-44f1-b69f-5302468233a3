<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Properties reference &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="General properties" href="properties-general.html" />
    <link rel="prev" title="Trino metrics with OpenMetrics" href="openmetrics.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties.html#admin/properties" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Properties reference </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Properties reference </label>
    
      <a href="properties.html#" class="md-nav__link md-nav__link--active">Properties reference</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties.html#property-value-types" class="md-nav__link">Property value types</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties.html#boolean" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">boolean</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#data-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#double" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">double</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#heap-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">heap</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#integer" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">integer</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#string" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">string</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Properties reference </label>
    
      <a href="properties.html#" class="md-nav__link md-nav__link--active">Properties reference</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties.html#property-value-types" class="md-nav__link">Property value types</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties.html#boolean" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">boolean</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#data-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#double" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">double</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#heap-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">heap</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#integer" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">integer</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#string" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">string</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-server.html" class="md-nav__link">HTTP server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties.html#property-value-types" class="md-nav__link">Property value types</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties.html#boolean" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">boolean</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#data-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#double" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">double</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">duration</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#heap-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">heap</span> <span class="pre">size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#integer" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">integer</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties.html#string" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">string</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="properties-reference">
<h1 id="admin-properties--page-root">Properties reference<a class="headerlink" href="properties.html#admin-properties--page-root" title="Link to this heading">#</a></h1>
<p>This section describes the most important configuration properties and (where
applicable) their corresponding <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session properties</span></a>, that may be used to tune Trino or alter its
behavior when required. Unless specified otherwise, configuration properties
must be set on the coordinator and all worker nodes.</p>
<p>The following pages are not a complete list of all configuration and
session properties available in Trino, and do not include any connector-specific
catalog configuration properties. For more information on catalog configuration
properties, refer to the <a class="reference internal" href="../connector.html"><span class="doc">connector documentation</span></a>.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="properties-general.html">General</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-client-protocol.html">Client protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-http-server.html">HTTP server</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-resource-management.html">Resource management</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-query-management.html">Query management</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-catalog.html">Catalog management</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-sql-environment.html">SQL environment</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-spilling.html">Spilling</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-exchange.html">Exchange</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-task.html">Task</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-write-partitioning.html">Write partitioning</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-writer-scaling.html">Writer scaling</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-node-scheduler.html">Node scheduler</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-optimizer.html">Optimizer</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-logging.html">Logging</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-web-interface.html">Web UI</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-regexp-function.html">Regular expression function</a></li>
<li class="toctree-l1"><a class="reference internal" href="properties-http-client.html">HTTP client</a></li>
</ul>
</div>
<section id="property-value-types">
<h2 id="property-value-types">Property value types<a class="headerlink" href="properties.html#property-value-types" title="Link to this heading">#</a></h2>
<p>Trino configuration properties support different value types with their own
allowed values and syntax. Additional limitations apply on a per-property basis,
and disallowed values result in a validation error.</p>
<section id="boolean">
<span id="prop-type-boolean"></span><h3 id="boolean"><code class="docutils literal notranslate"><span class="pre">boolean</span></code><a class="headerlink" href="properties.html#boolean" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">boolean</span></code> support two values, <code class="docutils literal notranslate"><span class="pre">true</span></code> or <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="data-size">
<span id="prop-type-data-size"></span><h3 id="data-size"><code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code><a class="headerlink" href="properties.html#data-size" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code> support values that describe an amount of
data, measured in byte-based units. These units are incremented in multiples of
1024, so one megabyte is 1024 kilobytes, one kilobyte is 1024 bytes, and so on.
For example, the value <code class="docutils literal notranslate"><span class="pre">6GB</span></code> describes six gigabytes, which is
(6 * 1024 * 1024 * 1024) = 6442450944 bytes.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code> type supports the following units:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">B</span></code>: Bytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">kB</span></code>: Kilobytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MB</span></code>: Megabytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GB</span></code>: Gigabytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TB</span></code>: Terabytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PB</span></code>: Petabytes</p></li>
</ul>
</section>
<section id="double">
<span id="prop-type-double"></span><h3 id="double"><code class="docutils literal notranslate"><span class="pre">double</span></code><a class="headerlink" href="properties.html#double" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">double</span></code> support numerical values including decimals,
such as <code class="docutils literal notranslate"><span class="pre">1.6</span></code>. <code class="docutils literal notranslate"><span class="pre">double</span></code> type values can be negative, if supported by the
specific property.</p>
</section>
<section id="duration">
<span id="prop-type-duration"></span><h3 id="duration"><code class="docutils literal notranslate"><span class="pre">duration</span></code><a class="headerlink" href="properties.html#duration" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">duration</span></code> support values describing an
amount of time, using the syntax of a non-negative number followed by a time
unit. For example, the value <code class="docutils literal notranslate"><span class="pre">7m</span></code> describes seven minutes.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">duration</span></code> type supports the following units:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ns</span></code>: Nanoseconds</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">us</span></code>: Microseconds</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ms</span></code>: Milliseconds</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">s</span></code>: Seconds</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">m</span></code>: Minutes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">h</span></code>: Hours</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">d</span></code>: Days</p></li>
</ul>
<p>A duration of <code class="docutils literal notranslate"><span class="pre">0</span></code> is treated as zero regardless of the unit that follows.
For example, <code class="docutils literal notranslate"><span class="pre">0s</span></code> and <code class="docutils literal notranslate"><span class="pre">0m</span></code> both mean the same thing.</p>
<p>Properties of type <code class="docutils literal notranslate"><span class="pre">duration</span></code> also support decimal values, such as <code class="docutils literal notranslate"><span class="pre">2.25d</span></code>.
These are handled as a fractional value of the specified unit. For example, the
value <code class="docutils literal notranslate"><span class="pre">1.5m</span></code> equals one and a half minutes, or 90 seconds.</p>
</section>
<section id="heap-size">
<span id="prop-type-heap-size"></span><h3 id="heap-size"><code class="docutils literal notranslate"><span class="pre">heap</span> <span class="pre">size</span></code><a class="headerlink" href="properties.html#heap-size" title="Link to this heading">#</a></h3>
<p>Properties of type <code class="docutils literal notranslate"><span class="pre">heap</span> <span class="pre">size</span></code> support values that specify an amount of heap memory.
These values can be provided in the same format as the <code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">size</span></code> property, or as <code class="docutils literal notranslate"><span class="pre">double</span></code>
values followed by a <code class="docutils literal notranslate"><span class="pre">%</span></code> suffix. The <code class="docutils literal notranslate"><span class="pre">%</span></code> suffix indicates a percentage of the maximum heap
memory available on the node. The minimum allowed value is <code class="docutils literal notranslate"><span class="pre">1B</span></code>, and the maximum is <code class="docutils literal notranslate"><span class="pre">100%</span></code>,
which corresponds to the maximum heap memory available on the node.</p>
</section>
<section id="integer">
<span id="prop-type-integer"></span><h3 id="integer"><code class="docutils literal notranslate"><span class="pre">integer</span></code><a class="headerlink" href="properties.html#integer" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">integer</span></code> support whole numeric values, such as <code class="docutils literal notranslate"><span class="pre">5</span></code>
and <code class="docutils literal notranslate"><span class="pre">1000</span></code>. Negative values are supported as well, for example <code class="docutils literal notranslate"><span class="pre">-7</span></code>.
<code class="docutils literal notranslate"><span class="pre">integer</span></code> type values must be whole numbers, decimal values such as <code class="docutils literal notranslate"><span class="pre">2.5</span></code>
are not supported.</p>
<p>Some <code class="docutils literal notranslate"><span class="pre">integer</span></code> type properties enforce their own minimum and maximum values.</p>
</section>
<section id="string">
<span id="prop-type-string"></span><h3 id="string"><code class="docutils literal notranslate"><span class="pre">string</span></code><a class="headerlink" href="properties.html#string" title="Link to this heading">#</a></h3>
<p>The properties of type <code class="docutils literal notranslate"><span class="pre">string</span></code> support a set of values that consist of a
sequence of characters. Allowed values are defined on a property-by-property
basis, refer to the specific property for its supported and default values.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="openmetrics.html" title="Trino metrics with OpenMetrics"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Trino metrics with OpenMetrics </span>
              </div>
            </a>
          
          
            <a href="properties-general.html" title="General properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> General properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>