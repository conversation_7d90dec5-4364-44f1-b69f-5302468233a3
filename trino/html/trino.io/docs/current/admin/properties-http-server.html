<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>HTTP server properties &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="properties-http-server.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Resource management properties" href="properties-resource-management.html" />
    <link rel="prev" title="Client protocol properties" href="properties-client-protocol.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="properties-http-server.html#admin/properties-http-server" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> HTTP server properties </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> HTTP server </label>
    
      <a href="properties-http-server.html#" class="md-nav__link md-nav__link--active">HTTP server</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-server.html#general" class="md-nav__link">General</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-process-forwarded" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.process-forwarded</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-and-https" class="md-nav__link">HTTP and HTTPS</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-http-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.http.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-included-cipher-and-http-server-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.included-cipher</span></code> and <code class="docutils literal notranslate"><span class="pre">http-server.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keymanager-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keymanager.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-cache-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-cache-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-context-refresh-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-context.refresh-time</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-allow-insecure-over-http" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.allow-insecure-over-http</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-certificate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-jwt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-krb5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-oauth2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#logging" class="md-nav__link">Logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-log" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.log.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication" class="md-nav__link">Internal communication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-shared-secret" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.shared-secret</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-http2-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.http2.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-https-required" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.https.required</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="properties-general.html" class="md-nav__link">General</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> HTTP server </label>
    
      <a href="properties-http-server.html#" class="md-nav__link md-nav__link--active">HTTP server</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-server.html#general" class="md-nav__link">General</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-process-forwarded" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.process-forwarded</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-and-https" class="md-nav__link">HTTP and HTTPS</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-http-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.http.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-included-cipher-and-http-server-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.included-cipher</span></code> and <code class="docutils literal notranslate"><span class="pre">http-server.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keymanager-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keymanager.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-cache-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-cache-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-context-refresh-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-context.refresh-time</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-allow-insecure-over-http" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.allow-insecure-over-http</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-certificate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-jwt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-krb5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-oauth2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#logging" class="md-nav__link">Logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-log" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.log.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication" class="md-nav__link">Internal communication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-shared-secret" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.shared-secret</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-http2-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.http2.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-https-required" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.https.required</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-resource-management.html" class="md-nav__link">Resource management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-query-management.html" class="md-nav__link">Query management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-catalog.html" class="md-nav__link">Catalog management</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-sql-environment.html" class="md-nav__link">SQL environment</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-spilling.html" class="md-nav__link">Spilling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-exchange.html" class="md-nav__link">Exchange</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-task.html" class="md-nav__link">Task</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-write-partitioning.html" class="md-nav__link">Write partitioning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-writer-scaling.html" class="md-nav__link">Writer scaling</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-node-scheduler.html" class="md-nav__link">Node scheduler</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-optimizer.html" class="md-nav__link">Optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-regexp-function.html" class="md-nav__link">Regular expression function</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties-http-client.html" class="md-nav__link">HTTP client</a>
      
    
    </li></ul>
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="properties-http-server.html#general" class="md-nav__link">General</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-process-forwarded" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.process-forwarded</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-and-https" class="md-nav__link">HTTP and HTTPS</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-http-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.http.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-port" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.port</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-included-cipher-and-http-server-https-excluded-cipher" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.included-cipher</span></code> and <code class="docutils literal notranslate"><span class="pre">http-server.https.excluded-cipher</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keystore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-truststore-key" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.key</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-keymanager-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.keymanager.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-secure-random-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.secure-random-algorithm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-session-cache-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-cache-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-https-ssl-context-refresh-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-context.refresh-time</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-allow-insecure-over-http" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.allow-insecure-over-http</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-certificate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-jwt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-krb5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-oauth2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.*</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-authentication-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#logging" class="md-nav__link">Logging</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#http-server-log" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">http-server.log.*</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication" class="md-nav__link">Internal communication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-shared-secret" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.shared-secret</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-http2-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.http2.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="properties-http-server.html#internal-communication-https-required" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">internal-communication.https.required</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="http-server-properties">
<h1 id="admin-properties-http-server--page-root">HTTP server properties<a class="headerlink" href="properties-http-server.html#admin-properties-http-server--page-root" title="Link to this heading">#</a></h1>
<p>HTTP server properties allow you to configure the HTTP server of Trino that
handles <a class="reference internal" href="../security.html"><span class="doc std std-doc">Security</span></a> including <a class="reference internal" href="../security/internal-communication.html"><span class="doc std std-doc">Secure internal communication</span></a>,  and
serves the <a class="reference internal" href="web-interface.html"><span class="doc std std-doc">Web UI</span></a> and the <a class="reference internal" href="../develop/client-protocol.html"><span class="doc std std-doc">client
API</span></a>.</p>
<section id="general">
<h2 id="general">General<a class="headerlink" href="properties-http-server.html#general" title="Link to this heading">#</a></h2>
<section id="http-server-process-forwarded">
<span id="id1"></span><h3 id="http-server-process-forwarded"><code class="docutils literal notranslate"><span class="pre">http-server.process-forwarded</span></code><a class="headerlink" href="properties-http-server.html#http-server-process-forwarded" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Enable treating forwarded HTTPS requests over HTTP as secure. Requires the
<a class="reference external" href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For#see_also"><code class="docutils literal notranslate"><span class="pre">X-Forwarded</span></code> headers</a>
to be set to <code class="docutils literal notranslate"><span class="pre">HTTPS</span></code> on forwarded requests. This is commonly performed by a load
balancer that terminates HTTPS to HTTP. Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> when using such a load
balancer in front of Trino or <a class="reference external" href="https://trinodb.github.io/trino-gateway/">Trino
Gateway</a>. Find more details in
<a class="reference internal" href="../security/tls.html#https-load-balancer"><span class="std std-ref">Use a load balancer to terminate TLS/HTTPS</span></a>.</p>
</section>
</section>
<section id="http-and-https">
<h2 id="http-and-https">HTTP and HTTPS<a class="headerlink" href="properties-http-server.html#http-and-https" title="Link to this heading">#</a></h2>
<section id="http-server-http-port">
<h3 id="http-server-http-port"><code class="docutils literal notranslate"><span class="pre">http-server.http.port</span></code><a class="headerlink" href="properties-http-server.html#http-server-http-port" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8080</span></code></p></li>
</ul>
<p>Specify the HTTP port for the HTTP server.</p>
</section>
<section id="http-server-https-enabled">
<h3 id="http-server-https-enabled"><code class="docutils literal notranslate"><span class="pre">http-server.https.enabled</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Enable <a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a>.</p>
</section>
<section id="http-server-https-port">
<h3 id="http-server-https-port"><code class="docutils literal notranslate"><span class="pre">http-server.https.port</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-port" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">8443</span></code></p></li>
</ul>
<p>Specify the HTTPS port for the HTTP server.</p>
</section>
<section id="http-server-https-included-cipher-and-http-server-https-excluded-cipher">
<h3 id="http-server-https-included-cipher-and-http-server-https-excluded-cipher"><code class="docutils literal notranslate"><span class="pre">http-server.https.included-cipher</span></code> and <code class="docutils literal notranslate"><span class="pre">http-server.https.excluded-cipher</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-included-cipher-and-http-server-https-excluded-cipher" title="Link to this heading">#</a></h3>
<p>Optional configuration for ciphers to use TLS, find details in
<a class="reference internal" href="../security/tls.html#tls-version-and-ciphers"><span class="std std-ref">Supported standards</span></a>.</p>
</section>
<section id="http-server-https-keystore-path">
<h3 id="http-server-https-keystore-path"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.path</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-keystore-path" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>The location of the PEM or Java keystore file used to enable <a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a>.</p>
</section>
<section id="http-server-https-keystore-key">
<h3 id="http-server-https-keystore-key"><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.key</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-keystore-key" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>The password for the PEM or Java keystore.</p>
</section>
<section id="http-server-https-truststore-path">
<h3 id="http-server-https-truststore-path"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.path</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-truststore-path" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>The location of the optional PEM or Java truststore file for additional
certificate authorities. Find details in <a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a>.</p>
</section>
<section id="http-server-https-truststore-key">
<h3 id="http-server-https-truststore-key"><code class="docutils literal notranslate"><span class="pre">http-server.https.truststore.key</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-truststore-key" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>The password for the optional PEM or Java truststore.</p>
</section>
<section id="http-server-https-keymanager-password">
<h3 id="http-server-https-keymanager-password"><code class="docutils literal notranslate"><span class="pre">http-server.https.keymanager.password</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-keymanager-password" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Password for a key within a keystore, when a different password is configured
for the specific key. Find details in <a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a>.</p>
</section>
<section id="http-server-https-secure-random-algorithm">
<h3 id="http-server-https-secure-random-algorithm"><code class="docutils literal notranslate"><span class="pre">http-server.https.secure-random-algorithm</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-secure-random-algorithm" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Optional name of the algorithm to generate secure random values for
<a class="reference internal" href="../security/internal-communication.html#internal-performance"><span class="std std-ref">internal communication</span></a>.</p>
</section>
<section id="http-server-https-ssl-session-timeout">
<h3 id="http-server-https-ssl-session-timeout"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-timeout</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-ssl-session-timeout" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">4h</span></code></p></li>
</ul>
<p>Time duration for a valid TLS client session.</p>
</section>
<section id="http-server-https-ssl-session-cache-size">
<h3 id="http-server-https-ssl-session-cache-size"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-session-cache-size</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-ssl-session-cache-size" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-integer"><span class="std std-ref">integer</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">10000</span></code></p></li>
</ul>
<p>Maximum number of SSL session cache entries.</p>
</section>
<section id="http-server-https-ssl-context-refresh-time">
<h3 id="http-server-https-ssl-context-refresh-time"><code class="docutils literal notranslate"><span class="pre">http-server.https.ssl-context.refresh-time</span></code><a class="headerlink" href="properties-http-server.html#http-server-https-ssl-context-refresh-time" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-duration"><span class="std std-ref">duration</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">1m</span></code></p></li>
</ul>
<p>Time between reloading default certificates.</p>
</section>
</section>
<section id="authentication">
<h2 id="authentication">Authentication<a class="headerlink" href="properties-http-server.html#authentication" title="Link to this heading">#</a></h2>
<section id="http-server-authentication-type">
<h3 id="http-server-authentication-type"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.type</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-type" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>Configures the ordered list of enabled <a class="reference internal" href="../security/authentication-types.html"><span class="doc std std-doc">authentication
types</span></a>.</p>
<p>All authentication requires secure connections using <a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a> or
<a class="reference internal" href="properties-http-server.html#http-server-process-forwarded"><span class="std std-ref">process forwarding enabled</span></a>, and <a class="reference internal" href="../security/internal-communication.html"><span class="doc std std-doc">a configured
shared secret</span></a>.</p>
</section>
<section id="http-server-authentication-allow-insecure-over-http">
<h3 id="http-server-authentication-allow-insecure-over-http"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.allow-insecure-over-http</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-allow-insecure-over-http" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
</ul>
<p>Enable HTTP when any authentication is active. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>, but is
automatically set to <code class="docutils literal notranslate"><span class="pre">false</span></code> with active authentication. Overriding the value to
<code class="docutils literal notranslate"><span class="pre">true</span></code> can be useful for testing, but is not secure. More details in
<a class="reference internal" href="../security/tls.html"><span class="doc std std-doc">TLS and HTTPS</span></a>.</p>
</section>
<section id="http-server-authentication-certificate">
<h3 id="http-server-authentication-certificate"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-certificate" title="Link to this heading">#</a></h3>
<p>Configuration properties for <a class="reference internal" href="../security/certificate.html"><span class="doc std std-doc">Certificate authentication</span></a>.</p>
</section>
<section id="http-server-authentication-jwt">
<h3 id="http-server-authentication-jwt"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-jwt" title="Link to this heading">#</a></h3>
<p>Configuration properties for <a class="reference internal" href="../security/jwt.html"><span class="doc std std-doc">JWT authentication</span></a>.</p>
</section>
<section id="http-server-authentication-krb5">
<h3 id="http-server-authentication-krb5"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-krb5" title="Link to this heading">#</a></h3>
<p>Configuration properties for <a class="reference internal" href="../security/kerberos.html"><span class="doc std std-doc">Kerberos authentication</span></a>.</p>
</section>
<section id="http-server-authentication-oauth2">
<h3 id="http-server-authentication-oauth2"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-oauth2" title="Link to this heading">#</a></h3>
<p>Configuration properties for <a class="reference internal" href="../security/oauth2.html"><span class="doc std std-doc">OAuth 2.0 authentication</span></a>.</p>
</section>
<section id="http-server-authentication-password">
<h3 id="http-server-authentication-password"><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-authentication-password" title="Link to this heading">#</a></h3>
<p>Configuration properties for the <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> authentication types
<a class="reference internal" href="../security/ldap.html"><span class="doc std std-doc">LDAP authentication</span></a>, <a class="reference internal" href="../security/password-file.html"><span class="doc std std-doc">Password file authentication</span></a>, and <a class="reference internal" href="../security/salesforce.html"><span class="doc std std-doc">Salesforce authentication</span></a>.</p>
</section>
</section>
<section id="logging">
<h2 id="logging">Logging<a class="headerlink" href="properties-http-server.html#logging" title="Link to this heading">#</a></h2>
<section id="http-server-log">
<h3 id="http-server-log"><code class="docutils literal notranslate"><span class="pre">http-server.log.*</span></code><a class="headerlink" href="properties-http-server.html#http-server-log" title="Link to this heading">#</a></h3>
<p>Configuration properties for <a class="reference internal" href="properties-logging.html"><span class="doc std std-doc">Logging properties</span></a>.</p>
<p>(props-internal-communication)</p>
</section>
</section>
<section id="internal-communication">
<h2 id="internal-communication">Internal communication<a class="headerlink" href="properties-http-server.html#internal-communication" title="Link to this heading">#</a></h2>
<p>The following properties are used for configuring the <a class="reference internal" href="../security/internal-communication.html"><span class="doc std std-doc">internal
communication</span></a> between all
<a class="reference internal" href="../overview/concepts.html#trino-concept-node"><span class="std std-ref">nodes</span></a> of a Trino cluster.</p>
<section id="internal-communication-shared-secret">
<h3 id="internal-communication-shared-secret"><code class="docutils literal notranslate"><span class="pre">internal-communication.shared-secret</span></code><a class="headerlink" href="properties-http-server.html#internal-communication-shared-secret" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-string"><span class="std std-ref">string</span></a></p></li>
</ul>
<p>The string to use as secret that only the coordinators and workers in a specific
cluster share and use to authenticate within the cluster. See
<a class="reference internal" href="../security/internal-communication.html#internal-secret"><span class="std std-ref">Configure shared secret</span></a> for details.</p>
</section>
<section id="internal-communication-http2-enabled">
<h3 id="internal-communication-http2-enabled"><code class="docutils literal notranslate"><span class="pre">internal-communication.http2.enabled</span></code><a class="headerlink" href="properties-http-server.html#internal-communication-http2-enabled" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
</ul>
<p>Enable use of the HTTP/2 protocol for internal communication for enhanced
scalability compared to HTTP/1.1. Only turn this feature off if you encounter
issues with HTTP/2 usage within the cluster in your deployment.</p>
</section>
<section id="internal-communication-https-required">
<h3 id="internal-communication-https-required"><code class="docutils literal notranslate"><span class="pre">internal-communication.https.required</span></code><a class="headerlink" href="properties-http-server.html#internal-communication-https-required" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Type:</strong> <a class="reference internal" href="properties.html#prop-type-boolean"><span class="std std-ref">boolean</span></a></p></li>
<li><p><strong>Default value:</strong> <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>Enable the use of <a class="reference internal" href="../security/internal-communication.html#internal-tls"><span class="std std-ref">SSL/TLS for all internal communication</span></a>.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-client-protocol.html" title="Client protocol properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Client protocol properties </span>
              </div>
            </a>
          
          
            <a href="properties-resource-management.html" title="Resource management properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Resource management properties </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>