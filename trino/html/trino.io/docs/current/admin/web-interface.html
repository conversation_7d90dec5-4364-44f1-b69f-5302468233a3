<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Web UI &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="web-interface.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Preview Web UI" href="preview-web-interface.html" />
    <link rel="prev" title="Administration" href="../admin.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="web-interface.html#admin/web-interface" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Web UI </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Web UI </label>
    
      <a href="web-interface.html#" class="md-nav__link md-nav__link--active">Web UI</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="web-interface.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="web-interface.html#password-authentication" class="md-nav__link">Password authentication</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#fixed-user-authentication" class="md-nav__link">Fixed user authentication</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#other-authentication-types" class="md-nav__link">Other authentication types</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#user-interface-overview" class="md-nav__link">User interface overview</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#configuring-query-history" class="md-nav__link">Configuring query history</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="web-interface.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="web-interface.html#password-authentication" class="md-nav__link">Password authentication</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#fixed-user-authentication" class="md-nav__link">Fixed user authentication</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#other-authentication-types" class="md-nav__link">Other authentication types</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#user-interface-overview" class="md-nav__link">User interface overview</a>
        </li>
        <li class="md-nav__item"><a href="web-interface.html#configuring-query-history" class="md-nav__link">Configuring query history</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="web-ui">
<h1 id="admin-web-interface--page-root">Web UI<a class="headerlink" href="web-interface.html#admin-web-interface--page-root" title="Link to this heading">#</a></h1>
<p>Trino provides a web-based user interface (UI) for monitoring a Trino cluster
and managing queries. The Web UI is accessible on the coordinator via
HTTP or HTTPS, using the corresponding port number specified in the coordinator
<a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>. It can be configured with <a class="reference internal" href="properties-web-interface.html"><span class="doc">Web UI properties</span></a>.</p>
<p>The Web UI can be disabled entirely with the <code class="docutils literal notranslate"><span class="pre">web-ui.enabled</span></code> property.</p>
<section id="authentication">
<span id="web-ui-authentication"></span><h2 id="authentication">Authentication<a class="headerlink" href="web-interface.html#authentication" title="Link to this heading">#</a></h2>
<p>The Web UI requires users to authenticate. If Trino is not configured to require
authentication, then any username can be used, and no password is required or
allowed. Typically, users login with the same username that they use for
running queries.</p>
<p>If no system access control is installed, then all users are able to view and kill
any query. This can be restricted by using <a class="reference internal" href="../security/file-system-access-control.html#query-rules"><span class="std std-ref">query rules</span></a> with the
<a class="reference internal" href="../security/built-in-system-access-control.html"><span class="doc">System access control</span></a>. Users always have permission to view
or kill their own queries.</p>
<section id="password-authentication">
<h3 id="password-authentication">Password authentication<a class="headerlink" href="web-interface.html#password-authentication" title="Link to this heading">#</a></h3>
<p>Typically, a password-based authentication method
such as <a class="reference internal" href="../security/ldap.html"><span class="doc">LDAP</span></a> or <a class="reference internal" href="../security/password-file.html"><span class="doc">password file</span></a>
is used to secure both the Trino server and the Web UI. When the Trino server
is configured to use a password authenticator, the Web UI authentication type
is automatically set to <code class="docutils literal notranslate"><span class="pre">FORM</span></code>. In this case, the Web UI displays a login form
that accepts a username and password.</p>
</section>
<section id="fixed-user-authentication">
<h3 id="fixed-user-authentication">Fixed user authentication<a class="headerlink" href="web-interface.html#fixed-user-authentication" title="Link to this heading">#</a></h3>
<p>If you require the Web UI to be accessible without authentication, you can set a fixed
username that will be used for all Web UI access by setting the authentication type to
<code class="docutils literal notranslate"><span class="pre">FIXED</span></code> and setting the username with the <code class="docutils literal notranslate"><span class="pre">web-ui.user</span></code> configuration property.
If there is a system access control installed, this user must have permission to view
(and possibly to kill) queries.</p>
</section>
<section id="other-authentication-types">
<h3 id="other-authentication-types">Other authentication types<a class="headerlink" href="web-interface.html#other-authentication-types" title="Link to this heading">#</a></h3>
<p>The following Web UI authentication types are also supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CERTIFICATE</span></code>, see details in <a class="reference internal" href="../security/certificate.html"><span class="doc">Certificate authentication</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>, see details in <a class="reference internal" href="../security/kerberos.html"><span class="doc">Kerberos authentication</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">JWT</span></code>, see details in <a class="reference internal" href="../security/jwt.html"><span class="doc">JWT authentication</span></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code>, see details in <a class="reference internal" href="../security/oauth2.html"><span class="doc">OAuth 2.0 authentication</span></a></p></li>
</ul>
<p>For these authentication types, the username is defined by <a class="reference internal" href="../security/user-mapping.html"><span class="doc">User mapping</span></a>.</p>
</section>
</section>
<section id="user-interface-overview">
<span id="web-ui-overview"></span><h2 id="user-interface-overview">User interface overview<a class="headerlink" href="web-interface.html#user-interface-overview" title="Link to this heading">#</a></h2>
<p>The main page has a list of queries along with information like unique query ID, query text,
query state, percentage completed, username and source from which this query originated.
The currently running queries are at the top of the page, followed by the most recently
completed or failed queries.</p>
<p>The possible query states are as follows:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">QUEUED</span></code> – Query has been accepted and is awaiting execution.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PLANNING</span></code> – Query is being planned.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">STARTING</span></code> – Query execution is being started.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> – Query has at least one running task.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BLOCKED</span></code> – Query is blocked and is waiting for resources (buffer space, memory, splits, etc.).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FINISHING</span></code> – Query is finishing (e.g. commit for autocommit queries).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FINISHED</span></code> – Query has finished executing and all output has been consumed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FAILED</span></code> – Query execution failed.</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">BLOCKED</span></code> state is normal, but if it is persistent, it should be investigated.
It has many potential causes: insufficient memory or splits, disk or network I/O bottlenecks, data skew
(all the data goes to a few workers), a lack of parallelism (only a few workers available), or computationally
expensive stages of the query following a given stage.  Additionally, a query can be in
the <code class="docutils literal notranslate"><span class="pre">BLOCKED</span></code> state if a client is not processing the data fast enough (common with “SELECT *” queries).</p>
<p>For more detailed information about a query, simply click the query ID link.
The query detail page has a summary section, graphical representation of various stages of the
query and a list of tasks. Each task ID can be clicked to get more information about that task.</p>
<p>The summary section has a button to kill the currently running query. There are two visualizations
available in the summary section: task execution and timeline. The full JSON document containing
information and statistics about the query is available by clicking the <em>JSON</em> link. These visualizations
and other statistics can be used to analyze where time is being spent for a query.</p>
</section>
<section id="configuring-query-history">
<h2 id="configuring-query-history">Configuring query history<a class="headerlink" href="web-interface.html#configuring-query-history" title="Link to this heading">#</a></h2>
<p>The following configuration properties affect <a class="reference internal" href="properties-query-management.html"><span class="doc">how query history is collected</span></a> for display in the Web UI:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">query.max-history</span></code></p></li>
</ul>
<p>Unrelated to the storage of queries and query history in memory, you can use an
<a class="reference internal" href="../admin.html#admin-event-listeners"><span class="std std-ref">event listener</span></a> to publish query events, such as
query started or query finished, to an external system.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../admin.html" title="Administration"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Administration </span>
              </div>
            </a>
          
          
            <a href="preview-web-interface.html" title="Preview Web UI"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Preview Web UI </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>