<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Spill to disk &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="spill.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Resource groups" href="resource-groups.html" />
    <link rel="prev" title="HTTP client properties" href="properties-http-client.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="spill.html#admin/spill" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Spill to disk </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Spill to disk </label>
    
      <a href="spill.html#" class="md-nav__link md-nav__link--active">Spill to disk</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="spill.html#overview" class="md-nav__link">Overview</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#memory-management-and-spill" class="md-nav__link">Memory management and spill</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-disk-space" class="md-nav__link">Spill disk space</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-compression" class="md-nav__link">Spill compression</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-encryption" class="md-nav__link">Spill encryption</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#supported-operations" class="md-nav__link">Supported operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="spill.html#joins" class="md-nav__link">Joins</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#aggregations" class="md-nav__link">Aggregations</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#order-by" class="md-nav__link">Order by</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#window-functions" class="md-nav__link">Window functions</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="spill.html#overview" class="md-nav__link">Overview</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#memory-management-and-spill" class="md-nav__link">Memory management and spill</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-disk-space" class="md-nav__link">Spill disk space</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-compression" class="md-nav__link">Spill compression</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#spill-encryption" class="md-nav__link">Spill encryption</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#supported-operations" class="md-nav__link">Supported operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="spill.html#joins" class="md-nav__link">Joins</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#aggregations" class="md-nav__link">Aggregations</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#order-by" class="md-nav__link">Order by</a>
        </li>
        <li class="md-nav__item"><a href="spill.html#window-functions" class="md-nav__link">Window functions</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="spill-to-disk">
<h1 id="admin-spill--page-root">Spill to disk<a class="headerlink" href="spill.html#admin-spill--page-root" title="Link to this heading">#</a></h1>
<section id="overview">
<h2 id="overview">Overview<a class="headerlink" href="spill.html#overview" title="Link to this heading">#</a></h2>
<p>In the case of memory intensive operations, Trino allows offloading
intermediate operation results to disk. The goal of this mechanism is to
enable execution of queries that require amounts of memory exceeding per query
or per node limits.</p>
<p>The mechanism is similar to OS level page swapping. However, it is
implemented on the application level to address specific needs of Trino.</p>
<p>Properties related to spilling are described in <a class="reference internal" href="properties-spilling.html"><span class="doc">Spilling properties</span></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The spill to disk feature and implementation are a legacy functionality of
Trino. Consider using <a class="reference internal" href="fault-tolerant-execution.html"><span class="doc std std-doc">Fault-tolerant execution</span></a> with the <code class="docutils literal notranslate"><span class="pre">task</span></code> retry
policy and a configured <a class="reference internal" href="fault-tolerant-execution.html#fte-exchange-manager"><span class="std std-ref">Exchange manager</span></a>.</p>
</div>
</section>
<section id="memory-management-and-spill">
<h2 id="memory-management-and-spill">Memory management and spill<a class="headerlink" href="spill.html#memory-management-and-spill" title="Link to this heading">#</a></h2>
<p>By default, Trino kills queries, if the memory requested by the query execution
exceeds session properties <code class="docutils literal notranslate"><span class="pre">query_max_memory</span></code> or
<code class="docutils literal notranslate"><span class="pre">query_max_memory_per_node</span></code>. This mechanism ensures fairness in allocation
of memory to queries, and prevents deadlock caused by memory allocation.
It is efficient when there is a lot of small queries in the cluster, but
leads to killing large queries that don’t stay within the limits.</p>
<p>To overcome this inefficiency, the concept of revocable memory was introduced. A
query can request memory that does not count toward the limits, but this memory
can be revoked by the memory manager at any time. When memory is revoked, the
query runner spills intermediate data from memory to disk and continues to
process it later.</p>
<p>In practice, when the cluster is idle, and all memory is available, a memory
intensive query may use all the memory in the cluster. On the other hand,
when the cluster does not have much free memory, the same query may be forced to
use disk as storage for intermediate data. A query, that is forced to spill to
disk, may have a longer execution time by orders of magnitude than a query that
runs completely in memory.</p>
<p>Please note that enabling spill-to-disk does not guarantee execution of all
memory intensive queries. It is still possible that the query runner fails
to divide intermediate data into chunks small enough so that every chunk fits into
memory, leading to <code class="docutils literal notranslate"><span class="pre">Out</span> <span class="pre">of</span> <span class="pre">memory</span></code> errors while loading the data from disk.</p>
</section>
<section id="spill-disk-space">
<h2 id="spill-disk-space">Spill disk space<a class="headerlink" href="spill.html#spill-disk-space" title="Link to this heading">#</a></h2>
<p>Spilling intermediate results to disk, and retrieving them back, is expensive
in terms of IO operations. Thus, queries that use spill likely become
throttled by disk. To increase query performance, it is recommended to
provide multiple paths on separate local devices for spill (property
<code class="docutils literal notranslate"><span class="pre">spiller-spill-path</span></code> in <a class="reference internal" href="properties-spilling.html"><span class="doc">Spilling properties</span></a>).</p>
<p>The system drive should not be used for spilling, especially not to the drive where the JVM
is running and writing logs. Doing so may lead to cluster instability. Additionally,
it is recommended to monitor the disk saturation of the configured spill paths.</p>
<p>Trino treats spill paths as independent disks (see <a class="reference external" href="https://wikipedia.org/wiki/Non-RAID_drive_architectures#JBOD">JBOD</a>), so
there is no need to use RAID for spill.</p>
</section>
<section id="spill-compression">
<h2 id="spill-compression">Spill compression<a class="headerlink" href="spill.html#spill-compression" title="Link to this heading">#</a></h2>
<p>When spill compression is enabled with the <a class="reference internal" href="properties-spilling.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">spill-compression-codec</span></code>
property</span></a>, spilled pages are compressed, before
being written to disk. Enabling this feature can reduce disk IO at the cost of
extra CPU load to compress and decompress spilled pages.</p>
</section>
<section id="spill-encryption">
<h2 id="spill-encryption">Spill encryption<a class="headerlink" href="spill.html#spill-encryption" title="Link to this heading">#</a></h2>
<p>When spill encryption is enabled (<code class="docutils literal notranslate"><span class="pre">spill-encryption-enabled</span></code> property in
<a class="reference internal" href="properties-spilling.html"><span class="doc">Spilling properties</span></a>), spill contents are encrypted with a randomly generated
(per spill file) secret key. Enabling this increases CPU load and reduces throughput
of spilling to disk, but can protect spilled data from being recovered from spill files.
Consider reducing the value of <code class="docutils literal notranslate"><span class="pre">memory-revoking-threshold</span></code> when spill
encryption is enabled, to account for the increase in latency of spilling.</p>
</section>
<section id="supported-operations">
<h2 id="supported-operations">Supported operations<a class="headerlink" href="spill.html#supported-operations" title="Link to this heading">#</a></h2>
<p>Not all operations support spilling to disk, and each handles spilling
differently. Currently, the mechanism is implemented for the following
operations.</p>
<section id="joins">
<h3 id="joins">Joins<a class="headerlink" href="spill.html#joins" title="Link to this heading">#</a></h3>
<p>During the join operation, one of the tables being joined is stored in memory.
This table is called the build table. The rows from the other table stream
through and are passed onto the next operation, if they match rows in the build
table. The most memory-intensive part of the join is this build table.</p>
<p>When the task concurrency is greater than one, the build table is partitioned.
The number of partitions is equal to the value of the <code class="docutils literal notranslate"><span class="pre">task.concurrency</span></code>
configuration parameter (see <a class="reference internal" href="properties-task.html"><span class="doc">Task properties</span></a>).</p>
<p>When the build table is partitioned, the spill-to-disk mechanism can decrease
the peak memory usage needed by the join operation. When a query approaches the
memory limit, a subset of the partitions of the build table gets spilled to disk,
along with rows from the other table that fall into those same partitions. The
number of partitions, that get spilled, influences the amount of disk space needed.</p>
<p>Afterward, the spilled partitions are read back one-by-one to finish the join
operation.</p>
<p>With this mechanism, the peak memory used by the join operator can be decreased
to the size of the largest build table partition. Assuming no data skew, this
is <code class="docutils literal notranslate"><span class="pre">1</span> <span class="pre">/</span> <span class="pre">task.concurrency</span></code> times the size of the whole build table.</p>
</section>
<section id="aggregations">
<h3 id="aggregations">Aggregations<a class="headerlink" href="spill.html#aggregations" title="Link to this heading">#</a></h3>
<p>Aggregation functions perform an operation on a group of values and return one
value. If the number of groups you’re aggregating over is large, a significant
amount of memory may be needed. When spill-to-disk is enabled, if there is not
enough memory, intermediate cumulated aggregation results are written to disk.
They are loaded back and merged with a lower memory footprint.</p>
</section>
<section id="order-by">
<h3 id="order-by">Order by<a class="headerlink" href="spill.html#order-by" title="Link to this heading">#</a></h3>
<p>If your trying to sort a larger amount of data, a significant amount of memory
may be needed. When spill to disk for <code class="docutils literal notranslate"><span class="pre">order</span> <span class="pre">by</span></code> is enabled, if there is not enough
memory, intermediate sorted results are written to disk. They are loaded back and
merged with a lower memory footprint.</p>
</section>
<section id="window-functions">
<h3 id="window-functions">Window functions<a class="headerlink" href="spill.html#window-functions" title="Link to this heading">#</a></h3>
<p>Window functions perform an operator over a window of rows, and return one value
for each row. If this window of rows is large, a significant amount of memory may
be needed. When spill to disk for window functions is enabled, if there is not enough
memory, intermediate results are written to disk. They are loaded back and merged
when memory is available. There is a current limitation that spill does not work
in all cases, such as when a single window is very large.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="properties-http-client.html" title="HTTP client properties"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> HTTP client properties </span>
              </div>
            </a>
          
          
            <a href="resource-groups.html" title="Resource groups"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Resource groups </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>