<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Observability with OpenTelemetry &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="opentelemetry.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Trino metrics with OpenMetrics" href="openmetrics.html" />
    <link rel="prev" title="Monitoring with JMX" href="jmx.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="opentelemetry.html#admin/opentelemetry" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Observability with OpenTelemetry </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Observability with OpenTelemetry </label>
    
      <a href="opentelemetry.html#" class="md-nav__link md-nav__link--active">Observability with OpenTelemetry</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="opentelemetry.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="opentelemetry.html#example-use" class="md-nav__link">Example use</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="opentelemetry.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="opentelemetry.html#example-use" class="md-nav__link">Example use</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="observability-with-opentelemetry">
<h1 id="admin-opentelemetry--page-root">Observability with OpenTelemetry<a class="headerlink" href="opentelemetry.html#admin-opentelemetry--page-root" title="Link to this heading">#</a></h1>
<p>Trino exposes tracing information for observability of a running Trino
deployment for the widely used <a class="reference external" href="https://opentelemetry.io/">OpenTelemetry</a>
collection of APIs, SDKs, and tools. You can use OpenTelemetry to instrument,
generate, collect, and export telemetry data such as metrics, logs, and traces
to help you analyze application performance and behavior. More information about
the observability and the concepts involved is available in the <a class="reference external" href="https://opentelemetry.io/docs/concepts/">OpenTelemetry
documentation</a>.</p>
<p>The integration of OpenTelemetry with Trino enables tracing Trino behavior and
performance. You can use it to diagnose the overall application as well as
processing of specific queries or other narrower aspects.</p>
<p>Trino emits trace information from the coordinator and the workers. Trace
information includes the core system such as the query planner and the
optimizer, and a wide range of connectors and other plugins.</p>
<p>Trino uses any supplied trace identifiers from client tools across the cluster.
If none are supplied, trace identifiers are created for each query. The
identifiers are propagated to data sources, metastores, and other connected
components. As a result you can use this distributed tracing information to follow
all the processing flow of a query from a client tool, through the
coordinator and all workers to the data sources and other integrations.</p>
<p>If you want to receive traces from data sources and other integrations, these
tools must also support OpenTelemetry tracing and use the supplied identifiers
from Trino to propagate the context. Tracing must be enabled separately on these
tools.</p>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="opentelemetry.html#configuration" title="Link to this heading">#</a></h2>
<p>Use tracing with OpenTelemetry by enabling it and configuring the endpoint in
the <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">config.properties file</span></a>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">tracing.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">tracing.exporter.endpoint</span><span class="o">=</span><span class="s">http://observe.example.com:4317</span>
</pre></div>
</div>
<p>Tracing is not enabled by default. The exporter endpoint must specify a URL that
is accessible from the coordinator and all workers of the cluster. The preceding
example uses a observability platform deployment available by
HTTP at the host <code class="docutils literal notranslate"><span class="pre">observe.example.com</span></code>, port <code class="docutils literal notranslate"><span class="pre">4317</span></code>.</p>
<p>Use the <code class="docutils literal notranslate"><span class="pre">tracing.exporter.protocol</span></code> property to configure the protocol for exporting traces.
Defaults to the gRPC protocol with the <code class="docutils literal notranslate"><span class="pre">grpc</span></code> value. Set the value to <code class="docutils literal notranslate"><span class="pre">http/protobuf</span></code> for
exporting traces using protocol buffers with HTTP transport.</p>
</section>
<section id="example-use">
<h2 id="example-use">Example use<a class="headerlink" href="opentelemetry.html#example-use" title="Link to this heading">#</a></h2>
<p>The following steps provide a simple demo setup to run the open source
observability platform <a class="reference external" href="https://www.jaegertracing.io/">Jaeger</a> and Trino locally
in Docker containers.</p>
<p>Create a shared network for both servers called <code class="docutils literal notranslate"><span class="pre">platform</span></code>:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>network<span class="w"> </span>create<span class="w"> </span>platform
</pre></div>
</div>
<p>Start Jaeger in the background:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>run<span class="w"> </span>-d<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--name<span class="w"> </span>jaeger<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--network<span class="o">=</span>platform<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--network-alias<span class="o">=</span>jaeger<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">COLLECTOR_OTLP_ENABLED</span><span class="o">=</span><span class="nb">true</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">16686</span>:16686<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">4317</span>:4317<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>jaegertracing/all-in-one:latest
</pre></div>
</div>
<p>The preceding command adds Jaeger to the <code class="docutils literal notranslate"><span class="pre">platform</span></code> network with the hostname
<code class="docutils literal notranslate"><span class="pre">jaeger</span></code>. It also maps the endpoint and Jaeger UI ports.</p>
<p>Create a <code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file that uses the default setup from the Trino
container, and adds the tracing configuration with the <code class="docutils literal notranslate"><span class="pre">jaeger</span></code> hostname:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">node-scheduler.include-coordinator</span><span class="o">=</span><span class="s">true</span>
<span class="na">http-server.http.port</span><span class="o">=</span><span class="s">8080</span>
<span class="na">discovery.uri</span><span class="o">=</span><span class="s">http://localhost:8080</span>
<span class="na">tracing.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">tracing.exporter.endpoint</span><span class="o">=</span><span class="s">http://jaeger:4317</span>
</pre></div>
</div>
<p>Start Trino in the background:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>run<span class="w"> </span>-d<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--name<span class="w"> </span>trino<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--network<span class="o">=</span>platform<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">8080</span>:8080<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--mount<span class="w"> </span><span class="nv">type</span><span class="o">=</span>bind,source<span class="o">=</span><span class="nv">$PWD</span>/config.properties,target<span class="o">=</span>/etc/trino/config.properties<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>trinodb/trino:latest
</pre></div>
</div>
<p>The preceding command adds Trino to the <code class="docutils literal notranslate"><span class="pre">platform</span></code> network. It also mounts the
configuration file into the container so that tracing is enabled.</p>
<p>Now everything is running.</p>
<p>Install and run the <a class="reference internal" href="../client/cli.html"><span class="doc std std-doc">Trino CLI</span></a> or any other client application and
submit a query such as <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">CATALOGS;</span></code> or <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span> <span class="pre">FROM</span> <span class="pre">tpch.tiny.nation;</span></code>.</p>
<p>Optionally, log into the <a class="reference internal" href="web-interface.html"><span class="doc std std-doc">Trino Web UI</span></a> at
<a class="reference external" href="http://localhost:8080">http://localhost:8080</a> with a random username. Press
the <strong>Finished</strong> button and inspect the details for the completed queries.</p>
<p>Access the Jaeger UI at <a class="reference external" href="http://localhost:16686/">http://localhost:16686/</a>,
select the service <code class="docutils literal notranslate"><span class="pre">trino</span></code>, and press <strong>Find traces</strong>.</p>
<p>As a next step, run more queries and inspect more traces with the Jaeger UI.</p>
<p>Once you are done you can stop the containers:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>stop<span class="w"> </span>trino
docker<span class="w"> </span>stop<span class="w"> </span>jaeger
</pre></div>
</div>
<p>You can start them again for further testing:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>start<span class="w"> </span>jaeger
docker<span class="w"> </span>start<span class="w"> </span>trino
</pre></div>
</div>
<p>Use the following commands to completely remove the network and containers:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>rm<span class="w"> </span>trino
docker<span class="w"> </span>rm<span class="w"> </span>jaeger
docker<span class="w"> </span>network<span class="w"> </span>rm<span class="w"> </span>platform
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="jmx.html" title="Monitoring with JMX"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Monitoring with JMX </span>
              </div>
            </a>
          
          
            <a href="openmetrics.html" title="Trino metrics with OpenMetrics"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Trino metrics with OpenMetrics </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>