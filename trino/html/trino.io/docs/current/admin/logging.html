<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Logging &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="logging.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Tuning Trino" href="tuning.html" />
    <link rel="prev" title="Preview Web UI" href="preview-web-interface.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="logging.html#admin/logging" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Logging </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Logging </label>
    
      <a href="logging.html#" class="md-nav__link md-nav__link--active">Logging</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="logging.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="logging.html#log-output" class="md-nav__link">Log output</a>
        </li>
        <li class="md-nav__item"><a href="logging.html#json-and-tcp-channel-logging" class="md-nav__link">JSON and TCP channel logging</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="logging.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="logging.html#log-output" class="md-nav__link">Log output</a>
        </li>
        <li class="md-nav__item"><a href="logging.html#json-and-tcp-channel-logging" class="md-nav__link">JSON and TCP channel logging</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="logging">
<h1 id="admin-logging--page-root">Logging<a class="headerlink" href="logging.html#admin-logging--page-root" title="Link to this heading">#</a></h1>
<p>Trino include numerous features to better understand and monitor a running
system, such as <a class="reference internal" href="opentelemetry.html"><span class="doc std std-doc">Observability with OpenTelemetry</span></a> or <a class="reference internal" href="jmx.html"><span class="doc std std-doc">Monitoring with JMX</span></a>. Logging and
configuring logging is one important aspect for operating and troubleshooting
Trino.</p>
<section id="configuration">
<span id="logging-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="logging.html#configuration" title="Link to this heading">#</a></h2>
<p>Trino application logging is optional and configured in the <code class="docutils literal notranslate"><span class="pre">log.properties</span></code>
file in your Trino installation <code class="docutils literal notranslate"><span class="pre">etc</span></code> configuration directory as set by the
<a class="reference internal" href="../installation/deployment.html#running-trino"><span class="std std-ref">launcher</span></a>.</p>
<p>Use it to add specific loggers and configure the minimum log levels. Every
logger has a name, which is typically the fully qualified name of the class that
uses the logger. Loggers have a hierarchy based on the dots in the name, like
Java packages. The four log levels are <code class="docutils literal notranslate"><span class="pre">DEBUG</span></code>, <code class="docutils literal notranslate"><span class="pre">INFO</span></code>, <code class="docutils literal notranslate"><span class="pre">WARN</span></code> and <code class="docutils literal notranslate"><span class="pre">ERROR</span></code>,
sorted by decreasing verbosity.</p>
<p>For example, consider the following log levels file:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">io.trino</span><span class="o">=</span><span class="s">WARN</span>
<span class="na">io.trino.plugin.iceberg</span><span class="o">=</span><span class="s">DEBUG</span>
<span class="na">io.trino.parquet</span><span class="o">=</span><span class="s">DEBUG</span>
</pre></div>
</div>
<p>The preceding configuration sets the changes the level for all loggers in the
<code class="docutils literal notranslate"><span class="pre">io.trino</span></code> namespace to <code class="docutils literal notranslate"><span class="pre">WARN</span></code> as an update from the default <code class="docutils literal notranslate"><span class="pre">INFO</span></code> to make
logging less verbose. The example also increases logging verbosity for the
Iceberg connector using the <code class="docutils literal notranslate"><span class="pre">io.trino.plugin.iceberg</span></code> namespace, and the Parquet
file reader and writer support located in the <code class="docutils literal notranslate"><span class="pre">io.trino.parquet</span></code> namespace to
<code class="docutils literal notranslate"><span class="pre">DEBUG</span></code> for troubleshooting purposes.</p>
<p>Additional loggers can include other package namespaces from libraries and
dependencies embedded within Trino or part of the Java runtime, for example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">io.airlift</span></code> for the <a class="reference external" href="https://github.com/airlift/airlift">Airlift</a> application
framework used by Trino.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">org.eclipse.jetty</span></code> for the <a class="reference external" href="https://jetty.org/">Eclipse Jetty</a> web server
used by Trino.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">org.postgresql</span></code> for the <a class="reference external" href="https://github.com/pgjdbc">PostgresSQL JDBC driver</a>
used by the PostgreSQL connector.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">javax.net.ssl</span></code> for TLS from the Java runtime.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">java.io</span></code> for I/O operations.</p></li>
</ul>
<p>There are numerous additional properties available to customize logging in
<a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>, with details documented in <a class="reference internal" href="properties-logging.html"><span class="doc std std-doc">Logging properties</span></a>
and in following example sections.</p>
</section>
<section id="log-output">
<h2 id="log-output">Log output<a class="headerlink" href="logging.html#log-output" title="Link to this heading">#</a></h2>
<p>By default, logging output is file-based with rotated files in <code class="docutils literal notranslate"><span class="pre">var/log</span></code>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">launcher.log</span></code> for logging out put from the application startup from the
<a class="reference internal" href="../installation/deployment.html#running-trino"><span class="std std-ref">launcher</span></a>. Only used if the launcher starts Trino in the
background, and therefore not used in the Trino container.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">http-request.log</span></code> for HTTP request logs, mostly from the <a class="reference internal" href="../client/client-protocol.html"><span class="doc std std-doc">client
protocol</span></a> and the <a class="reference internal" href="web-interface.html"><span class="doc std std-doc">Web UI</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">server.log</span></code> for the main application log of Trino, including logging from all
plugins.</p></li>
</ul>
</section>
<section id="json-and-tcp-channel-logging">
<h2 id="json-and-tcp-channel-logging">JSON and TCP channel logging<a class="headerlink" href="logging.html#json-and-tcp-channel-logging" title="Link to this heading">#</a></h2>
<p>Trino supports logging to JSON-formatted output files with the configuration
<code class="docutils literal notranslate"><span class="pre">log.format=json</span></code>. Optionally you can set <code class="docutils literal notranslate"><span class="pre">node.annotations-file</span></code> as path to a
properties file such as the following example:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">host_ip</span><span class="o">=</span><span class="s">*******</span>
<span class="na">service_name</span><span class="o">=</span><span class="s">trino</span>
<span class="na">node_name</span><span class="o">=</span><span class="s">${ENV:MY_NODE_NAME}</span>
<span class="na">pod_name</span><span class="o">=</span><span class="s">${ENV:MY_POD_NAME}</span>
<span class="na">pod_namespace</span><span class="o">=</span><span class="s">${ENV:MY_POD_NAMESPACE}</span>
</pre></div>
</div>
<p>The annotations file supports environment variable substitution, so that the
above example attaches the name of the Trino node as <code class="docutils literal notranslate"><span class="pre">pod_name</span></code> and other
information to every log line. When running Trino on Kubernetes, you have access
to <a class="reference external" href="https://kubernetes.io/docs/tasks/inject-data-application/environment-variable-expose-pod-information/">a lot of information to use in the
log</a>.</p>
<p>TCP logging allows you to log to a TCP socket instead of a file with the
configuration <code class="docutils literal notranslate"><span class="pre">log.path=tcp://&lt;server_ip&gt;:&lt;server_port&gt;</span></code>. The endpoint must be
available at the URL configured with <code class="docutils literal notranslate"><span class="pre">server_ip</span></code> and <code class="docutils literal notranslate"><span class="pre">server_port</span></code> and is
assumed to be stable.</p>
<p>You can use an application such as <a class="reference external" href="https://fluentbit.io/">fluentbit</a> as a
consumer for these JSON-formatted logs.</p>
<p>Example fluentbit configuration file <code class="docutils literal notranslate"><span class="pre">config.yaml</span></code>:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">pipeline</span><span class="p">:</span>
<span class="w">  </span><span class="nt">inputs</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">tcp</span>
<span class="w">    </span><span class="nt">tag</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">trino</span>
<span class="w">    </span><span class="nt">listen</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0.0.0.0</span>
<span class="w">    </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5170</span>
<span class="w">    </span><span class="nt">buffer_size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2048</span>
<span class="w">    </span><span class="nt">format</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">json</span>
<span class="w">  </span><span class="nt">outputs</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">stdout</span>
<span class="w">    </span><span class="nt">match</span><span class="p">:</span><span class="w"> </span><span class="s">'*'</span>
</pre></div>
</div>
<p>Start the application with the command:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>fluent-bit<span class="w"> </span>-c<span class="w"> </span>config.yaml
</pre></div>
</div>
<p>Use the following Trino properties configuration:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">log.path</span><span class="o">=</span><span class="s">tcp://localhost:5170</span>
<span class="na">log.format</span><span class="o">=</span><span class="s">json</span>
<span class="na">node.annotation-file</span><span class="o">=</span><span class="s">etc/annotations.properties</span>
</pre></div>
</div>
<p>File <code class="docutils literal notranslate"><span class="pre">etc/annotation.properties</span></code>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">host_ip</span><span class="o">=</span><span class="s">*******</span>
<span class="na">service_name</span><span class="o">=</span><span class="s">trino</span>
<span class="na">pod_name</span><span class="o">=</span><span class="s">${ENV:HOSTNAME}</span>
</pre></div>
</div>
<p>As a result, Trino logs appear as structured JSON log lines in fluentbit in the
user interface, and can also be <a class="reference external" href="https://docs.fluentbit.io/manual/pipeline/outputs">forwarded into a configured logging
system</a>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="preview-web-interface.html" title="Preview Web UI"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Preview Web UI </span>
              </div>
            </a>
          
          
            <a href="tuning.html" title="Tuning Trino"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Tuning Trino </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>