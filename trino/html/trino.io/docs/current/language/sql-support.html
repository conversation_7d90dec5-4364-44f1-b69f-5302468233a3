<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>SQL statement support &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="sql-support.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Data types" href="types.html" />
    <link rel="prev" title="SQL language" href="../language.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="sql-support.html#language/sql-support" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> SQL statement support </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> SQL statement support </label>
    
      <a href="sql-support.html#" class="md-nav__link md-nav__link--active">SQL statement support</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="sql-support.html#globally-available-statements" class="md-nav__link">Globally available statements</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="sql-support.html#catalog-management" class="md-nav__link">Catalog management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#read-operations" class="md-nav__link">Read operations</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#write-operations" class="md-nav__link">Write operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="sql-support.html#data-management" class="md-nav__link">Data management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#schema-and-table-management" class="md-nav__link">Schema and table management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#view-management" class="md-nav__link">View management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#materialized-view-management" class="md-nav__link">Materialized view management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#user-defined-function-management" class="md-nav__link">User-defined function management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#security-operations" class="md-nav__link">Security operations</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#transactions" class="md-nav__link">Transactions</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Data types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reserved.html" class="md-nav__link">Keywords and identifiers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comments.html" class="md-nav__link">Comments</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="sql-support.html#globally-available-statements" class="md-nav__link">Globally available statements</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="sql-support.html#catalog-management" class="md-nav__link">Catalog management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#read-operations" class="md-nav__link">Read operations</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#write-operations" class="md-nav__link">Write operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="sql-support.html#data-management" class="md-nav__link">Data management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#schema-and-table-management" class="md-nav__link">Schema and table management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#view-management" class="md-nav__link">View management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#materialized-view-management" class="md-nav__link">Materialized view management</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#user-defined-function-management" class="md-nav__link">User-defined function management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#security-operations" class="md-nav__link">Security operations</a>
        </li>
        <li class="md-nav__item"><a href="sql-support.html#transactions" class="md-nav__link">Transactions</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="sql-statement-support">
<h1 id="language-sql-support--page-root">SQL statement support<a class="headerlink" href="sql-support.html#language-sql-support--page-root" title="Link to this heading">#</a></h1>
<p>The SQL statement support in Trino can be categorized into several topics. Many
statements are part of the core engine and therefore available in all use cases.
For example, you can always set session properties or inspect an explain plan
and perform other actions with the <a class="reference internal" href="sql-support.html#sql-globally-available"><span class="std std-ref">globally available statements</span></a>.</p>
<p>However, the details and architecture of the connected data sources can limit
some SQL functionality. For example, if the data source does not support any
write operations, then a <a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a> statement cannot be executed against
the data source.</p>
<p>Similarly, if the underlying system does not have any security concepts, SQL
statements like <a class="reference internal" href="../sql/create-role.html"><span class="doc">CREATE ROLE</span></a> cannot be supported by Trino and the
connector.</p>
<p>The categories of these different topics are related to <a class="reference internal" href="sql-support.html#sql-read-operations"><span class="std std-ref">read operations</span></a>, <a class="reference internal" href="sql-support.html#sql-write-operations"><span class="std std-ref">write operations</span></a>,
<a class="reference internal" href="sql-support.html#sql-security-operations"><span class="std std-ref">security operations</span></a> and <a class="reference internal" href="sql-support.html#sql-transactions"><span class="std std-ref">transactions</span></a>.</p>
<p>Details of the support for specific statements is available with the
documentation for each connector.</p>
<section id="globally-available-statements">
<span id="sql-globally-available"></span><h2 id="globally-available-statements">Globally available statements<a class="headerlink" href="sql-support.html#globally-available-statements" title="Link to this heading">#</a></h2>
<p>The following statements are implemented in the core engine and available with
any connector:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/call.html"><span class="doc">CALL</span></a></p></li>
<li><p><a class="reference internal" href="../sql/deallocate-prepare.html"><span class="doc">DEALLOCATE PREPARE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/describe-input.html"><span class="doc">DESCRIBE INPUT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/describe-output.html"><span class="doc">DESCRIBE OUTPUT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/execute.html"><span class="doc">EXECUTE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/execute-immediate.html"><span class="doc">EXECUTE IMMEDIATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/explain.html"><span class="doc">EXPLAIN</span></a></p></li>
<li><p><a class="reference internal" href="../sql/explain-analyze.html"><span class="doc">EXPLAIN ANALYZE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/prepare.html"><span class="doc">PREPARE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/reset-session.html"><span class="doc">RESET SESSION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/set-session.html"><span class="doc">SET SESSION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/set-time-zone.html"><span class="doc">SET TIME ZONE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-functions.html"><span class="doc">SHOW FUNCTIONS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-session.html"><span class="doc">SHOW SESSION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/use.html"><span class="doc">USE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/values.html"><span class="doc">VALUES</span></a></p></li>
</ul>
<section id="catalog-management">
<span id="sql-catalog-management"></span><h3 id="catalog-management">Catalog management<a class="headerlink" href="sql-support.html#catalog-management" title="Link to this heading">#</a></h3>
<p>The following statements are used to <a class="reference internal" href="../admin/properties-catalog.html"><span class="doc std std-doc">manage dynamic
catalogs</span></a>:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-catalog.html"><span class="doc">CREATE CATALOG</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-catalog.html"><span class="doc">DROP CATALOG</span></a></p></li>
</ul>
</section>
</section>
<section id="read-operations">
<span id="sql-read-operations"></span><h2 id="read-operations">Read operations<a class="headerlink" href="sql-support.html#read-operations" title="Link to this heading">#</a></h2>
<p>The following statements provide read access to data and metadata exposed by a
connector accessing a data source. They are supported by all connectors:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a> including <a class="reference internal" href="../sql/match-recognize.html"><span class="doc">MATCH_RECOGNIZE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/describe.html"><span class="doc">DESCRIBE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-catalogs.html"><span class="doc">SHOW CATALOGS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-columns.html"><span class="doc">SHOW COLUMNS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-materialized-view.html"><span class="doc">SHOW CREATE MATERIALIZED VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-schema.html"><span class="doc">SHOW CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-table.html"><span class="doc">SHOW CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-view.html"><span class="doc">SHOW CREATE VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-grants.html"><span class="doc">SHOW GRANTS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-roles.html"><span class="doc">SHOW ROLES</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-schemas.html"><span class="doc">SHOW SCHEMAS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-tables.html"><span class="doc">SHOW TABLES</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-stats.html"><span class="doc">SHOW STATS</span></a></p></li>
</ul>
</section>
<section id="write-operations">
<span id="sql-write-operations"></span><h2 id="write-operations">Write operations<a class="headerlink" href="sql-support.html#write-operations" title="Link to this heading">#</a></h2>
<p>The following statements provide write access to data and metadata exposed
by a connector accessing a data source. Availability varies widely from
connector to connector:</p>
<section id="data-management">
<span id="sql-data-management"></span><h3 id="data-management">Data management<a class="headerlink" href="sql-support.html#data-management" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/update.html"><span class="doc">UPDATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/truncate.html"><span class="doc">TRUNCATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/merge.html"><span class="doc">MERGE</span></a></p></li>
</ul>
</section>
<section id="schema-and-table-management">
<span id="sql-schema-table-management"></span><h3 id="schema-and-table-management">Schema and table management<a class="headerlink" href="sql-support.html#schema-and-table-management" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-schema.html"><span class="doc">ALTER SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
</section>
<section id="view-management">
<span id="sql-view-management"></span><h3 id="view-management">View management<a class="headerlink" href="sql-support.html#view-management" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-view.html"><span class="doc">CREATE VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-view.html"><span class="doc">DROP VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-view.html"><span class="doc">ALTER VIEW</span></a></p></li>
</ul>
</section>
<section id="materialized-view-management">
<span id="sql-materialized-view-management"></span><h3 id="materialized-view-management">Materialized view management<a class="headerlink" href="sql-support.html#materialized-view-management" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-materialized-view.html"><span class="doc">CREATE MATERIALIZED VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-materialized-view.html"><span class="doc">ALTER MATERIALIZED VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-materialized-view.html"><span class="doc">DROP MATERIALIZED VIEW</span></a></p></li>
<li><p><a class="reference internal" href="../sql/refresh-materialized-view.html"><span class="doc">REFRESH MATERIALIZED VIEW</span></a></p></li>
</ul>
</section>
<section id="user-defined-function-management">
<span id="udf-management"></span><h3 id="user-defined-function-management">User-defined function management<a class="headerlink" href="sql-support.html#user-defined-function-management" title="Link to this heading">#</a></h3>
<p>The following statements are used to manage <a class="reference internal" href="../udf/introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-function.html"><span class="doc std std-doc">CREATE FUNCTION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-function.html"><span class="doc std std-doc">DROP FUNCTION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-functions.html"><span class="doc std std-doc">SHOW FUNCTIONS</span></a></p></li>
</ul>
</section>
</section>
<section id="security-operations">
<span id="sql-security-operations"></span><h2 id="security-operations">Security operations<a class="headerlink" href="sql-support.html#security-operations" title="Link to this heading">#</a></h2>
<p>The following statements provide security-related operations to security
configuration, data, and metadata exposed by a connector accessing a data
source. Most connectors do not support these operations:</p>
<p>Connector roles:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-role.html"><span class="doc">CREATE ROLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-role.html"><span class="doc">DROP ROLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/grant-roles.html"><span class="doc">GRANT role</span></a></p></li>
<li><p><a class="reference internal" href="../sql/revoke-roles.html"><span class="doc">REVOKE role</span></a></p></li>
<li><p><a class="reference internal" href="../sql/set-role.html"><span class="doc">SET ROLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-role-grants.html"><span class="doc">SHOW ROLE GRANTS</span></a></p></li>
</ul>
<p>Grants management:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/deny.html"><span class="doc">DENY</span></a></p></li>
<li><p><a class="reference internal" href="../sql/grant.html"><span class="doc">GRANT privilege</span></a></p></li>
<li><p><a class="reference internal" href="../sql/revoke.html"><span class="doc">REVOKE privilege</span></a></p></li>
</ul>
</section>
<section id="transactions">
<span id="sql-transactions"></span><h2 id="transactions">Transactions<a class="headerlink" href="sql-support.html#transactions" title="Link to this heading">#</a></h2>
<p>The following statements manage transactions. Most connectors do not support
transactions:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/start-transaction.html"><span class="doc">START TRANSACTION</span></a></p></li>
<li><p><a class="reference internal" href="../sql/commit.html"><span class="doc">COMMIT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/rollback.html"><span class="doc">ROLLBACK</span></a></p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../language.html" title="SQL language"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> SQL language </span>
              </div>
            </a>
          
          
            <a href="types.html" title="Data types"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Data types </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>