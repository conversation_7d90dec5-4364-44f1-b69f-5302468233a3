<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Data types &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="types.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Keywords and identifiers" href="reserved.html" />
    <link rel="prev" title="SQL statement support" href="sql-support.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="types.html#language/types" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Data types </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="sql-support.html" class="md-nav__link">SQL statement support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Data types </label>
    
      <a href="types.html#" class="md-nav__link md-nav__link--active">Data types</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="types.html#trino-type-support-and-mapping" class="md-nav__link">Trino type support and mapping</a>
        </li>
        <li class="md-nav__item"><a href="types.html#boolean" class="md-nav__link">Boolean</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#id1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#integer" class="md-nav__link">Integer</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#tinyint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#smallint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#integer-or-int" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> or <code class="docutils literal notranslate"><span class="pre">INT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#bigint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#floating-point" class="md-nav__link">Floating-point</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#real" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">REAL</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#double" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#exact-numeric" class="md-nav__link">Exact numeric</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#decimal" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#string" class="md-nav__link">String</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#varchar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#char" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">CHAR</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#varbinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#json" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">JSON</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#date-and-time" class="md-nav__link">Date and time</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DATE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time-p" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME(P)</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-p" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-p-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#interval-year-to-month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">YEAR</span> <span class="pre">TO</span> <span class="pre">MONTH</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#interval-day-to-second" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">DAY</span> <span class="pre">TO</span> <span class="pre">SECOND</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#structural" class="md-nav__link">Structural</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#array" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#map" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">MAP</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#row" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ROW</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#network-address" class="md-nav__link">Network address</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#ipaddress" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#uuid" class="md-nav__link">UUID</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#uuid-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">UUID</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#hyperloglog" class="md-nav__link">HyperLogLog</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#hyperloglog-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">HyperLogLog</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#p4hyperloglog" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">P4HyperLogLog</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#setdigest" class="md-nav__link">SetDigest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#setdigest-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">SetDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#quantile-digest" class="md-nav__link">Quantile digest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#qdigest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">QDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#t-digest" class="md-nav__link">T-Digest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#tdigest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reserved.html" class="md-nav__link">Keywords and identifiers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comments.html" class="md-nav__link">Comments</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="types.html#trino-type-support-and-mapping" class="md-nav__link">Trino type support and mapping</a>
        </li>
        <li class="md-nav__item"><a href="types.html#boolean" class="md-nav__link">Boolean</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#id1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#integer" class="md-nav__link">Integer</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#tinyint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#smallint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#integer-or-int" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> or <code class="docutils literal notranslate"><span class="pre">INT</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#bigint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#floating-point" class="md-nav__link">Floating-point</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#real" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">REAL</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#double" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#exact-numeric" class="md-nav__link">Exact numeric</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#decimal" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#string" class="md-nav__link">String</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#varchar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#char" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">CHAR</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#varbinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#json" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">JSON</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#date-and-time" class="md-nav__link">Date and time</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">DATE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time-p" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME(P)</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#time-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-p" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#timestamp-p-with-time-zone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#interval-year-to-month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">YEAR</span> <span class="pre">TO</span> <span class="pre">MONTH</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#interval-day-to-second" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">DAY</span> <span class="pre">TO</span> <span class="pre">SECOND</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#structural" class="md-nav__link">Structural</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#array" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#map" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">MAP</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#row" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ROW</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#network-address" class="md-nav__link">Network address</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#ipaddress" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#uuid" class="md-nav__link">UUID</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#uuid-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">UUID</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#hyperloglog" class="md-nav__link">HyperLogLog</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#hyperloglog-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">HyperLogLog</span></code></a>
        </li>
        <li class="md-nav__item"><a href="types.html#p4hyperloglog" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">P4HyperLogLog</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#setdigest" class="md-nav__link">SetDigest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#setdigest-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">SetDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#quantile-digest" class="md-nav__link">Quantile digest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#qdigest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">QDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="types.html#t-digest" class="md-nav__link">T-Digest</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="types.html#tdigest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">TDigest</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="data-types">
<h1 id="language-types--page-root">Data types<a class="headerlink" href="types.html#language-types--page-root" title="Link to this heading">#</a></h1>
<p>Trino has a set of built-in data types, described below. Additional types can be
<a class="reference internal" href="../develop/types.html"><span class="doc std std-doc">provided by plugins</span></a>.</p>
<section id="trino-type-support-and-mapping">
<span id="type-mapping-overview"></span><h2 id="trino-type-support-and-mapping">Trino type support and mapping<a class="headerlink" href="types.html#trino-type-support-and-mapping" title="Link to this heading">#</a></h2>
<p>Connectors to data sources are not required to support all Trino data types
described on this page. If there are data types similar to Trino’s that are used
on the data source, the connector may map the Trino and remote data types to
each other as needed.</p>
<p>Depending on the connector and the data source, type mapping may apply
in either direction as follows:</p>
<ul class="simple">
<li><p><strong>Data source to Trino</strong> mapping applies to any operation where columns in the
data source are read by Trino, such as a <a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a> statement, and the
underlying source data type needs to be represented by a Trino data type.</p></li>
<li><p><strong>Trino to data source</strong> mapping applies to any operation where the columns
or expressions in Trino need to be translated into data types or expressions
compatible with the underlying data source. For example,
<a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a> statements specify Trino types that are then
mapped to types on the remote data source. Predicates like <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> also use
these mappings in order to ensure that the predicate is translated to valid
syntax on the remote data source.</p></li>
</ul>
<p>Data type support and mappings vary depending on the connector. Refer to the
<a class="reference internal" href="../connector.html"><span class="doc">connector documentation</span></a> for more information.</p>
</section>
<section id="boolean">
<span id="boolean-data-types"></span><h2 id="boolean">Boolean<a class="headerlink" href="types.html#boolean" title="Link to this heading">#</a></h2>
<section id="id1">
<h3 id="id1"><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code><a class="headerlink" href="types.html#id1" title="Link to this heading">#</a></h3>
<p>This type captures boolean values <code class="docutils literal notranslate"><span class="pre">true</span></code> and <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
</section>
<section id="integer">
<span id="integer-data-types"></span><h2 id="integer">Integer<a class="headerlink" href="types.html#integer" title="Link to this heading">#</a></h2>
<p>Integer numbers can be expressed as numeric literals in the following formats:</p>
<ul class="simple">
<li><p>Decimal integer. Examples are <code class="docutils literal notranslate"><span class="pre">-7</span></code>, <code class="docutils literal notranslate"><span class="pre">0</span></code>, or <code class="docutils literal notranslate"><span class="pre">3</span></code>.</p></li>
<li><p>Hexadecimal integer composed of <code class="docutils literal notranslate"><span class="pre">0X</span></code> or <code class="docutils literal notranslate"><span class="pre">0x</span></code> and the value. Examples are
<code class="docutils literal notranslate"><span class="pre">0x0A</span></code> for decimal <code class="docutils literal notranslate"><span class="pre">10</span></code> or <code class="docutils literal notranslate"><span class="pre">0x11</span></code> for decimal <code class="docutils literal notranslate"><span class="pre">17</span></code>.</p></li>
<li><p>Octal integer composed of <code class="docutils literal notranslate"><span class="pre">0O</span></code> or <code class="docutils literal notranslate"><span class="pre">0o</span></code> and the value. Examples are <code class="docutils literal notranslate"><span class="pre">0o40</span></code> for
decimal <code class="docutils literal notranslate"><span class="pre">32</span></code> or <code class="docutils literal notranslate"><span class="pre">0o11</span></code> for decimal <code class="docutils literal notranslate"><span class="pre">9</span></code>.</p></li>
<li><p>Binary integer composed of <code class="docutils literal notranslate"><span class="pre">0B</span></code> or <code class="docutils literal notranslate"><span class="pre">0b</span></code> and the value. Examples are <code class="docutils literal notranslate"><span class="pre">0b1001</span></code>
for decimal <code class="docutils literal notranslate"><span class="pre">9</span></code> or <code class="docutils literal notranslate"><span class="pre">0b101010</span></code> for decimal `42``.</p></li>
</ul>
<p>Underscore characters are ignored within literal values, and can be used to
increase readability. For example, decimal integer <code class="docutils literal notranslate"><span class="pre">123_456</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">123456</span></code>. Preceding underscores, trailing underscores, and consecutive underscores
are not permitted.</p>
<p>Integers are supported by the following data types.</p>
<section id="tinyint">
<h3 id="tinyint"><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code><a class="headerlink" href="types.html#tinyint" title="Link to this heading">#</a></h3>
<p>A 8-bit signed two’s complement integer with a minimum value of
<code class="docutils literal notranslate"><span class="pre">-2^7</span></code> or <code class="docutils literal notranslate"><span class="pre">-0x80</span></code> and a maximum value of <code class="docutils literal notranslate"><span class="pre">2^7</span> <span class="pre">-</span> <span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0x7F</span></code>.</p>
</section>
<section id="smallint">
<h3 id="smallint"><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code><a class="headerlink" href="types.html#smallint" title="Link to this heading">#</a></h3>
<p>A 16-bit signed two’s complement integer with a minimum value of
<code class="docutils literal notranslate"><span class="pre">-2^15</span></code> or <code class="docutils literal notranslate"><span class="pre">-0x8000</span></code> and a maximum value of <code class="docutils literal notranslate"><span class="pre">2^15</span> <span class="pre">-</span> <span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0x7FFF</span></code>.</p>
</section>
<section id="integer-or-int">
<h3 id="integer-or-int"><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> or <code class="docutils literal notranslate"><span class="pre">INT</span></code><a class="headerlink" href="types.html#integer-or-int" title="Link to this heading">#</a></h3>
<p>A 32-bit signed two’s complement integer with a minimum value of <code class="docutils literal notranslate"><span class="pre">-2^31</span></code> or
<code class="docutils literal notranslate"><span class="pre">-0x80000000</span></code> and a maximum value of <code class="docutils literal notranslate"><span class="pre">2^31</span> <span class="pre">-</span> <span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0x7FFFFFFF</span></code>.  The names
<code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> and <code class="docutils literal notranslate"><span class="pre">INT</span></code> can both be used for this type.</p>
</section>
<section id="bigint">
<h3 id="bigint"><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code><a class="headerlink" href="types.html#bigint" title="Link to this heading">#</a></h3>
<p>A 64-bit signed two’s complement integer with a minimum value of <code class="docutils literal notranslate"><span class="pre">-2^63</span></code> or
<code class="docutils literal notranslate"><span class="pre">-0x8000000000000000</span></code> and a maximum value of <code class="docutils literal notranslate"><span class="pre">2^63</span> <span class="pre">-</span> <span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0x7FFFFFFFFFFFFFFF</span></code>.</p>
</section>
</section>
<section id="floating-point">
<span id="floating-point-data-types"></span><h2 id="floating-point">Floating-point<a class="headerlink" href="types.html#floating-point" title="Link to this heading">#</a></h2>
<p>Floating-point, fixed-precision numbers can be expressed as numeric literal
using scientific notation such as <code class="docutils literal notranslate"><span class="pre">1.03e1</span></code> and are cast as <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> data type.
Underscore characters are ignored within literal values, and can be used to
increase readability. For example, value <code class="docutils literal notranslate"><span class="pre">123_456.789e4</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">123456.789e4</span></code>. Preceding underscores, trailing underscores, consecutive
underscores, and underscores beside the comma (<code class="docutils literal notranslate"><span class="pre">.</span></code>) are not permitted.</p>
<section id="real">
<h3 id="real"><code class="docutils literal notranslate"><span class="pre">REAL</span></code><a class="headerlink" href="types.html#real" title="Link to this heading">#</a></h3>
<p>A real is a 32-bit inexact, variable-precision implementing the
IEEE Standard 754 for Binary Floating-Point Arithmetic.</p>
<p>Example literals: <code class="docutils literal notranslate"><span class="pre">REAL</span> <span class="pre">'10.3'</span></code>, <code class="docutils literal notranslate"><span class="pre">REAL</span> <span class="pre">'10.3e0'</span></code>, <code class="docutils literal notranslate"><span class="pre">REAL</span> <span class="pre">'1.03e1'</span></code></p>
</section>
<section id="double">
<h3 id="double"><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code><a class="headerlink" href="types.html#double" title="Link to this heading">#</a></h3>
<p>A double is a 64-bit inexact, variable-precision implementing the
IEEE Standard 754 for Binary Floating-Point Arithmetic.</p>
<p>Example literals: <code class="docutils literal notranslate"><span class="pre">DOUBLE</span> <span class="pre">'10.3'</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span> <span class="pre">'1.03e1'</span></code>, <code class="docutils literal notranslate"><span class="pre">10.3e0</span></code>, <code class="docutils literal notranslate"><span class="pre">1.03e1</span></code></p>
</section>
</section>
<section id="exact-numeric">
<span id="exact-numeric-data-types"></span><h2 id="exact-numeric">Exact numeric<a class="headerlink" href="types.html#exact-numeric" title="Link to this heading">#</a></h2>
<p>Exact numeric values can be expressed as numeric literals such as <code class="docutils literal notranslate"><span class="pre">1.1</span></code>, and
are supported by the <code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code> data type.</p>
<p>Underscore characters are ignored within literal values, and can be used to
increase readability. For example, decimal <code class="docutils literal notranslate"><span class="pre">123_456.789_123</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">123456.789123</span></code>. Preceding underscores, trailing underscores, consecutive
underscores, and underscores beside the comma (<code class="docutils literal notranslate"><span class="pre">.</span></code>) are not permitted.</p>
<p>Leading zeros in literal values are permitted and ignored. For example,
<code class="docutils literal notranslate"><span class="pre">000123.456</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">123.456</span></code>.</p>
<section id="decimal">
<h3 id="decimal"><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code><a class="headerlink" href="types.html#decimal" title="Link to this heading">#</a></h3>
<p>A exact decimal number. Precision up to 38 digits is supported but performance
is best up to 18 digits.</p>
<p>The decimal type takes two literal parameters:</p>
<ul class="simple">
<li><p><strong>precision</strong> - total number of digits</p></li>
<li><p><strong>scale</strong> - number of digits in fractional part. Scale is optional and defaults to 0.</p></li>
</ul>
<p>Example type definitions: <code class="docutils literal notranslate"><span class="pre">DECIMAL(10,3)</span></code>, <code class="docutils literal notranslate"><span class="pre">DECIMAL(20)</span></code></p>
<p>Example literals: <code class="docutils literal notranslate"><span class="pre">DECIMAL</span> <span class="pre">'10.3'</span></code>, <code class="docutils literal notranslate"><span class="pre">DECIMAL</span> <span class="pre">'1234567890'</span></code>, <code class="docutils literal notranslate"><span class="pre">1.1</span></code></p>
</section>
</section>
<section id="string">
<span id="string-data-types"></span><h2 id="string">String<a class="headerlink" href="types.html#string" title="Link to this heading">#</a></h2>
<section id="varchar">
<h3 id="varchar"><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code><a class="headerlink" href="types.html#varchar" title="Link to this heading">#</a></h3>
<p>Variable length character data with an optional maximum length.</p>
<p>Example type definitions: <code class="docutils literal notranslate"><span class="pre">varchar</span></code>, <code class="docutils literal notranslate"><span class="pre">varchar(20)</span></code></p>
<p>SQL statements support simple literal, as well as Unicode usage:</p>
<ul class="simple">
<li><p>literal string : <code class="docutils literal notranslate"><span class="pre">'Hello</span> <span class="pre">winter</span> <span class="pre">!'</span></code></p></li>
<li><p>Unicode string with default escape character: <code class="docutils literal notranslate"><span class="pre">U&amp;'Hello</span> <span class="pre">winter</span> <span class="pre">\2603</span> <span class="pre">!'</span></code></p></li>
<li><p>Unicode string with custom escape character: <code class="docutils literal notranslate"><span class="pre">U&amp;'Hello</span> <span class="pre">winter</span> <span class="pre">#2603</span> <span class="pre">!'</span> <span class="pre">UESCAPE</span> <span class="pre">'#'</span></code></p></li>
</ul>
<p>A Unicode string is prefixed with <code class="docutils literal notranslate"><span class="pre">U&amp;</span></code> and requires an escape character
before any Unicode character usage with 4 digits. In the examples above
<code class="docutils literal notranslate"><span class="pre">\2603</span></code> and <code class="docutils literal notranslate"><span class="pre">#2603</span></code> represent a snowman character. Long Unicode codes
with 6 digits require usage of the plus symbol before the code. For example,
you need to use <code class="docutils literal notranslate"><span class="pre">\+01F600</span></code> for a grinning face emoji.</p>
<p>Single quotes in string literals can be escaped by using another single quote:
<code class="docutils literal notranslate"><span class="pre">'I</span> <span class="pre">am</span> <span class="pre">big,</span> <span class="pre">it''s</span> <span class="pre">the</span> <span class="pre">pictures</span> <span class="pre">that</span> <span class="pre">got</span> <span class="pre">small!'</span></code></p>
</section>
<section id="char">
<h3 id="char"><code class="docutils literal notranslate"><span class="pre">CHAR</span></code><a class="headerlink" href="types.html#char" title="Link to this heading">#</a></h3>
<p>Fixed length character data. A <code class="docutils literal notranslate"><span class="pre">CHAR</span></code> type without length specified has a
default length of 1. A <code class="docutils literal notranslate"><span class="pre">CHAR(x)</span></code> value always has a fixed length of <code class="docutils literal notranslate"><span class="pre">x</span></code>
characters. For example, casting <code class="docutils literal notranslate"><span class="pre">dog</span></code> to <code class="docutils literal notranslate"><span class="pre">CHAR(7)</span></code> adds four implicit trailing
spaces.</p>
<p>As with <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, a single quote in a <code class="docutils literal notranslate"><span class="pre">CHAR</span></code> literal can be escaped with
another single quote:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="nb">CHAR</span><span class="w"> </span><span class="s1">'All right, Mr. DeMille, I''m ready for my close-up.'</span>
</pre></div>
</div>
<p>Example type definitions: <code class="docutils literal notranslate"><span class="pre">char</span></code>, <code class="docutils literal notranslate"><span class="pre">char(20)</span></code></p>
</section>
<section id="varbinary">
<h3 id="varbinary"><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code><a class="headerlink" href="types.html#varbinary" title="Link to this heading">#</a></h3>
<p>Variable length binary data.</p>
<p>SQL statements support usage of binary literal data with the prefix <code class="docutils literal notranslate"><span class="pre">X</span></code> or <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The binary data has to use hexadecimal format. For example, the binary form of
<code class="docutils literal notranslate"><span class="pre">eh?</span></code> is <code class="docutils literal notranslate"><span class="pre">X'65683F'</span></code> as you can confirm with the following statement:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_utf8</span><span class="p">(</span><span class="n">x</span><span class="s1">'65683F'</span><span class="p">);</span>
</pre></div>
</div>
<p>Binary literals ignore any whitespace characters. For example, the literal
<code class="docutils literal notranslate"><span class="pre">X'FFFF</span> <span class="pre">0FFF</span>  <span class="pre">3FFF</span> <span class="pre">FFFF'</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">X'FFFF0FFF3FFFFFFF'</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Binary strings with length are not yet supported: <code class="docutils literal notranslate"><span class="pre">varbinary(n)</span></code></p>
</div>
</section>
<section id="json">
<h3 id="json"><code class="docutils literal notranslate"><span class="pre">JSON</span></code><a class="headerlink" href="types.html#json" title="Link to this heading">#</a></h3>
<p>JSON value type, which can be a JSON object, a JSON array, a JSON number, a JSON string,
<code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code> or <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
</section>
</section>
<section id="date-and-time">
<span id="date-time-data-types"></span><h2 id="date-and-time">Date and time<a class="headerlink" href="types.html#date-and-time" title="Link to this heading">#</a></h2>
<p>See also <a class="reference internal" href="../functions/datetime.html"><span class="doc">Date and time functions and operators</span></a></p>
<section id="date">
<span id="date-data-type"></span><h3 id="date"><code class="docutils literal notranslate"><span class="pre">DATE</span></code><a class="headerlink" href="types.html#date" title="Link to this heading">#</a></h3>
<p>Calendar date (year, month, day).</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">DATE</span> <span class="pre">'2001-08-22'</span></code></p>
</section>
<section id="time">
<h3 id="time"><code class="docutils literal notranslate"><span class="pre">TIME</span></code><a class="headerlink" href="types.html#time" title="Link to this heading">#</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">TIME</span></code> is an alias for <code class="docutils literal notranslate"><span class="pre">TIME(3)</span></code> (millisecond precision).</p>
</section>
<section id="time-p">
<h3 id="time-p"><code class="docutils literal notranslate"><span class="pre">TIME(P)</span></code><a class="headerlink" href="types.html#time-p" title="Link to this heading">#</a></h3>
<p>Time of day (hour, minute, second) without a time zone with <code class="docutils literal notranslate"><span class="pre">P</span></code> digits of precision
for the fraction of seconds. A precision of up to 12 (picoseconds) is supported.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">'01:02:03.456'</span></code></p>
</section>
<section id="time-with-time-zone">
<span id="time-with-time-zone-data-type"></span><h3 id="time-with-time-zone"><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code><a class="headerlink" href="types.html#time-with-time-zone" title="Link to this heading">#</a></h3>
<p>Time of day (hour, minute, second, millisecond) with a time zone.
Values of this type are rendered using the time zone from the value.
Time zones are expressed as the numeric UTC offset value:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="s1">'01:02:03.456 -08:00'</span><span class="p">;</span>
<span class="c1">-- 1:02:03.456-08:00</span>
</pre></div>
</div>
</section>
<section id="timestamp">
<span id="timestamp-data-type"></span><h3 id="timestamp"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code><a class="headerlink" href="types.html#timestamp" title="Link to this heading">#</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> is an alias for <code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span></code> (millisecond precision).</p>
</section>
<section id="timestamp-p">
<h3 id="timestamp-p"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span></code><a class="headerlink" href="types.html#timestamp-p" title="Link to this heading">#</a></h3>
<p>Calendar date and time of day without a time zone with <code class="docutils literal notranslate"><span class="pre">P</span></code> digits of precision
for the fraction of seconds. A precision of up to 12 (picoseconds) is supported.
This type is effectively a combination of the <code class="docutils literal notranslate"><span class="pre">DATE</span></code> and <code class="docutils literal notranslate"><span class="pre">TIME(P)</span></code> types.</p>
<p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span> <span class="pre">WITHOUT</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> is an equivalent name.</p>
<p>Timestamp values can be constructed with the <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> literal
expression. Alternatively, language constructs such as
<code class="docutils literal notranslate"><span class="pre">localtimestamp(p)</span></code>, or a number of <a class="reference internal" href="../functions/datetime.html"><span class="doc">date and time functions and operators</span></a> can return timestamp values.</p>
<p>Casting to lower precision causes the value to be rounded, and not
truncated. Casting to higher precision appends zeros for the additional
digits.</p>
<p>The following examples illustrate the behavior:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-10 15:55:23'</span><span class="p">;</span>
<span class="c1">-- 2020-06-10 15:55:23</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-10 15:55:23.383345'</span><span class="p">;</span>
<span class="c1">-- 2020-06-10 15:55:23.383345</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">typeof</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-10 15:55:23.383345'</span><span class="p">);</span>
<span class="c1">-- timestamp(6)</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-10 15:55:23.383345'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="p">(</span><span class="mi">1</span><span class="p">));</span>
<span class="w"> </span><span class="c1">-- 2020-06-10 15:55:23.4</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-10 15:55:23.383345'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="p">(</span><span class="mi">12</span><span class="p">));</span>
<span class="c1">-- 2020-06-10 15:55:23.383345000000</span>
</pre></div>
</div>
</section>
<section id="timestamp-with-time-zone">
<span id="timestamp-with-time-zone-data-type"></span><h3 id="timestamp-with-time-zone"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code><a class="headerlink" href="types.html#timestamp-with-time-zone" title="Link to this heading">#</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> is an alias for <code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code>
(millisecond precision).</p>
</section>
<section id="timestamp-p-with-time-zone">
<span id="timestamp-p-with-time-zone-data-type"></span><h3 id="timestamp-p-with-time-zone"><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(P)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code><a class="headerlink" href="types.html#timestamp-p-with-time-zone" title="Link to this heading">#</a></h3>
<p>Instant in time that includes the date and time of day with <code class="docutils literal notranslate"><span class="pre">P</span></code> digits of
precision for the fraction of seconds and with a time zone. Values of this type
are rendered using the time zone from the value. Time zones can be expressed in
the following ways:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">UTC</span></code>, with <code class="docutils literal notranslate"><span class="pre">GMT</span></code>, <code class="docutils literal notranslate"><span class="pre">Z</span></code>, or <code class="docutils literal notranslate"><span class="pre">UT</span></code> usable as aliases for UTC.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">+hh:mm</span></code> or <code class="docutils literal notranslate"><span class="pre">-hh:mm</span></code> with <code class="docutils literal notranslate"><span class="pre">hh:mm</span></code> as an hour and minute offset from UTC.
Can be written with or without <code class="docutils literal notranslate"><span class="pre">UTC</span></code>, <code class="docutils literal notranslate"><span class="pre">GMT</span></code>, or <code class="docutils literal notranslate"><span class="pre">UT</span></code> as an alias for
UTC.</p></li>
<li><p>An <a class="reference external" href="https://www.iana.org/time-zones">IANA time zone name</a>.</p></li>
</ul>
<p>The following examples demonstrate some of these syntax options:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2001-08-22 03:04:05.321 UTC'</span><span class="p">;</span>
<span class="c1">-- 2001-08-22 03:04:05.321 UTC</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2001-08-22 03:04:05.321 -08:30'</span><span class="p">;</span>
<span class="c1">-- 2001-08-22 03:04:05.321 -08:30</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2001-08-22 03:04:05.321 GMT-08:30'</span><span class="p">;</span>
<span class="c1">-- 2001-08-22 03:04:05.321 -08:30</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2001-08-22 03:04:05.321 America/New_York'</span><span class="p">;</span>
<span class="c1">-- 2001-08-22 03:04:05.321 America/New_York</span>
</pre></div>
</div>
</section>
<section id="interval-year-to-month">
<h3 id="interval-year-to-month"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">YEAR</span> <span class="pre">TO</span> <span class="pre">MONTH</span></code><a class="headerlink" href="types.html#interval-year-to-month" title="Link to this heading">#</a></h3>
<p>Span of years and months.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">'3'</span> <span class="pre">MONTH</span></code></p>
</section>
<section id="interval-day-to-second">
<h3 id="interval-day-to-second"><code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">DAY</span> <span class="pre">TO</span> <span class="pre">SECOND</span></code><a class="headerlink" href="types.html#interval-day-to-second" title="Link to this heading">#</a></h3>
<p>Span of days, hours, minutes, seconds and milliseconds.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">INTERVAL</span> <span class="pre">'2'</span> <span class="pre">DAY</span></code></p>
</section>
</section>
<section id="structural">
<span id="structural-data-types"></span><h2 id="structural">Structural<a class="headerlink" href="types.html#structural" title="Link to this heading">#</a></h2>
<section id="array">
<span id="array-type"></span><h3 id="array"><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code><a class="headerlink" href="types.html#array" title="Link to this heading">#</a></h3>
<p>An array of the given component type.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">ARRAY[1,</span> <span class="pre">2,</span> <span class="pre">3]</span></code></p>
<p>More information in <a class="reference internal" href="../functions/array.html"><span class="doc std std-doc">Array functions and operators</span></a>.</p>
</section>
<section id="map">
<span id="map-type"></span><h3 id="map"><code class="docutils literal notranslate"><span class="pre">MAP</span></code><a class="headerlink" href="types.html#map" title="Link to this heading">#</a></h3>
<p>A map between the given component types. A map is a collection of key-value
pairs, where each key is associated with a single value.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">MAP(ARRAY['foo',</span> <span class="pre">'bar'],</span> <span class="pre">ARRAY[1,</span> <span class="pre">2])</span></code></p>
<p>More information in <a class="reference internal" href="../functions/map.html"><span class="doc std std-doc">Map functions and operators</span></a>.</p>
</section>
<section id="row">
<span id="row-type"></span><h3 id="row"><code class="docutils literal notranslate"><span class="pre">ROW</span></code><a class="headerlink" href="types.html#row" title="Link to this heading">#</a></h3>
<p>A structure made up of fields that allows mixed types.
The fields may be of any SQL type.</p>
<p>By default, row fields are not named, but names can be assigned.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">CAST(ROW(1,</span> <span class="pre">2e0)</span> <span class="pre">AS</span> <span class="pre">ROW(x</span> <span class="pre">BIGINT,</span> <span class="pre">y</span> <span class="pre">DOUBLE))</span></code></p>
<p>Named row fields are accessed with field reference operator (<code class="docutils literal notranslate"><span class="pre">.</span></code>).</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">CAST(ROW(1,</span> <span class="pre">2.0)</span> <span class="pre">AS</span> <span class="pre">ROW(x</span> <span class="pre">BIGINT,</span> <span class="pre">y</span> <span class="pre">DOUBLE)).x</span></code></p>
<p>Named or unnamed row fields are accessed by position with the subscript
operator (<code class="docutils literal notranslate"><span class="pre">[]</span></code>). The position starts at <code class="docutils literal notranslate"><span class="pre">1</span></code> and must be a constant.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">ROW(1,</span> <span class="pre">2.0)[1]</span></code></p>
</section>
</section>
<section id="network-address">
<h2 id="network-address">Network address<a class="headerlink" href="types.html#network-address" title="Link to this heading">#</a></h2>
<section id="ipaddress">
<span id="ipaddress-type"></span><h3 id="ipaddress"><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code><a class="headerlink" href="types.html#ipaddress" title="Link to this heading">#</a></h3>
<p>An IP address that can represent either an IPv4 or IPv6 address. Internally,
the type is a pure IPv6 address. Support for IPv4 is handled using the
<em>IPv4-mapped IPv6 address</em> range (<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4291.html#section-*******"><strong>RFC 4291#section-*******</strong></a>).
When creating an <code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code>, IPv4 addresses will be mapped into that range.
When formatting an <code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code>, any address within the mapped range will
be formatted as an IPv4 address. Other addresses will be formatted as IPv6
using the canonical format defined in <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5952.html"><strong>RFC 5952</strong></a>.</p>
<p>Examples: <code class="docutils literal notranslate"><span class="pre">IPADDRESS</span> <span class="pre">'********'</span></code>, <code class="docutils literal notranslate"><span class="pre">IPADDRESS</span> <span class="pre">'2001:db8::1'</span></code></p>
</section>
</section>
<section id="uuid">
<h2 id="uuid">UUID<a class="headerlink" href="types.html#uuid" title="Link to this heading">#</a></h2>
<section id="uuid-type">
<span id="id2"></span><h3 id="uuid-type"><code class="docutils literal notranslate"><span class="pre">UUID</span></code><a class="headerlink" href="types.html#uuid-type" title="Link to this heading">#</a></h3>
<p>This type represents a UUID (Universally Unique IDentifier), also known as a
GUID (Globally Unique IDentifier), using the format defined in <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">UUID</span> <span class="pre">'12151fd2-7586-11e9-8f9e-2a86e4085a59'</span></code></p>
</section>
</section>
<section id="hyperloglog">
<h2 id="hyperloglog">HyperLogLog<a class="headerlink" href="types.html#hyperloglog" title="Link to this heading">#</a></h2>
<p>Calculating the approximate distinct count can be done much more cheaply than an exact count using the
<a class="reference external" href="https://wikipedia.org/wiki/HyperLogLog">HyperLogLog</a> data sketch. See <a class="reference internal" href="../functions/hyperloglog.html"><span class="doc">HyperLogLog functions</span></a>.</p>
<section id="hyperloglog-type">
<span id="id3"></span><h3 id="hyperloglog-type"><code class="docutils literal notranslate"><span class="pre">HyperLogLog</span></code><a class="headerlink" href="types.html#hyperloglog-type" title="Link to this heading">#</a></h3>
<p>A HyperLogLog sketch allows efficient computation of <a class="reference internal" href="../functions/aggregate.html#approx_distinct" title="approx_distinct"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_distinct()</span></code></a>. It starts as a
sparse representation, switching to a dense representation when it becomes more efficient.</p>
</section>
<section id="p4hyperloglog">
<span id="p4hyperloglog-type"></span><h3 id="p4hyperloglog"><code class="docutils literal notranslate"><span class="pre">P4HyperLogLog</span></code><a class="headerlink" href="types.html#p4hyperloglog" title="Link to this heading">#</a></h3>
<p>A P4HyperLogLog sketch is similar to <a class="reference internal" href="types.html#hyperloglog-type"><span class="std std-ref">HyperLogLog</span></a>, but it starts (and remains)
in the dense representation.</p>
</section>
</section>
<section id="setdigest">
<h2 id="setdigest">SetDigest<a class="headerlink" href="types.html#setdigest" title="Link to this heading">#</a></h2>
<section id="setdigest-type">
<span id="id4"></span><h3 id="setdigest-type"><code class="docutils literal notranslate"><span class="pre">SetDigest</span></code><a class="headerlink" href="types.html#setdigest-type" title="Link to this heading">#</a></h3>
<p>A SetDigest (setdigest) is a data sketch structure used
in calculating <a class="reference external" href="https://wikipedia.org/wiki/Jaccard_index">Jaccard similarity coefficient</a>
between two sets.</p>
<p>SetDigest encapsulates the following components:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://wikipedia.org/wiki/HyperLogLog">HyperLogLog</a></p></li>
<li><p><a class="reference external" href="https://wikipedia.org/wiki/MinHash#Variant_with_a_single_hash_function">MinHash with a single hash function</a></p></li>
</ul>
<p>The HyperLogLog structure is used for the approximation of the distinct elements
in the original set.</p>
<p>The MinHash structure is used to store a low memory footprint signature of the original set.
The similarity of any two sets is estimated by comparing their signatures.</p>
<p>SetDigests are additive, meaning they can be merged together.</p>
</section>
</section>
<section id="quantile-digest">
<h2 id="quantile-digest">Quantile digest<a class="headerlink" href="types.html#quantile-digest" title="Link to this heading">#</a></h2>
<section id="qdigest">
<span id="qdigest-type"></span><h3 id="qdigest"><code class="docutils literal notranslate"><span class="pre">QDigest</span></code><a class="headerlink" href="types.html#qdigest" title="Link to this heading">#</a></h3>
<p>A quantile digest (qdigest) is a summary structure which captures the approximate
distribution of data for a given input set, and can be queried to retrieve approximate
quantile values from the distribution.  The level of accuracy for a qdigest
is tunable, allowing for more precise results at the expense of space.</p>
<p>A qdigest can be used to give approximate answer to queries asking for what value
belongs at a certain quantile.  A useful property of qdigests is that they are
additive, meaning they can be merged together without losing precision.</p>
<p>A qdigest may be helpful whenever the partial results of <code class="docutils literal notranslate"><span class="pre">approx_percentile</span></code>
can be reused.  For example, one may be interested in a daily reading of the 99th
percentile values that are read over the course of a week.  Instead of calculating
the past week of data with <code class="docutils literal notranslate"><span class="pre">approx_percentile</span></code>, <code class="docutils literal notranslate"><span class="pre">qdigest</span></code>s could be stored
daily, and quickly merged to retrieve the 99th percentile value.</p>
</section>
</section>
<section id="t-digest">
<h2 id="t-digest">T-Digest<a class="headerlink" href="types.html#t-digest" title="Link to this heading">#</a></h2>
<section id="tdigest">
<span id="tdigest-type"></span><h3 id="tdigest"><code class="docutils literal notranslate"><span class="pre">TDigest</span></code><a class="headerlink" href="types.html#tdigest" title="Link to this heading">#</a></h3>
<p>A T-digest (tdigest) is a summary structure which, similarly to qdigest, captures the
approximate distribution of data for a given input set. It can be queried to retrieve
approximate quantile values from the distribution.</p>
<p>TDigest has the following advantages compared to QDigest:</p>
<ul class="simple">
<li><p>higher performance</p></li>
<li><p>lower memory usage</p></li>
<li><p>higher accuracy at high and low percentiles</p></li>
</ul>
<p>T-digests are additive, meaning they can be merged together.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="sql-support.html" title="SQL statement support"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> SQL statement support </span>
              </div>
            </a>
          
          
            <a href="reserved.html" title="Keywords and identifiers"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Keywords and identifiers </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>