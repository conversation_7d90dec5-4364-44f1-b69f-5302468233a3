<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Object storage &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="_static/trino.css@v=b5fc78e7.css" />
    <script src="_static/jquery.js@v=5d32c60e"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="_static/documentation_options.js@v=febf07ea"></script>
    <script src="_static/doctools.js@v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="object-storage.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Azure Storage file system support" href="object-storage/file-system-azure.html" />
    <link rel="prev" title="404 - Connector removed" href="connector/removed.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="object-storage.html#object-storage" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Object storage </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../versions.json",
        target_loc = "../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Object storage </label>
    
      <a href="object-storage.html#" class="md-nav__link md-nav__link--active">Object storage</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="object-storage.html#object-storage-connectors" class="md-nav__link">Object storage connectors</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#native-file-system-support" class="md-nav__link">Native file system support</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#legacy-file-system-support" class="md-nav__link">Legacy file system support</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#other-object-storage-support" class="md-nav__link">Other object storage support</a>
        </li>
    </ul>
</nav>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-azure.html" class="md-nav__link">Azure Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-s3.html" class="md-nav__link">S3 file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-hdfs.html" class="md-nav__link">HDFS file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-cache.html" class="md-nav__link">File system cache</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/metastores.html" class="md-nav__link">Metastores</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage/file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="object-storage.html#object-storage-connectors" class="md-nav__link">Object storage connectors</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#native-file-system-support" class="md-nav__link">Native file system support</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#legacy-file-system-support" class="md-nav__link">Legacy file system support</a>
        </li>
        <li class="md-nav__item"><a href="object-storage.html#other-object-storage-support" class="md-nav__link">Other object storage support</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="object-storage">
<h1 id="object-storage--page-root">Object storage<a class="headerlink" href="object-storage.html#object-storage--page-root" title="Link to this heading">#</a></h1>
<p>Object storage systems are commonly used to create data lakes or data lake
houses. These systems provide methods to store objects in a structured manner
and means to access them, for example using an API over HTTP. The objects are
files in various format including ORC, Parquet and others. Object storage
systems are available as service from public cloud providers and others vendors,
or can be self-hosted using commercial as well as open source offerings.</p>
<section id="object-storage-connectors">
<h2 id="object-storage-connectors">Object storage connectors<a class="headerlink" href="object-storage.html#object-storage-connectors" title="Link to this heading">#</a></h2>
<p>Trino accesses files directly on object storage and remote file system storage.
The following connectors use this direct approach to read and write data files.</p>
<ul class="simple">
<li><p><a class="reference internal" href="connector/delta-lake.html"><span class="doc std std-doc">Delta Lake connector</span></a></p></li>
<li><p><a class="reference internal" href="connector/hive.html"><span class="doc std std-doc">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="connector/hudi.html"><span class="doc std std-doc">Hudi connector</span></a></p></li>
<li><p><a class="reference internal" href="connector/iceberg.html"><span class="doc std std-doc">Iceberg connector</span></a></p></li>
</ul>
<p>The connectors all support a variety of protocols and formats used on these
object storage systems, and have separate requirements for metadata
availability.</p>
</section>
<section id="configuration">
<span id="file-system-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="object-storage.html#configuration" title="Link to this heading">#</a></h2>
<p>By default, no file system support is activated for your catalog. You must
select and configure one of the following properties to determine the support
for different file systems in the catalog. Each catalog can only use one file
system support.</p>
<table id="id1">
<caption><span class="caption-text">File system support properties</span><a class="headerlink" href="object-storage.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.native-azure.enabled</span></code></p></td>
<td><p>Activate the <a class="reference internal" href="object-storage/file-system-azure.html"><span class="doc std std-doc">native implementation for Azure Storage
support</span></a>. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.native-gcs.enabled</span></code></p></td>
<td><p>Activate the <a class="reference internal" href="object-storage/file-system-gcs.html"><span class="doc std std-doc">native implementation for Google Cloud Storage
support</span></a>. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.native-s3.enabled</span></code></p></td>
<td><p>Activate the <a class="reference internal" href="object-storage/file-system-s3.html"><span class="doc std std-doc">native implementation for S3 storage
support</span></a>. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.hadoop.enabled</span></code></p></td>
<td><p>Activate <a class="reference internal" href="object-storage/file-system-hdfs.html"><span class="doc std std-doc">support for HDFS</span></a> and <a class="reference internal" href="object-storage.html#file-system-legacy"><span class="std std-ref">legacy
support for other file systems</span></a> using the HDFS
libraries. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="native-file-system-support">
<span id="file-system-native"></span><h2 id="native-file-system-support">Native file system support<a class="headerlink" href="object-storage.html#native-file-system-support" title="Link to this heading">#</a></h2>
<p>Trino includes optimized implementations to access the following systems, and
compatible replacements:</p>
<ul class="simple">
<li><p><a class="reference internal" href="object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-local.html"><span class="doc std std-doc">Local file system support</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-alluxio.html"><span class="doc std std-doc">Alluxio file system support</span></a></p></li>
</ul>
<p>The native support is available in all four connectors, and must be activated
for use.</p>
</section>
<section id="legacy-file-system-support">
<span id="file-system-legacy"></span><h2 id="legacy-file-system-support">Legacy file system support<a class="headerlink" href="object-storage.html#legacy-file-system-support" title="Link to this heading">#</a></h2>
<p>The default behavior uses legacy libraries that originate from the Hadoop
ecosystem. It should only be used for accessing the Hadoop Distributed File
System (HDFS):</p>
<ul class="simple">
<li><p><a class="reference internal" href="object-storage/file-system-hdfs.html"><span class="doc std std-doc">HDFS file system support</span></a></p></li>
</ul>
<p>All four connectors can use the deprecated <code class="docutils literal notranslate"><span class="pre">hive.*</span></code> properties for access to
other object storage system as <em>legacy</em> support. These properties will be
removed in a future release. Additional documentation is available with the Hive
connector and relevant migration guides pages:</p>
<ul class="simple">
<li><p><a class="reference internal" href="connector/hive.html"><span class="doc std std-doc">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-azure.html#fs-legacy-azure-migration"><span class="std std-ref">Azure Storage migration from hive.azure.* properties</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-gcs.html#fs-legacy-gcs-migration"><span class="std std-ref">Google Cloud Storage migration from hive.gcs.* properties</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-s3.html#fs-legacy-s3-migration"><span class="std std-ref">S3 migration from hive.s3.* properties</span></a></p></li>
</ul>
</section>
<section id="other-object-storage-support">
<span id="object-storage-other"></span><h2 id="other-object-storage-support">Other object storage support<a class="headerlink" href="object-storage.html#other-object-storage-support" title="Link to this heading">#</a></h2>
<p>Trino also provides the following additional support and features for object
storage:</p>
<ul class="simple">
<li><p><a class="reference internal" href="object-storage/file-system-cache.html"><span class="doc std std-doc">File system cache</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-system-alluxio.html"><span class="doc std std-doc">Alluxio file system support</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/metastores.html"><span class="doc std std-doc">Metastores</span></a></p></li>
<li><p><a class="reference internal" href="object-storage/file-formats.html"><span class="doc std std-doc">Object storage file formats</span></a></p></li>
</ul>
<div class="toctree-wrapper compound">
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="connector/removed.html" title="404 - Connector removed"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> 404 - Connector removed </span>
              </div>
            </a>
          
          
            <a href="object-storage/file-system-azure.html" title="Azure Storage file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Azure Storage file system support </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>