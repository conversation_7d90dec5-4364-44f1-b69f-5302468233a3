<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Trino on Kubernetes with Helm &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="kubernetes.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Plugins" href="plugins.html" />
    <link rel="prev" title="Trino in a Docker container" href="containers.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="kubernetes.html#installation/kubernetes" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Trino on Kubernetes with Helm </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="deployment.html" class="md-nav__link">Deploying Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="containers.html" class="md-nav__link">Trino in a Docker container</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Trino on Kubernetes with Helm </label>
    
      <a href="kubernetes.html#" class="md-nav__link md-nav__link--active">Trino on Kubernetes with Helm</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kubernetes.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#running-trino-using-helm" class="md-nav__link">Running Trino using Helm</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#executing-queries" class="md-nav__link">Executing queries</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kubernetes.html#creating-your-own-yaml-configuration" class="md-nav__link">Creating your own YAML configuration</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#adding-catalogs" class="md-nav__link">Adding catalogs</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#running-a-local-kubernetes-cluster-with-kind" class="md-nav__link">Running a local Kubernetes cluster with kind</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#cleaning-up" class="md-nav__link">Cleaning up</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="plugins.html" class="md-nav__link">Plugins</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="query-resiliency.html" class="md-nav__link">Improve query processing resilience</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kubernetes.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#running-trino-using-helm" class="md-nav__link">Running Trino using Helm</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#executing-queries" class="md-nav__link">Executing queries</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kubernetes.html#creating-your-own-yaml-configuration" class="md-nav__link">Creating your own YAML configuration</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#adding-catalogs" class="md-nav__link">Adding catalogs</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#running-a-local-kubernetes-cluster-with-kind" class="md-nav__link">Running a local Kubernetes cluster with kind</a>
        </li>
        <li class="md-nav__item"><a href="kubernetes.html#cleaning-up" class="md-nav__link">Cleaning up</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="trino-on-kubernetes-with-helm">
<h1 id="installation-kubernetes--page-root">Trino on Kubernetes with Helm<a class="headerlink" href="kubernetes.html#installation-kubernetes--page-root" title="Link to this heading">#</a></h1>
<p><a class="reference external" href="https://kubernetes.io">Kubernetes</a> is a container orchestration platform that
allows you to deploy Trino and other applications in a repeatable manner across
different types of infrastructure. This can range from deploying on your laptop
using tools like <a class="reference external" href="https://kind.sigs.k8s.io">kind</a>, to running on a managed
Kubernetes service on cloud services like
<a class="reference external" href="https://aws.amazon.com/eks">Amazon Elastic Kubernetes Service</a>,
<a class="reference external" href="https://cloud.google.com/kubernetes-engine">Google Kubernetes Engine</a>,
<a class="reference external" href="https://azure.microsoft.com/services/kubernetes-service">Azure Kubernetes Service</a>,
and others.</p>
<p>The fastest way to run Trino on Kubernetes is to use the
<a class="reference external" href="https://github.com/trinodb/charts">Trino Helm chart</a>.
<a class="reference external" href="https://helm.sh">Helm</a> is a package manager for Kubernetes applications that
allows for simpler installation and versioning by templating Kubernetes
configuration files. This allows you to prototype on your local or on-premise
cluster and use the same deployment mechanism to deploy to the cloud to scale
up.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="kubernetes.html#requirements" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>A Kubernetes cluster with a
<a class="reference external" href="https://kubernetes.io/releases/">supported version</a> of Kubernetes.</p>
<ul>
<li><p>If you don’t have a Kubernetes cluster, you can
<a class="reference internal" href="kubernetes.html#running-a-local-kubernetes-cluster-with-kind"><span class="std std-ref">run one locally using kind</span></a>.</p></li>
</ul>
</li>
<li><p><a class="reference external" href="https://kubernetes.io/docs/tasks/tools/#kubectl">kubectl</a> with a version
that adheres to the
<a class="reference external" href="https://kubernetes.io/releases/version-skew-policy/">Kubernetes version skew policy</a>
installed on the machine managing the Kubernetes deployment.</p></li>
<li><p><a class="reference external" href="https://helm.sh">helm</a> with a version that adheres to the
<a class="reference external" href="https://helm.sh/docs/topics/version_skew/">Helm version skew policy</a>
installed on the machine managing the Kubernetes deployment.</p></li>
</ul>
</section>
<section id="running-trino-using-helm">
<span id="id1"></span><h2 id="running-trino-using-helm">Running Trino using Helm<a class="headerlink" href="kubernetes.html#running-trino-using-helm" title="Link to this heading">#</a></h2>
<p>Run the following commands from the system with <code class="docutils literal notranslate"><span class="pre">helm</span></code> and <code class="docutils literal notranslate"><span class="pre">kubectl</span></code>
installed and configured to connect to your running Kubernetes cluster:</p>
<ol class="arabic">
<li><p>Validate <code class="docutils literal notranslate"><span class="pre">kubectl</span></code> is pointing to the correct cluster by running the
command:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl cluster-info
</pre></div>
</div>
<p>You should see output that shows the correct Kubernetes control plane
address.</p>
</li>
<li><p>Add the Trino Helm chart repository to Helm if you haven’t done so already.
This tells Helm where to find the Trino charts. You can name the repository
whatever you want, <code class="docutils literal notranslate"><span class="pre">trino</span></code> is a good choice.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>helm repo add trino https://trinodb.github.io/charts
</pre></div>
</div>
</li>
<li><p>Install Trino on the Kubernetes cluster using the Helm chart. Start by
running the <code class="docutils literal notranslate"><span class="pre">install</span></code> command to use all default values and create
a cluster called <code class="docutils literal notranslate"><span class="pre">example-trino-cluster</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>helm install example-trino-cluster trino/trino
</pre></div>
</div>
<p>This generates the Kubernetes configuration files by inserting properties
into helm templates. The Helm chart contains
<a class="reference external" href="https://trinodb.github.io/charts/charts/trino/">default values</a>
that can be overridden by a YAML file to update default settings.</p>
<ol class="arabic">
<li><p><em>(Optional)</em> To override the default values,
<a class="reference internal" href="kubernetes.html#creating-your-own-yaml"><span class="std std-ref">create your own YAML configuration</span></a> to
define the parameters of your deployment. To run the install command using
the <code class="docutils literal notranslate"><span class="pre">example.yaml</span></code>, add the <code class="docutils literal notranslate"><span class="pre">f</span></code> parameter in you <code class="docutils literal notranslate"><span class="pre">install</span></code> command.
Be sure to follow
<a class="reference internal" href="kubernetes.html#kubernetes-configuration-best-practices"><span class="std std-ref">best practices and naming conventions</span></a>
for your configuration files.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>helm install -f example.yaml example-trino-cluster trino/trino
</pre></div>
</div>
</li>
</ol>
<p>You should see output as follows:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>NAME: example-trino-cluster
LAST DEPLOYED: Tue Sep 13 14:12:09 2022
NAMESPACE: default
STATUS: deployed
REVISION: 1
TEST SUITE: None
NOTES:
Get the application URL by running these commands:
  export POD_NAME=$(kubectl get pods --namespace default --selector "app.kubernetes.io/name=trino,app.kubernetes.io/instance=example-trino-cluster,app.kubernetes.io/component=coordinator" --output name)
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl port-forward $POD_NAME 8080:8080
</pre></div>
</div>
<p>This output depends on your configuration and cluster name. For example, the
port <code class="docutils literal notranslate"><span class="pre">8080</span></code> is set by the <code class="docutils literal notranslate"><span class="pre">.service.port</span></code> in the <code class="docutils literal notranslate"><span class="pre">example.yaml</span></code>.</p>
</li>
<li><p>Run the following command to check that all pods, deployments, and services
are running properly.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl get all
</pre></div>
</div>
<p>You should expect to see output that shows running pods, deployments, and
replica sets. A good indicator that everything is running properly is to see
all pods are returning a ready status in the  <code class="docutils literal notranslate"><span class="pre">READY</span></code> column.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>NAME                                               READY   STATUS    RESTARTS   AGE
pod/example-trino-cluster-coordinator-bfb74c98d-rnrxd   1/1     Running   0          161m
pod/example-trino-cluster-worker-76f6bf54d6-hvl8n       1/1     Running   0          161m
pod/example-trino-cluster-worker-76f6bf54d6-tcqgb       1/1     Running   0          161m

NAME                       TYPE        CLUSTER-IP    EXTERNAL-IP   PORT(S)    AGE
service/example-trino-cluster   ClusterIP   ***********   &lt;none&gt;        8080/TCP   161m

NAME                                           READY   UP-TO-DATE   AVAILABLE   AGE
deployment.apps/example-trino-cluster-coordinator   1/1     1            1           161m
deployment.apps/example-trino-cluster-worker        2/2     2            2           161m

NAME                                                     DESIRED   CURRENT   READY   AGE
replicaset.apps/example-trino-cluster-coordinator-bfb74c98d   1         1         1       161m
replicaset.apps/example-trino-cluster-worker-76f6bf54d6       2         2         2       161m
</pre></div>
</div>
<p>The output shows running pods. These include the actual Trino containers. To
better understand this output, check out the following resources:</p>
<ol class="arabic simple">
<li><p><a class="reference external" href="https://kubernetes.io/docs/reference/generated/kubectl/kubectl-commands#get">kubectl get command reference</a>.</p></li>
<li><p><a class="reference external" href="https://kubernetes.io/docs/reference/kubectl/docker-cli-to-kubectl/#docker-ps">kubectl get command example</a>.</p></li>
<li><p><a class="reference external" href="https://kubernetes.io/docs/tasks/debug/">Debugging Kubernetes reference</a>.</p></li>
</ol>
</li>
<li><p>If all pods, deployments, and replica sets are running and in the ready
state, Trino has been successfully deployed.</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike some Kubernetes applications, where it’s better to have many small
pods, Trino works best with fewer pods each having more resources
available. We strongly recommend to avoid having multiple Trino pods on a
single physical host to avoid contention for resources.</p>
</div>
</section>
<section id="executing-queries">
<span id="id2"></span><h2 id="executing-queries">Executing queries<a class="headerlink" href="kubernetes.html#executing-queries" title="Link to this heading">#</a></h2>
<p>The pods running the Trino containers are all running on a private network
internal to Kubernetes. In order to access them, specifically the coordinator,
you need to create a tunnel to the coordinator pod and your computer. You can do
this by running the commands generated upon installation.</p>
<ol class="arabic">
<li><p>Create the tunnel from the client to the coordinator service.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl port-forward svc/trino 8080:8080
</pre></div>
</div>
<p>Now you can connect to the Trino coordinator at <code class="docutils literal notranslate"><span class="pre">http://localhost:8080</span></code>.</p>
</li>
<li><p>To connect to Trino, you can use the
<a class="reference internal" href="../client/cli.html"><span class="doc">command-line interface</span></a>, a
<a class="reference internal" href="../client/jdbc.html"><span class="doc">JDBC client</span></a>, or any of the
<a class="reference internal" href="../client.html"><span class="doc">other clients</span></a>. For this example,
<a class="reference internal" href="../client/cli.html#cli-installation"><span class="std std-ref">install the command-line interface</span></a>, and connect to
Trino in a new console session.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino --server http://localhost:8080
</pre></div>
</div>
</li>
<li><p>Using the sample data in the <code class="docutils literal notranslate"><span class="pre">tpch</span></code> catalog, type and execute a query on
the <code class="docutils literal notranslate"><span class="pre">nation</span></code> table using the <code class="docutils literal notranslate"><span class="pre">tiny</span></code> schema:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; select count(*) from tpch.tiny.nation;
 _col0
-------
  25
(1 row)

Query 20181105_001601_00002_e6r6y, FINISHED, 1 node
Splits: 21 total, 21 done (100.00%)
0:06 [25 rows, 0B] [4 rows/s, 0B/s]
</pre></div>
</div>
<p>Try other SQL queries to explore the data set and test your cluster.</p>
</li>
<li><p>Once you are done with your exploration, enter the <code class="docutils literal notranslate"><span class="pre">quit</span></code> command in the
CLI.</p></li>
<li><p>Kill the tunnel to the coordinator pod. The is only available while the
<code class="docutils literal notranslate"><span class="pre">kubectl</span></code> process is running, so you can just kill the <code class="docutils literal notranslate"><span class="pre">kubectl</span></code> process
that’s forwarding the port. In most cases that means pressing <code class="docutils literal notranslate"><span class="pre">CTRL</span></code> +
<code class="docutils literal notranslate"><span class="pre">C</span></code> in the terminal where the port-forward command is running.</p></li>
</ol>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="kubernetes.html#configuration" title="Link to this heading">#</a></h2>
<p>The Helm chart uses the <a class="reference internal" href="containers.html"><span class="doc">Trino container image</span></a>.
The Docker image already contains a default configuration to get started, and
some catalogs to allow you to explore Trino. Kubernetes allows you to mimic a
<a class="reference internal" href="deployment.html"><span class="doc">traditional deployment</span></a> by supplying
configuration in YAML files. It’s important to understand how files such as the
Trino configuration, JVM, and various <a class="reference internal" href="../connector.html"><span class="doc">catalog properties</span></a> are
configured in Trino before updating the values.</p>
<section id="creating-your-own-yaml-configuration">
<span id="creating-your-own-yaml"></span><h3 id="creating-your-own-yaml-configuration">Creating your own YAML configuration<a class="headerlink" href="kubernetes.html#creating-your-own-yaml-configuration" title="Link to this heading">#</a></h3>
<p>When you use your own YAML Kubernetes configuration, you only override the values you specify.
The remaining properties use their default values. Add an <code class="docutils literal notranslate"><span class="pre">example.yaml</span></code> with
the following configuration:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">image</span><span class="p">:</span>
<span class="w">  </span><span class="nt">tag</span><span class="p">:</span><span class="w"> </span><span class="s">"476"</span>
<span class="nt">server</span><span class="p">:</span>
<span class="w">  </span><span class="nt">workers</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="nt">coordinator</span><span class="p">:</span>
<span class="w">  </span><span class="nt">jvm</span><span class="p">:</span>
<span class="w">    </span><span class="nt">maxHeapSize</span><span class="p">:</span><span class="w"> </span><span class="s">"8G"</span>
<span class="nt">worker</span><span class="p">:</span>
<span class="w">  </span><span class="nt">jvm</span><span class="p">:</span>
<span class="w">    </span><span class="nt">maxHeapSize</span><span class="p">:</span><span class="w"> </span><span class="s">"8G"</span>
</pre></div>
</div>
<p>These values are higher than the defaults and allow Trino to use more memory
and run more demanding queries. If the values are too high, Kubernetes might
not be able to schedule some Trino pods, depending on other applications
deployed in this cluster and the size of the cluster nodes.</p>
<ol class="arabic simple">
<li><p><code class="docutils literal notranslate"><span class="pre">.image.tag</span></code> is set to the current version, 476. Set
this value if you need to use a specific version of Trino. The default is
<code class="docutils literal notranslate"><span class="pre">latest</span></code>, which is not recommended. Using <code class="docutils literal notranslate"><span class="pre">latest</span></code> will publish a new
version of Trino with each release and a following Kubernetes deployment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">.server.workers</span></code> is set to <code class="docutils literal notranslate"><span class="pre">3</span></code>. This value sets the number of
workers, in this case, a coordinator and three worker nodes are deployed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">.coordinator.jvm.maxHeapSize</span></code> is set to <code class="docutils literal notranslate"><span class="pre">8GB</span></code>.
This sets the maximum heap size in the JVM of the coordinator. See
<a class="reference internal" href="deployment.html#jvm-config"><span class="std std-ref">JVM config</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">.worker.jvm.maxHeapSize</span></code> is set to <code class="docutils literal notranslate"><span class="pre">8GB</span></code>.
This sets the maximum heap size in the JVM of the worker. See
<a class="reference internal" href="deployment.html#jvm-config"><span class="std std-ref">JVM config</span></a>.</p></li>
</ol>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Some memory settings need to be tuned carefully as setting some values
outside the range of the maximum heap size will cause Trino startup to
fail. See the warnings listed on <a class="reference internal" href="../admin/properties-resource-management.html"><span class="doc">Resource management properties</span></a>.</p>
</div>
<p>Reference <a class="reference external" href="https://trinodb.github.io/charts/charts/trino/">the full list of properties</a>
that can be overridden in the Helm chart.</p>
<div class="admonition note" id="kubernetes-configuration-best-practices">
<p class="admonition-title">Note</p>
<p>Although <code class="docutils literal notranslate"><span class="pre">example.yaml</span></code> is used to refer to the Kubernetes configuration
file in this document, you should use clear naming guidelines for the cluster
and deployment you are managing. For example,
<code class="docutils literal notranslate"><span class="pre">cluster-example-trino-etl.yaml</span></code> might refer to a Trino deployment for a
cluster used primarily for extract-transform-load queries deployed on the
<code class="docutils literal notranslate"><span class="pre">example</span></code> Kubernetes cluster. See
<a class="reference external" href="https://kubernetes.io/docs/concepts/configuration/overview/">Configuration Best Practices</a>
for more tips on configuring Kubernetes deployments.</p>
</div>
</section>
<section id="adding-catalogs">
<h3 id="adding-catalogs">Adding catalogs<a class="headerlink" href="kubernetes.html#adding-catalogs" title="Link to this heading">#</a></h3>
<p>A common use-case is to add custom catalogs. You can do this by adding values to
the <code class="docutils literal notranslate"><span class="pre">catalogs</span></code> property in the <code class="docutils literal notranslate"><span class="pre">example.yaml</span></code> file.</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">catalogs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">lakehouse</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|-</span>
<span class="w">    </span><span class="no">connector.name=iceberg</span>
<span class="w">    </span><span class="no">hive.metastore.uri=thrift://example.net:9083</span>
<span class="w">  </span><span class="nt">rdbms</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|-</span>
<span class="w">    </span><span class="no">connector.name=postgresql</span>
<span class="w">    </span><span class="no">connection-url=*******************************************</span>
<span class="w">    </span><span class="no">connection-user=root</span>
<span class="w">    </span><span class="no">connection-password=secret</span>
<span class="w">  </span><span class="nt">tpch</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|-</span>
<span class="w">    </span><span class="no">connector.name=tpch</span>
<span class="w">    </span><span class="no">tpch.splits-per-node=4</span>
</pre></div>
</div>
<p>This adds both <code class="docutils literal notranslate"><span class="pre">lakehouse</span></code> and <code class="docutils literal notranslate"><span class="pre">rdbms</span></code> catalogs to the Kubernetes deployment
configuration.</p>
</section>
</section>
<section id="running-a-local-kubernetes-cluster-with-kind">
<span id="id3"></span><h2 id="running-a-local-kubernetes-cluster-with-kind">Running a local Kubernetes cluster with kind<a class="headerlink" href="kubernetes.html#running-a-local-kubernetes-cluster-with-kind" title="Link to this heading">#</a></h2>
<p>For local deployments, you can use
<a class="reference external" href="https://kind.sigs.k8s.io">kind (Kubernetes in Docker)</a>. Follow the steps
below to run <code class="docutils literal notranslate"><span class="pre">kind</span></code> on your system.</p>
<ol class="arabic">
<li><p><code class="docutils literal notranslate"><span class="pre">kind</span></code> runs on <a class="reference external" href="https://www.docker.com">Docker</a>, so first check if Docker
is installed:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>docker --version
</pre></div>
</div>
<p>If this command fails, install Docker by following
<a class="reference external" href="https://docs.docker.com/engine/install/">Docker installation instructions</a>.</p>
</li>
<li><p>Install <code class="docutils literal notranslate"><span class="pre">kind</span></code> by following the
<a class="reference external" href="https://kind.sigs.k8s.io/docs/user/quick-start/#installation">kind installation instructions</a>.</p></li>
<li><p>Run a Kubernetes cluster in <code class="docutils literal notranslate"><span class="pre">kind</span></code> by running the command:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kind create cluster --name trino
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">name</span></code> parameter is optional but is used to showcase how the
namespace is applied in future commands. The cluster name defaults to
<code class="docutils literal notranslate"><span class="pre">kind</span></code> if no parameter is added. Use <code class="docutils literal notranslate"><span class="pre">trino</span></code> to make the application
on this cluster obvious.</p>
</div>
</li>
<li><p>Verify that <code class="docutils literal notranslate"><span class="pre">kubectl</span></code> is running against the correct Kubernetes cluster.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl cluster-info --context kind-trino
</pre></div>
</div>
<p>If you have multiple Kubernetes clusters already configured within
<code class="docutils literal notranslate"><span class="pre">~/.kube/config</span></code>, you need to pass the <code class="docutils literal notranslate"><span class="pre">context</span></code> parameter to the
<code class="docutils literal notranslate"><span class="pre">kubectl</span></code> commands to operate with the local <code class="docutils literal notranslate"><span class="pre">kind</span></code> cluster. <code class="docutils literal notranslate"><span class="pre">kubectl</span></code>
uses the
<a class="reference external" href="https://kubernetes.io/docs/reference/kubectl/cheatsheet/#kubectl-context-and-configuration">default context</a>
if this parameter isn’t supplied. Notice the context is the name of the
cluster with the <code class="docutils literal notranslate"><span class="pre">kind-</span></code> prefix added. Now you can look at all the
Kubernetes objects running on your <code class="docutils literal notranslate"><span class="pre">kind</span></code> cluster.</p>
</li>
<li><p>Set up Trino by following the <a class="reference internal" href="kubernetes.html#running-trino-using-helm"><span class="std std-ref">Running Trino using Helm</span></a> steps. When
running the <code class="docutils literal notranslate"><span class="pre">kubectl</span> <span class="pre">get</span> <span class="pre">all</span></code> command, add the <code class="docutils literal notranslate"><span class="pre">context</span></code> parameter.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl get all --context kind-trino
</pre></div>
</div>
</li>
<li><p>Run some queries by following the <a class="reference internal" href="kubernetes.html#executing-queries">Executing queries</a> steps.</p></li>
<li><p>Once you are done with the cluster using kind, you can delete the cluster.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kind delete cluster -n trino
</pre></div>
</div>
</li>
</ol>
</section>
<section id="cleaning-up">
<h2 id="cleaning-up">Cleaning up<a class="headerlink" href="kubernetes.html#cleaning-up" title="Link to this heading">#</a></h2>
<p>To uninstall Trino from the Kubernetes cluster, run the following command:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>helm uninstall my-trino-cluster
</pre></div>
</div>
<p>You should expect to see the following output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>release "my-trino-cluster" uninstalled
</pre></div>
</div>
<p>To validate that this worked, you can run this <code class="docutils literal notranslate"><span class="pre">kubectl</span></code> command to make sure
there are no remaining Kubernetes objects related to the Trino cluster.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kubectl get all
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="containers.html" title="Trino in a Docker container"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Trino in a Docker container </span>
              </div>
            </a>
          
          
            <a href="plugins.html" title="Plugins"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Plugins </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>