<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Plugins &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="plugins.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Improve query processing resilience" href="query-resiliency.html" />
    <link rel="prev" title="Trino on Kubernetes with Helm" href="kubernetes.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="plugins.html#installation/plugins" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Plugins </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="deployment.html" class="md-nav__link">Deploying Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="containers.html" class="md-nav__link">Trino in a Docker container</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kubernetes.html" class="md-nav__link">Trino on Kubernetes with Helm</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Plugins </label>
    
      <a href="plugins.html#" class="md-nav__link md-nav__link--active">Plugins</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="plugins.html#download" class="md-nav__link">Download</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#removal" class="md-nav__link">Removal</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#development" class="md-nav__link">Development</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#list-of-plugins" class="md-nav__link">List of plugins</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="query-resiliency.html" class="md-nav__link">Improve query processing resilience</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="plugins.html#download" class="md-nav__link">Download</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#removal" class="md-nav__link">Removal</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#development" class="md-nav__link">Development</a>
        </li>
        <li class="md-nav__item"><a href="plugins.html#list-of-plugins" class="md-nav__link">List of plugins</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="plugins">
<h1 id="installation-plugins--page-root">Plugins<a class="headerlink" href="plugins.html#installation-plugins--page-root" title="Link to this heading">#</a></h1>
<p>Trino uses a plugin architecture to extend its capabilities and integrate with
various data sources and other systems. Trino includes many plugins as part of
the binary packages - specifically the <a class="reference internal" href="../glossary.html#glosstarball"><span class="std std-ref">tarball</span></a> and the <a class="reference internal" href="../glossary.html#glosscontainer"><span class="std std-ref">Docker
image</span></a>.</p>
<p>Plugins implement some of the following capabilities:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../connector.html"><span class="doc std std-doc">Connectors</span></a></p></li>
<li><p><a class="reference internal" href="../security.html#security-authentication"><span class="std std-ref">Authentication types</span></a></p></li>
<li><p><a class="reference internal" href="../security.html#security-access-control"><span class="std std-ref">Access control systems</span></a></p></li>
<li><p><a class="reference internal" href="../admin.html#admin-event-listeners"><span class="std std-ref">Event listeners</span></a></p></li>
<li><p>Additional types and global functions</p></li>
<li><p>Block encodings</p></li>
<li><p>Resource group configuration managers</p></li>
<li><p>Session property configuration managers</p></li>
<li><p>Exchange managers</p></li>
<li><p>Spooling managers</p></li>
</ul>
<p>All plugins are optional for your use of Trino because they support specific
functionality that is potentially not needed for your use case. Plugins are
located in the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> folder of your Trino installation and are loaded
automatically during Trino startup.</p>
<section id="download">
<span id="plugins-download"></span><h2 id="download">Download<a class="headerlink" href="plugins.html#download" title="Link to this heading">#</a></h2>
<p>Typically, downloading a plugin is not necessary because Trino binaries include
many plugins as part of the binary package.</p>
<p>Every Trino release publishes each plugin as a ZIP archive to the <a class="reference external" href="https://central.sonatype.com/">Maven Central
Repository</a>. Refer to <a class="reference internal" href="plugins.html#plugins-list"><span class="std std-ref">List of plugins</span></a> for details.
The specific location is derived from the Maven coordinates of each plugin as
defined in the <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code> of the source code for the plugin.</p>
<p>For example, the PostgreSQL connector plugin can be found in the
<code class="docutils literal notranslate"><span class="pre">plugin/trino-postgresql</span></code> directory, and the <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code> file contains the
following identifier section:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;parent&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>io.trino<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>trino-root<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>470<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;relativePath&gt;</span>../../pom.xml<span class="nt">&lt;/relativePath&gt;</span>
<span class="nt">&lt;/parent&gt;</span>

<span class="nt">&lt;artifactId&gt;</span>trino-postgresql<span class="nt">&lt;/artifactId&gt;</span>
<span class="nt">&lt;packaging&gt;</span>trino-plugin<span class="nt">&lt;/packaging&gt;</span>
</pre></div>
</div>
<p>The Maven coordinates are therefore <code class="docutils literal notranslate"><span class="pre">io.trino:trino-postgresql:470</span></code> with version
or <code class="docutils literal notranslate"><span class="pre">io.trino:trino-postgresql</span></code> without version. Use this term for a <a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-postgresql">search to
locate the
artifact</a>.</p>
<p>After searching, click <strong>View all</strong> next to <strong>Latest version</strong>, then click
<strong>Browse</strong> to find the ZIP file for the desired version.</p>
<p>The coordinates translate into a path to the ZIP archive on the Maven Central
Repository. Use this URL to download the plugin.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">https</span><span class="p">:</span><span class="o">//</span><span class="n">repo1</span><span class="p">.</span><span class="n">maven</span><span class="p">.</span><span class="n">org</span><span class="o">/</span><span class="n">maven2</span><span class="o">/</span><span class="n">io</span><span class="o">/</span><span class="n">trino</span><span class="o">/</span><span class="n">trino</span><span class="o">-</span><span class="n">postgresql</span><span class="o">/</span><span class="mi">470</span><span class="o">/</span><span class="n">trino</span><span class="o">-</span><span class="n">postgresql</span><span class="o">-</span><span class="mi">470</span><span class="p">.</span><span class="n">zip</span>
</pre></div>
</div>
<p>Availability of plugins from other projects and organizations varies widely, and
may require building a plugin from source.</p>
<p>When downloading a plugin you must ensure to download a version of the plugin
that is compatible with your Trino installation. Full compatibility is only
guaranteed when using the same Trino version used for the plugin build and the
deployment, and therefore using the same version is recommended. Use the
documentation or the source code of the specific plugin to confirm and refer to
the <a class="reference internal" href="../develop/spi-overview.html#spi-compatibility"><span class="std std-ref">SPI compatibility notes</span></a> for further technical details.</p>
</section>
<section id="installation">
<span id="plugins-installation"></span><h2 id="installation">Installation<a class="headerlink" href="plugins.html#installation" title="Link to this heading">#</a></h2>
<p>To install a plugin, extract the ZIP archive into a directory in the <code class="docutils literal notranslate"><span class="pre">plugin</span></code>
directory of your Trino installation on all nodes of the cluster. The directory
contains all necessary resources.</p>
<p>For example, for a plugin called <code class="docutils literal notranslate"><span class="pre">example-plugin</span></code> with a version of <code class="docutils literal notranslate"><span class="pre">1.0</span></code>,
extract the <code class="docutils literal notranslate"><span class="pre">example-plugin-1.0.zip</span></code> archive. Rename the resulting directory
<code class="docutils literal notranslate"><span class="pre">example-plugin-1.0</span></code> to <code class="docutils literal notranslate"><span class="pre">example-plugin</span></code> and copy it into the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> directory
of your Trino installation on all workers and the coordinator of the cluster.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Every Trino plugin must be in a separate directory underneath the <code class="docutils literal notranslate"><span class="pre">plugin</span></code>
directory. Do not put JAR files directly into the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> directory. Each
plugin directory should only contain JAR files. Any subdirectories and other
files are ignored.</p>
</div>
<p>By default, the plugin directory is the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> directory relative to the
directory in which Trino is installed, but it is configurable using the
configuration variable <code class="docutils literal notranslate"><span class="pre">plugin.dir</span></code> with the launcher. The <a class="reference internal" href="containers.html"><span class="doc std std-doc">Docker
image</span></a> uses the path <code class="docutils literal notranslate"><span class="pre">/usr/lib/trino/plugin</span></code>.</p>
<p>Restart Trino to use the plugin.</p>
<p>The <a class="reference external" href="https://github.com/trinodb/trino-packages">trino-packages project</a> contains
example projects to create a tarball and Docker image with a selection of
plugins by installing only the desired plugins.</p>
</section>
<section id="removal">
<span id="plugins-removal"></span><h2 id="removal">Removal<a class="headerlink" href="plugins.html#removal" title="Link to this heading">#</a></h2>
<p>Plugins can be safely removed if the functionality is not needed or desired on
your Trino cluster. Use the following steps for a safe removal across the
cluster:</p>
<ul class="simple">
<li><p>Shut down Trino on all nodes.</p></li>
<li><p>Delete the directory in the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> folder of the Trino installation on all
nodes.</p></li>
<li><p>Start Trino on all nodes.</p></li>
</ul>
<p>Refer to the <a class="reference internal" href="plugins.html#plugins-list"><span class="std std-ref">List of plugins</span></a> for relevant directory names.</p>
<p>For repeated deployments, you can remove the plugin from the binary package for
your installation by creating a custom tarball or a custom Docker image.</p>
</section>
<section id="development">
<span id="plugins-development"></span><h2 id="development">Development<a class="headerlink" href="plugins.html#development" title="Link to this heading">#</a></h2>
<p>You can develop plugins in your own fork of the Trino codebase or a separate
project. Refer to the <a class="reference internal" href="../develop.html"><span class="doc std std-doc">Developer guide</span></a> for further details.</p>
</section>
<section id="list-of-plugins">
<span id="plugins-list"></span><h2 id="list-of-plugins">List of plugins<a class="headerlink" href="plugins.html#list-of-plugins" title="Link to this heading">#</a></h2>
<p>The following list of plugins is available from the Trino project. They are
included in the build and release process and the resulting the binary packages.
You can also <a class="reference internal" href="plugins.html#plugins-download"><span class="std std-ref">download</span></a> them from the Maven Central Repository
with the listed coordinates.</p>
<table id="id1">
<caption><span class="caption-text">List of plugins</span><a class="headerlink" href="plugins.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 25%"/>
<col style="width: 25%"/>
<col style="width: 25%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Plugin directory</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Maven coordinates</p></th>
<th class="head"><p>Download</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>ai-functions</p></td>
<td><p><a class="reference internal" href="../functions/ai.html"><span class="doc std std-doc">AI functions</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-ai-functions">io.trino:trino-ai-functions</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-ai-functions/476/trino-ai-functions-476.zip">trino-ai-functions-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>bigquery</p></td>
<td><p><a class="reference internal" href="../connector/bigquery.html"><span class="doc std std-doc">BigQuery connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-bigquery">io.trino:trino-bigquery</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-bigquery/476/trino-bigquery-476.zip">trino-bigquery-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>blackhole</p></td>
<td><p><a class="reference internal" href="../connector/blackhole.html"><span class="doc std std-doc">Black Hole connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-blackhole">io.trino:trino-blackhole</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-blackhole/476/trino-blackhole-476.zip">trino-blackhole-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>cassandra</p></td>
<td><p><a class="reference internal" href="../connector/cassandra.html"><span class="doc std std-doc">Cassandra connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-cassandra">io.trino:trino-cassandra</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-cassandra/476/trino-cassandra-476.zip">trino-cassandra-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>clickhouse</p></td>
<td><p><a class="reference internal" href="../connector/clickhouse.html"><span class="doc std std-doc">ClickHouse connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-clickhouse">io.trino:trino-clickhouse</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-clickhouse/476/trino-clickhouse-476.zip">trino-clickhouse-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>delta-lake</p></td>
<td><p><a class="reference internal" href="../connector/delta-lake.html"><span class="doc std std-doc">Delta Lake connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-delta-lake">io.trino:trino-delta-lake</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-delta-lake/476/trino-delta-lake-476.zip">trino-delta-lake-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>druid</p></td>
<td><p><a class="reference internal" href="../connector/druid.html"><span class="doc std std-doc">Druid connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-druid">io.trino:trino-druid</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-druid/476/trino-druid-476.zip">trino-druid-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>duckdb</p></td>
<td><p><a class="reference internal" href="../connector/duckdb.html"><span class="doc std std-doc">DuckDB connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-duckdb">io.trino:trino-duckdb</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-duckdb/476/trino-duckdb-476.zip">trino-duckdb-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>elasticsearch</p></td>
<td><p><a class="reference internal" href="../connector/elasticsearch.html"><span class="doc std std-doc">Elasticsearch connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-elasticsearch">io.trino:trino-elasticsearch</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-elasticsearch/476/trino-elasticsearch-476.zip">trino-elasticsearch-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>example-http</p></td>
<td><p><a class="reference internal" href="../develop/example-http.html"><span class="doc std std-doc">Example HTTP connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-example-http">io.trino:trino-example-http</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-example-http/476/trino-example-http-476.zip">trino-example-http-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>exasol</p></td>
<td><p><a class="reference internal" href="../connector/exasol.html"><span class="doc std std-doc">Exasol connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-exasol">io.trino:trino-exasol</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-exasol/476/trino-exasol-476.zip">trino-exasol-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>exchange-filesystem</p></td>
<td><p><a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc std std-doc">Fault-tolerant execution</span></a> exchange file system</p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-exchange-filesystem">io.trino:trino-exchange-filesystem</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-exchange-filesystem/476/trino-exchange-filesystem-476.zip">trino-exchange-filesystem-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>exchange-hdfs</p></td>
<td><p><a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc std std-doc">Fault-tolerant execution</span></a> exchange file system for HDFS</p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-exchange-hdfs">io.trino:trino-exchange-hdfs</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-exchange-hdfs/476/trino-exchange-hdfs-476.zip">trino-exchange-hdfs-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>faker</p></td>
<td><p><a class="reference internal" href="../connector/faker.html"><span class="doc std std-doc">Faker connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-faker">io.trino:trino-faker</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-faker/476/trino-faker-476.zip">trino-faker-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>functions-python</p></td>
<td><p><a class="reference internal" href="../udf/python.html"><span class="doc std std-doc">Python user-defined functions</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-functions-python">io.trino:trino-functions-python</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-functions-python/476/trino-functions-python-476.zip">trino-functions-python-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>geospatial</p></td>
<td><p><a class="reference internal" href="../functions/geospatial.html"><span class="doc std std-doc">Geospatial functions</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-geospatial">io.trino:trino-geospatial</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-geospatial/476/trino-geospatial-476.zip">trino-geospatial-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>google-sheets</p></td>
<td><p><a class="reference internal" href="../connector/googlesheets.html"><span class="doc std std-doc">Google Sheets connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-google-sheets">io.trino:trino-google-sheets</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-google-sheets/476/trino-google-sheets-476.zip">trino-google-sheets-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>hive</p></td>
<td><p><a class="reference internal" href="../connector/hive.html"><span class="doc std std-doc">Hive connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-hive">io.trino:trino-hive</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-hive/476/trino-hive-476.zip">trino-hive-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>http-event-listener</p></td>
<td><p><a class="reference internal" href="../admin/event-listeners-http.html"><span class="doc std std-doc">HTTP event listener</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-http-event-listener">io.trino:trino-http-event-listener</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-http-event-listener/476/trino-http-event-listener-476.zip">trino-http-event-listener-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>http-server-event-listener</p></td>
<td><p>HTTP server event listener</p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-http-server-event-listener">io.trino:trino-http-server-event-listener</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-http-server-event-listener/476/trino-http-server-event-listener-476.zip">trino-http-server-event-listener-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>hudi</p></td>
<td><p><a class="reference internal" href="../connector/hudi.html"><span class="doc std std-doc">Hudi connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-hudi">io.trino:trino-hudi</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-hudi/476/trino-hudi-476.zip">trino-hudi-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>iceberg</p></td>
<td><p><a class="reference internal" href="../connector/iceberg.html"><span class="doc std std-doc">Iceberg connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-iceberg">io.trino:trino-iceberg</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-iceberg/476/trino-iceberg-476.zip">trino-iceberg-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>ignite</p></td>
<td><p><a class="reference internal" href="../connector/ignite.html"><span class="doc std std-doc">Ignite connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-ignite">io.trino:trino-ignite</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-ignite/476/trino-ignite-476.zip">trino-ignite-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>jmx</p></td>
<td><p><a class="reference internal" href="../connector/jmx.html"><span class="doc std std-doc">JMX connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-jmx">io.trino:trino-jmx</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-jmx/476/trino-jmx-476.zip">trino-jmx-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>kafka</p></td>
<td><p><a class="reference internal" href="../connector/kafka.html"><span class="doc std std-doc">Kafka connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-kafka">io.trino:trino-kafka</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-kafka/476/trino-kafka-476.zip">trino-kafka-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>kafka-event-listener</p></td>
<td><p><a class="reference internal" href="../admin/event-listeners-kafka.html"><span class="doc std std-doc">Kafka event listener</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-kafka-event-listener">io.trino:trino-kafka-event-listener</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-kafka-event-listener/476/trino-kafka-event-listener-476.zip">trino-kafka-event-listener-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>loki</p></td>
<td><p><a class="reference internal" href="../connector/loki.html"><span class="doc std std-doc">Loki connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-loki">io.trino:trino-loki</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-loki/476/trino-loki-476.zip">trino-loki-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>mariadb</p></td>
<td><p><a class="reference internal" href="../connector/mariadb.html"><span class="doc std std-doc">MariaDB connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-mariadb">io.trino:trino-mariadb</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-mariadb/476/trino-mariadb-476.zip">trino-mariadb-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>memory</p></td>
<td><p><a class="reference internal" href="../connector/memory.html"><span class="doc std std-doc">Memory connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-memory">io.trino:trino-memory</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-memory/476/trino-memory-476.zip">trino-memory-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>ml</p></td>
<td><p><a class="reference internal" href="../functions/ml.html"><span class="doc std std-doc">Machine learning functions</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-ml">io.trino:trino-ml</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-ml/476/trino-ml-476.zip">trino-ml-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>mongodb</p></td>
<td><p><a class="reference internal" href="../connector/mongodb.html"><span class="doc std std-doc">MongoDB connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-mongodb">io.trino:trino-mongodb</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-mongodb/476/trino-mongodb-476.zip">trino-mongodb-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>mysql</p></td>
<td><p><a class="reference internal" href="../connector/mysql.html"><span class="doc std std-doc">MySQL connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-mysql">io.trino:trino-mysql</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-mysql/476/trino-mysql-476.zip">trino-mysql-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>mysql-event-listener</p></td>
<td><p><a class="reference internal" href="../admin/event-listeners-mysql.html"><span class="doc std std-doc">MySQL event listener</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-mysql-event-listener">io.trino:trino-mysql-event-listener</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-mysql-event-listener/476/trino-mysql-event-listener-476.zip">trino-mysql-event-listener-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>opa</p></td>
<td><p><a class="reference internal" href="../security/opa-access-control.html"><span class="doc std std-doc">Open Policy Agent access control</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-opa">io.trino:trino-opa</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-opa/476/trino-opa-476.zip">trino-opa-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>openlineage</p></td>
<td><p><a class="reference internal" href="../admin/event-listeners-openlineage.html"><span class="doc std std-doc">OpenLineage event listener</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-openlineage">io.trino:trino-openlineage</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-openlineage/476/trino-openlineage-476.zip">trino-openlineage-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>opensearch</p></td>
<td><p><a class="reference internal" href="../connector/opensearch.html"><span class="doc std std-doc">OpenSearch connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-opensearch">io.trino:trino-opensearch</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-opensearch/476/trino-opensearch-476.zip">trino-opensearch-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>oracle</p></td>
<td><p><a class="reference internal" href="../connector/oracle.html"><span class="doc std std-doc">Oracle connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-oracle">io.trino:trino-oracle</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-oracle/476/trino-oracle-476.zip">trino-oracle-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>password-authenticators</p></td>
<td><p>Password authentication</p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-password-authenticators">io.trino:trino-password-authenticators</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-password-authenticators/476/trino-password-authenticators-476.zip">trino-password-authenticators-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>pinot</p></td>
<td><p><a class="reference internal" href="../connector/pinot.html"><span class="doc std std-doc">Pinot connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-pinot">io.trino:trino-pinot</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-pinot/476/trino-pinot-476.zip">trino-pinot-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>postgresql</p></td>
<td><p><a class="reference internal" href="../connector/postgresql.html"><span class="doc std std-doc">PostgreSQL connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-postgresql">io.trino:trino-postgresql</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-postgresql/476/trino-postgresql-476.zip">trino-postgresql-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>prometheus</p></td>
<td><p><a class="reference internal" href="../connector/prometheus.html"><span class="doc std std-doc">Prometheus connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-prometheus">io.trino:trino-prometheus</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-prometheus/476/trino-prometheus-476.zip">trino-prometheus-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>ranger</p></td>
<td><p><a class="reference internal" href="../security/ranger-access-control.html"><span class="doc std std-doc">Ranger access control</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-ranger">io.trino:trino-ranger</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-ranger/476/trino-ranger-476.zip">trino-ranger-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>redis</p></td>
<td><p><a class="reference internal" href="../connector/redis.html"><span class="doc std std-doc">Redis connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-redis">io.trino:trino-redis</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-redis/476/trino-redis-476.zip">trino-redis-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>redshift</p></td>
<td><p><a class="reference internal" href="../connector/redshift.html"><span class="doc std std-doc">Redshift connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-redshift">io.trino:trino-redshift</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-redshift/476/trino-redshift-476.zip">trino-redshift-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>resource-group-managers</p></td>
<td><p><a class="reference internal" href="../admin/resource-groups.html"><span class="doc std std-doc">Resource groups</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-resource-group-managers">io.trino:trino-resource-group-managers</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-resource-group-managers/476/trino-resource-group-managers-476.zip">trino-resource-group-managers-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>session-property-managers</p></td>
<td><p><a class="reference internal" href="../admin/session-property-managers.html"><span class="doc std std-doc">Session property managers</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-session-property-managers">io.trino:trino-session-property-managers</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-session-property-managers/476/trino-session-property-managers-476.zip">trino-session-property-managers-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>singlestore</p></td>
<td><p><a class="reference internal" href="../connector/singlestore.html"><span class="doc std std-doc">SingleStore connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-singlestore">io.trino:trino-singlestore</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-singlestore/476/trino-singlestore-476.zip">trino-singlestore-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>snowflake</p></td>
<td><p><a class="reference internal" href="../connector/snowflake.html"><span class="doc std std-doc">Snowflake connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-snowflake">io.trino:trino-snowflake</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-snowflake/476/trino-snowflake-476.zip">trino-snowflake-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>spooling-filesystem</p></td>
<td><p>Server side support for <a class="reference internal" href="../client/client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-spooling-filesystem">io.trino:trino-spooling-filesystem</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-spooling-filesystem/476/trino-spooling-filesystem-476.zip">trino-spooling-filesystem-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>sqlserver</p></td>
<td><p><a class="reference internal" href="../connector/sqlserver.html"><span class="doc std std-doc">SQL Server connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-sqlserver">io.trino:trino-sqlserver</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-sqlserver/476/trino-sqlserver-476.zip">trino-sqlserver-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>teradata-functions</p></td>
<td><p><a class="reference internal" href="../functions/teradata.html"><span class="doc std std-doc">Teradata functions</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-teradata-functions">io.trino:trino-teradata-functions</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-teradata-functions/476/trino-teradata-functions-476.zip">trino-teradata-functions-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>thrift</p></td>
<td><p><a class="reference internal" href="../connector/thrift.html"><span class="doc std std-doc">Thrift connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-thrift">io.trino:trino-thrift</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-thrift/476/trino-thrift-476.zip">trino-thrift-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>tpcds</p></td>
<td><p><a class="reference internal" href="../connector/tpcds.html"><span class="doc std std-doc">TPC-DS connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-tpcds">io.trino:trino-tpcds</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-tpcds/476/trino-tpcds-476.zip">trino-tpcds-476.zip</a></p></td>
</tr>
<tr class="row-odd"><td><p>tpch</p></td>
<td><p><a class="reference internal" href="../connector/tpch.html"><span class="doc std std-doc">TPC-H connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-tpch">io.trino:trino-tpch</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-tpch/476/trino-tpch-476.zip">trino-tpch-476.zip</a></p></td>
</tr>
<tr class="row-even"><td><p>vertica</p></td>
<td><p><a class="reference internal" href="../connector/vertica.html"><span class="doc std std-doc">Vertica connector</span></a></p></td>
<td><p><a class="reference external" href="https://central.sonatype.com/search?q=io.trino%3Atrino-vertica">io.trino:trino-vertica</a></p></td>
<td><p><a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-vertica/476/trino-vertica-476.zip">trino-vertica-476.zip</a></p></td>
</tr>
</tbody>
</table>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="kubernetes.html" title="Trino on Kubernetes with Helm"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Trino on Kubernetes with Helm </span>
              </div>
            </a>
          
          
            <a href="query-resiliency.html" title="Improve query processing resilience"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Improve query processing resilience </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>