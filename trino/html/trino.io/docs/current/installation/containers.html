<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Trino in a Docker container &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="containers.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Trino on Kubernetes with Helm" href="kubernetes.html" />
    <link rel="prev" title="Deploying Trino" href="deployment.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="containers.html#installation/containers" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Trino in a Docker container </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="deployment.html" class="md-nav__link">Deploying Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Trino in a Docker container </label>
    
      <a href="containers.html#" class="md-nav__link md-nav__link--active">Trino in a Docker container</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="containers.html#running-the-container" class="md-nav__link">Running the container</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#executing-queries" class="md-nav__link">Executing queries</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#configuring-trino" class="md-nav__link">Configuring Trino</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#cleaning-up" class="md-nav__link">Cleaning up</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kubernetes.html" class="md-nav__link">Trino on Kubernetes with Helm</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="plugins.html" class="md-nav__link">Plugins</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="query-resiliency.html" class="md-nav__link">Improve query processing resilience</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="containers.html#running-the-container" class="md-nav__link">Running the container</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#executing-queries" class="md-nav__link">Executing queries</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#configuring-trino" class="md-nav__link">Configuring Trino</a>
        </li>
        <li class="md-nav__item"><a href="containers.html#cleaning-up" class="md-nav__link">Cleaning up</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="trino-in-a-docker-container">
<h1 id="installation-containers--page-root">Trino in a Docker container<a class="headerlink" href="containers.html#installation-containers--page-root" title="Link to this heading">#</a></h1>
<p>The Trino project provides the default
<a class="reference external" href="https://hub.docker.com/r/trinodb/trino">trinodb/trino</a> Docker image that
includes the Trino server, all plugins, and a default configuration.</p>
<p>The additional <a class="reference external" href="https://hub.docker.com/r/trinodb/trino-core">trinodb/trino-core</a>
Docker images contains only a minimal set of essential plugins, and it is
therefore mostly suitable as a base for custom container creation. The
<a class="reference external" href="https://github.com/trinodb/trino-packages">trino-packages project</a> includes a
module to create a customized Docker image with your own selection of plugins.</p>
<p>The Docker images are published to Docker Hub and can be used with the Docker
runtime, among several others. The images are suitable to use with the <a class="reference external" href="https://github.com/trinodb/charts">Trino
Helm chart</a>.</p>
<section id="running-the-container">
<h2 id="running-the-container">Running the container<a class="headerlink" href="containers.html#running-the-container" title="Link to this heading">#</a></h2>
<p>To run Trino in Docker, you must have the Docker engine installed on your
machine. You can download Docker from the <a class="reference external" href="https://www.docker.com">Docker website</a>,
or use the packaging system of your operating systems.</p>
<p>Use the <code class="docutils literal notranslate"><span class="pre">docker</span></code> command to create a container from the <code class="docutils literal notranslate"><span class="pre">trinodb/trino</span></code>
image. Assign it the <code class="docutils literal notranslate"><span class="pre">trino</span></code> name, to make it easier to reference it later.
Run it in the background, and map the default Trino port, which is 8080,
from inside the container to port 8080 on your workstation.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>docker run --name trino -d -p 8080:8080 trinodb/trino
</pre></div>
</div>
<p>Without specifying the container image tag, it defaults to <code class="docutils literal notranslate"><span class="pre">latest</span></code>,
but a number of any released Trino version can be used, for example
<code class="docutils literal notranslate"><span class="pre">trinodb/trino:476</span></code>.</p>
<p>Run <code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">ps</span></code> to see all the containers running in the background.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>% docker ps
CONTAINER ID   IMAGE               COMMAND                  CREATED        STATUS                  PORTS                    NAMES
955c3b3d3d0a   trinodb/trino:390   "/usr/lib/trino/bin/…"   39 hours ago   Up 39 hours (healthy)   0.0.0.0:8080-&gt;8080/tcp   trino
</pre></div>
</div>
<p>When Trino is still starting, it shows <code class="docutils literal notranslate"><span class="pre">(health:</span> <span class="pre">starting)</span></code>,
and <code class="docutils literal notranslate"><span class="pre">(healthy)</span></code> when it’s ready.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>There are multiple ways to use Trino within containers. You can either run
Trino in Docker containers locally, as explained in the following sections,
or use a container orchestration platform like Kubernetes. For the Kubernetes
instructions see <a class="reference internal" href="kubernetes.html"><span class="doc">Trino on Kubernetes with Helm</span></a>.</p>
</div>
</section>
<section id="executing-queries">
<h2 id="executing-queries">Executing queries<a class="headerlink" href="containers.html#executing-queries" title="Link to this heading">#</a></h2>
<p>The image includes the Trino command-line interface (CLI) client, <code class="docutils literal notranslate"><span class="pre">trino</span></code>.
Execute it in the existing container to connect to the Trino server running
inside it. After starting the client, type and execute a query on a table
of the <code class="docutils literal notranslate"><span class="pre">tpch</span></code> catalog, which includes example data:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ docker exec -it trino trino
trino&gt; select count(*) from tpch.sf1.nation;
 _col0
-------
    25
(1 row)

Query 20181105_001601_00002_e6r6y, FINISHED, 1 node
Splits: 21 total, 21 done (100.00%)
0:06 [25 rows, 0B] [4 rows/s, 0B/s]
</pre></div>
</div>
<p>Once you are done with your exploration, enter the <code class="docutils literal notranslate"><span class="pre">quit</span></code> command.</p>
<p>Alternatively, you can use the Trino CLI installed directly on your workstation.
The default server URL in the CLI of <a class="reference external" href="http://localhost:8080">http://localhost:8080</a> matches the port used
in the command to start the container. More information about using the CLI can
be found in <a class="reference internal" href="../client/cli.html"><span class="doc">Command line interface</span></a>. You can also connect with any other client
application using the <a class="reference internal" href="../client/jdbc.html"><span class="doc">JDBC driver</span></a>.</p>
</section>
<section id="configuring-trino">
<h2 id="configuring-trino">Configuring Trino<a class="headerlink" href="containers.html#configuring-trino" title="Link to this heading">#</a></h2>
<p>The image already contains a default configuration to get started, and some
catalogs to allow you to explore Trino. You can also use the container with your
custom configuration files in a local <code class="docutils literal notranslate"><span class="pre">etc</span></code> directory structure as created in
the <a class="reference internal" href="deployment.html"><span class="doc">Deploying Trino</span></a>. If you mount this directory as a volume
in the path <code class="docutils literal notranslate"><span class="pre">/etc/trino</span></code> when starting the container, your configuration
is used instead of the default in the image.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ docker run --name trino -d -p 8080:8080 --volume $PWD/etc:/etc/trino trinodb/trino
</pre></div>
</div>
<p>To keep the default configuration and only configure catalogs, mount a folder
at <code class="docutils literal notranslate"><span class="pre">/etc/trino/catalog</span></code>, or individual catalog property files in it.</p>
<p>If you want to use additional plugins, mount them at <code class="docutils literal notranslate"><span class="pre">/usr/lib/trino/plugin</span></code>.</p>
<p>To avoid having to create catalog files and mount them in the container,
you can enable dynamic catalog management by setting the <code class="docutils literal notranslate"><span class="pre">CATALOG_MANAGEMENT</span></code>
environmental variable to <code class="docutils literal notranslate"><span class="pre">dynamic</span></code>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>$<span class="w"> </span>docker<span class="w"> </span>run<span class="w"> </span>--name<span class="w"> </span>trino<span class="w"> </span>-d<span class="w"> </span>-p<span class="w"> </span><span class="m">8080</span>:8080<span class="w"> </span>-e<span class="w"> </span><span class="nv">CATALOG_MANAGEMENT</span><span class="o">=</span>dynamic<span class="w"> </span>trinodb/trino
</pre></div>
</div>
<p>After connecting to Trino, execute <a class="reference internal" href="../language/sql-support.html#sql-catalog-management"><span class="std std-ref">Catalog management</span></a> statements to
create drop catalogs as desired. To make these changes persistent across
container restarts, a volume must be mounted at <code class="docutils literal notranslate"><span class="pre">/etc/trino/catalog</span></code>.</p>
</section>
<section id="cleaning-up">
<h2 id="cleaning-up">Cleaning up<a class="headerlink" href="containers.html#cleaning-up" title="Link to this heading">#</a></h2>
<p>You can stop and start the container, using the <code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">stop</span> <span class="pre">trino</span></code> and
<code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">start</span> <span class="pre">trino</span></code> commands. To fully remove the stopped container, run
<code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">rm</span> <span class="pre">trino</span></code>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="deployment.html" title="Deploying Trino"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Deploying Trino </span>
              </div>
            </a>
          
          
            <a href="kubernetes.html" title="Trino on Kubernetes with Helm"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Trino on Kubernetes with Helm </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>