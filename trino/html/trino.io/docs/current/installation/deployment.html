<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Deploying Trino &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="deployment.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Trino in a Docker container" href="containers.html" />
    <link rel="prev" title="Installation" href="../installation.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="deployment.html#installation/deployment" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Deploying Trino </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Deploying Trino </label>
    
      <a href="deployment.html#" class="md-nav__link md-nav__link--active">Deploying Trino</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="deployment.html#requirements" class="md-nav__link">Requirements</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#linux-operating-system" class="md-nav__link">Linux operating system</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#java-runtime-environment" class="md-nav__link">Java runtime environment</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#installing-trino" class="md-nav__link">Installing Trino</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#configuring-trino" class="md-nav__link">Configuring Trino</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#node-properties" class="md-nav__link">Node properties</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#jvm-config" class="md-nav__link">JVM config</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#temporary-directory" class="md-nav__link">Temporary directory</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#config-properties" class="md-nav__link">Config properties</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#catalog-properties" class="md-nav__link">Catalog properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#running-trino" class="md-nav__link">Running Trino</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="containers.html" class="md-nav__link">Trino in a Docker container</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kubernetes.html" class="md-nav__link">Trino on Kubernetes with Helm</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="plugins.html" class="md-nav__link">Plugins</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="query-resiliency.html" class="md-nav__link">Improve query processing resilience</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="deployment.html#requirements" class="md-nav__link">Requirements</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#linux-operating-system" class="md-nav__link">Linux operating system</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#java-runtime-environment" class="md-nav__link">Java runtime environment</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#installing-trino" class="md-nav__link">Installing Trino</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#configuring-trino" class="md-nav__link">Configuring Trino</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#node-properties" class="md-nav__link">Node properties</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#jvm-config" class="md-nav__link">JVM config</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="deployment.html#temporary-directory" class="md-nav__link">Temporary directory</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#config-properties" class="md-nav__link">Config properties</a>
        </li>
        <li class="md-nav__item"><a href="deployment.html#catalog-properties" class="md-nav__link">Catalog properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="deployment.html#running-trino" class="md-nav__link">Running Trino</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="deploying-trino">
<h1 id="installation-deployment--page-root">Deploying Trino<a class="headerlink" href="deployment.html#installation-deployment--page-root" title="Link to this heading">#</a></h1>
<section id="requirements">
<span id="id1"></span><h2 id="requirements">Requirements<a class="headerlink" href="deployment.html#requirements" title="Link to this heading">#</a></h2>
<section id="linux-operating-system">
<span id="requirements-linux"></span><h3 id="linux-operating-system">Linux operating system<a class="headerlink" href="deployment.html#linux-operating-system" title="Link to this heading">#</a></h3>
<ul>
<li><p>64-bit required</p></li>
<li><p>newer release preferred, especially when running on containers</p></li>
<li><p>adequate ulimits for the user that runs the Trino process. These limits may
depend on the specific Linux distribution you are using. The number of open
file descriptors needed for a particular Trino instance scales as roughly the
number of machines in the cluster, times some factor depending on the
workload. The <code class="docutils literal notranslate"><span class="pre">nofile</span></code> limit sets the maximum number of file descriptors
that a process can have, while the <code class="docutils literal notranslate"><span class="pre">nproc</span></code> limit restricts the number of
processes, and therefore threads on the JVM, a user can create. We recommend
setting limits to the following values at a minimum. Typically, this
configuration is located in <code class="docutils literal notranslate"><span class="pre">/etc/security/limits.conf</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino soft nofile 131072
trino hard nofile 131072
trino soft nproc 128000
trino hard nproc 128000
</pre></div>
</div>
</li>
</ul>
</section>
<section id="java-runtime-environment">
<span id="requirements-java"></span><h3 id="java-runtime-environment">Java runtime environment<a class="headerlink" href="deployment.html#java-runtime-environment" title="Link to this heading">#</a></h3>
<p>Trino requires a 64-bit version of Java 24, with a minimum required version of
24.0.1 and a recommendation to use the latest patch version. Earlier versions
such as Java 8, Java 11, Java 17, Java 21 or Java 23 do not work.
Newer versions such as Java 25 are not supported – they may work, but are not tested.</p>
<p>We recommend using the Eclipse Temurin OpenJDK distribution from
<a class="reference external" href="https://adoptium.net/">Adoptium</a> as the JDK for Trino, as Trino is tested
against that distribution. Eclipse Temurin is also the JDK used by the <a class="reference external" href="https://hub.docker.com/r/trinodb/trino">Trino
Docker image</a>.</p>
</section>
</section>
<section id="installing-trino">
<h2 id="installing-trino">Installing Trino<a class="headerlink" href="deployment.html#installing-trino" title="Link to this heading">#</a></h2>
<p>Download the Trino server tarball, <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-server/476/trino-server-476.tar.gz">trino-server-476.tar.gz</a>, and unpack it. The
tarball contains a single top-level directory, <code class="docutils literal notranslate"><span class="pre">trino-server-476</span></code>,
which we call the <em>installation</em> directory.</p>
<p>The default tarball contains all plugins and must be configured for use. The
minimal <code class="docutils literal notranslate"><span class="pre">server-core</span></code> tarball, <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-server-core/476/trino-server-core-476.tar.gz">trino-server-core-476.tar.gz</a>, contains a minimal
set of essential plugins, and it is therefore mostly suitable as a base for
custom tarball creation.</p>
<p>The <a class="reference external" href="https://github.com/trinodb/trino-packages">trino-packages project</a> includes
a module to create a fully configured tarball with an example configuration. The
custom tarball is ready to use and can be further configured and adjusted to
your needs.</p>
<p>Trino needs a <em>data</em> directory for storing logs, etc. By default, an
installation from the tarball uses the same location for the installation and data
directories.</p>
<p>We recommend creating a data directory outside the installation directory,
which allows it to be easily preserved when upgrading Trino. This directory path
must be configured with the <a class="reference internal" href="deployment.html#node-properties"><span class="std std-ref">Node properties</span></a>.</p>
<p>The user that runs the Trino process must have full read access to the
installation directory, and read and write access to the data directory.</p>
</section>
<section id="configuring-trino">
<h2 id="configuring-trino">Configuring Trino<a class="headerlink" href="deployment.html#configuring-trino" title="Link to this heading">#</a></h2>
<p>Create an <code class="docutils literal notranslate"><span class="pre">etc</span></code> directory inside the installation directory.
This holds the following configuration:</p>
<ul class="simple">
<li><p>Node Properties: environmental configuration specific to each node</p></li>
<li><p>JVM Config: command line options for the Java Virtual Machine</p></li>
<li><p>Config Properties: configuration for the Trino server. See the
<a class="reference internal" href="../admin/properties.html"><span class="doc">Properties reference</span></a> for available configuration properties.</p></li>
<li><p>Catalog Properties: configuration for <a class="reference internal" href="../connector.html"><span class="doc">Connectors</span></a> (data sources).
The available catalog configuration properties for a connector are described
in the respective connector documentation.</p></li>
</ul>
<section id="node-properties">
<span id="id2"></span><h3 id="node-properties">Node properties<a class="headerlink" href="deployment.html#node-properties" title="Link to this heading">#</a></h3>
<p>The node properties file, <code class="docutils literal notranslate"><span class="pre">etc/node.properties</span></code>, contains configuration
specific to each node. A <em>node</em> is a single installed instance of Trino
on a machine. This file is typically created by the deployment system when
Trino is first installed. The following is a minimal <code class="docutils literal notranslate"><span class="pre">etc/node.properties</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>node.environment=production
node.id=ffffffff-ffff-ffff-ffff-ffffffffffff
node.data-dir=/var/trino/data
</pre></div>
</div>
<p>The above properties are described below:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">node.environment</span></code>:
The name of the environment. All Trino nodes in a cluster must have the same
environment name. The name must start with a lowercase alphanumeric character
and only contain lowercase alphanumeric or underscore (<code class="docutils literal notranslate"><span class="pre">_</span></code>) characters.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">node.id</span></code>:
The unique identifier for this installation of Trino. This must be
unique for every node. This identifier should remain consistent across
reboots or upgrades of Trino. If running multiple installations of
Trino on a single machine (i.e. multiple nodes on the same machine),
each installation must have a unique identifier. The identifier must start
with an alphanumeric character and only contain alphanumeric, <code class="docutils literal notranslate"><span class="pre">-</span></code>, or <code class="docutils literal notranslate"><span class="pre">_</span></code>
characters.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">node.data-dir</span></code>:
The location (filesystem path) of the data directory. Trino stores
logs and other data here.</p></li>
</ul>
</section>
<section id="jvm-config">
<span id="id3"></span><h3 id="jvm-config">JVM config<a class="headerlink" href="deployment.html#jvm-config" title="Link to this heading">#</a></h3>
<p>The JVM config file, <code class="docutils literal notranslate"><span class="pre">etc/jvm.config</span></code>, contains a list of command line
options used for launching the Java Virtual Machine. The format of the file
is a list of options, one per line. These options are not interpreted by
the shell, so options containing spaces or other special characters should
not be quoted.</p>
<p>The following provides a good starting point for creating <code class="docutils literal notranslate"><span class="pre">etc/jvm.config</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-server
-Xmx16G
-XX:InitialRAMPercentage=80
-XX:MaxRAMPercentage=80
-XX:G1HeapRegionSize=32M
-XX:+ExplicitGCInvokesConcurrent
-XX:+ExitOnOutOfMemoryError
-XX:+HeapDumpOnOutOfMemoryError
-XX:-OmitStackTraceInFastThrow
-XX:ReservedCodeCacheSize=512M
-XX:PerMethodRecompilationCutoff=10000
-XX:PerBytecodeRecompilationCutoff=10000
-Djdk.attach.allowAttachSelf=true
-Djdk.nio.maxCachedBufferSize=2000000
-Dfile.encoding=UTF-8
# Allow loading dynamic agent used by JOL
-XX:+EnableDynamicAgentLoading
</pre></div>
</div>
<p>You must adjust the value for the memory used by Trino, specified with <code class="docutils literal notranslate"><span class="pre">-Xmx</span></code>
to the available memory on your nodes. Typically, values representing 70 to 85
percent of the total available memory is recommended. For example, if all
workers and the coordinator use nodes with 64GB of RAM, you can use <code class="docutils literal notranslate"><span class="pre">-Xmx54G</span></code>.
Trino uses most of the allocated memory for processing, with a small percentage
used by JVM-internal processes such as garbage collection.</p>
<p>The rest of the available node memory must be sufficient for the operating
system and other running services, as well as off-heap memory used for native
code initiated the JVM process.</p>
<p>On larger nodes, the percentage value can be lower. Allocation of all memory  to
the JVM or using swap space is not supported, and disabling swap space on the
operating system level is recommended.</p>
<p>Large memory allocation beyond 32GB is recommended for production clusters.</p>
<p>Because an <code class="docutils literal notranslate"><span class="pre">OutOfMemoryError</span></code> typically leaves the JVM in an
inconsistent state, we write a heap dump, for debugging, and forcibly
terminate the process when this occurs.</p>
<section id="temporary-directory">
<span id="tmp-directory"></span><h4 id="temporary-directory">Temporary directory<a class="headerlink" href="deployment.html#temporary-directory" title="Link to this heading">#</a></h4>
<p>The temporary directory used by the JVM must allow execution of code, because
Trino accesses and uses shared library binaries for purposes such as
<a class="reference internal" href="../admin/properties-general.html#file-compression"><span class="std std-ref">File compression and decompression</span></a>.</p>
<p>Specifically, the partition mount and directory must not have the <code class="docutils literal notranslate"><span class="pre">noexec</span></code> flag
set. The default <code class="docutils literal notranslate"><span class="pre">/tmp</span></code> directory is mounted with this flag in some operating
system installations, which prevents Trino from starting. You can work around
this by overriding the temporary directory by adding
<code class="docutils literal notranslate"><span class="pre">-Djava.io.tmpdir=/path/to/other/tmpdir</span></code> to the list of JVM options.</p>
</section>
</section>
<section id="config-properties">
<span id="id4"></span><h3 id="config-properties">Config properties<a class="headerlink" href="deployment.html#config-properties" title="Link to this heading">#</a></h3>
<p>The config properties file, <code class="docutils literal notranslate"><span class="pre">etc/config.properties</span></code>, contains the
configuration for the Trino server. Every Trino server can function as both a
coordinator and a worker. A cluster is required to include one coordinator, and
dedicating a machine to only perform coordination work provides the best
performance on larger clusters. Scaling and parallelization is achieved by using
many workers.</p>
<p>The following is a minimal configuration for the coordinator:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>coordinator=true
node-scheduler.include-coordinator=false
http-server.http.port=8080
discovery.uri=http://example.net:8080
</pre></div>
</div>
<p>And this is a minimal configuration for the workers:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>coordinator=false
http-server.http.port=8080
discovery.uri=http://example.net:8080
</pre></div>
</div>
<p>Alternatively, if you are setting up a single machine for testing, that
functions as both a coordinator and worker, use this configuration:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>coordinator=true
node-scheduler.include-coordinator=true
http-server.http.port=8080
discovery.uri=http://example.net:8080
</pre></div>
</div>
<p>These properties require some explanation:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">coordinator</span></code>:
Allow this Trino instance to function as a coordinator, so to
accept queries from clients and manage query execution.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">node-scheduler.include-coordinator</span></code>:
Allow scheduling work on the coordinator.
For larger clusters, processing work on the coordinator
can impact query performance because the machine’s resources are not
available for the critical task of scheduling, managing and monitoring
query execution.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">http-server.http.port</span></code>:
Specifies the port for the <a class="reference internal" href="../admin/properties-http-server.html"><span class="doc std std-doc">HTTP server</span></a>.
Trino uses HTTP for all communication, internal and external.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">discovery.uri</span></code>:
The Trino coordinator has a discovery service that is used by all the nodes
to find each other. Every Trino instance registers itself with the discovery
service on startup and continuously heartbeats to keep its registration
active. The discovery service shares the HTTP server with Trino and thus
uses the same port. Replace <code class="docutils literal notranslate"><span class="pre">example.net:8080</span></code> to match the host and
port of the Trino coordinator. If you have disabled HTTP on the coordinator,
the URI scheme must be <code class="docutils literal notranslate"><span class="pre">https</span></code>, not <code class="docutils literal notranslate"><span class="pre">http</span></code>.</p></li>
</ul>
<p>The above configuration properties are a <em>minimal set</em> to help you get started.
All additional configuration is optional and varies widely based on the specific
cluster and supported use cases. The <a class="reference internal" href="../admin.html"><span class="doc">Administration</span></a> and <a class="reference internal" href="../security.html"><span class="doc">Security</span></a> sections
contain documentation for many aspects, including <a class="reference internal" href="../admin/resource-groups.html"><span class="doc">Resource groups</span></a>
for configuring queuing policies and <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a>.</p>
<p>The <a class="reference internal" href="../admin/properties.html"><span class="doc">Properties reference</span></a> provides a comprehensive list of the supported
properties for topics such as <a class="reference internal" href="../admin/properties-general.html"><span class="doc">General properties</span></a>,
<a class="reference internal" href="../admin/properties-resource-management.html"><span class="doc">Resource management properties</span></a>,
<a class="reference internal" href="../admin/properties-query-management.html"><span class="doc">Query management properties</span></a>,
<a class="reference internal" href="../admin/properties-web-interface.html"><span class="doc">Web UI properties</span></a>, and others.</p>
<p>Further configuration can include <a class="reference internal" href="../admin/logging.html"><span class="doc std std-doc">Logging</span></a>, <a class="reference internal" href="../admin/opentelemetry.html"><span class="doc std std-doc">Observability with OpenTelemetry</span></a>,
<a class="reference internal" href="../admin/jmx.html"><span class="doc std std-doc">Monitoring with JMX</span></a>, <a class="reference internal" href="../admin/openmetrics.html"><span class="doc std std-doc">Trino metrics with OpenMetrics</span></a>, and other functionality described in the
<a class="reference internal" href="../admin.html"><span class="doc std std-doc">Administration</span></a> section.</p>
</section>
<section id="catalog-properties">
<span id="id5"></span><h3 id="catalog-properties">Catalog properties<a class="headerlink" href="deployment.html#catalog-properties" title="Link to this heading">#</a></h3>
<p>Trino accesses data in a <a class="reference internal" href="../overview/concepts.html#trino-concept-data-source"><span class="std std-ref">data source</span></a> with a
<a class="reference internal" href="../overview/concepts.html#trino-concept-connector"><span class="std std-ref">connector</span></a>, which is configured in a
<a class="reference internal" href="../overview/concepts.html#trino-concept-catalog"><span class="std std-ref">catalog</span></a>. The connector provides all the schemas and
tables inside the catalog.</p>
<p>For example, the Hive connector maps each Hive database to a schema. If the Hive
connector is configured in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog, and Hive contains a table
<code class="docutils literal notranslate"><span class="pre">clicks</span></code> in the database <code class="docutils literal notranslate"><span class="pre">web</span></code>, that table can be accessed in Trino as
<code class="docutils literal notranslate"><span class="pre">example.web.clicks</span></code>.</p>
<p>Catalogs are registered by creating a catalog properties file
in the <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code> directory.
For example, create <code class="docutils literal notranslate"><span class="pre">etc/catalog/jmx.properties</span></code> with the following
contents to mount the <code class="docutils literal notranslate"><span class="pre">jmx</span></code> connector as the <code class="docutils literal notranslate"><span class="pre">jmx</span></code> catalog:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=jmx
</pre></div>
</div>
<p>See <a class="reference internal" href="../connector.html"><span class="doc">Connectors</span></a> for more information about configuring catalogs.</p>
</section>
</section>
<section id="running-trino">
<span id="id6"></span><h2 id="running-trino">Running Trino<a class="headerlink" href="deployment.html#running-trino" title="Link to this heading">#</a></h2>
<p>The installation provides a <code class="docutils literal notranslate"><span class="pre">bin/launcher</span></code> script that can be used manually
or as a daemon startup script. It accepts the following commands:</p>
<table id="id7">
<caption><span class="caption-text"><code class="docutils literal notranslate"><span class="pre">launcher</span></code> commands</span><a class="headerlink" href="deployment.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 15%"/>
<col style="width: 85%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Command</p></th>
<th class="head"><p>Action</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">run</span></code></p></td>
<td><p>Starts the server in the foreground and leaves it running. To shut down
the server, use Ctrl+C in this terminal or the <code class="docutils literal notranslate"><span class="pre">stop</span></code> command from
another terminal.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">start</span></code></p></td>
<td><p>Starts the server as a daemon and returns its process ID.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">stop</span></code></p></td>
<td><p>Shuts down a server started with either <code class="docutils literal notranslate"><span class="pre">start</span></code> or <code class="docutils literal notranslate"><span class="pre">run</span></code>. Sends the
SIGTERM signal.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">restart</span></code></p></td>
<td><p>Stops then restarts a running server, or starts a stopped server,
assigning a new process ID.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kill</span></code></p></td>
<td><p>Shuts down a possibly hung server by sending the SIGKILL signal.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">status</span></code></p></td>
<td><p>Prints a status line, either <em>Stopped pid</em> or <em>Running as pid</em>.</p></td>
</tr>
</tbody>
</table>
<p>A number of additional options allow you to specify configuration file and
directory locations, as well as Java options. Run the launcher with <code class="docutils literal notranslate"><span class="pre">--help</span></code>
to see the supported commands, command line options, and default values.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">-v</span></code> or <code class="docutils literal notranslate"><span class="pre">--verbose</span></code> option for each command prepends the server’s
current settings before the command’s usual output.</p>
<p>Trino can be started as a daemon by running the following:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>bin/launcher start
</pre></div>
</div>
<p>Use the status command with the verbose option for the pid and a list of
configuration settings:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>bin/launcher -v status
</pre></div>
</div>
<p>Alternatively, it can be run in the foreground, with the logs and other
output written to stdout/stderr. Both streams should be captured
if using a supervision system like daemontools:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>bin/launcher run
</pre></div>
</div>
<p>The launcher configures default values for the configuration directory <code class="docutils literal notranslate"><span class="pre">etc</span></code>,
configuration files in <code class="docutils literal notranslate"><span class="pre">etc</span></code>, the data directory identical to the installation
directory, the pid file as <code class="docutils literal notranslate"><span class="pre">var/run/launcher.pid</span></code> and log files in the <code class="docutils literal notranslate"><span class="pre">var/log</span></code>
directory.</p>
<p>You can change these values to adjust your Trino usage to any requirements, such
as using a directory outside the installation directory, specific mount points
or locations, and even using other file names. For example, the <a class="reference external" href="https://github.com/trinodb/trino-packages">Trino
RPM</a> adjusts the used directories to
better follow the Linux Filesystem Hierarchy Standard (FHS).</p>
<p>After starting Trino, you can find log files in the <code class="docutils literal notranslate"><span class="pre">log</span></code> directory inside
the data directory <code class="docutils literal notranslate"><span class="pre">var</span></code>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">launcher.log</span></code>:
This log is created by the launcher and is connected to the stdout
and stderr streams of the server. It contains a few log messages
that occur while the server logging is being initialized, and any
errors or diagnostics produced by the JVM.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">server.log</span></code>:
This is the main log file used by Trino. It typically contains
the relevant information if the server fails during initialization.
It is automatically rotated and compressed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">http-request.log</span></code>:
This is the HTTP request log which contains every HTTP request
received by the server. It is automatically rotated and compressed.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../installation.html" title="Installation"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Installation </span>
              </div>
            </a>
          
          
            <a href="containers.html" title="Trino in a Docker container"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Trino in a Docker container </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>