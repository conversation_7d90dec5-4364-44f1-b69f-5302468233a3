<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Improve query processing resilience &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="query-resiliency.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Clients" href="../client.html" />
    <link rel="prev" title="Plugins" href="plugins.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="query-resiliency.html#installation/query-resiliency" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Improve query processing resilience </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="deployment.html" class="md-nav__link">Deploying Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="containers.html" class="md-nav__link">Trino in a Docker container</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kubernetes.html" class="md-nav__link">Trino on Kubernetes with Helm</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="plugins.html" class="md-nav__link">Plugins</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Improve query processing resilience </label>
    
      <a href="query-resiliency.html#" class="md-nav__link md-nav__link--active">Improve query processing resilience</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="query-resiliency.html#architecture" class="md-nav__link">Architecture</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#best-practices-and-considerations" class="md-nav__link">Best practices and considerations</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#next-steps" class="md-nav__link">Next steps</a>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="query-resiliency.html#architecture" class="md-nav__link">Architecture</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#best-practices-and-considerations" class="md-nav__link">Best practices and considerations</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="query-resiliency.html#next-steps" class="md-nav__link">Next steps</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="improve-query-processing-resilience">
<h1 id="installation-query-resiliency--page-root">Improve query processing resilience<a class="headerlink" href="query-resiliency.html#installation-query-resiliency--page-root" title="Link to this heading">#</a></h1>
<p>You can configure Trino to be more resilient against failures during query
processing by enabling fault-tolerant execution. This allows Trino to handle
larger queries such as batch operations without worker node interruptions
causing the query to fail.</p>
<p>When configured, the Trino cluster buffers data used by workers during query
processing. If processing on a worker node fails for any reason, such as a
network outage or running out of available resources, the coordinator
reschedules processing of the failed piece of work on another worker. This
allows query processing to continue using buffered data.</p>
<section id="architecture">
<h2 id="architecture">Architecture<a class="headerlink" href="query-resiliency.html#architecture" title="Link to this heading">#</a></h2>
<p>The coordinator node uses a configured exchange manager service that buffers
data during query processing in an external location, such as an S3 object
storage bucket. Worker nodes send data to the buffer as they execute their
query tasks.</p>
</section>
<section id="best-practices-and-considerations">
<h2 id="best-practices-and-considerations">Best practices and considerations<a class="headerlink" href="query-resiliency.html#best-practices-and-considerations" title="Link to this heading">#</a></h2>
<p>A fault-tolerant cluster is best suited for large batch queries. Users may
experience latency or similar behavior if they issue a high volume of
short-running queries on a fault-tolerant cluster. As such, it is recommended to
run a dedicated fault-tolerant cluster for handling batch operations, separate
from a cluster that is designated for a higher query volume.</p>
<p>Catalogs using the following connectors support fault-tolerant execution of read
and write operations:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../connector/delta-lake.html"><span class="doc">Delta Lake connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/hive.html"><span class="doc">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/iceberg.html"><span class="doc">Iceberg connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/mysql.html"><span class="doc">MySQL connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/postgresql.html"><span class="doc">PostgreSQL connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/sqlserver.html"><span class="doc">SQL Server connector</span></a></p></li>
</ul>
<p>Catalogs using other connectors only support fault-tolerant execution of read
operations. When fault-tolerant execution is enabled on a cluster, write
operations fail on any catalogs that do not support fault-tolerant
execution of those operations.</p>
<p>The exchange manager may send a large amount of data to the exchange storage,
resulting in high I/O load on that storage. You can configure multiple storage
locations for use by the exchange manager to help balance the I/O load between
them.</p>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="query-resiliency.html#configuration" title="Link to this heading">#</a></h2>
<p>The following steps describe how to configure a Trino cluster for
fault-tolerant execution with an S3-based exchange:</p>
<ol class="arabic">
<li><p>Set up an S3 bucket to use as the exchange storage. For this example we are
using an AWS S3 bucket, but other storage options are described in the
<a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">reference documentation</span></a>
as well. You can use multiple S3 buckets for exchange storage.</p>
<p>For each bucket in AWS, collect the following information:</p>
<ul class="simple">
<li><p>S3 URI location for the bucket, such as <code class="docutils literal notranslate"><span class="pre">s3://exchange-spooling-bucket</span></code></p></li>
<li><p>Region that the bucket is located in, such as <code class="docutils literal notranslate"><span class="pre">us-west-1</span></code></p></li>
<li><p>AWS access and secret keys for the bucket</p></li>
</ul>
</li>
<li><p>For a <a class="reference internal" href="kubernetes.html"><span class="doc">Kubernetes deployment of Trino</span></a>, add
the following exchange manager configuration in the
<code class="docutils literal notranslate"><span class="pre">server.exchangeManager</span></code> and <code class="docutils literal notranslate"><span class="pre">additionalExchangeManagerProperties</span></code>
sections of the Helm chart, using the gathered S3 bucket information:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">server</span><span class="p">:</span>
<span class="w">  </span><span class="nt">exchangeManager</span><span class="p">:</span>
<span class="w">    </span><span class="l l-Scalar l-Scalar-Plain">name=filesystem</span>
<span class="w">    </span><span class="l l-Scalar l-Scalar-Plain">base-directories=s3://exchange-spooling-bucket-1,s3://exchange-spooling-bucket-2</span>

<span class="nt">additionalExchangeManagerProperties</span><span class="p">:</span>
<span class="w">  </span><span class="l l-Scalar l-Scalar-Plain">exchange.s3.region=us-west-1</span>
<span class="w">  </span><span class="l l-Scalar l-Scalar-Plain">exchange.s3.aws-access-key=example-access-key</span>
<span class="w">  </span><span class="l l-Scalar l-Scalar-Plain">exchange.s3.aws-secret-key=example-secret-key</span>
</pre></div>
</div>
<p>In non-Kubernetes installations, the same properties must be defined in an
<code class="docutils literal notranslate"><span class="pre">exchange-manager.properties</span></code> configuration file on the coordinator and
all worker nodes.</p>
</li>
<li><p>Add the following configuration for fault-tolerant execution in the
<code class="docutils literal notranslate"><span class="pre">additionalConfigProperties:</span></code> section of the Helm chart:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">additionalConfigProperties</span><span class="p">:</span>
<span class="w">  </span><span class="l l-Scalar l-Scalar-Plain">retry-policy=TASK</span>
</pre></div>
</div>
<p>In non-Kubernetes installations, the same property must be defined in the
<code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file on the coordinator and all worker nodes.</p>
</li>
<li><p>Re-deploy your instance of Trino or, for non-Kubernetes
installations, restart the cluster.</p></li>
</ol>
<p>Your Trino cluster is now configured with fault-tolerant query
execution. If a query run on the cluster would normally fail due to an
interruption of query processing, fault-tolerant execution now resumes the
query processing to ensure successful execution of the query.</p>
</section>
<section id="next-steps">
<h2 id="next-steps">Next steps<a class="headerlink" href="query-resiliency.html#next-steps" title="Link to this heading">#</a></h2>
<p>For more information about fault-tolerant execution, including simple query
retries that do not require an exchange manager and advanced configuration
operations, see the <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">reference documentation</span></a>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="plugins.html" title="Plugins"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Plugins </span>
              </div>
            </a>
          
          
            <a href="../client.html" title="Clients"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Clients </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>