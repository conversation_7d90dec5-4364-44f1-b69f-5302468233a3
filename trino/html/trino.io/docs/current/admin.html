<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Administration &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="_static/trino.css@v=b5fc78e7.css" />
    <script src="_static/jquery.js@v=5d32c60e"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="_static/documentation_options.js@v=febf07ea"></script>
    <script src="_static/doctools.js@v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="admin.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Web UI" href="admin/web-interface.html" />
    <link rel="prev" title="Secrets" href="security/secrets.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="admin.html#admin" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Administration </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../versions.json",
        target_loc = "../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Administration </label>
    
      <a href="admin.html#" class="md-nav__link md-nav__link--active">Administration</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="admin.html#event-listeners" class="md-nav__link">Event listeners</a>
        </li>
        <li class="md-nav__item"><a href="admin.html#properties-reference" class="md-nav__link">Properties reference</a>
        </li>
    </ul>
</nav>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="admin/web-interface.html" class="md-nav__link">Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/preview-web-interface.html" class="md-nav__link">Preview Web UI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/logging.html" class="md-nav__link">Logging</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/tuning.html" class="md-nav__link">Tuning Trino</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/jmx.html" class="md-nav__link">Monitoring with JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/opentelemetry.html" class="md-nav__link">Observability with OpenTelemetry</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/openmetrics.html" class="md-nav__link">Trino metrics with OpenMetrics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/spill.html" class="md-nav__link">Spill to disk</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/resource-groups.html" class="md-nav__link">Resource groups</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/session-property-managers.html" class="md-nav__link">Session property managers</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/dist-sort.html" class="md-nav__link">Distributed sort</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/dynamic-filtering.html" class="md-nav__link">Dynamic filtering</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/graceful-shutdown.html" class="md-nav__link">Graceful shutdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/fault-tolerant-execution.html" class="md-nav__link">Fault-tolerant execution</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/event-listeners-http.html" class="md-nav__link">HTTP event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/event-listeners-kafka.html" class="md-nav__link">Kafka event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/event-listeners-mysql.html" class="md-nav__link">MySQL event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/event-listeners-openlineage.html" class="md-nav__link">OpenLineage event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin/properties.html" class="md-nav__link">Properties reference</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="admin.html#event-listeners" class="md-nav__link">Event listeners</a>
        </li>
        <li class="md-nav__item"><a href="admin.html#properties-reference" class="md-nav__link">Properties reference</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="administration">
<h1 id="admin--page-root">Administration<a class="headerlink" href="admin.html#admin--page-root" title="Link to this heading">#</a></h1>
<p>The following documents cover a number of different aspect of configuring,
running, and managing Trino clusters.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/web-interface.html">Web UI</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/preview-web-interface.html">Preview Web UI</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/logging.html">Logging</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/tuning.html">Tuning Trino</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/jmx.html">Monitoring with JMX</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/opentelemetry.html">Observability with OpenTelemetry</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/openmetrics.html">Trino metrics with OpenMetrics</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/properties.html">Properties reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/spill.html">Spill to disk</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/resource-groups.html">Resource groups</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/session-property-managers.html">Session property managers</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/dist-sort.html">Distributed sort</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/dynamic-filtering.html">Dynamic filtering</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/graceful-shutdown.html">Graceful shutdown</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/fault-tolerant-execution.html">Fault-tolerant execution</a></li>
</ul>
</div>
<p>Details about connecting <a class="reference internal" href="overview/concepts.html#trino-concept-data-source"><span class="std std-ref">data sources</span></a> as
<a class="reference internal" href="overview/concepts.html#trino-concept-catalog"><span class="std std-ref">catalogs</span></a> are available in the <a class="reference internal" href="connector.html"><span class="doc std std-doc">connector
documentation</span></a>.</p>
<section id="event-listeners">
<span id="admin-event-listeners"></span><h2 id="event-listeners">Event listeners<a class="headerlink" href="admin.html#event-listeners" title="Link to this heading">#</a></h2>
<p>Event listeners are plugins that allow streaming of query events, such as query
started or query finished, to an external system.</p>
<p>Using an event listener you can process and store the query events in a separate
system for long periods of time. Some of these external systems can be queried
with Trino for further analysis or reporting.</p>
<p>The following event listeners are available:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/event-listeners-http.html">HTTP event listener</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/event-listeners-kafka.html">Kafka event listener</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/event-listeners-mysql.html">MySQL event listener</a></li>
<li class="toctree-l1"><a class="reference internal" href="admin/event-listeners-openlineage.html">OpenLineage event listener</a></li>
</ul>
</div>
<p>Unrelated to event listeners, the coordinator stores information about recent
queries in memory for usage by the <a class="reference internal" href="admin/web-interface.html"><span class="doc std std-doc">Web UI</span></a> - see also
<code class="docutils literal notranslate"><span class="pre">query.max-history</span></code> and <code class="docutils literal notranslate"><span class="pre">query.min-expire-age</span></code> in
<a class="reference internal" href="admin/properties-query-management.html"><span class="doc std std-doc">Query management properties</span></a>.</p>
</section>
<section id="properties-reference">
<h2 id="properties-reference">Properties reference<a class="headerlink" href="admin.html#properties-reference" title="Link to this heading">#</a></h2>
<p>Many aspects for running Trino are <a class="reference internal" href="installation/deployment.html#config-properties"><span class="std std-ref">configured with properties</span></a>.
The following pages provide an overview and details for specific topics.</p>
<div class="toctree-wrapper compound">
</div>
<ul class="simple">
<li><p><a class="reference internal" href="admin/properties.html"><span class="doc std std-doc">Properties reference overview</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-general.html"><span class="doc std std-doc">General properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-client-protocol.html"><span class="doc std std-doc">Client protocol properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-http-server.html"><span class="doc std std-doc">HTTP server properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-resource-management.html"><span class="doc std std-doc">Resource management properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-query-management.html"><span class="doc std std-doc">Query management properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-catalog.html"><span class="doc std std-doc">Catalog management properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-sql-environment.html"><span class="doc std std-doc">SQL environment properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-spilling.html"><span class="doc std std-doc">Spilling properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-exchange.html"><span class="doc std std-doc">Exchange properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-task.html"><span class="doc std std-doc">Task properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-write-partitioning.html"><span class="doc std std-doc">Write partitioning properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-writer-scaling.html"><span class="doc std std-doc">Writer scaling properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-node-scheduler.html"><span class="doc std std-doc">Node scheduler properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-optimizer.html"><span class="doc std std-doc">Optimizer properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-logging.html"><span class="doc std std-doc">Logging properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-web-interface.html"><span class="doc std std-doc">Web UI properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-regexp-function.html"><span class="doc std std-doc">Regular expression function properties</span></a></p></li>
<li><p><a class="reference internal" href="admin/properties-http-client.html"><span class="doc std std-doc">HTTP client properties</span></a></p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="security/secrets.html" title="Secrets"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Secrets </span>
              </div>
            </a>
          
          
            <a href="admin/web-interface.html" title="Web UI"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Web UI </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>