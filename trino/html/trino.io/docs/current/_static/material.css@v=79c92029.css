.dropdown {
    width: 125px;
    vertical-align: middle;
}

.dropdownbutton {
    color: inherit;
    font-weight: 700;
    font-size: .65rem;

}

.dropdown-content {
    display: none;
    position: absolute;
    z-index: 1;
    background-color: inherit;
}

.dropdown-content a {
    display: block;
    width: 125px;
    margin: 8px;
    font-size: .65rem;
    font-weight: 200;
}

/* Change color of dropdown links on hover */
.dropdown-content a:hover {}

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {display: block;}

/* Change the background color of the dropdown button when the dropdown content is shown */
.dropdown:hover .dropbtn {}