.md-nav--primary ul, .md-nav--primary ul li ul, .md-nav--secondary ul, .md-nav--secondary ul li ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.md-nav--primary ul li, .md-nav--primary ul li ul li, .md-nav--secondary ul li, .md-nav--secondary ul li ul li {
    padding: 0 .6rem;
}

.md-nav--primary a.reference, .md-nav--secondary a.reference {
    display: block;
    margin-top: .625em;
    transition: color .125s;
    text-overflow: ellipsis;
    cursor: pointer;
    overflow: hidden
}

.md-typeset td p, .md-typeset th p {
    margin: 0
}

.md-typeset .admonition, .md-typeset details {
    font-size: 0.8rem
}

.classifier:before {
    font-style: normal;
    margin: 0.5em;
    content: ":";
}

dl.footnote > dt, dl.citation > dt {
    float: left;
}

code.xref {
    background-color: transparent;
    font-weight: bold;
}

table.docutils {
    width: 100%;
}

.longtable tr td:first-child {
    width: 50%;
    white-space: nowrap;
}

dt:target {
    margin-top: -3.55rem;
    padding-top: 3.45rem;
}

.md-typeset code {
    margin: 0
}

ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left;
}

span.highlighted {
    background-color: #fbe54e;
}

p.rubric {
    margin-top: 1rem;
    font-weight: bold;
}

dl.field-list > dt {
    font-weight: bold;
    word-break: break-word;
    padding-left: 0.5em;
    padding-right: 5px;
}

table.longtable {
    border-collapse: collapse;
}

.longtable tr {
    border: solid;
    border-width: 1px 0;
}

.longtable tr:first-child {
    border-top: none;
}

.md-tabs code, kbd, pre, .md-footer-nav code, kbd, pre {
    color: rgb(255, 255, 255);
}

.toctree-wrapper.compound li {
    list-style: none;
    padding: 0;
    margin: 0 0 0.1rem 0.2rem;
}

table.indextable.genindextable li {
    margin: 0 0.5rem;
}

table.indextable.genindextable li ul li {
    list-style: none;
}

p.highlight-link {
    margin: 0.625rem 0 0 0;
}

table.longtable.docutils.align-default {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media only screen and (max-width: 40em) {
    table.longtable.docutils.align-default {
        display: block;
    }

    .longtable.docutils.align-default td {
        padding-right: 1rem;
    }
}

.md-nav__extra_link:after {
    font-family: Material Icons;
    font-style: normal;
    font-variant: normal;
    font-weight: 400;
    line-height: 1;
    text-transform: none;
    white-space: nowrap;
    speak: none;
    word-wrap: normal;
    direction: ltr
}

.md-nav__extra_link {
    display: block;
    margin-top: .625em;
    transition: color .125s;
    text-overflow: ellipsis;
    cursor: pointer;
    overflow: hidden
}

.md-nav__extra_link:active {
    color: #3f51b5
}

.md-nav__extra_link:focus, .md-nav__extra_link:hover {
    color: #536dfe
}

@media only screen and (max-width: 76.1875em) {
    .md-nav--primary .md-nav--secondary .md-nav__extra_link {
        position: static
    }

    .md-nav--primary .md-nav--secondary .md-nav .md-nav__extra_link {
        padding-left: 1.4rem
    }

    .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav__extra_link {
        padding-left: 2.6rem
    }
}

[data-md-color-primary=red] .md-nav__extra_link:active {
    color: #ef5350
}

[data-md-color-primary=red] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=pink] .md-nav__extra_link:active {
    color: #e91e63
}

[data-md-color-primary=pink] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=purple] .md-nav__extra_link:active {
    color: #ab47bc
}

[data-md-color-primary=purple] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=deep-purple] .md-nav__extra_link:active {
    color: #7e57c2
}

[data-md-color-primary=deep-purple] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=indigo] .md-nav__extra_link:active {
    color: #3f51b5
}

[data-md-color-primary=indigo] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=blue] .md-nav__extra_link:active {
    color: #2196f3
}

[data-md-color-primary=blue] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=light-blue] .md-nav__extra_link:active {
    color: #03a9f4
}

[data-md-color-primary=light-blue] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=cyan] .md-nav__extra_link:active {
    color: #00bcd4
}

[data-md-color-primary=cyan] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=teal] .md-nav__extra_link:active {
    color: #009688
}

[data-md-color-primary=teal] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=green] .md-nav__extra_link:active {
    color: #4caf50
}

[data-md-color-primary=green] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=light-green] .md-nav__extra_link:active {
    color: #7cb342
}

[data-md-color-primary=light-green] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=lime] .md-nav__extra_link:active {
    color: #c0ca33
}

[data-md-color-primary=lime] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=yellow] .md-nav__extra_link:active {
    color: #f9a825
}

[data-md-color-primary=yellow] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=amber] .md-nav__extra_link:active {
    color: #ffa000
}

[data-md-color-primary=amber] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=orange] .md-nav__extra_link:active {
    color: #fb8c00
}

[data-md-color-primary=orange] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=deep-orange] .md-nav__extra_link:active {
    color: #ff7043
}

[data-md-color-primary=deep-orange] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=brown] .md-nav__extra_link:active {
    color: #795548
}

[data-md-color-primary=brown] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=grey] .md-nav__extra_link:active {
    color: #757575
}

[data-md-color-primary=grey] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-primary=blue-grey] .md-nav__extra_link:active {
    color: #546e7a
}

[data-md-color-primary=blue-grey] .md-nav__item--nested > .md-nav__extra_link {
    color: inherit
}

[data-md-color-accent=red] .md-nav__extra_link:focus, [data-md-color-accent=red] .md-nav__extra_link:hover {
    color: #ff1744
}

[data-md-color-accent=pink] .md-nav__extra_link:focus, [data-md-color-accent=pink] .md-nav__extra_link:hover {
    color: #f50057
}

[data-md-color-accent=purple] .md-nav__extra_link:focus, [data-md-color-accent=purple] .md-nav__extra_link:hover {
    color: #e040fb
}

[data-md-color-accent=deep-purple] .md-nav__extra_link:focus, [data-md-color-accent=deep-purple] .md-nav__extra_link:hover {
    color: #7c4dff
}

[data-md-color-accent=indigo] .md-nav__extra_link:focus, [data-md-color-accent=indigo] .md-nav__extra_link:hover {
    color: #536dfe
}

[data-md-color-accent=blue] .md-nav__extra_link:focus, [data-md-color-accent=blue] .md-nav__extra_link:hover {
    color: #448aff
}

[data-md-color-accent=light-blue] .md-nav__extra_link:focus, [data-md-color-accent=light-blue] .md-nav__extra_link:hover {
    color: #0091ea
}

[data-md-color-accent=cyan] .md-nav__extra_link:focus, [data-md-color-accent=cyan] .md-nav__extra_link:hover {
    color: #00b8d4
}

[data-md-color-accent=teal] .md-nav__extra_link:focus, [data-md-color-accent=teal] .md-nav__extra_link:hover {
    color: #00bfa5
}

[data-md-color-accent=green] .md-nav__extra_link:focus, [data-md-color-accent=green] .md-nav__extra_link:hover {
    color: #00c853
}

[data-md-color-accent=light-green] .md-nav__extra_link:focus, [data-md-color-accent=light-green] .md-nav__extra_link:hover {
    color: #64dd17
}

[data-md-color-accent=lime] .md-nav__extra_link:focus, [data-md-color-accent=lime] .md-nav__extra_link:hover {
    color: #aeea00
}

[data-md-color-accent=yellow] .md-nav__extra_link:focus, [data-md-color-accent=yellow] .md-nav__extra_link:hover {
    color: #ffd600
}

[data-md-color-accent=amber] .md-nav__extra_link:focus, [data-md-color-accent=amber] .md-nav__extra_link:hover {
    color: #ffab00
}

[data-md-color-accent=orange] .md-nav__extra_link:focus, [data-md-color-accent=orange] .md-nav__extra_link:hover {
    color: #ff9100
}

[data-md-color-accent=deep-orange] .md-nav__extra_link:focus, [data-md-color-accent=deep-orange] .md-nav__extra_link:hover {
    color: #ff6e40
}

div.rendered_html table {
    font-size: 0.8rem !important;
}

/* TODO: This is hacky; should have own class and not derive from link */
.md-nav span.caption {
    font-weight: 700;
    pointer-events: none;
}

.md-nav span.caption:hover, .md-nav span.caption:active {
    color: #000;
}

.md-typeset img.align-right {
    clear: right;
    float: right;
    margin-left: 1em;
}

.md-typeset img.align-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

/* GH 93 */
dl.citation dt span.brackets {
  margin-right: 0.3rem;
}
