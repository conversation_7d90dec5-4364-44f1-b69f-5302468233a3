button[data-md-color-accent],
button[data-md-color-primary] {
    width: 6.5rem;
    margin-bottom: .2rem;
    padding: 1.2rem .4rem .2rem;
    transition: background-color .25s, opacity .25s;
    border-radius: .1rem;
    color: #fff;
    font-size: .64rem;
    text-align: left;
    cursor: pointer
}

button[data-md-color-accent]:hover,
button[data-md-color-primary]:hover {
    opacity: .75
}

button[data-md-color-primary=red] {
    background-color: #ef5350
}

[data-md-color-primary=red] .md-typeset a {
    color: #ef5350
}

[data-md-color-primary=red] .md-header,
[data-md-color-primary=red] .md-hero {
    background-color: #ef5350
}

[data-md-color-primary=red] .md-nav__link--active,
[data-md-color-primary=red] .md-nav__link:active {
    color: #ef5350
}

[data-md-color-primary=red] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=pink] {
    background-color: #e91e63
}

[data-md-color-primary=pink] .md-typeset a {
    color: #e91e63
}

[data-md-color-primary=pink] .md-header,
[data-md-color-primary=pink] .md-hero {
    background-color: #e91e63
}

[data-md-color-primary=pink] .md-nav__link--active,
[data-md-color-primary=pink] .md-nav__link:active {
    color: #e91e63
}

[data-md-color-primary=pink] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=purple] {
    background-color: #ab47bc
}

[data-md-color-primary=purple] .md-typeset a {
    color: #ab47bc
}

[data-md-color-primary=purple] .md-header,
[data-md-color-primary=purple] .md-hero {
    background-color: #ab47bc
}

[data-md-color-primary=purple] .md-nav__link--active,
[data-md-color-primary=purple] .md-nav__link:active {
    color: #ab47bc
}

[data-md-color-primary=purple] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=deep-purple] {
    background-color: #7e57c2
}

[data-md-color-primary=deep-purple] .md-typeset a {
    color: #7e57c2
}

[data-md-color-primary=deep-purple] .md-header,
[data-md-color-primary=deep-purple] .md-hero {
    background-color: #7e57c2
}

[data-md-color-primary=deep-purple] .md-nav__link--active,
[data-md-color-primary=deep-purple] .md-nav__link:active {
    color: #7e57c2
}

[data-md-color-primary=deep-purple] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=indigo] {
    background-color: #3f51b5
}

[data-md-color-primary=indigo] .md-typeset a {
    color: #3f51b5
}

[data-md-color-primary=indigo] .md-header,
[data-md-color-primary=indigo] .md-hero {
    background-color: #3f51b5
}

[data-md-color-primary=indigo] .md-nav__link--active,
[data-md-color-primary=indigo] .md-nav__link:active {
    color: #3f51b5
}

[data-md-color-primary=indigo] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=blue] {
    background-color: #2196f3
}

[data-md-color-primary=blue] .md-typeset a {
    color: #2196f3
}

[data-md-color-primary=blue] .md-header,
[data-md-color-primary=blue] .md-hero {
    background-color: #2196f3
}

[data-md-color-primary=blue] .md-nav__link--active,
[data-md-color-primary=blue] .md-nav__link:active {
    color: #2196f3
}

[data-md-color-primary=blue] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=light-blue] {
    background-color: #03a9f4
}

[data-md-color-primary=light-blue] .md-typeset a {
    color: #03a9f4
}

[data-md-color-primary=light-blue] .md-header,
[data-md-color-primary=light-blue] .md-hero {
    background-color: #03a9f4
}

[data-md-color-primary=light-blue] .md-nav__link--active,
[data-md-color-primary=light-blue] .md-nav__link:active {
    color: #03a9f4
}

[data-md-color-primary=light-blue] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=cyan] {
    background-color: #00bcd4
}

[data-md-color-primary=cyan] .md-typeset a {
    color: #00bcd4
}

[data-md-color-primary=cyan] .md-header,
[data-md-color-primary=cyan] .md-hero {
    background-color: #00bcd4
}

[data-md-color-primary=cyan] .md-nav__link--active,
[data-md-color-primary=cyan] .md-nav__link:active {
    color: #00bcd4
}

[data-md-color-primary=cyan] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=teal] {
    background-color: #009688
}

[data-md-color-primary=teal] .md-typeset a {
    color: #009688
}

[data-md-color-primary=teal] .md-header,
[data-md-color-primary=teal] .md-hero {
    background-color: #009688
}

[data-md-color-primary=teal] .md-nav__link--active,
[data-md-color-primary=teal] .md-nav__link:active {
    color: #009688
}

[data-md-color-primary=teal] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=green] {
    background-color: #4caf50
}

[data-md-color-primary=green] .md-typeset a {
    color: #4caf50
}

[data-md-color-primary=green] .md-header,
[data-md-color-primary=green] .md-hero {
    background-color: #4caf50
}

[data-md-color-primary=green] .md-nav__link--active,
[data-md-color-primary=green] .md-nav__link:active {
    color: #4caf50
}

[data-md-color-primary=green] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=light-green] {
    background-color: #7cb342
}

[data-md-color-primary=light-green] .md-typeset a {
    color: #7cb342
}

[data-md-color-primary=light-green] .md-header,
[data-md-color-primary=light-green] .md-hero {
    background-color: #7cb342
}

[data-md-color-primary=light-green] .md-nav__link--active,
[data-md-color-primary=light-green] .md-nav__link:active {
    color: #7cb342
}

[data-md-color-primary=light-green] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=lime] {
    background-color: #c0ca33
}

[data-md-color-primary=lime] .md-typeset a {
    color: #c0ca33
}

[data-md-color-primary=lime] .md-header,
[data-md-color-primary=lime] .md-hero {
    background-color: #c0ca33
}

[data-md-color-primary=lime] .md-nav__link--active,
[data-md-color-primary=lime] .md-nav__link:active {
    color: #c0ca33
}

[data-md-color-primary=lime] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=yellow] {
    background-color: #f9a825
}

[data-md-color-primary=yellow] .md-typeset a {
    color: #f9a825
}

[data-md-color-primary=yellow] .md-header,
[data-md-color-primary=yellow] .md-hero {
    background-color: #f9a825
}

[data-md-color-primary=yellow] .md-nav__link--active,
[data-md-color-primary=yellow] .md-nav__link:active {
    color: #f9a825
}

[data-md-color-primary=yellow] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=amber] {
    background-color: #ffa000
}

[data-md-color-primary=amber] .md-typeset a {
    color: #ffa000
}

[data-md-color-primary=amber] .md-header,
[data-md-color-primary=amber] .md-hero {
    background-color: #ffa000
}

[data-md-color-primary=amber] .md-nav__link--active,
[data-md-color-primary=amber] .md-nav__link:active {
    color: #ffa000
}

[data-md-color-primary=amber] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=orange] {
    background-color: #fb8c00
}

[data-md-color-primary=orange] .md-typeset a {
    color: #fb8c00
}

[data-md-color-primary=orange] .md-header,
[data-md-color-primary=orange] .md-hero {
    background-color: #fb8c00
}

[data-md-color-primary=orange] .md-nav__link--active,
[data-md-color-primary=orange] .md-nav__link:active {
    color: #fb8c00
}

[data-md-color-primary=orange] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=deep-orange] {
    background-color: #ff7043
}

[data-md-color-primary=deep-orange] .md-typeset a {
    color: #ff7043
}

[data-md-color-primary=deep-orange] .md-header,
[data-md-color-primary=deep-orange] .md-hero {
    background-color: #ff7043
}

[data-md-color-primary=deep-orange] .md-nav__link--active,
[data-md-color-primary=deep-orange] .md-nav__link:active {
    color: #ff7043
}

[data-md-color-primary=deep-orange] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=brown] {
    background-color: #795548
}

[data-md-color-primary=brown] .md-typeset a {
    color: #795548
}

[data-md-color-primary=brown] .md-header,
[data-md-color-primary=brown] .md-hero {
    background-color: #795548
}

[data-md-color-primary=brown] .md-nav__link--active,
[data-md-color-primary=brown] .md-nav__link:active {
    color: #795548
}

[data-md-color-primary=brown] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=grey] {
    background-color: #757575
}

[data-md-color-primary=grey] .md-typeset a {
    color: #757575
}

[data-md-color-primary=grey] .md-header,
[data-md-color-primary=grey] .md-hero {
    background-color: #757575
}

[data-md-color-primary=grey] .md-nav__link--active,
[data-md-color-primary=grey] .md-nav__link:active {
    color: #757575
}

[data-md-color-primary=grey] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=blue-grey] {
    background-color: #546e7a
}

[data-md-color-primary=blue-grey] .md-typeset a {
    color: #546e7a
}

[data-md-color-primary=blue-grey] .md-header,
[data-md-color-primary=blue-grey] .md-hero {
    background-color: #546e7a
}

[data-md-color-primary=blue-grey] .md-nav__link--active,
[data-md-color-primary=blue-grey] .md-nav__link:active {
    color: #546e7a
}

[data-md-color-primary=blue-grey] .md-nav__item--nested>.md-nav__link {
    color: inherit
}

button[data-md-color-primary=white] {
    box-shadow: inset 0 0 .05rem rgba(0, 0, 0, .54)
}

[data-md-color-primary=white] .md-header,
[data-md-color-primary=white] .md-hero,
button[data-md-color-primary=white] {
    background-color: #fff;
    color: rgba(0, 0, 0, .87)
}

[data-md-color-primary=white] .md-hero--expand {
    border-bottom: .05rem solid rgba(0, 0, 0, .07)
}

button[data-md-color-accent=red] {
    background-color: #ff1744
}

[data-md-color-accent=red] .md-typeset a:active,
[data-md-color-accent=red] .md-typeset a:hover {
    color: #ff1744
}

[data-md-color-accent=red] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=red] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #ff1744
}

[data-md-color-accent=red] .md-nav__link:focus,
[data-md-color-accent=red] .md-nav__link:hover,
[data-md-color-accent=red] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=red] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=red] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=red] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=red] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=red] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=red] .md-typeset [id]:target .headerlink {
    color: #ff1744
}

[data-md-color-accent=red] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff1744
}

[data-md-color-accent=red] .md-search-result__link:hover,
[data-md-color-accent=red] .md-search-result__link[data-md-state=active] {
    background-color: rgba(255, 23, 68, .1)
}

[data-md-color-accent=red] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff1744
}

[data-md-color-accent=red] .md-source-file:hover:before {
    background-color: #ff1744
}

button[data-md-color-accent=pink] {
    background-color: #f50057
}

[data-md-color-accent=pink] .md-typeset a:active,
[data-md-color-accent=pink] .md-typeset a:hover {
    color: #f50057
}

[data-md-color-accent=pink] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=pink] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #f50057
}

[data-md-color-accent=pink] .md-nav__link:focus,
[data-md-color-accent=pink] .md-nav__link:hover,
[data-md-color-accent=pink] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=pink] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=pink] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=pink] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=pink] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=pink] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=pink] .md-typeset [id]:target .headerlink {
    color: #f50057
}

[data-md-color-accent=pink] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #f50057
}

[data-md-color-accent=pink] .md-search-result__link:hover,
[data-md-color-accent=pink] .md-search-result__link[data-md-state=active] {
    background-color: rgba(245, 0, 87, .1)
}

[data-md-color-accent=pink] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #f50057
}

[data-md-color-accent=pink] .md-source-file:hover:before {
    background-color: #f50057
}

button[data-md-color-accent=purple] {
    background-color: #e040fb
}

[data-md-color-accent=purple] .md-typeset a:active,
[data-md-color-accent=purple] .md-typeset a:hover {
    color: #e040fb
}

[data-md-color-accent=purple] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=purple] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #e040fb
}

[data-md-color-accent=purple] .md-nav__link:focus,
[data-md-color-accent=purple] .md-nav__link:hover,
[data-md-color-accent=purple] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=purple] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=purple] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=purple] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=purple] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=purple] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=purple] .md-typeset [id]:target .headerlink {
    color: #e040fb
}

[data-md-color-accent=purple] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #e040fb
}

[data-md-color-accent=purple] .md-search-result__link:hover,
[data-md-color-accent=purple] .md-search-result__link[data-md-state=active] {
    background-color: rgba(224, 64, 251, .1)
}

[data-md-color-accent=purple] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #e040fb
}

[data-md-color-accent=purple] .md-source-file:hover:before {
    background-color: #e040fb
}

button[data-md-color-accent=deep-purple] {
    background-color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-typeset a:active,
[data-md-color-accent=deep-purple] .md-typeset a:hover {
    color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=deep-purple] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-nav__link:focus,
[data-md-color-accent=deep-purple] .md-nav__link:hover,
[data-md-color-accent=deep-purple] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=deep-purple] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=deep-purple] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=deep-purple] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=deep-purple] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=deep-purple] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=deep-purple] .md-typeset [id]:target .headerlink {
    color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-search-result__link:hover,
[data-md-color-accent=deep-purple] .md-search-result__link[data-md-state=active] {
    background-color: rgba(124, 77, 255, .1)
}

[data-md-color-accent=deep-purple] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #7c4dff
}

[data-md-color-accent=deep-purple] .md-source-file:hover:before {
    background-color: #7c4dff
}

button[data-md-color-accent=indigo] {
    background-color: #536dfe
}

[data-md-color-accent=indigo] .md-typeset a:active,
[data-md-color-accent=indigo] .md-typeset a:hover {
    color: #536dfe
}

[data-md-color-accent=indigo] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=indigo] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #536dfe
}

[data-md-color-accent=indigo] .md-nav__link:focus,
[data-md-color-accent=indigo] .md-nav__link:hover,
[data-md-color-accent=indigo] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=indigo] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=indigo] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=indigo] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=indigo] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=indigo] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=indigo] .md-typeset [id]:target .headerlink {
    color: #536dfe
}

[data-md-color-accent=indigo] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #536dfe
}

[data-md-color-accent=indigo] .md-search-result__link:hover,
[data-md-color-accent=indigo] .md-search-result__link[data-md-state=active] {
    background-color: rgba(83, 109, 254, .1)
}

[data-md-color-accent=indigo] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #536dfe
}

[data-md-color-accent=indigo] .md-source-file:hover:before {
    background-color: #536dfe
}

button[data-md-color-accent=blue] {
    background-color: #448aff
}

[data-md-color-accent=blue] .md-typeset a:active,
[data-md-color-accent=blue] .md-typeset a:hover {
    color: #448aff
}

[data-md-color-accent=blue] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=blue] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #448aff
}

[data-md-color-accent=blue] .md-nav__link:focus,
[data-md-color-accent=blue] .md-nav__link:hover,
[data-md-color-accent=blue] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=blue] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=blue] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=blue] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=blue] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=blue] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=blue] .md-typeset [id]:target .headerlink {
    color: #448aff
}

[data-md-color-accent=blue] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #448aff
}

[data-md-color-accent=blue] .md-search-result__link:hover,
[data-md-color-accent=blue] .md-search-result__link[data-md-state=active] {
    background-color: rgba(68, 138, 255, .1)
}

[data-md-color-accent=blue] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #448aff
}

[data-md-color-accent=blue] .md-source-file:hover:before {
    background-color: #448aff
}

button[data-md-color-accent=light-blue] {
    background-color: #0091ea
}

[data-md-color-accent=light-blue] .md-typeset a:active,
[data-md-color-accent=light-blue] .md-typeset a:hover {
    color: #0091ea
}

[data-md-color-accent=light-blue] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=light-blue] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #0091ea
}

[data-md-color-accent=light-blue] .md-nav__link:focus,
[data-md-color-accent=light-blue] .md-nav__link:hover,
[data-md-color-accent=light-blue] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=light-blue] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=light-blue] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=light-blue] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=light-blue] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=light-blue] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=light-blue] .md-typeset [id]:target .headerlink {
    color: #0091ea
}

[data-md-color-accent=light-blue] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #0091ea
}

[data-md-color-accent=light-blue] .md-search-result__link:hover,
[data-md-color-accent=light-blue] .md-search-result__link[data-md-state=active] {
    background-color: rgba(0, 145, 234, .1)
}

[data-md-color-accent=light-blue] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #0091ea
}

[data-md-color-accent=light-blue] .md-source-file:hover:before {
    background-color: #0091ea
}

button[data-md-color-accent=cyan] {
    background-color: #00b8d4
}

[data-md-color-accent=cyan] .md-typeset a:active,
[data-md-color-accent=cyan] .md-typeset a:hover {
    color: #00b8d4
}

[data-md-color-accent=cyan] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=cyan] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #00b8d4
}

[data-md-color-accent=cyan] .md-nav__link:focus,
[data-md-color-accent=cyan] .md-nav__link:hover,
[data-md-color-accent=cyan] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=cyan] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=cyan] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=cyan] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=cyan] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=cyan] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=cyan] .md-typeset [id]:target .headerlink {
    color: #00b8d4
}

[data-md-color-accent=cyan] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00b8d4
}

[data-md-color-accent=cyan] .md-search-result__link:hover,
[data-md-color-accent=cyan] .md-search-result__link[data-md-state=active] {
    background-color: rgba(0, 184, 212, .1)
}

[data-md-color-accent=cyan] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00b8d4
}

[data-md-color-accent=cyan] .md-source-file:hover:before {
    background-color: #00b8d4
}

button[data-md-color-accent=teal] {
    background-color: #00bfa5
}

[data-md-color-accent=teal] .md-typeset a:active,
[data-md-color-accent=teal] .md-typeset a:hover {
    color: #00bfa5
}

[data-md-color-accent=teal] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=teal] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #00bfa5
}

[data-md-color-accent=teal] .md-nav__link:focus,
[data-md-color-accent=teal] .md-nav__link:hover,
[data-md-color-accent=teal] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=teal] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=teal] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=teal] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=teal] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=teal] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=teal] .md-typeset [id]:target .headerlink {
    color: #00bfa5
}

[data-md-color-accent=teal] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00bfa5
}

[data-md-color-accent=teal] .md-search-result__link:hover,
[data-md-color-accent=teal] .md-search-result__link[data-md-state=active] {
    background-color: rgba(0, 191, 165, .1)
}

[data-md-color-accent=teal] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00bfa5
}

[data-md-color-accent=teal] .md-source-file:hover:before {
    background-color: #00bfa5
}

button[data-md-color-accent=green] {
    background-color: #00c853
}

[data-md-color-accent=green] .md-typeset a:active,
[data-md-color-accent=green] .md-typeset a:hover {
    color: #00c853
}

[data-md-color-accent=green] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=green] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #00c853
}

[data-md-color-accent=green] .md-nav__link:focus,
[data-md-color-accent=green] .md-nav__link:hover,
[data-md-color-accent=green] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=green] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=green] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=green] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=green] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=green] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=green] .md-typeset [id]:target .headerlink {
    color: #00c853
}

[data-md-color-accent=green] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00c853
}

[data-md-color-accent=green] .md-search-result__link:hover,
[data-md-color-accent=green] .md-search-result__link[data-md-state=active] {
    background-color: rgba(0, 200, 83, .1)
}

[data-md-color-accent=green] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #00c853
}

[data-md-color-accent=green] .md-source-file:hover:before {
    background-color: #00c853
}

button[data-md-color-accent=light-green] {
    background-color: #64dd17
}

[data-md-color-accent=light-green] .md-typeset a:active,
[data-md-color-accent=light-green] .md-typeset a:hover {
    color: #64dd17
}

[data-md-color-accent=light-green] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=light-green] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #64dd17
}

[data-md-color-accent=light-green] .md-nav__link:focus,
[data-md-color-accent=light-green] .md-nav__link:hover,
[data-md-color-accent=light-green] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=light-green] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=light-green] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=light-green] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=light-green] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=light-green] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=light-green] .md-typeset [id]:target .headerlink {
    color: #64dd17
}

[data-md-color-accent=light-green] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #64dd17
}

[data-md-color-accent=light-green] .md-search-result__link:hover,
[data-md-color-accent=light-green] .md-search-result__link[data-md-state=active] {
    background-color: rgba(100, 221, 23, .1)
}

[data-md-color-accent=light-green] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #64dd17
}

[data-md-color-accent=light-green] .md-source-file:hover:before {
    background-color: #64dd17
}

button[data-md-color-accent=lime] {
    background-color: #aeea00
}

[data-md-color-accent=lime] .md-typeset a:active,
[data-md-color-accent=lime] .md-typeset a:hover {
    color: #aeea00
}

[data-md-color-accent=lime] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=lime] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #aeea00
}

[data-md-color-accent=lime] .md-nav__link:focus,
[data-md-color-accent=lime] .md-nav__link:hover,
[data-md-color-accent=lime] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=lime] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=lime] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=lime] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=lime] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=lime] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=lime] .md-typeset [id]:target .headerlink {
    color: #aeea00
}

[data-md-color-accent=lime] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #aeea00
}

[data-md-color-accent=lime] .md-search-result__link:hover,
[data-md-color-accent=lime] .md-search-result__link[data-md-state=active] {
    background-color: rgba(174, 234, 0, .1)
}

[data-md-color-accent=lime] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #aeea00
}

[data-md-color-accent=lime] .md-source-file:hover:before {
    background-color: #aeea00
}

button[data-md-color-accent=yellow] {
    background-color: #ffd600
}

[data-md-color-accent=yellow] .md-typeset a:active,
[data-md-color-accent=yellow] .md-typeset a:hover {
    color: #ffd600
}

[data-md-color-accent=yellow] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=yellow] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #ffd600
}

[data-md-color-accent=yellow] .md-nav__link:focus,
[data-md-color-accent=yellow] .md-nav__link:hover,
[data-md-color-accent=yellow] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=yellow] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=yellow] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=yellow] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=yellow] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=yellow] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=yellow] .md-typeset [id]:target .headerlink {
    color: #ffd600
}

[data-md-color-accent=yellow] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ffd600
}

[data-md-color-accent=yellow] .md-search-result__link:hover,
[data-md-color-accent=yellow] .md-search-result__link[data-md-state=active] {
    background-color: rgba(255, 214, 0, .1)
}

[data-md-color-accent=yellow] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ffd600
}

[data-md-color-accent=yellow] .md-source-file:hover:before {
    background-color: #ffd600
}

button[data-md-color-accent=amber] {
    background-color: #ffab00
}

[data-md-color-accent=amber] .md-typeset a:active,
[data-md-color-accent=amber] .md-typeset a:hover {
    color: #ffab00
}

[data-md-color-accent=amber] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=amber] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #ffab00
}

[data-md-color-accent=amber] .md-nav__link:focus,
[data-md-color-accent=amber] .md-nav__link:hover,
[data-md-color-accent=amber] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=amber] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=amber] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=amber] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=amber] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=amber] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=amber] .md-typeset [id]:target .headerlink {
    color: #ffab00
}

[data-md-color-accent=amber] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ffab00
}

[data-md-color-accent=amber] .md-search-result__link:hover,
[data-md-color-accent=amber] .md-search-result__link[data-md-state=active] {
    background-color: rgba(255, 171, 0, .1)
}

[data-md-color-accent=amber] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ffab00
}

[data-md-color-accent=amber] .md-source-file:hover:before {
    background-color: #ffab00
}

button[data-md-color-accent=orange] {
    background-color: #ff9100
}

[data-md-color-accent=orange] .md-typeset a:active,
[data-md-color-accent=orange] .md-typeset a:hover {
    color: #ff9100
}

[data-md-color-accent=orange] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=orange] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #ff9100
}

[data-md-color-accent=orange] .md-nav__link:focus,
[data-md-color-accent=orange] .md-nav__link:hover,
[data-md-color-accent=orange] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=orange] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=orange] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=orange] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=orange] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=orange] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=orange] .md-typeset [id]:target .headerlink {
    color: #ff9100
}

[data-md-color-accent=orange] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff9100
}

[data-md-color-accent=orange] .md-search-result__link:hover,
[data-md-color-accent=orange] .md-search-result__link[data-md-state=active] {
    background-color: rgba(255, 145, 0, .1)
}

[data-md-color-accent=orange] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff9100
}

[data-md-color-accent=orange] .md-source-file:hover:before {
    background-color: #ff9100
}

button[data-md-color-accent=deep-orange] {
    background-color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-typeset a:active,
[data-md-color-accent=deep-orange] .md-typeset a:hover {
    color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-typeset .codehilite pre::-webkit-scrollbar-thumb:hover,
[data-md-color-accent=deep-orange] .md-typeset pre code::-webkit-scrollbar-thumb:hover {
    background-color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-nav__link:focus,
[data-md-color-accent=deep-orange] .md-nav__link:hover,
[data-md-color-accent=deep-orange] .md-typeset .footnote li:hover .footnote-backref:hover,
[data-md-color-accent=deep-orange] .md-typeset .footnote li:target .footnote-backref,
[data-md-color-accent=deep-orange] .md-typeset .md-clipboard:active:before,
[data-md-color-accent=deep-orange] .md-typeset .md-clipboard:hover:before,
[data-md-color-accent=deep-orange] .md-typeset [id] .headerlink:focus,
[data-md-color-accent=deep-orange] .md-typeset [id]:hover .headerlink:hover,
[data-md-color-accent=deep-orange] .md-typeset [id]:target .headerlink {
    color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-search-result__link:hover,
[data-md-color-accent=deep-orange] .md-search-result__link[data-md-state=active] {
    background-color: rgba(255, 110, 64, .1)
}

[data-md-color-accent=deep-orange] .md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
    background-color: #ff6e40
}

[data-md-color-accent=deep-orange] .md-source-file:hover:before {
    background-color: #ff6e40
}

@media only screen and (max-width:59.9375em) {
    [data-md-color-primary=red] .md-nav__source {
        background-color: rgba(190, 66, 64, .9675)
    }
    [data-md-color-primary=pink] .md-nav__source {
        background-color: rgba(185, 24, 79, .9675)
    }
    [data-md-color-primary=purple] .md-nav__source {
        background-color: rgba(136, 57, 150, .9675)
    }
    [data-md-color-primary=deep-purple] .md-nav__source {
        background-color: rgba(100, 69, 154, .9675)
    }
    [data-md-color-primary=indigo] .md-nav__source {
        background-color: rgba(50, 64, 144, .9675)
    }
    [data-md-color-primary=blue] .md-nav__source {
        background-color: rgba(26, 119, 193, .9675)
    }
    [data-md-color-primary=light-blue] .md-nav__source {
        background-color: rgba(2, 134, 194, .9675)
    }
    [data-md-color-primary=cyan] .md-nav__source {
        background-color: rgba(0, 150, 169, .9675)
    }
    [data-md-color-primary=teal] .md-nav__source {
        background-color: rgba(0, 119, 108, .9675)
    }
    [data-md-color-primary=green] .md-nav__source {
        background-color: rgba(60, 139, 64, .9675)
    }
    [data-md-color-primary=light-green] .md-nav__source {
        background-color: rgba(99, 142, 53, .9675)
    }
    [data-md-color-primary=lime] .md-nav__source {
        background-color: rgba(153, 161, 41, .9675)
    }
    [data-md-color-primary=yellow] .md-nav__source {
        background-color: rgba(198, 134, 29, .9675)
    }
    [data-md-color-primary=amber] .md-nav__source {
        background-color: rgba(203, 127, 0, .9675)
    }
    [data-md-color-primary=orange] .md-nav__source {
        background-color: rgba(200, 111, 0, .9675)
    }
    [data-md-color-primary=deep-orange] .md-nav__source {
        background-color: rgba(203, 89, 53, .9675)
    }
    [data-md-color-primary=brown] .md-nav__source {
        background-color: rgba(96, 68, 57, .9675)
    }
    [data-md-color-primary=grey] .md-nav__source {
        background-color: rgba(93, 93, 93, .9675)
    }
    [data-md-color-primary=blue-grey] .md-nav__source {
        background-color: rgba(67, 88, 97, .9675)
    }
    [data-md-color-primary=white] .md-nav__source {
        background-color: rgba(0, 0, 0, .07);
        color: rgba(0, 0, 0, .87)
    }
}

@media only screen and (max-width:76.1875em) {
    html [data-md-color-primary=red] .md-nav--primary .md-nav__title--site {
        background-color: #ef5350
    }
    html [data-md-color-primary=pink] .md-nav--primary .md-nav__title--site {
        background-color: #e91e63
    }
    html [data-md-color-primary=purple] .md-nav--primary .md-nav__title--site {
        background-color: #ab47bc
    }
    html [data-md-color-primary=deep-purple] .md-nav--primary .md-nav__title--site {
        background-color: #7e57c2
    }
    html [data-md-color-primary=indigo] .md-nav--primary .md-nav__title--site {
        background-color: #3f51b5
    }
    html [data-md-color-primary=blue] .md-nav--primary .md-nav__title--site {
        background-color: #2196f3
    }
    html [data-md-color-primary=light-blue] .md-nav--primary .md-nav__title--site {
        background-color: #03a9f4
    }
    html [data-md-color-primary=cyan] .md-nav--primary .md-nav__title--site {
        background-color: #00bcd4
    }
    html [data-md-color-primary=teal] .md-nav--primary .md-nav__title--site {
        background-color: #009688
    }
    html [data-md-color-primary=green] .md-nav--primary .md-nav__title--site {
        background-color: #4caf50
    }
    html [data-md-color-primary=light-green] .md-nav--primary .md-nav__title--site {
        background-color: #7cb342
    }
    html [data-md-color-primary=lime] .md-nav--primary .md-nav__title--site {
        background-color: #c0ca33
    }
    html [data-md-color-primary=yellow] .md-nav--primary .md-nav__title--site {
        background-color: #f9a825
    }
    html [data-md-color-primary=amber] .md-nav--primary .md-nav__title--site {
        background-color: #ffa000
    }
    html [data-md-color-primary=orange] .md-nav--primary .md-nav__title--site {
        background-color: #fb8c00
    }
    html [data-md-color-primary=deep-orange] .md-nav--primary .md-nav__title--site {
        background-color: #ff7043
    }
    html [data-md-color-primary=brown] .md-nav--primary .md-nav__title--site {
        background-color: #795548
    }
    html [data-md-color-primary=grey] .md-nav--primary .md-nav__title--site {
        background-color: #757575
    }
    html [data-md-color-primary=blue-grey] .md-nav--primary .md-nav__title--site {
        background-color: #546e7a
    }
    html [data-md-color-primary=white] .md-nav--primary .md-nav__title--site {
        background-color: #fff;
        color: rgba(0, 0, 0, .87)
    }
    [data-md-color-primary=white] .md-hero {
        border-bottom: .05rem solid rgba(0, 0, 0, .07)
    }
}

@media only screen and (min-width:76.25em) {
    [data-md-color-primary=red] .md-tabs {
        background-color: #ef5350
    }
    [data-md-color-primary=pink] .md-tabs {
        background-color: #e91e63
    }
    [data-md-color-primary=purple] .md-tabs {
        background-color: #ab47bc
    }
    [data-md-color-primary=deep-purple] .md-tabs {
        background-color: #7e57c2
    }
    [data-md-color-primary=indigo] .md-tabs {
        background-color: #3f51b5
    }
    [data-md-color-primary=blue] .md-tabs {
        background-color: #2196f3
    }
    [data-md-color-primary=light-blue] .md-tabs {
        background-color: #03a9f4
    }
    [data-md-color-primary=cyan] .md-tabs {
        background-color: #00bcd4
    }
    [data-md-color-primary=teal] .md-tabs {
        background-color: #009688
    }
    [data-md-color-primary=green] .md-tabs {
        background-color: #4caf50
    }
    [data-md-color-primary=light-green] .md-tabs {
        background-color: #7cb342
    }
    [data-md-color-primary=lime] .md-tabs {
        background-color: #c0ca33
    }
    [data-md-color-primary=yellow] .md-tabs {
        background-color: #f9a825
    }
    [data-md-color-primary=amber] .md-tabs {
        background-color: #ffa000
    }
    [data-md-color-primary=orange] .md-tabs {
        background-color: #fb8c00
    }
    [data-md-color-primary=deep-orange] .md-tabs {
        background-color: #ff7043
    }
    [data-md-color-primary=brown] .md-tabs {
        background-color: #795548
    }
    [data-md-color-primary=grey] .md-tabs {
        background-color: #757575
    }
    [data-md-color-primary=blue-grey] .md-tabs {
        background-color: #546e7a
    }
    [data-md-color-primary=white] .md-tabs {
        border-bottom: .05rem solid rgba(0, 0, 0, .07);
        background-color: #fff;
        color: rgba(0, 0, 0, .87)
    }
}

@media only screen and (min-width:60em) {
    [data-md-color-primary=white] .md-search__input {
        background-color: rgba(0, 0, 0, .07)
    }
    [data-md-color-primary=white] .md-search__input::-webkit-input-placeholder {
        color: rgba(0, 0, 0, .54)
    }
    [data-md-color-primary=white] .md-search__input:-ms-input-placeholder {
        color: rgba(0, 0, 0, .54)
    }
    [data-md-color-primary=white] .md-search__input::-ms-input-placeholder {
        color: rgba(0, 0, 0, .54)
    }
    [data-md-color-primary=white] .md-search__input::placeholder {
        color: rgba(0, 0, 0, .54)
    }
}
