:root {
    --trino-black: #212121;
    --trino-dark-blue: #000033;
    --trino-cobalt-blue: #001c93;
    --trino-pink: #dd00a1;
    --trino-orange: #f88600;
    --trino-yellow: #f8b600;
    --trino-grey: #9e9e9e;
}

html {
    scroll-behavior: smooth;
}

div.highlight {
    background-color: #f5f5f5;
}

.md-header, .md-hero, .md-tabs {
    background-color: #0B1367;
}

.md-typeset a:active, .md-typeset a:hover {
    color: var(--trino-orange);
}

/* Fix vertical alignment for links to header from other documents */
.md-typeset span[id]:target:before {
    display:block;
    margin-top:-40px;
    padding-top:40px;
    content:"";
}

/* Fix vertical alignment for links to header from right hand nav */
.md-typeset h1[id]:before,
.md-typeset h2[id]:before,
.md-typeset h3[id]:before,
.md-typeset h4[id]:before,
.md-typeset h5[id]:before,
.md-typeset h6[id]:before,
.md-typeset dl[id]:before {
    margin-top:-3.45rem;
    padding-top:3.45rem;
}

.md-typeset dl[id]:target:before {
    display:block;
    content:"";
}

.md-nav__button img {
    height: 48px;
}

.md-typeset h1 {
    color: rgba(0, 0, 0, .80);
}

.sig-name, .sig-paren {
    font-weight: bold;
}

.md-typeset code {
    padding: 0 0.3em;
}

pre.literal-block {
    padding: .525rem .6rem;
}

.md-nav code {
    font-size: smaller;
}

.md-typeset .admonition, .md-typeset .admonition-title {
    font-size: 0.8rem
}

.md-typeset table:not([class]) {
    font-size: unset;
}

@media only screen and (min-width: 88.25em) {
    .md-grid {
        max-width: 75rem;
    }

    .md-sidebar--secondary {
        margin-left: 75rem;
    }
}

.copybtn:hover {
    cursor: copy;
}

button.copybtn {
    opacity: 0.4;
    font-size: 0.6rem;
}

.connector-logo {
    float: right;
    position: relative;
    top: -5.5rem;
}
