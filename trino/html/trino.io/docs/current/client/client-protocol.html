<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Client protocol &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="client-protocol.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Command line interface" href="cli.html" />
    <link rel="prev" title="Clients" href="../client.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="client-protocol.html#client/client-protocol" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Client protocol </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Client protocol </label>
    
      <a href="client-protocol.html#" class="md-nav__link md-nav__link--active">Client protocol</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="client-protocol.html#spooling-protocol" class="md-nav__link">Spooling protocol</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="client-protocol.html#configuration" class="md-nav__link">Configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#direct-protocol" class="md-nav__link">Direct protocol</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="client-protocol.html#id1" class="md-nav__link">Configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#development-and-reference-information" class="md-nav__link">Development and reference information</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cli.html" class="md-nav__link">Command line interface</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jdbc.html" class="md-nav__link">JDBC driver</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="client-protocol.html#spooling-protocol" class="md-nav__link">Spooling protocol</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="client-protocol.html#configuration" class="md-nav__link">Configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#direct-protocol" class="md-nav__link">Direct protocol</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="client-protocol.html#id1" class="md-nav__link">Configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#development-and-reference-information" class="md-nav__link">Development and reference information</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="client-protocol">
<h1 id="client-client-protocol--page-root">Client protocol<a class="headerlink" href="client-protocol.html#client-client-protocol--page-root" title="Link to this heading">#</a></h1>
<p>The Trino client protocol is a HTTP-based protocol that allows
<a class="reference internal" href="../client.html"><span class="doc std std-doc">clients</span></a> to submit SQL queries and receive results.</p>
<p>The protocol is a sequence of REST API calls to the
<a class="reference internal" href="../overview/concepts.html#trino-concept-coordinator"><span class="std std-ref">coordinator</span></a> of the Trino
<a class="reference internal" href="../overview/concepts.html#trino-concept-cluster"><span class="std std-ref">cluster</span></a>. Following is a high-level overview:</p>
<ol class="arabic simple">
<li><p>Client submits SQL query text to the coordinator of the Trino cluster.</p></li>
<li><p>The coordinator starts processing the query.</p></li>
<li><p>The coordinator returns a result set and a URI <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> on the coordinator.</p></li>
<li><p>The client receives the result set and initiates another request for more
data from the URI <code class="docutils literal notranslate"><span class="pre">nextUri</span></code>.</p></li>
<li><p>The coordinator continues processing the query and returns further data with
a new URI.</p></li>
<li><p>The client and coordinator continue with steps 4. and 5. until all
result set data is returned to the client or the client stops requesting
more data.</p></li>
<li><p>If the client fails to fetch the result set, the coordinator does not initiate
further processing, fails the query, and returns a <code class="docutils literal notranslate"><span class="pre">USER_CANCELED</span></code> error.</p></li>
<li><p>The final response when the query is complete is <code class="docutils literal notranslate"><span class="pre">FINISHED</span></code>.</p></li>
</ol>
<p>The client protocol supports two modes. Configure the <a class="reference internal" href="client-protocol.html#protocol-spooling"><span class="std std-ref">spooling
protocol</span></a> for optimal throughput for your clients.</p>
<section id="spooling-protocol">
<span id="protocol-spooling"></span><h2 id="spooling-protocol">Spooling protocol<a class="headerlink" href="client-protocol.html#spooling-protocol" title="Link to this heading">#</a></h2>
<p>The spooling protocol uses an object storage location to store the data for
retrieval by the client. The coordinator and all workers can write result set
data to the storage in parallel. The coordinator only provides the URLs to all
the individual data segments on the object storage to the cluster. The spooling
protocol also allows compression of the data.</p>
<p>Data on the object storage is automatically removed after download by the
client.</p>
<p>The spooling protocol has the following characteristics, compared to the <a class="reference internal" href="client-protocol.html#protocol-direct"><span class="std std-ref">direct
protocol</span></a>.</p>
<ul class="simple">
<li><p>Provides higher throughput for data transfer, specifically for queries that
return more data.</p></li>
<li><p>Results in faster query processing completion on the cluster, independent of
the client retrieving all data, since data is read from the object storage.</p></li>
<li><p>Requires object storage and configuration on the Trino cluster.</p></li>
<li><p>Reduces CPU and I/O load on the coordinator.</p></li>
<li><p>Automatically falls back to the direct protocol for queries that don’t benefit
from using the spooling protocol.</p></li>
<li><p>Requires newer client drivers or client applications that support the spooling
protocol and actively request usage of the spooling protocol.</p></li>
<li><p>Clients must have access to the object storage.</p></li>
<li><p>Works with older client drivers and client applications by automatically
falling back to the direct protocol if spooling protocol is not supported.</p></li>
</ul>
<section id="configuration">
<h3 id="configuration">Configuration<a class="headerlink" href="client-protocol.html#configuration" title="Link to this heading">#</a></h3>
<p>The following steps are necessary to configure support for the spooling protocol
on a Trino cluster:</p>
<ul class="simple">
<li><p>Configure the spooling protocol usage in <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a> using the
<a class="reference internal" href="../admin/properties-client-protocol.html#prop-protocol-spooling"><span class="std std-ref">Spooling protocol properties</span></a>.</p></li>
<li><p>Choose a suitable object storage that is accessible to your Trino cluster and
your clients.</p></li>
<li><p>Create a location in your object storage that is not shared with any object
storage catalog or spooling for any other Trino clusters.</p></li>
<li><p>Configure the object storage in <code class="docutils literal notranslate"><span class="pre">etc/spooling-manager.properties</span></code> using the
<a class="reference internal" href="../admin/properties-client-protocol.html#prop-spooling-file-system"><span class="std std-ref">Spooling file system properties</span></a>.</p></li>
</ul>
<p>Minimal configuration in <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">protocol.spooling.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">protocol.spooling.shared-secret-key</span><span class="o">=</span><span class="s">jxTKysfCBuMZtFqUf8UJDQ1w9ez8rynEJsJqgJf66u0=</span>
</pre></div>
</div>
<p>Refer to <a class="reference internal" href="../admin/properties-client-protocol.html#prop-protocol-spooling"><span class="std std-ref">Spooling protocol properties</span></a> for further optional configuration.</p>
<p>Suitable object storage systems for spooling are S3 and compatible systems,
Azure Storage, and Google Cloud Storage. The object storage system must provide
good connectivity for all cluster nodes as well as any clients.</p>
<p>Activate the desired system with
<code class="docutils literal notranslate"><span class="pre">fs.s3.enabled</span></code>, <code class="docutils literal notranslate"><span class="pre">fs.azure.enabled</span></code>, or <code class="docutils literal notranslate"><span class="pre">fs.s3.enabled=true</span></code> in
<code class="docutils literal notranslate"><span class="pre">etc/spooling-manager.properties</span></code>and configure further details using relevant
properties from <a class="reference internal" href="../admin/properties-client-protocol.html#prop-spooling-file-system"><span class="std std-ref">Spooling file system properties</span></a>,
<a class="reference internal" href="../object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a>, <a class="reference internal" href="../object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a>, and
<a class="reference internal" href="../object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">spooling-manager.name</span></code> property must be set to <code class="docutils literal notranslate"><span class="pre">filesystem</span></code>.</p>
<p>Following is a minimalistic example for using the S3-compatible MinIO object
storage:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">spooling-manager.name</span><span class="o">=</span><span class="s">filesystem</span>
<span class="na">fs.s3.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">fs.location</span><span class="o">=</span><span class="s">s3://spooling</span>
<span class="na">s3.endpoint</span><span class="o">=</span><span class="s">http://minio:9080/</span>
<span class="na">s3.region</span><span class="o">=</span><span class="s">fake-value</span>
<span class="na">s3.aws-access-key</span><span class="o">=</span><span class="s">minio-access-key</span>
<span class="na">s3.aws-secret-key</span><span class="o">=</span><span class="s">minio-secret-key</span>
<span class="na">s3.path-style-access</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
<p>Refer to <a class="reference internal" href="../admin/properties-client-protocol.html#prop-spooling-file-system"><span class="std std-ref">Spooling file system properties</span></a> for further configuration properties.</p>
<p>The system assumes the object storage to be unbounded in terms of data and data
transfer volume. Spooled segments on object storage are automatically removed by
the clients after reads as well as the coordinator in specific intervals. Sizing
and transfer demands vary with the query workload on your cluster.</p>
<p>Segments on object storage are encrypted, compressed, and can only be used by
the specific client who initiated the query.</p>
<p>The following client drivers and client applications support the spooling protocol.</p>
<ul class="simple">
<li><p><a class="reference internal" href="jdbc.html#jdbc-spooling-protocol"><span class="std std-ref">Trino JDBC driver</span></a>, version 466 and newer</p></li>
<li><p><a class="reference internal" href="cli.html#cli-spooling-protocol"><span class="std std-ref">Trino command line interface</span></a>, version 466 and newer</p></li>
<li><p><a class="reference external" href="https://github.com/trinodb/trino-python-client">Trino Python client</a>, version
0.332.0 and newer</p></li>
</ul>
<p>Refer to the documentation for other your specific client drivers and client
applications for up to date information.</p>
</section>
</section>
<section id="direct-protocol">
<span id="protocol-direct"></span><h2 id="direct-protocol">Direct protocol<a class="headerlink" href="client-protocol.html#direct-protocol" title="Link to this heading">#</a></h2>
<p>The direct protocol transfers all data from the workers to the coordinator, and
from there directly to the client.</p>
<p>The direct protocol, also known as the <code class="docutils literal notranslate"><span class="pre">v1</span></code> protocol, has the following
characteristics, compared to the spooling protocol:</p>
<ul class="simple">
<li><p>Provides lower performance, specifically for queries that return more data.</p></li>
<li><p>Results in slower query processing completion on the cluster, since data is
provided by the coordinator and read by the client sequentially.</p></li>
<li><p>Requires <strong>no</strong> object storage or configuration in the Trino cluster.</p></li>
<li><p>Increases CPU and I/O load on the coordinator.</p></li>
<li><p>Works with older client drivers and client applications without support for
the spooling protocol.</p></li>
</ul>
<section id="id1">
<h3 id="id1">Configuration<a class="headerlink" href="client-protocol.html#id1" title="Link to this heading">#</a></h3>
<p>Use of the direct protocol requires not configuration. Find optional
configuration properties in <a class="reference internal" href="../admin/properties-client-protocol.html#prop-protocol-shared"><span class="std std-ref">Shared protocol properties</span></a>.</p>
</section>
</section>
<section id="development-and-reference-information">
<h2 id="development-and-reference-information">Development and reference information<a class="headerlink" href="client-protocol.html#development-and-reference-information" title="Link to this heading">#</a></h2>
<p>Further technical details about the client protocol, including information
useful for developing a client driver, are available in the <a class="reference internal" href="../develop/client-protocol.html"><span class="doc std std-doc">Trino client REST
API developer reference</span></a>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../client.html" title="Clients"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Clients </span>
              </div>
            </a>
          
          
            <a href="cli.html" title="Command line interface"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Command line interface </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>