<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>JDBC driver &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="jdbc.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Security" href="../security.html" />
    <link rel="prev" title="Command line interface" href="cli.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="jdbc.html#client/jdbc" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> JDBC driver </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cli.html" class="md-nav__link">Command line interface</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> JDBC driver </label>
    
      <a href="jdbc.html#" class="md-nav__link md-nav__link--active">JDBC driver</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="jdbc.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#registering-and-configuring-the-driver" class="md-nav__link">Registering and configuring the driver</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#connecting" class="md-nav__link">Connecting</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#connection-parameters" class="md-nav__link">Connection parameters</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#parameter-reference" class="md-nav__link">Parameter reference</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#spooling-protocol" class="md-nav__link">Spooling protocol</a>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="jdbc.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#registering-and-configuring-the-driver" class="md-nav__link">Registering and configuring the driver</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#connecting" class="md-nav__link">Connecting</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#connection-parameters" class="md-nav__link">Connection parameters</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#parameter-reference" class="md-nav__link">Parameter reference</a>
        </li>
        <li class="md-nav__item"><a href="jdbc.html#spooling-protocol" class="md-nav__link">Spooling protocol</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="jdbc-driver">
<h1 id="client-jdbc--page-root">JDBC driver<a class="headerlink" href="jdbc.html#client-jdbc--page-root" title="Link to this heading">#</a></h1>
<p>The Trino <a class="reference external" href="https://wikipedia.org/wiki/JDBC_driver">JDBC driver</a> allows
users to access Trino using Java-based applications, and other non-Java
applications running in a JVM. Both desktop and server-side applications, such
as those used for reporting and database development, use the JDBC driver.</p>
<p>The JDBC driver uses the <a class="reference internal" href="client-protocol.html"><span class="doc std std-doc">Client protocol</span></a> over HTTP/HTTPS to
communicate with the coordinator on the cluster.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="jdbc.html#requirements" title="Link to this heading">#</a></h2>
<p>The Trino JDBC driver has the following requirements:</p>
<ul class="simple">
<li><p>Java version 11 or higher. Java 22 or higher is recommended for improved
decompression performance.</p></li>
<li><p>All users that connect to Trino with the JDBC driver must be granted access to
query tables in the <code class="docutils literal notranslate"><span class="pre">system.jdbc</span></code> schema.</p></li>
<li><p>Network access over HTTP/HTTPS to the coordinator of the Trino cluster.</p></li>
<li><p>Network access to the configured object storage, if the
<a class="reference internal" href="jdbc.html#jdbc-spooling-protocol"><span class="std std-ref">Spooling protocol</span></a> is enabled.</p></li>
</ul>
<p>The JDBC driver version should be identical to the version of the Trino cluster,
or newer. Older versions typically work, but only a subset is regularly tested.
Versions before 350 are not supported.</p>
</section>
<section id="installation">
<span id="jdbc-installation"></span><h2 id="installation">Installation<a class="headerlink" href="jdbc.html#installation" title="Link to this heading">#</a></h2>
<p>Download <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-jdbc/476/trino-jdbc-476.jar">trino-jdbc-476.jar</a> and add it to the classpath of your Java application.</p>
<p>The driver is also available from Maven Central:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>io.trino<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>trino-jdbc<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>476<span class="nt">&lt;/version&gt;</span>
<span class="nt">&lt;/dependency&gt;</span>
</pre></div>
</div>
<p>We recommend using the latest version of the JDBC driver. A list of all
available versions can be found in the <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-jdbc/">Maven Central Repository</a>. Navigate to the
directory for the desired version, and select the <code class="docutils literal notranslate"><span class="pre">trino-jdbc-xxx.jar</span></code> file
to download, where <code class="docutils literal notranslate"><span class="pre">xxx</span></code> is the version number.</p>
<p>Once downloaded, you must add the JAR file to a directory in the classpath
of users on systems where they will access Trino.</p>
<p>After you have downloaded the JDBC driver and added it to your
classpath, you’ll typically need to restart your application in order to
recognize the new driver. Then, depending on your application, you
may need to manually register and configure the driver.</p>
</section>
<section id="registering-and-configuring-the-driver">
<h2 id="registering-and-configuring-the-driver">Registering and configuring the driver<a class="headerlink" href="jdbc.html#registering-and-configuring-the-driver" title="Link to this heading">#</a></h2>
<p>Drivers are commonly loaded automatically by applications once they are added to
its classpath. If your application does not, such as is the case for some
GUI-based SQL editors, read this section. The steps to register the JDBC driver
in a UI or on the command line depend upon the specific application you are
using. Please check your application’s documentation.</p>
<p>Once registered, you must also configure the connection information as described
in the following section.</p>
</section>
<section id="connecting">
<h2 id="connecting">Connecting<a class="headerlink" href="jdbc.html#connecting" title="Link to this heading">#</a></h2>
<p>When your driver is loaded, registered and configured, you are ready to connect
to Trino from your application. The following JDBC URL formats are supported:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>**********************
******************************
*************************************
</pre></div>
</div>
<p>The value for <code class="docutils literal notranslate"><span class="pre">port</span></code> is optional if Trino is available at the default HTTP port
<code class="docutils literal notranslate"><span class="pre">80</span></code> or with <code class="docutils literal notranslate"><span class="pre">SSL=true</span></code> and the default HTTPS port <code class="docutils literal notranslate"><span class="pre">443</span></code>.</p>
<p>The following is an example of a JDBC URL used to create a connection:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>****************************************
</pre></div>
</div>
<p>This example JDBC URL locates a Trino instance running on port <code class="docutils literal notranslate"><span class="pre">8080</span></code> on
<code class="docutils literal notranslate"><span class="pre">example.net</span></code>, with the catalog <code class="docutils literal notranslate"><span class="pre">hive</span></code> and the schema <code class="docutils literal notranslate"><span class="pre">sales</span></code> defined.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Typically, the JDBC driver classname is configured automatically by your
client. If it is not, use <code class="docutils literal notranslate"><span class="pre">io.trino.jdbc.TrinoDriver</span></code> wherever a driver
classname is required.</p>
</div>
</section>
<section id="connection-parameters">
<span id="jdbc-java-connection"></span><h2 id="connection-parameters">Connection parameters<a class="headerlink" href="jdbc.html#connection-parameters" title="Link to this heading">#</a></h2>
<p>The driver supports various parameters that may be set as URL parameters,
or as properties passed to <code class="docutils literal notranslate"><span class="pre">DriverManager</span></code>. Both of the following
examples are equivalent:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="c1">// properties</span>
<span class="n">String</span><span class="w"> </span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"****************************************"</span><span class="p">;</span>
<span class="n">Properties</span><span class="w"> </span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Properties</span><span class="p">();</span>
<span class="n">properties</span><span class="p">.</span><span class="na">setProperty</span><span class="p">(</span><span class="s">"user"</span><span class="p">,</span><span class="w"> </span><span class="s">"test"</span><span class="p">);</span>
<span class="n">properties</span><span class="p">.</span><span class="na">setProperty</span><span class="p">(</span><span class="s">"password"</span><span class="p">,</span><span class="w"> </span><span class="s">"secret"</span><span class="p">);</span>
<span class="n">properties</span><span class="p">.</span><span class="na">setProperty</span><span class="p">(</span><span class="s">"SSL"</span><span class="p">,</span><span class="w"> </span><span class="s">"true"</span><span class="p">);</span>
<span class="n">Connection</span><span class="w"> </span><span class="n">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">DriverManager</span><span class="p">.</span><span class="na">getConnection</span><span class="p">(</span><span class="n">url</span><span class="p">,</span><span class="w"> </span><span class="n">properties</span><span class="p">);</span>

<span class="c1">// URL parameters</span>
<span class="n">String</span><span class="w"> </span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"***********************************************************************************"</span><span class="p">;</span>
<span class="n">Connection</span><span class="w"> </span><span class="n">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">DriverManager</span><span class="p">.</span><span class="na">getConnection</span><span class="p">(</span><span class="n">url</span><span class="p">);</span>
</pre></div>
</div>
<p>These methods may be mixed; some parameters may be specified in the URL,
while others are specified using properties. However, the same parameter
may not be specified using both methods.</p>
</section>
<section id="parameter-reference">
<span id="jdbc-parameter-reference"></span><h2 id="parameter-reference">Parameter reference<a class="headerlink" href="jdbc.html#parameter-reference" title="Link to this heading">#</a></h2>
<table>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">user</span></code></p></td>
<td><p>Username to use for authentication and authorization.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">password</span></code></p></td>
<td><p>Password to use for LDAP authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">sessionUser</span></code></p></td>
<td><p>Session username override, used for impersonation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">socksProxy</span></code></p></td>
<td><p>SOCKS proxy host and port. Example: <code class="docutils literal notranslate"><span class="pre">localhost:1080</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">httpProxy</span></code></p></td>
<td><p>HTTP proxy host and port. Example: <code class="docutils literal notranslate"><span class="pre">localhost:8888</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">clientInfo</span></code></p></td>
<td><p>Extra information about the client.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">clientTags</span></code></p></td>
<td><p>Client tags for selecting resource groups. Example: <code class="docutils literal notranslate"><span class="pre">abc,xyz</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">path</span></code></p></td>
<td><p>Set the default <a class="reference internal" href="../sql/set-path.html"><span class="doc std std-doc">SQL path</span></a> for the session. Useful for
setting a catalog and schema location for <a class="reference internal" href="../udf/introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">traceToken</span></code></p></td>
<td><p>Trace token for correlating requests across systems.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">source</span></code></p></td>
<td><p>Source name for the Trino query. This parameter should be used in preference
to <code class="docutils literal notranslate"><span class="pre">ApplicationName</span></code>. Thus, it takes precedence over <code class="docutils literal notranslate"><span class="pre">ApplicationName</span></code>
and/or <code class="docutils literal notranslate"><span class="pre">applicationNamePrefix</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">applicationNamePrefix</span></code></p></td>
<td><p>Prefix to append to any specified <code class="docutils literal notranslate"><span class="pre">ApplicationName</span></code> client info property,
which is used to set the source name for the Trino query if the <code class="docutils literal notranslate"><span class="pre">source</span></code>
parameter has not been set. If neither this property nor <code class="docutils literal notranslate"><span class="pre">ApplicationName</span></code>
or <code class="docutils literal notranslate"><span class="pre">source</span></code> are set, the source name for the query is <code class="docutils literal notranslate"><span class="pre">trino-jdbc</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">accessToken</span></code></p></td>
<td><p><a class="reference internal" href="../security/jwt.html"><span class="doc std std-doc">JWT</span></a> access token for token based authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SSL</span></code></p></td>
<td><p>Set <code class="docutils literal notranslate"><span class="pre">true</span></code> to specify using TLS/HTTPS for connections.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SSLVerification</span></code></p></td>
<td><p>The method of TLS verification. There are three modes: <code class="docutils literal notranslate"><span class="pre">FULL</span></code>
(default), <code class="docutils literal notranslate"><span class="pre">CA</span></code> and <code class="docutils literal notranslate"><span class="pre">NONE</span></code>. For <code class="docutils literal notranslate"><span class="pre">FULL</span></code>, the normal TLS verification
is performed. For <code class="docutils literal notranslate"><span class="pre">CA</span></code>, only the CA is verified but hostname mismatch
is allowed. For <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, there is no verification.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SSLKeyStorePath</span></code></p></td>
<td><p>Use only when connecting to a Trino cluster that has <a class="reference internal" href="../security/certificate.html"><span class="doc std std-doc">certificate
authentication</span></a> enabled. Specifies the path to a
<a class="reference internal" href="../security/inspect-pem.html"><span class="doc std std-doc">PEM</span></a> or <a class="reference internal" href="../security/inspect-jks.html"><span class="doc std std-doc">JKS</span></a> file, which must
contain a certificate that is trusted by the Trino cluster you connect to.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SSLKeyStorePassword</span></code></p></td>
<td><p>The password for the KeyStore, if any.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SSLKeyStoreType</span></code></p></td>
<td><p>The type of the KeyStore. The default type is provided by the Java
<code class="docutils literal notranslate"><span class="pre">keystore.type</span></code> security property or <code class="docutils literal notranslate"><span class="pre">jks</span></code> if none exists.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SSLUseSystemKeyStore</span></code></p></td>
<td><p>Set <code class="docutils literal notranslate"><span class="pre">true</span></code> to automatically use the system KeyStore based on the operating
system. The supported OSes are Windows and macOS. For Windows, the
<code class="docutils literal notranslate"><span class="pre">Windows-MY</span></code> KeyStore is selected. For macOS, the <code class="docutils literal notranslate"><span class="pre">KeychainStore</span></code>
KeyStore is selected. For other OSes, the default Java KeyStore is loaded.
The KeyStore specification can be overridden using <code class="docutils literal notranslate"><span class="pre">SSLKeyStoreType</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SSLTrustStorePath</span></code></p></td>
<td><p>The location of the Java TrustStore file to use to validate HTTPS server
certificates.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SSLTrustStorePassword</span></code></p></td>
<td><p>The password for the TrustStore.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SSLTrustStoreType</span></code></p></td>
<td><p>The type of the TrustStore. The default type is provided by the Java
<code class="docutils literal notranslate"><span class="pre">keystore.type</span></code> security property or <code class="docutils literal notranslate"><span class="pre">jks</span></code> if none exists.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SSLUseSystemTrustStore</span></code></p></td>
<td><p>Set <code class="docutils literal notranslate"><span class="pre">true</span></code> to automatically use the system TrustStore based on the operating
system. The supported OSes are Windows and macOS. For Windows, the
<code class="docutils literal notranslate"><span class="pre">Windows-ROOT</span></code> TrustStore is selected. For macOS, the <code class="docutils literal notranslate"><span class="pre">KeychainStore</span></code>
TrustStore is selected. For other OSes, the default Java TrustStore is
loaded. The TrustStore specification can be overridden using
<code class="docutils literal notranslate"><span class="pre">SSLTrustStoreType</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hostnameInCertificate</span></code></p></td>
<td><p>Expected hostname in the certificate presented by the Trino server. Only
applicable with full SSL verification enabled.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosRemoteServiceName</span></code></p></td>
<td><p>Trino coordinator Kerberos service name. This parameter is required for
Kerberos authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosPrincipal</span></code></p></td>
<td><p>The principal to use when authenticating to the Trino coordinator.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosUseCanonicalHostname</span></code></p></td>
<td><p>Use the canonical hostname of the Trino coordinator for the Kerberos service
principal by first resolving the hostname to an IP address and then doing a
reverse DNS lookup for that IP address. This is enabled by default.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosServicePrincipalPattern</span></code></p></td>
<td><p>Trino coordinator Kerberos service principal pattern. The default is
<code class="docutils literal notranslate"><span class="pre">${SERVICE}@${HOST}</span></code>. <code class="docutils literal notranslate"><span class="pre">${SERVICE}</span></code> is replaced with the value of
<code class="docutils literal notranslate"><span class="pre">KerberosRemoteServiceName</span></code> and <code class="docutils literal notranslate"><span class="pre">${HOST}</span></code> is replaced with the hostname of
the coordinator (after canonicalization if enabled).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosConfigPath</span></code></p></td>
<td><p>Kerberos configuration file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosKeytabPath</span></code></p></td>
<td><p>Kerberos keytab file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosCredentialCachePath</span></code></p></td>
<td><p>Kerberos credential cache.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">KerberosDelegation</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to use the token from an existing Kerberos context. This
allows client to use Kerberos authentication without passing the Keytab or
credential cache. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">extraCredentials</span></code></p></td>
<td><p>Extra credentials for connecting to external services, specified as a list
of key-value pairs. For example, <code class="docutils literal notranslate"><span class="pre">foo:bar;abc:xyz</span></code> creates the credential
named <code class="docutils literal notranslate"><span class="pre">abc</span></code> with value <code class="docutils literal notranslate"><span class="pre">xyz</span></code> and the credential named <code class="docutils literal notranslate"><span class="pre">foo</span></code> with value
<code class="docutils literal notranslate"><span class="pre">bar</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">roles</span></code></p></td>
<td><p>Authorization roles to use for catalogs, specified as a list of key-value
pairs for the catalog and role. For example, <code class="docutils literal notranslate"><span class="pre">catalog1:roleA;catalog2:roleB</span></code>
sets <code class="docutils literal notranslate"><span class="pre">roleA</span></code> for <code class="docutils literal notranslate"><span class="pre">catalog1</span></code> and <code class="docutils literal notranslate"><span class="pre">roleB</span></code> for <code class="docutils literal notranslate"><span class="pre">catalog2</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">sessionProperties</span></code></p></td>
<td><p>Session properties to set for the system and for catalogs, specified as a
list of key-value pairs. For example, <code class="docutils literal notranslate"><span class="pre">abc:xyz;example.foo:bar</span></code> sets the
system property <code class="docutils literal notranslate"><span class="pre">abc</span></code> to the value <code class="docutils literal notranslate"><span class="pre">xyz</span></code> and the <code class="docutils literal notranslate"><span class="pre">foo</span></code> property for catalog
<code class="docutils literal notranslate"><span class="pre">example</span></code> to the value <code class="docutils literal notranslate"><span class="pre">bar</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">externalAuthentication</span></code></p></td>
<td><p>Set to true if you want to use external authentication via
<a class="reference internal" href="../security/oauth2.html"><span class="doc std std-doc">OAuth 2.0 authentication</span></a>. Use a local web browser to authenticate with an
identity provider (IdP) that has been configured for the Trino coordinator.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">externalAuthenticationTokenCache</span></code></p></td>
<td><p>Allows the sharing of external authentication tokens between different
connections for the same authenticated user until the cache is invalidated,
such as when a client is restarted or when the classloader reloads the JDBC
driver. This is disabled by default, with a value of <code class="docutils literal notranslate"><span class="pre">NONE</span></code>. To enable, set
the value to <code class="docutils literal notranslate"><span class="pre">MEMORY</span></code>. If the JDBC driver is used in a shared mode by
different users, the first registered token is stored and authenticates all
users.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">disableCompression</span></code></p></td>
<td><p>Whether compression should be enabled.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">assumeLiteralUnderscoreInMetadataCallsForNonConformingClients</span></code></p></td>
<td><p>When enabled, the name patterns passed to <code class="docutils literal notranslate"><span class="pre">DatabaseMetaData</span></code> methods are
treated as underscores. You can use this as a workaround for applications
that do not escape schema or table names when passing them to
<code class="docutils literal notranslate"><span class="pre">DatabaseMetaData</span></code> methods as schema or table name patterns.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">timezone</span></code></p></td>
<td><p>Sets the time zone for the session using the <a class="reference external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/ZoneId.html#of(java.lang.String)">time zone
passed</a>.
Defaults to the timezone of the JVM running the JDBC driver.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">explicitPrepare</span></code></p></td>
<td><p>Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>. When set to <code class="docutils literal notranslate"><span class="pre">false</span></code>, prepared statements are executed
calling a single <code class="docutils literal notranslate"><span class="pre">EXECUTE</span> <span class="pre">IMMEDIATE</span></code> query instead of the standard
<code class="docutils literal notranslate"><span class="pre">PREPARE</span> <span class="pre">&lt;statement&gt;</span></code> followed by <code class="docutils literal notranslate"><span class="pre">EXECUTE</span> <span class="pre">&lt;statement&gt;</span></code>. This reduces
network overhead and uses smaller HTTP headers and requires Trino 431 or
greater.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">encoding</span></code></p></td>
<td><p>Set the encoding when using the <a class="reference internal" href="jdbc.html#jdbc-spooling-protocol"><span class="std std-ref">spooling protocol</span></a>.
Valid values are JSON with Zstandard compression, <code class="docutils literal notranslate"><span class="pre">json+zstd</span></code> (recommended),
JSON with LZ4 compression <code class="docutils literal notranslate"><span class="pre">json+lz4</span></code>, and uncompressed JSON <code class="docutils literal notranslate"><span class="pre">json</span></code>. By
default, the default encoding configured on the cluster is used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">validateConnection</span></code></p></td>
<td><p>Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>. If set to <code class="docutils literal notranslate"><span class="pre">true</span></code>, connectivity and credentials are validated
when the connection is created, and when <code class="docutils literal notranslate"><span class="pre">java.sql.Connection.isValid(int)</span></code> is called.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="spooling-protocol">
<span id="jdbc-spooling-protocol"></span><h2 id="spooling-protocol">Spooling protocol<a class="headerlink" href="jdbc.html#spooling-protocol" title="Link to this heading">#</a></h2>
<p>The Trino JDBC driver automatically uses of the spooling protocol to improve
throughput for client interactions with higher data transfer demands, if the
<a class="reference internal" href="client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a> is configured on the cluster.</p>
<p>Optionally use the <code class="docutils literal notranslate"><span class="pre">encoding</span></code> parameter to configure a different desired
encoding, compared to the default on the cluster.</p>
<p>The JVM process using the JDBC driver must have network access to the spooling
object storage.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="cli.html" title="Command line interface"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Command line interface </span>
              </div>
            </a>
          
          
            <a href="../security.html" title="Security"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Security </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>