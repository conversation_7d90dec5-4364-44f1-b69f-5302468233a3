<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Command line interface &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="cli.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="JDBC driver" href="jdbc.html" />
    <link rel="prev" title="Client protocol" href="client-protocol.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="cli.html#client/cli" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Command line interface </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Client protocol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Command line interface </label>
    
      <a href="cli.html#" class="md-nav__link md-nav__link--active">Command line interface</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cli.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#running-the-cli" class="md-nav__link">Running the CLI</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#tls-https" class="md-nav__link">TLS/HTTPS</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#username-and-password-authentication" class="md-nav__link">Username and password authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#external-authentication-sso" class="md-nav__link">External authentication - SSO</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#certificate-authentication" class="md-nav__link">Certificate authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#jwt-authentication" class="md-nav__link">JWT authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#kerberos-authentication" class="md-nav__link">Kerberos authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#additional-kerberos-debugging-information" class="md-nav__link">Additional Kerberos debugging information</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#pagination" class="md-nav__link">Pagination</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#history" class="md-nav__link">History</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#auto-suggestion" class="md-nav__link">Auto suggestion</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#configuration-file" class="md-nav__link">Configuration file</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#batch-mode" class="md-nav__link">Batch mode</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#spooling-protocol" class="md-nav__link">Spooling protocol</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#output-formats" class="md-nav__link">Output formats</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#troubleshooting" class="md-nav__link">Troubleshooting</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jdbc.html" class="md-nav__link">JDBC driver</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cli.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#running-the-cli" class="md-nav__link">Running the CLI</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#tls-https" class="md-nav__link">TLS/HTTPS</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#username-and-password-authentication" class="md-nav__link">Username and password authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#external-authentication-sso" class="md-nav__link">External authentication - SSO</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#certificate-authentication" class="md-nav__link">Certificate authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#jwt-authentication" class="md-nav__link">JWT authentication</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#kerberos-authentication" class="md-nav__link">Kerberos authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#additional-kerberos-debugging-information" class="md-nav__link">Additional Kerberos debugging information</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#pagination" class="md-nav__link">Pagination</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#history" class="md-nav__link">History</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#auto-suggestion" class="md-nav__link">Auto suggestion</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#configuration-file" class="md-nav__link">Configuration file</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#batch-mode" class="md-nav__link">Batch mode</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cli.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cli.html#spooling-protocol" class="md-nav__link">Spooling protocol</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#output-formats" class="md-nav__link">Output formats</a>
        </li>
        <li class="md-nav__item"><a href="cli.html#troubleshooting" class="md-nav__link">Troubleshooting</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="command-line-interface">
<h1 id="client-cli--page-root">Command line interface<a class="headerlink" href="cli.html#client-cli--page-root" title="Link to this heading">#</a></h1>
<p>The Trino CLI provides a terminal-based, interactive shell for running
queries. The CLI is a
<a class="reference external" href="http://skife.org/java/unix/2011/06/20/really_executable_jars.html">self-executing</a>
JAR file, which means it acts like a normal UNIX executable.</p>
<p>The CLI uses the <a class="reference internal" href="client-protocol.html"><span class="doc std std-doc">Client protocol</span></a> over HTTP/HTTPS to communicate with
the coordinator on the cluster.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="cli.html#requirements" title="Link to this heading">#</a></h2>
<p>The Trino CLI has the following requirements:</p>
<ul class="simple">
<li><p>Java version 11 or higher available on the path. Java 22 or higher is
recommended for improved decompression performance.</p></li>
<li><p>Network access over HTTP/HTTPS to the coordinator of the Trino cluster.</p></li>
<li><p>Network access to the configured object storage, if the
<a class="reference internal" href="cli.html#cli-spooling-protocol"><span class="std std-ref">Spooling protocol</span></a> is enabled.</p></li>
</ul>
<p>The CLI version should be identical to the version of the Trino cluster, or
newer. Older versions typically work, but only a subset is regularly tested.
Versions before 350 are not supported.</p>
</section>
<section id="installation">
<span id="cli-installation"></span><h2 id="installation">Installation<a class="headerlink" href="cli.html#installation" title="Link to this heading">#</a></h2>
<p>Download <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-cli/476/trino-cli-476-executable.jar">trino-cli-476-executable.jar</a>, rename it to <code class="docutils literal notranslate"><span class="pre">trino</span></code>, make it executable with
<code class="docutils literal notranslate"><span class="pre">chmod</span> <span class="pre">+x</span></code>, and run it to show the version of the CLI:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino --version
</pre></div>
</div>
<p>Run the CLI with <code class="docutils literal notranslate"><span class="pre">--help</span></code> or <code class="docutils literal notranslate"><span class="pre">-h</span></code> to see all available options.</p>
<p>Windows users, and users unable to execute the preceding steps, can use the
equivalent <code class="docutils literal notranslate"><span class="pre">java</span></code> command with the <code class="docutils literal notranslate"><span class="pre">-jar</span></code> option to run the CLI, and show
the version:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>java -jar trino-cli-*-executable.jar --version
</pre></div>
</div>
<p>The syntax can be used for the examples in the following sections. In addition,
using the <code class="docutils literal notranslate"><span class="pre">java</span></code> command allows you to add configuration options for the Java
runtime with the <code class="docutils literal notranslate"><span class="pre">-D</span></code> syntax. You can use this for debugging and
troubleshooting, such as when <a class="reference internal" href="cli.html#cli-kerberos-debug"><span class="std std-ref">specifying additional Kerberos debug options</span></a>.</p>
</section>
<section id="running-the-cli">
<h2 id="running-the-cli">Running the CLI<a class="headerlink" href="cli.html#running-the-cli" title="Link to this heading">#</a></h2>
<p>The minimal command to start the CLI in interactive mode specifies the URL of
the coordinator in the Trino cluster:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino http://trino.example.com:8080
</pre></div>
</div>
<p>If successful, you will get a prompt to execute commands. Use the <code class="docutils literal notranslate"><span class="pre">help</span></code>
command to see a list of supported commands. Use the <code class="docutils literal notranslate"><span class="pre">clear</span></code> command to clear
the terminal. To stop and exit the CLI, run <code class="docutils literal notranslate"><span class="pre">exit</span></code> or <code class="docutils literal notranslate"><span class="pre">quit</span></code>.:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; help

Supported commands:
QUIT
EXIT
CLEAR
EXPLAIN [ ( option [, ...] ) ] &lt;query&gt;
    options: FORMAT { TEXT | GRAPHVIZ | JSON }
            TYPE { LOGICAL | DISTRIBUTED | VALIDATE | IO }
DESCRIBE &lt;table&gt;
SHOW COLUMNS FROM &lt;table&gt;
SHOW FUNCTIONS
SHOW CATALOGS [LIKE &lt;pattern&gt;]
SHOW SCHEMAS [FROM &lt;catalog&gt;] [LIKE &lt;pattern&gt;]
SHOW TABLES [FROM &lt;schema&gt;] [LIKE &lt;pattern&gt;]
USE [&lt;catalog&gt;.]&lt;schema&gt;
</pre></div>
</div>
<p>You can now run SQL statements. After processing, the CLI will show results and
statistics.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; SELECT count(*) FROM tpch.tiny.nation;

_col0
-------
    25
(1 row)

Query 20220324_213359_00007_w6hbk, FINISHED, 1 node
Splits: 13 total, 13 done (100.00%)
2.92 [25 rows, 0B] [8 rows/s, 0B/s]
</pre></div>
</div>
<p>As part of starting the CLI, you can set the default catalog and schema. This
allows you to query tables directly without specifying catalog and schema.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino http://trino.example.com:8080/tpch/tiny

trino:tiny&gt; SHOW TABLES;

  Table
----------
customer
lineitem
nation
orders
part
partsupp
region
supplier
(8 rows)
</pre></div>
</div>
<p>You can also set the default catalog and schema with the <a class="reference internal" href="../sql/use.html"><span class="doc">USE</span></a>
statement.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; USE tpch.tiny;
USE
trino:tiny&gt;
</pre></div>
</div>
<p>Many other options are available to further configure the CLI in interactive
mode:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--catalog</span></code></p></td>
<td><p>Sets the default catalog. Optionally also use <code class="docutils literal notranslate"><span class="pre">--schema</span></code> to set the default
schema. You can change the default catalog and default schema with<a class="reference internal" href="../sql/use.html"><span class="doc std std-doc">USE</span></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--client-info</span></code></p></td>
<td><p>Adds arbitrary text as extra information about the client.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--client-request-timeout</span></code></p></td>
<td><p>Sets the duration for query processing, after which, the client request is
terminated. Defaults to <code class="docutils literal notranslate"><span class="pre">2m</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--client-tags</span></code></p></td>
<td><p>Adds extra tags information about the client and the CLI user. Separate
multiple tags with commas. The tags can be used as input for
<a class="reference internal" href="../admin/resource-groups.html"><span class="doc std std-doc">Resource groups</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--debug</span></code></p></td>
<td><p>Enables display of debug information during CLI usage for
<a class="reference internal" href="cli.html#cli-troubleshooting"><span class="std std-ref">Troubleshooting</span></a>. Displays more information about query
processing statistics.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--decimal-data-size</span></code></p></td>
<td><p>Show data size and rate in base 10 (KB, MB, etc.) rather than the default
base 2 (KiB, MiB, etc.).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--disable-auto-suggestion</span></code></p></td>
<td><p>Disables autocomplete suggestions.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--disable-compression</span></code></p></td>
<td><p>Disables compression of query results.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--editing-mode</span></code></p></td>
<td><p>Sets key bindings in the CLI to be compatible with VI or
EMACS editors. Defaults to <code class="docutils literal notranslate"><span class="pre">EMACS</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--extra-credential</span></code></p></td>
<td><p>Extra credentials (property can be used multiple times; format is key=value)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--http-proxy</span></code></p></td>
<td><p>Configures the URL of the HTTP proxy to connect to Trino.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--history-file</span></code></p></td>
<td><p>Path to the <a class="reference internal" href="cli.html#cli-history"><span class="std std-ref">history file</span></a>. Defaults to <code class="docutils literal notranslate"><span class="pre">~/.trino_history</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--network-logging</span></code></p></td>
<td><p>Configures the level of detail provided for network logging of the CLI.
Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, other options are <code class="docutils literal notranslate"><span class="pre">BASIC</span></code>, <code class="docutils literal notranslate"><span class="pre">HEADERS</span></code>, or <code class="docutils literal notranslate"><span class="pre">BODY</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--output-format-interactive=&lt;format&gt;</span></code></p></td>
<td><p>Specify the <a class="reference internal" href="cli.html#cli-output-format"><span class="std std-ref">format</span></a> to use for printing query results.
Defaults to <code class="docutils literal notranslate"><span class="pre">ALIGNED</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--pager=&lt;pager&gt;</span></code></p></td>
<td><p>Path to the pager program used to display the query results. Set to an empty
value to completely disable pagination. Defaults to <code class="docutils literal notranslate"><span class="pre">less</span></code> with a carefully
selected set of options.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--no-progress</span></code></p></td>
<td><p>Do not show query processing progress.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--path</span></code></p></td>
<td><p>Set the default <a class="reference internal" href="../sql/set-path.html"><span class="doc std std-doc">SQL path</span></a> for the session. Useful for
setting a catalog and schema location for <a class="reference internal" href="../udf/introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--password</span></code></p></td>
<td><p>Prompts for a password. Use if your Trino server requires password
authentication. You can set the <code class="docutils literal notranslate"><span class="pre">TRINO_PASSWORD</span></code> environment variable with
the password value to avoid the prompt. For more information, see
<a class="reference internal" href="cli.html#cli-username-password-auth"><span class="std std-ref">Username and password authentication</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--schema</span></code></p></td>
<td><p>Sets the default schema. Must be combined with <code class="docutils literal notranslate"><span class="pre">--catalog</span></code>. You can change
the default catalog and default schema with <a class="reference internal" href="../sql/use.html"><span class="doc std std-doc">USE</span></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--server</span></code></p></td>
<td><p>The HTTP/HTTPS address and port of the Trino coordinator. The port must be
set to the port the Trino coordinator is listening for connections on. Port
80 for HTTP and Port 443 for HTTPS can be omitted. Trino server location
defaults to <code class="docutils literal notranslate"><span class="pre">http://localhost:8080</span></code>. Can only be set if URL is not
specified.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--session</span></code></p></td>
<td><p>Sets one or more <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session properties</span></a>.
Property can be used multiple times with the format
<code class="docutils literal notranslate"><span class="pre">session_property_name=value</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--socks-proxy</span></code></p></td>
<td><p>Configures the URL of the SOCKS proxy to connect to Trino.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--source</span></code></p></td>
<td><p>Specifies the name of the application or source connecting to Trino.
Defaults to <code class="docutils literal notranslate"><span class="pre">trino-cli</span></code>. The value can be used as input for
<a class="reference internal" href="../admin/resource-groups.html"><span class="doc std std-doc">Resource groups</span></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--timezone</span></code></p></td>
<td><p>Sets the time zone for the session using the <a class="reference external" href="https://wikipedia.org/wiki/List_of_tz_database_time_zones">time zone name</a>. Defaults to
the timezone set on your workstation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--user</span></code></p></td>
<td><p>Sets the username for <a class="reference internal" href="cli.html#cli-username-password-auth"><span class="std std-ref">Username and password authentication</span></a>. Defaults to your
operating system username. You can override the default username, if your
cluster uses a different username or authentication mechanism.</p></td>
</tr>
</tbody>
</table>
<p>Most of the options can also be set as parameters in the URL. This means
a JDBC URL can be used in the CLI after removing the <code class="docutils literal notranslate"><span class="pre">jdbc:</span></code> prefix.
However, the same parameter may not be specified using both methods.
See <a class="reference internal" href="jdbc.html"><span class="doc">the JDBC driver parameter reference</span></a>
to find out URL parameter names. For example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino 'https://trino.example.com?SSL=true&amp;SSLVerification=FULL&amp;clientInfo=extra'
</pre></div>
</div>
</section>
<section id="tls-https">
<span id="cli-tls"></span><h2 id="tls-https">TLS/HTTPS<a class="headerlink" href="cli.html#tls-https" title="Link to this heading">#</a></h2>
<p>Trino is typically available with an HTTPS URL. This means that all network
traffic between the CLI and Trino uses TLS. <a class="reference internal" href="../security/tls.html"><span class="doc">TLS configuration</span></a> is common, since it is a requirement for <a class="reference internal" href="cli.html#cli-authentication"><span class="std std-ref">any authentication</span></a>.</p>
<p>Use the HTTPS URL to connect to the server:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino https://trino.example.com
</pre></div>
</div>
<p>The recommended TLS implementation is to use a globally trusted certificate. In
this case, no other options are necessary, since the JVM running the CLI
recognizes these certificates.</p>
<p>Use the options from the following table to further configure TLS and
certificate usage:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--insecure</span></code></p></td>
<td><p>Skip certificate validation when connecting with TLS/HTTPS (should only be
used for debugging).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--keystore-path</span></code></p></td>
<td><p>The location of the Java Keystore file that contains the certificate of the
server to connect with TLS.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--keystore-password</span></code></p></td>
<td><p>The password for the keystore. This must match the password you specified
when creating the keystore.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--keystore-type</span></code></p></td>
<td><p>Determined by the keystore file format. The default keystore type is JKS.
This advanced option is only necessary if you use a custom Java Cryptography
Architecture (JCA) provider implementation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--use-system-keystore</span></code></p></td>
<td><p>Use a client certificate obtained from the system keystore of the operating
system. Windows and macOS are supported. For other operating systems, the
default Java keystore is used. The keystore type can be overridden using
<code class="docutils literal notranslate"><span class="pre">--keystore-type</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--truststore-password</span></code></p></td>
<td><p>The password for the truststore. This must match the password you specified
when creating the truststore.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--truststore-path</span></code></p></td>
<td><p>The location of the Java truststore file that will be used to secure TLS.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--truststore-type</span></code></p></td>
<td><p>Determined by the truststore file format. The default keystore type is JKS.
This advanced option is only necessary if you use a custom Java Cryptography
Architecture (JCA) provider implementation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--use-system-truststore</span></code></p></td>
<td><p>Verify the server certificate using the system truststore of the operating
system. Windows and macOS are supported. For other operating systems, the
default Java truststore is used. The truststore type can be overridden using
<code class="docutils literal notranslate"><span class="pre">--truststore-type</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="authentication">
<span id="cli-authentication"></span><h2 id="authentication">Authentication<a class="headerlink" href="cli.html#authentication" title="Link to this heading">#</a></h2>
<p>The Trino CLI supports many <a class="reference internal" href="../security/authentication-types.html"><span class="doc">Authentication types</span></a> detailed in
the following sections:</p>
<section id="username-and-password-authentication">
<span id="cli-username-password-auth"></span><h3 id="username-and-password-authentication">Username and password authentication<a class="headerlink" href="cli.html#username-and-password-authentication" title="Link to this heading">#</a></h3>
<p>Username and password authentication is typically configured in a cluster using
the <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> <a class="reference internal" href="../security/authentication-types.html"><span class="doc">authentication type</span></a>,
for example with <a class="reference internal" href="../security/ldap.html"><span class="doc">LDAP authentication</span></a> or <a class="reference internal" href="../security/password-file.html"><span class="doc">Password file authentication</span></a>.</p>
<p>The following code example connects to the server, establishes your username,
and prompts the CLI for your password:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino https://trino.example.com --user=exampleusername --password
</pre></div>
</div>
<p>Alternatively, set the password as the value of the <code class="docutils literal notranslate"><span class="pre">TRINO_PASSWORD</span></code>
environment variable. Typically use single quotes to avoid problems with
special characters such as <code class="docutils literal notranslate"><span class="pre">$</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>export TRINO_PASSWORD='LongSecurePassword123!@#'
</pre></div>
</div>
<p>If the <code class="docutils literal notranslate"><span class="pre">TRINO_PASSWORD</span></code> environment variable is set, you are not prompted
to provide a password to connect with the CLI.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino https://trino.example.com --user=exampleusername --password
</pre></div>
</div>
</section>
<section id="external-authentication-sso">
<span id="cli-external-sso-auth"></span><h3 id="external-authentication-sso">External authentication - SSO<a class="headerlink" href="cli.html#external-authentication-sso" title="Link to this heading">#</a></h3>
<p>Use the <code class="docutils literal notranslate"><span class="pre">--external-authentication</span></code> option for browser-based SSO
authentication, as detailed in <a class="reference internal" href="../security/oauth2.html"><span class="doc">OAuth 2.0 authentication</span></a>. With this configuration,
the CLI displays a URL that you must open in a web browser for authentication.</p>
<p>The detailed behavior is as follows:</p>
<ul class="simple">
<li><p>Start the CLI with the <code class="docutils literal notranslate"><span class="pre">--external-authentication</span></code> option and execute a
query.</p></li>
<li><p>The CLI starts and connects to Trino.</p></li>
<li><p>A message appears in the CLI directing you to open a browser with a specified
URL when the first query is submitted.</p></li>
<li><p>Open the URL in a browser and follow through the authentication process.</p></li>
<li><p>The CLI automatically receives a token.</p></li>
<li><p>When successfully authenticated in the browser, the CLI proceeds to execute
the query.</p></li>
<li><p>Further queries in the CLI session do not require additional logins while the
authentication token remains valid. Token expiration depends on the external
authentication type configuration.</p></li>
<li><p>Expired tokens force you to log in again.</p></li>
</ul>
</section>
<section id="certificate-authentication">
<span id="cli-certificate-auth"></span><h3 id="certificate-authentication">Certificate authentication<a class="headerlink" href="cli.html#certificate-authentication" title="Link to this heading">#</a></h3>
<p>Use the following CLI arguments to connect to a cluster that uses
<a class="reference internal" href="../security/certificate.html"><span class="doc">certificate authentication</span></a>.</p>
<table id="id1">
<caption><span class="caption-text">CLI options for certificate authentication</span><a class="headerlink" href="cli.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--keystore-path=&lt;path&gt;</span></code></p></td>
<td><p>Absolute or relative path to a <a class="reference internal" href="../security/inspect-pem.html"><span class="doc std std-doc">PEM</span></a> or
<a class="reference internal" href="../security/inspect-jks.html"><span class="doc std std-doc">JKS</span></a> file, which must contain a certificate
that is trusted by the Trino cluster you are connecting to.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--keystore-password=&lt;password&gt;</span></code></p></td>
<td><p>Only required if the keystore has a password.</p></td>
</tr>
</tbody>
</table>
<p>The truststore related options are independent of client certificate
authentication with the CLI; instead, they control the client’s trust of the
server’s certificate.</p>
</section>
<section id="jwt-authentication">
<span id="cli-jwt-auth"></span><h3 id="jwt-authentication">JWT authentication<a class="headerlink" href="cli.html#jwt-authentication" title="Link to this heading">#</a></h3>
<p>To access a Trino cluster configured to use <a class="reference internal" href="../security/jwt.html"><span class="doc">JWT authentication</span></a>, use the
<code class="docutils literal notranslate"><span class="pre">--access-token=&lt;token&gt;</span></code> option to pass a JWT to the server.</p>
</section>
<section id="kerberos-authentication">
<span id="cli-kerberos-auth"></span><h3 id="kerberos-authentication">Kerberos authentication<a class="headerlink" href="cli.html#kerberos-authentication" title="Link to this heading">#</a></h3>
<p>The Trino CLI can connect to a Trino cluster that has <a class="reference internal" href="../security/kerberos.html"><span class="doc">Kerberos authentication</span></a>
enabled.</p>
<p>Invoking the CLI with Kerberos support enabled requires a number of additional
command line options. You also need the <a class="reference internal" href="../security/kerberos.html#server-kerberos-principals"><span class="std std-ref">Kerberos configuration files</span></a> for your user on the machine running the CLI. The
simplest way to invoke the CLI is with a wrapper script:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>#!/bin/bash

./trino \
  --server https://trino.example.com \
  --krb5-config-path /etc/krb5.conf \
  --krb5-principal <a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="c4b7aba9a1b1b7a1b684819c8589948881ea878b89">[email&#160;protected]</a> \
  --krb5-keytab-path /home/<USER>/someuser.keytab \
  --krb5-remote-service-name trino
</pre></div>
</div>
<p>When using Kerberos authentication, access to the Trino coordinator must be
through <a class="reference internal" href="../security/tls.html"><span class="doc">TLS and HTTPS</span></a>.</p>
<p>The following table lists the available options for Kerberos authentication:</p>
<table id="id2">
<caption><span class="caption-text">CLI options for Kerberos authentication</span><a class="headerlink" href="cli.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-config-path</span></code></p></td>
<td><p>Path to Kerberos configuration files.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-credential-cache-path</span></code></p></td>
<td><p>Kerberos credential cache path.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-disable-remote-service-hostname-canonicalization</span></code></p></td>
<td><p>Disable service hostname canonicalization using the DNS reverse lookup.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-keytab-path</span></code></p></td>
<td><p>The location of the keytab that can be used to authenticate the principal
specified by <code class="docutils literal notranslate"><span class="pre">--krb5-principal</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-principal</span></code></p></td>
<td><p>The principal to use when authenticating to the coordinator.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-remote-service-name</span></code></p></td>
<td><p>Trino coordinator Kerberos service name.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--krb5-service-principal-pattern</span></code></p></td>
<td><p>Remote kerberos service principal pattern. Defaults to <code class="docutils literal notranslate"><span class="pre">${SERVICE}@${HOST}</span></code>.</p></td>
</tr>
</tbody>
</table>
<section id="additional-kerberos-debugging-information">
<span id="cli-kerberos-debug"></span><h4 id="additional-kerberos-debugging-information">Additional Kerberos debugging information<a class="headerlink" href="cli.html#additional-kerberos-debugging-information" title="Link to this heading">#</a></h4>
<p>You can enable additional Kerberos debugging information for the Trino CLI
process by passing <code class="docutils literal notranslate"><span class="pre">-Dsun.security.krb5.debug=true</span></code>,
<code class="docutils literal notranslate"><span class="pre">-Dtrino.client.debugKerberos=true</span></code>, and
<code class="docutils literal notranslate"><span class="pre">-Djava.security.debug=gssloginconfig,configfile,configparser,logincontext</span></code>
as a JVM argument when <a class="reference internal" href="cli.html#cli-installation"><span class="std std-ref">starting the CLI process</span></a>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>java \
  -Dsun.security.krb5.debug=true \
  -Djava.security.debug=gssloginconfig,configfile,configparser,logincontext \
  -Dtrino.client.debugKerberos=true \
  -jar trino-cli-*-executable.jar \
  --server https://trino.example.com \
  --krb5-config-path /etc/krb5.conf \
  --krb5-principal <a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="b1c2dedcd4c4c2d4c3f1f4e9f0fce1fdf49ff2fefc">[email&#160;protected]</a> \
  --krb5-keytab-path /home/<USER>/someuser.keytab \
  --krb5-remote-service-name trino
</pre></div>
</div>
<p>For help with interpreting Kerberos debugging messages, see <a class="reference internal" href="../security/kerberos.html#kerberos-debug"><span class="std std-ref">additional resources</span></a>.</p>
</section>
</section>
</section>
<section id="pagination">
<h2 id="pagination">Pagination<a class="headerlink" href="cli.html#pagination" title="Link to this heading">#</a></h2>
<p>By default, the results of queries are paginated using the <code class="docutils literal notranslate"><span class="pre">less</span></code> program
which is configured with a carefully selected set of options. This behavior
can be overridden by setting the <code class="docutils literal notranslate"><span class="pre">--pager</span></code> option or
the <code class="docutils literal notranslate"><span class="pre">TRINO_PAGER</span></code> environment variable to the name of a different program
such as <code class="docutils literal notranslate"><span class="pre">more</span></code> or <a class="reference external" href="https://github.com/okbob/pspg">pspg</a>,
or it can be set to an empty value to completely disable pagination.</p>
</section>
<section id="history">
<span id="cli-history"></span><h2 id="history">History<a class="headerlink" href="cli.html#history" title="Link to this heading">#</a></h2>
<p>The CLI keeps a history of your previously used commands. You can access your
history by scrolling or searching. Use the up and down arrows to scroll and
<kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>+<kbd class="kbd docutils literal notranslate">S</kbd></kbd> and <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>+<kbd class="kbd docutils literal notranslate">R</kbd></kbd> to search. To execute a query again,
press <kbd class="kbd docutils literal notranslate">Enter</kbd>.</p>
<p>By default, you can locate the Trino history file in <code class="docutils literal notranslate"><span class="pre">~/.trino_history</span></code>.
Use the <code class="docutils literal notranslate"><span class="pre">--history-file</span></code> option or the <code class="docutils literal notranslate"><span class="pre">`TRINO_HISTORY_FILE</span></code> environment variable
to change the default.</p>
<section id="auto-suggestion">
<h3 id="auto-suggestion">Auto suggestion<a class="headerlink" href="cli.html#auto-suggestion" title="Link to this heading">#</a></h3>
<p>The CLI generates autocomplete suggestions based on command history.</p>
<p>Press <kbd class="kbd docutils literal notranslate">→</kbd> to accept the suggestion and replace the current command line
buffer. Press <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Ctrl</kbd>+<kbd class="kbd docutils literal notranslate">→</kbd></kbd> (<kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Option</kbd>+<kbd class="kbd docutils literal notranslate">→</kbd></kbd> on Mac) to accept only the next
keyword. Continue typing to reject the suggestion.</p>
</section>
</section>
<section id="configuration-file">
<h2 id="configuration-file">Configuration file<a class="headerlink" href="cli.html#configuration-file" title="Link to this heading">#</a></h2>
<p>The CLI can read default values for all options from a file. It uses the first
file found from the ordered list of locations:</p>
<ul class="simple">
<li><p>File path set as value of the <code class="docutils literal notranslate"><span class="pre">TRINO_CONFIG</span></code> environment variable.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">.trino_config</span></code> in the current users home directory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$XDG_CONFIG_HOME/trino/config</span></code>.</p></li>
</ul>
<p>For example, you could create separate configuration files with different
authentication options, like <code class="docutils literal notranslate"><span class="pre">kerberos-cli.properties</span></code> and <code class="docutils literal notranslate"><span class="pre">ldap-cli.properties</span></code>.
Assuming they’re located in the current directory, you can set the
<code class="docutils literal notranslate"><span class="pre">TRINO_CONFIG</span></code> environment variable for a single invocation of the CLI by
adding it before the <code class="docutils literal notranslate"><span class="pre">trino</span></code> command:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>TRINO_CONFIG=kerberos-cli.properties trino https://first-cluster.example.com:8443
TRINO_CONFIG=ldap-cli.properties trino https://second-cluster.example.com:8443
</pre></div>
</div>
<p>In the preceding example, the default configuration files are not used.</p>
<p>You can use all supported options without the <code class="docutils literal notranslate"><span class="pre">--</span></code> prefix in the configuration
properties file. Options that normally don’t take an argument are boolean, so
set them to either <code class="docutils literal notranslate"><span class="pre">true</span></code> or <code class="docutils literal notranslate"><span class="pre">false</span></code>. For example:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">output-format-interactive</span><span class="o">=</span><span class="s">AUTO</span>
<span class="na">timezone</span><span class="o">=</span><span class="s">Europe/Warsaw</span>
<span class="na">user</span><span class="o">=</span><span class="s">trino-client</span>
<span class="na">network-logging</span><span class="o">=</span><span class="s">BASIC</span>
<span class="na">krb5-disable-remote-service-hostname-canonicalization</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
</section>
<section id="batch-mode">
<h2 id="batch-mode">Batch mode<a class="headerlink" href="cli.html#batch-mode" title="Link to this heading">#</a></h2>
<p>Running the Trino CLI with the <code class="docutils literal notranslate"><span class="pre">--execute</span></code>, <code class="docutils literal notranslate"><span class="pre">--file</span></code>, or passing queries to
the standard input uses the batch (non-interactive) mode. In this mode
the CLI does not report progress, and exits after processing the supplied
queries. Results are printed in <code class="docutils literal notranslate"><span class="pre">CSV</span></code> format by default. You can configure
other formats and redirect the output to a file.</p>
<p>The following options are available to further configure the CLI in batch
mode:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--execute=&lt;execute&gt;</span></code></p></td>
<td><p>Execute specified statements and exit.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">-f</span></code>, <code class="docutils literal notranslate"><span class="pre">--file=&lt;file&gt;</span></code></p></td>
<td><p>Execute statements from file and exit.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--ignore-errors</span></code></p></td>
<td><p>Continue processing in batch mode when an error occurs. Default is to exit
immediately.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">--output-format=&lt;format&gt;</span></code></p></td>
<td><p>Specify the <a class="reference internal" href="cli.html#cli-output-format"><span class="std std-ref">format</span></a> to use for printing query results.
Defaults to <code class="docutils literal notranslate"><span class="pre">CSV</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">--progress</span></code></p></td>
<td><p>Show query progress in batch mode. It does not affect the output, which, for
example can be safely redirected to a file.</p></td>
</tr>
</tbody>
</table>
<section id="examples">
<h3 id="examples">Examples<a class="headerlink" href="cli.html#examples" title="Link to this heading">#</a></h3>
<p>Consider the following command run as shown, or with the
<code class="docutils literal notranslate"><span class="pre">--output-format=CSV</span></code> option, which is the default for non-interactive usage:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino --execute 'SELECT nationkey, name, regionkey FROM tpch.sf1.nation LIMIT 3'
</pre></div>
</div>
<p>The output is as follows:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>"0","ALGERIA","0"
"1","ARGENTINA","1"
"2","BRAZIL","1"
</pre></div>
</div>
<p>The output with the <code class="docutils literal notranslate"><span class="pre">--output-format=JSON</span></code> option:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span><span class="nt">"nationkey"</span><span class="p">:</span><span class="mi">0</span><span class="p">,</span><span class="nt">"name"</span><span class="p">:</span><span class="s2">"ALGERIA"</span><span class="p">,</span><span class="nt">"regionkey"</span><span class="p">:</span><span class="mi">0</span><span class="p">}</span>
<span class="p">{</span><span class="nt">"nationkey"</span><span class="p">:</span><span class="mi">1</span><span class="p">,</span><span class="nt">"name"</span><span class="p">:</span><span class="s2">"ARGENTINA"</span><span class="p">,</span><span class="nt">"regionkey"</span><span class="p">:</span><span class="mi">1</span><span class="p">}</span>
<span class="p">{</span><span class="nt">"nationkey"</span><span class="p">:</span><span class="mi">2</span><span class="p">,</span><span class="nt">"name"</span><span class="p">:</span><span class="s2">"BRAZIL"</span><span class="p">,</span><span class="nt">"regionkey"</span><span class="p">:</span><span class="mi">1</span><span class="p">}</span>
</pre></div>
</div>
<p>The output with the <code class="docutils literal notranslate"><span class="pre">--output-format=ALIGNED</span></code> option, which is the default
for interactive usage:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>nationkey |   name    | regionkey
----------+-----------+----------
        0 | ALGERIA   |         0
        1 | ARGENTINA |         1
        2 | BRAZIL    |         1
</pre></div>
</div>
<p>The output with the <code class="docutils literal notranslate"><span class="pre">--output-format=VERTICAL</span></code> option:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-[ RECORD 1 ]--------
nationkey | 0
name      | ALGERIA
regionkey | 0
-[ RECORD 2 ]--------
nationkey | 1
name      | ARGENTINA
regionkey | 1
-[ RECORD 3 ]--------
nationkey | 2
name      | BRAZIL
regionkey | 1
</pre></div>
</div>
<p>The preceding command with <code class="docutils literal notranslate"><span class="pre">--output-format=NULL</span></code> produces no output.
However, if you have an error in the query, such as incorrectly using
<code class="docutils literal notranslate"><span class="pre">region</span></code> instead of <code class="docutils literal notranslate"><span class="pre">regionkey</span></code>, the command has an exit status of 1
and displays an error message (which is unaffected by the output format):</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Query 20200707_170726_00030_2iup9 failed: line 1:25: Column 'region' cannot be resolved
SELECT nationkey, name, region FROM tpch.sf1.nation LIMIT 3
</pre></div>
</div>
</section>
</section>
<section id="spooling-protocol">
<span id="cli-spooling-protocol"></span><h2 id="spooling-protocol">Spooling protocol<a class="headerlink" href="cli.html#spooling-protocol" title="Link to this heading">#</a></h2>
<p>The Trino CLI automatically uses the spooling protocol to improve throughput
for client interactions with higher data transfer demands, if the
<a class="reference internal" href="client-protocol.html#protocol-spooling"><span class="std std-ref">Spooling protocol</span></a> is configured on the cluster.</p>
<p>Optionally use the <code class="docutils literal notranslate"><span class="pre">--encoding</span></code> option to configure a different desired
encoding, compared to the default on the cluster. The available values are
<code class="docutils literal notranslate"><span class="pre">json+zstd</span></code> (recommended) for JSON with Zstandard compression, and <code class="docutils literal notranslate"><span class="pre">json+lz4</span></code>
for JSON with LZ4 compression, and <code class="docutils literal notranslate"><span class="pre">json</span></code> for uncompressed JSON.</p>
<p>The CLI process must have network access to the spooling object storage.</p>
</section>
<section id="output-formats">
<span id="cli-output-format"></span><h2 id="output-formats">Output formats<a class="headerlink" href="cli.html#output-formats" title="Link to this heading">#</a></h2>
<p>The Trino CLI provides the options <code class="docutils literal notranslate"><span class="pre">--output-format</span></code>
and <code class="docutils literal notranslate"><span class="pre">--output-format-interactive</span></code> to control how the output is displayed.
The available options shown in the following table must be entered
in uppercase. The default value is <code class="docutils literal notranslate"><span class="pre">ALIGNED</span></code> in interactive mode,
and <code class="docutils literal notranslate"><span class="pre">CSV</span></code> in non-interactive mode.</p>
<table id="id3">
<caption><span class="caption-text">Output format options</span><a class="headerlink" href="cli.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 75%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Option</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">CSV</span></code></p></td>
<td><p>Comma-separated values, each value quoted. No header row.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CSV_HEADER</span></code></p></td>
<td><p>Comma-separated values, quoted with header row.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">CSV_UNQUOTED</span></code></p></td>
<td><p>Comma-separated values without quotes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CSV_HEADER_UNQUOTED</span></code></p></td>
<td><p>Comma-separated values with header row but no quotes.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TSV</span></code></p></td>
<td><p>Tab-separated values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TSV_HEADER</span></code></p></td>
<td><p>Tab-separated values with header row.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p>Output rows emitted as JSON objects with name-value pairs.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ALIGNED</span></code></p></td>
<td><p>Output emitted as an ASCII character table with values.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VERTICAL</span></code></p></td>
<td><p>Output emitted as record-oriented top-down lines, one per value.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">AUTO</span></code></p></td>
<td><p>Same as <code class="docutils literal notranslate"><span class="pre">ALIGNED</span></code> if output would fit the current terminal width,
and <code class="docutils literal notranslate"><span class="pre">VERTICAL</span></code> otherwise.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MARKDOWN</span></code></p></td>
<td><p>Output emitted as a Markdown table.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p>Suppresses normal query results. This can be useful during development to
test a query’s shell return code or to see whether it results in error
messages.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="troubleshooting">
<span id="cli-troubleshooting"></span><h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="cli.html#troubleshooting" title="Link to this heading">#</a></h2>
<p>If something goes wrong, you see an error message:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ trino
trino&gt; select count(*) from tpch.tiny.nations;
Query 20200804_201646_00003_f5f6c failed: line 1:22: Table 'tpch.tiny.nations' does not exist
select count(*) from tpch.tiny.nations
</pre></div>
</div>
<p>To view debug information, including the stack trace for failures, use the
<code class="docutils literal notranslate"><span class="pre">--debug</span></code> option:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ trino --debug
trino&gt; select count(*) from tpch.tiny.nations;
Query 20200804_201629_00002_f5f6c failed: line 1:22: Table 'tpch.tiny.nations' does not exist
io.trino.spi.TrinoException: line 1:22: Table 'tpch.tiny.nations' does not exist
at io.trino.sql.analyzer.SemanticExceptions.semanticException(SemanticExceptions.java:48)
at io.trino.sql.analyzer.SemanticExceptions.semanticException(SemanticExceptions.java:43)
...
at java.base/java.lang.Thread.run(Thread.java:834)
select count(*) from tpch.tiny.nations
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="client-protocol.html" title="Client protocol"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Client protocol </span>
              </div>
            </a>
          
          
            <a href="jdbc.html" title="JDBC driver"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> JDBC driver </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>