<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Azure Storage file system support &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="file-system-azure.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Google Cloud Storage file system support" href="file-system-gcs.html" />
    <link rel="prev" title="Object storage" href="../object-storage.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="file-system-azure.html#object-storage/file-system-azure" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Azure Storage file system support </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Azure Storage file system support </label>
    
      <a href="file-system-azure.html#" class="md-nav__link md-nav__link--active">Azure Storage file system support</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-azure.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#user-assigned-managed-identity-authentication" class="md-nav__link">User-assigned managed identity authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#access-key-authentication" class="md-nav__link">Access key authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#oauth-2-0-authentication" class="md-nav__link">OAuth 2.0 authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#access-multiple-storage-accounts" class="md-nav__link">Access multiple storage accounts</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#migration-from-legacy-azure-storage-file-system" class="md-nav__link">Migration from legacy Azure Storage file system</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-s3.html" class="md-nav__link">S3 file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-hdfs.html" class="md-nav__link">HDFS file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-cache.html" class="md-nav__link">File system cache</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="metastores.html" class="md-nav__link">Metastores</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-azure.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#user-assigned-managed-identity-authentication" class="md-nav__link">User-assigned managed identity authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#access-key-authentication" class="md-nav__link">Access key authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#oauth-2-0-authentication" class="md-nav__link">OAuth 2.0 authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#access-multiple-storage-accounts" class="md-nav__link">Access multiple storage accounts</a>
        </li>
        <li class="md-nav__item"><a href="file-system-azure.html#migration-from-legacy-azure-storage-file-system" class="md-nav__link">Migration from legacy Azure Storage file system</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="azure-storage-file-system-support">
<h1 id="object-storage-file-system-azure--page-root">Azure Storage file system support<a class="headerlink" href="file-system-azure.html#object-storage-file-system-azure--page-root" title="Link to this heading">#</a></h1>
<p>Trino includes a native implementation to access <a class="reference external" href="https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blobs-overview#about-azure-data-lake-storage-gen2">Azure Data Lake Storage
Gen2</a>
with a catalog using the Delta Lake, Hive, Hudi, or Iceberg connectors.</p>
<p>Enable the native implementation with <code class="docutils literal notranslate"><span class="pre">fs.native-azure.enabled=true</span></code> in your
catalog properties file. Additionally, the Azure storage account must have
hierarchical namespace enabled.</p>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="file-system-azure.html#general-configuration" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure general aspects of Azure Storage file
system support:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.native-azure.enabled</span></code></p></td>
<td><p>Activate the native implementation for Azure Storage support. Defaults to
<code class="docutils literal notranslate"><span class="pre">false</span></code>. Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to use Azure Storage and enable all other properties.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.auth-type</span></code></p></td>
<td><p>Authentication type to use for Azure Storage access. Defaults to <code class="docutils literal notranslate"><span class="pre">DEFAULT</span></code> which
loads from environment variables if configured or <a class="reference internal" href="file-system-azure.html#azure-user-assigned-managed-identity-authentication"><span class="std std-ref">User-assigned managed identity authentication</span></a>.
Use <code class="docutils literal notranslate"><span class="pre">ACCESS_KEY</span></code> for <a class="reference internal" href="file-system-azure.html#azure-access-key-authentication"><span class="std std-ref">Access key authentication</span></a> or and <code class="docutils literal notranslate"><span class="pre">OAUTH</span></code>
for <a class="reference internal" href="file-system-azure.html#azure-oauth-authentication"><span class="std std-ref">OAuth 2.0 authentication</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.endpoint</span></code></p></td>
<td><p>Hostname suffix of the Azure storage endpoint.
Defaults to <code class="docutils literal notranslate"><span class="pre">core.windows.net</span></code> for the global Azure cloud.
Use <code class="docutils literal notranslate"><span class="pre">core.usgovcloudapi.net</span></code> for the Azure US Government cloud,
<code class="docutils literal notranslate"><span class="pre">core.cloudapi.de</span></code> for the Azure Germany cloud,
or <code class="docutils literal notranslate"><span class="pre">core.chinacloudapi.cn</span></code> for the Azure China cloud.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.read-block-size</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">Data size</span></a> for blocks during read operations. Defaults
to <code class="docutils literal notranslate"><span class="pre">4MB</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.write-block-size</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">Data size</span></a> for blocks during write operations.
Defaults to <code class="docutils literal notranslate"><span class="pre">4MB</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.max-write-concurrency</span></code></p></td>
<td><p>Maximum number of concurrent write operations. Defaults to 8.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.max-single-upload-size</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">Data size</span></a> Defaults to <code class="docutils literal notranslate"><span class="pre">4MB</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.max-http-requests</span></code></p></td>
<td><p>Maximum <a class="reference internal" href="../admin/properties.html#prop-type-integer"><span class="std std-ref">integer</span></a> number of concurrent HTTP requests to
Azure from every node. Defaults to double the number of processors on the
node. Minimum <code class="docutils literal notranslate"><span class="pre">1</span></code>. Use this property to reduce the number of requests when
you encounter rate limiting issues.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.application-id</span></code></p></td>
<td><p>Specify the application identifier appended to the <code class="docutils literal notranslate"><span class="pre">User-Agent</span></code> header
for all requests sent to Azure Storage. Defaults to <code class="docutils literal notranslate"><span class="pre">Trino</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="user-assigned-managed-identity-authentication">
<span id="azure-user-assigned-managed-identity-authentication"></span><h2 id="user-assigned-managed-identity-authentication">User-assigned managed identity authentication<a class="headerlink" href="file-system-azure.html#user-assigned-managed-identity-authentication" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure <a class="reference external" href="https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/">user-assigned managed
identity</a>
authentication to Azure Storage:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.auth-type</span></code></p></td>
<td><p>Must be set to <code class="docutils literal notranslate"><span class="pre">DEFAULT</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.user-assigned-managed-identity.client-id</span></code></p></td>
<td><p>Specifies the client ID of user-assigned managed identity.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.user-assigned-managed-identity.resource-id</span></code></p></td>
<td><p>Specifies the resource ID of user-assigned managed identity.</p></td>
</tr>
</tbody>
</table>
<p>Only one of <code class="docutils literal notranslate"><span class="pre">azure.user-assigned-managed-identity.client-id</span></code> or <code class="docutils literal notranslate"><span class="pre">azure.user-assigned-managed-identity.resource-id</span></code> can be
specified.</p>
</section>
<section id="access-key-authentication">
<span id="azure-access-key-authentication"></span><h2 id="access-key-authentication">Access key authentication<a class="headerlink" href="file-system-azure.html#access-key-authentication" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure access key authentication to Azure
Storage:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.auth-type</span></code></p></td>
<td><p>Must be set to <code class="docutils literal notranslate"><span class="pre">ACCESS_KEY</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.access-key</span></code></p></td>
<td><p>The decrypted access key for the Azure Storage account. Requires
authentication type <code class="docutils literal notranslate"><span class="pre">ACCESSS_KEY</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="oauth-2-0-authentication">
<span id="azure-oauth-authentication"></span><h2 id="oauth-2-0-authentication">OAuth 2.0 authentication<a class="headerlink" href="file-system-azure.html#oauth-2-0-authentication" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure OAuth 2.0 authentication to Azure
Storage:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.auth-type</span></code></p></td>
<td><p>Must be set to <code class="docutils literal notranslate"><span class="pre">OAUTH</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.tenant-id</span></code></p></td>
<td><p>Tenant ID for Azure authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.endpoint</span></code></p></td>
<td><p>The endpoint URL for OAuth 2.0 authentication.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.client-id</span></code></p></td>
<td><p>The OAuth 2.0 service principal’s client or application ID.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.secret</span></code></p></td>
<td><p>A OAuth 2.0 client secret for the service principal.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="access-multiple-storage-accounts">
<h2 id="access-multiple-storage-accounts">Access multiple storage accounts<a class="headerlink" href="file-system-azure.html#access-multiple-storage-accounts" title="Link to this heading">#</a></h2>
<p>To allow Trino to access multiple Azure storage accounts from a single
catalog configuration, you can use <a class="reference internal" href="file-system-azure.html#azure-oauth-authentication"><span class="std std-ref">OAuth 2.0 authentication</span></a> with
an Azure service principal. The following steps describe how to create
a service principal in Azure and assign an IAM role granting access to the
storage accounts:</p>
<ul class="simple">
<li><p>Create a service principal in Azure Active Directory using Azure
<strong>App Registrations</strong> and save the client secret.</p></li>
<li><p>Assign access to the storage accounts from the account’s
<strong>Access Control (IAM)</strong> section. You can add <strong>Role Assignments</strong> and
select appropriate roles, such as <strong>Storage Blob Data Contributor</strong>.</p></li>
<li><p>Assign access using the option <strong>User, group, or service principal</strong> and
select the service principal created. Save to finalize the role
assignment.</p></li>
</ul>
<p>Once you create the service principal and configure the storage accounts
use the <strong>Client ID</strong>, <strong>Secret</strong> and <strong>Tenant ID</strong> values from the
application registration, to configure the catalog using properties from
<a class="reference internal" href="file-system-azure.html#azure-oauth-authentication"><span class="std std-ref">OAuth 2.0 authentication</span></a>.</p>
</section>
<section id="migration-from-legacy-azure-storage-file-system">
<span id="fs-legacy-azure-migration"></span><h2 id="migration-from-legacy-azure-storage-file-system">Migration from legacy Azure Storage file system<a class="headerlink" href="file-system-azure.html#migration-from-legacy-azure-storage-file-system" title="Link to this heading">#</a></h2>
<p>Trino includes legacy Azure Storage support to use with a catalog using the
Delta Lake, Hive, Hudi, or Iceberg connectors. Upgrading existing deployments to
the current native implementation is recommended. Legacy support is deprecated
and will be removed.</p>
<p>To migrate a catalog to use the native file system implementation for Azure,
make the following edits to your catalog configuration:</p>
<ol class="arabic simple">
<li><p>Add the <code class="docutils literal notranslate"><span class="pre">fs.native-azure.enabled=true</span></code> catalog configuration property.</p></li>
<li><p>Configure the <code class="docutils literal notranslate"><span class="pre">azure.auth-type</span></code> catalog configuration property.</p></li>
<li><p>Refer to the following table to rename your existing legacy catalog
configuration properties to the corresponding native configuration
properties. Supported configuration values are identical unless otherwise
noted.</p></li>
</ol>
<table>
<colgroup>
<col style="width: 26%"/>
<col style="width: 26%"/>
<col style="width: 48%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Legacy property</p></th>
<th class="head"><p>Native property</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs-access-key</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">azure.access-key</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs.oauth.endpoint</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.endpoint</span></code></p></td>
<td><p>Also see <code class="docutils literal notranslate"><span class="pre">azure.oauth.tenant-id</span></code> in <a class="reference internal" href="file-system-azure.html#azure-oauth-authentication"><span class="std std-ref">OAuth 2.0 authentication</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs.oauth.client-id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.client-id</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs.oauth.secret</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">azure.oauth.secret</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs.oauth2.passthrough</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">azure.use-oauth-passthrough-token</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<ol class="arabic simple" start="4">
<li><p>Remove the following legacy configuration properties if they exist in your
catalog configuration:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">hive.azure.abfs-storage-account</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.azure.wasb-access-key</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.azure.wasb-storage-account</span></code></p></li>
</ul>
</li>
</ol>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../object-storage.html" title="Object storage"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Object storage </span>
              </div>
            </a>
          
          
            <a href="file-system-gcs.html" title="Google Cloud Storage file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Google Cloud Storage file system support </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>