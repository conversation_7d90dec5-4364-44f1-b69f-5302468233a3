<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>File system cache &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="file-system-cache.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Alluxio file system support" href="file-system-alluxio.html" />
    <link rel="prev" title="HDFS file system support" href="file-system-hdfs.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="file-system-cache.html#object-storage/file-system-cache" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> File system cache </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="file-system-azure.html" class="md-nav__link">Azure Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-s3.html" class="md-nav__link">S3 file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-hdfs.html" class="md-nav__link">HDFS file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> File system cache </label>
    
      <a href="file-system-cache.html#" class="md-nav__link md-nav__link--active">File system cache</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-cache.html#distributed-caching" class="md-nav__link">Distributed caching</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#benefits" class="md-nav__link">Benefits</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#monitoring" class="md-nav__link">Monitoring</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#recommendations" class="md-nav__link">Recommendations</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="metastores.html" class="md-nav__link">Metastores</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-cache.html#distributed-caching" class="md-nav__link">Distributed caching</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#benefits" class="md-nav__link">Benefits</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#monitoring" class="md-nav__link">Monitoring</a>
        </li>
        <li class="md-nav__item"><a href="file-system-cache.html#recommendations" class="md-nav__link">Recommendations</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="file-system-cache">
<h1 id="object-storage-file-system-cache--page-root">File system cache<a class="headerlink" href="file-system-cache.html#object-storage-file-system-cache--page-root" title="Link to this heading">#</a></h1>
<p>Trino accesses files directly on object storage and remote file system storage.
This often involves the transfer of large amounts of data. The files are
retrieved from HDFS, or any other supported object storage, by multiple workers
and processed on these workers. Repeated queries with different parameters, or
even different queries from different users, often access, and therefore
transfer, the same objects.</p>
<p>Trino includes support for caching these files with the help of the open
source <a class="reference external" href="https://github.com/Alluxio/alluxio">Alluxio</a> libraries with catalogs
using the following connectors:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../connector/delta-lake.html"><span class="doc std std-doc">Delta Lake connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/hive.html"><span class="doc std std-doc">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/iceberg.html"><span class="doc std std-doc">Iceberg connector</span></a></p></li>
</ul>
<section id="distributed-caching">
<span id="fs-cache-distributed"></span><h2 id="distributed-caching">Distributed caching<a class="headerlink" href="file-system-cache.html#distributed-caching" title="Link to this heading">#</a></h2>
<p>File system caching is distributed in Trino as part of the mechanism of any other
query processing. Query processing, detailed more in <a class="reference internal" href="../overview/concepts.html"><span class="doc std std-doc">Trino concepts</span></a> is
broken up into different stages, where tasks and splits are processed by
different nodes in the cluster. The lowest level splits retrieve data from the
data source with the help of the connector of the specific catalog. For
file system caching, these splits result in the retrieval of files from object
storage.</p>
<p>Different nodes process splits with data from objects storage randomly over
time, but with preference for using a fixed set of nodes for a given file. If
the preferred nodes are too busy, the split, and hence the caching, takes place
on a non-preferred, less busy node. File system caching keeps copies of the
retrieved files on a local cache storage, separate for each node. Over time the
same files from object storage are cached on any nodes that require the data
file for processing a specific task. Each cache on each node is managed
separately, following the TTL and size configuration, and cached files are
evicted from the cache.</p>
<p>You can limit the number of hosts that are preferred to process these tasks with
<code class="docutils literal notranslate"><span class="pre">fs.cache.preferred-hosts-count</span></code>. Query processing still uses all other nodes as
required for the parallel processing of tasks, and therefore potentially caches
files on more nodes than the preferred hosts only. A low setting, such as the
default 2, can reduce the overall size of the cache because it can reduce how
often the same file is cached on multiple nodes. A higher setting, up to the
number of nodes in the cluster, distributes the workload across more workers by
default, and leads to more resilience against node failures at the expense of
effective cache size.</p>
</section>
<section id="benefits">
<span id="fs-cache-benefits"></span><h2 id="benefits">Benefits<a class="headerlink" href="file-system-cache.html#benefits" title="Link to this heading">#</a></h2>
<p>Enabling caching can result in the following significant benefits:</p>
<p><strong>Reduced load on storage</strong></p>
<p>Every retrieved and cached file avoids repeated retrieval from the storage in
subsequent queries on the same worker. As a result the storage system does not
have to provide the file again and again.</p>
<p>For example, if your query accesses 100MB of files from the storage, the first
time the query runs 100MB are downloaded and cached. Any following query uses
these files. If your users run another 100 queries accessing the same files,
your storage system does not have to provide all data repeatedly. Without
caching it has to provide the same files again and again, resulting in up to
10GB of total files to serve.</p>
<p><strong>Increased query performance</strong></p>
<p>Caching can provide significant performance benefits, by avoiding the repeated
network transfers and instead accessing copies of the files from a local
cache. Performance gains are more significant if the performance of directly
accessing the storage is low compared to accessing the local cache.</p>
<p>For example, if you access storage in a different network, different data
center, or even different cloud-provider region query performance is slow. Adding
caching using fast, local storage has a significant impact and makes your
queries much faster.</p>
<p>On the other hand, if your storage is already running at very high performance
for I/O and network access, and your local cache storage is at similar speeds,
or even slower, performance benefits can be minimal.</p>
<p><strong>Reduced query costs</strong></p>
<p>A result of the reduced load on the storage, mentioned earlier, is significantly
reduced network traffic and access to storage. Network traffic and access, often
in the form of API access, are often a considerable cost factor, specifically
also when hosted in public cloud provider systems.</p>
</section>
<section id="configuration">
<span id="fs-cache-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="file-system-cache.html#configuration" title="Link to this heading">#</a></h2>
<p>Use the properties from the following table in your catalog properties files to
enable and configure caching for the specific catalogs.</p>
<table id="id1">
<caption><span class="caption-text">File system cache configuration properties</span><a class="headerlink" href="file-system-cache.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 75%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.enabled</span></code></p></td>
<td><p>Enable object storage caching. Defaults to no caching with the value <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.directories</span></code></p></td>
<td><p>Required, comma-separated list of absolute paths to directories to use for
caching. All directories must exist on the coordinator and all workers.
Trino must have read and write permissions for files and nested directories.
A valid example with only one directory is <code class="docutils literal notranslate"><span class="pre">/tmp/trino-cache</span></code>.</p>
<p>Directories must be specific for each catalog with caching enabled. When
enabling caching in multiple catalogs, you must use different directories
and set the values for <code class="docutils literal notranslate"><span class="pre">fs.cache.max-sizes</span></code> or
<code class="docutils literal notranslate"><span class="pre">fs.cache.max-disk-usage-percentages</span></code> accordingly.</p>
</td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.max-sizes</span></code></p></td>
<td><p>Comma-separated list of maximum <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data sizes</span></a> for each
caching directory. Order of values must be identical to the directories
list. Configuring either <code class="docutils literal notranslate"><span class="pre">fs.cache.max-sizes</span></code> or
<code class="docutils literal notranslate"><span class="pre">fs.cache.max-disk-usage-percentages</span></code> is required.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.max-disk-usage-percentages</span></code></p></td>
<td><p>Comma-separated list of maximum percentage values of the used disk for each
directory. Each value is an integer between 1 and 100. Order of values must
be identical to the directories list. If multiple directories use the same
disk, ensure that total percentages per drive remains below 100 percent.
Configuring either <code class="docutils literal notranslate"><span class="pre">fs.cache.max-sizes</span></code> or
<code class="docutils literal notranslate"><span class="pre">fs.cache.max-disk-usage-percentages</span></code> is required.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.ttl</span></code></p></td>
<td><p>The maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for objects to remain in the cache
before eviction. Defaults to <code class="docutils literal notranslate"><span class="pre">7d</span></code>. The minimum value of <code class="docutils literal notranslate"><span class="pre">0s</span></code> means that caching
is effectively turned off.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.preferred-hosts-count</span></code></p></td>
<td><p>The number of preferred nodes for caching files. Defaults to 2. Processing
identifies and subsequently prefers using specific nodes. If the preferred
nodes identified for caching a split are unavailable or too busy, then an
available node is chosen at random from the cluster. More information in
<a class="reference internal" href="file-system-cache.html#fs-cache-distributed"><span class="std std-ref">Distributed caching</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.cache.page-size</span></code></p></td>
<td><p>The page <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> used for caching data. Each transfer of files
uses at least this amount of data. Defaults to <code class="docutils literal notranslate"><span class="pre">1MB</span></code>. Values must be between
<code class="docutils literal notranslate"><span class="pre">64kB</span></code> and <code class="docutils literal notranslate"><span class="pre">15MB</span></code>. Larger value potentially result in too much data transfer
smaller values are less efficient since they result in more individual downloads.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="monitoring">
<h2 id="monitoring">Monitoring<a class="headerlink" href="file-system-cache.html#monitoring" title="Link to this heading">#</a></h2>
<p>The cache exposes the
<a class="reference external" href="https://docs.alluxio.io/ee-da/user/stable/en/reference/Metrics-List.html#client-metrics">Alluxio JMX client metrics</a>
under the <code class="docutils literal notranslate"><span class="pre">org.alluxio</span></code> package, and metrics on external reads and cache reads under
<code class="docutils literal notranslate"><span class="pre">io.trino.filesystem.alluxio.AlluxioCacheStats</span></code>.</p>
<p>The cache code uses <a class="reference internal" href="../admin/opentelemetry.html"><span class="doc std std-doc">OpenTelemetry tracing</span></a>.</p>
</section>
<section id="recommendations">
<h2 id="recommendations">Recommendations<a class="headerlink" href="file-system-cache.html#recommendations" title="Link to this heading">#</a></h2>
<p>The speed of the local cache storage is crucial to the performance of the cache.
The most common and cost-efficient approach is to attach high performance SSD
disk or equivalents. Fast cache performance can be also be achieved with a RAM
disk used as in-memory cache.</p>
<p>In all cases, avoid using the root partition and disk of the node. Instead
attach one or more dedicated storage devices for the cache on each node. Storage
should be local, dedicated on each node, and not shared.</p>
<p>Your deployment method for Trino decides how to attach storage and create the
directories for caching. Typically you need to connect a fast storage system,
like an SSD drive, and ensure that is it mounted on the configured path.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="file-system-hdfs.html" title="HDFS file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> HDFS file system support </span>
              </div>
            </a>
          
          
            <a href="file-system-alluxio.html" title="Alluxio file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Alluxio file system support </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>