<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>S3 file system support &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="file-system-s3.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Local file system support" href="file-system-local.html" />
    <link rel="prev" title="Google Cloud Storage file system support" href="file-system-gcs.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="file-system-s3.html#object-storage/file-system-s3" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> S3 file system support </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="file-system-azure.html" class="md-nav__link">Azure Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> S3 file system support </label>
    
      <a href="file-system-s3.html#" class="md-nav__link md-nav__link--active">S3 file system support</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-s3.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#authentication" class="md-nav__link">Authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#security-mapping" class="md-nav__link">Security mapping</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#migration-from-legacy-s3-file-system" class="md-nav__link">Migration from legacy S3 file system</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-hdfs.html" class="md-nav__link">HDFS file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-cache.html" class="md-nav__link">File system cache</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="metastores.html" class="md-nav__link">Metastores</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-s3.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#authentication" class="md-nav__link">Authentication</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#security-mapping" class="md-nav__link">Security mapping</a>
        </li>
        <li class="md-nav__item"><a href="file-system-s3.html#migration-from-legacy-s3-file-system" class="md-nav__link">Migration from legacy S3 file system</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="s3-file-system-support">
<h1 id="object-storage-file-system-s3--page-root">S3 file system support<a class="headerlink" href="file-system-s3.html#object-storage-file-system-s3--page-root" title="Link to this heading">#</a></h1>
<p>Trino includes a native implementation to access <a class="reference external" href="https://aws.amazon.com/s3/">Amazon
S3</a> and compatible storage systems with a catalog
using the Delta Lake, Hive, Hudi, or Iceberg connectors. While Trino is designed
to support S3-compatible storage systems, only AWS S3 and MinIO are tested for
compatibility. For other storage systems, perform your own testing and consult
your vendor for more information.</p>
<p>Enable the native implementation with <code class="docutils literal notranslate"><span class="pre">fs.native-s3.enabled=true</span></code> in your
catalog properties file.</p>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="file-system-s3.html#general-configuration" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure general aspects of S3 file system
support:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.native-s3.enabled</span></code></p></td>
<td><p>Activate the native implementation for S3 storage support. Defaults to
<code class="docutils literal notranslate"><span class="pre">false</span></code>. Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to use S3 and enable all other properties.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.endpoint</span></code></p></td>
<td><p>Required endpoint URL for S3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.region</span></code></p></td>
<td><p>Required region name for S3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.path-style-access</span></code></p></td>
<td><p>Use path-style access for all requests to S3</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.storage-class</span></code></p></td>
<td><p>S3 storage class to use while writing data. Defaults to <code class="docutils literal notranslate"><span class="pre">STANDARD</span></code>. Other allowed
values are: <code class="docutils literal notranslate"><span class="pre">STANDARD_IA</span></code>, <code class="docutils literal notranslate"><span class="pre">INTELLIGENT_TIERING</span></code>, <code class="docutils literal notranslate"><span class="pre">REDUCED_REDUNDANCY</span></code>, <code class="docutils literal notranslate"><span class="pre">ONEZONE_IA</span></code>,
<code class="docutils literal notranslate"><span class="pre">GLACIER</span></code>, <code class="docutils literal notranslate"><span class="pre">DEEP_ARCHIVE</span></code>, <code class="docutils literal notranslate"><span class="pre">OUTPOSTS</span></code>, <code class="docutils literal notranslate"><span class="pre">GLACIER_IR</span></code>, <code class="docutils literal notranslate"><span class="pre">SNOW</span></code>, <code class="docutils literal notranslate"><span class="pre">EXPRESS_ONEZONE</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.signer-type</span></code></p></td>
<td><p>AWS signing protocol to use for authenticating S3 requests. Supported values are:
<code class="docutils literal notranslate"><span class="pre">AwsS3V4Signer</span></code>, <code class="docutils literal notranslate"><span class="pre">Aws4Signer</span></code>, <code class="docutils literal notranslate"><span class="pre">AsyncAws4Signer</span></code>, <code class="docutils literal notranslate"><span class="pre">Aws4UnsignedPayloadSigner</span></code>,
<code class="docutils literal notranslate"><span class="pre">EventStreamAws4Signer</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.exclusive-create</span></code></p></td>
<td><p>Whether conditional write is supported by the S3-compatible storage. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.canned-acl</span></code></p></td>
<td><p><a class="reference external" href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/acl-overview.html#canned-acl">Canned ACL</a>
to use when uploading files to S3. Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, which has the same
effect as <code class="docutils literal notranslate"><span class="pre">PRIVATE</span></code>. If the files are to be uploaded to an S3 bucket owned
by a different AWS user, the canned ACL may be set to one of the following:
<code class="docutils literal notranslate"><span class="pre">PRIVATE</span></code>, <code class="docutils literal notranslate"><span class="pre">PUBLIC_READ</span></code>, <code class="docutils literal notranslate"><span class="pre">PUBLIC_READ_WRITE</span></code>, <code class="docutils literal notranslate"><span class="pre">AUTHENTICATED_READ</span></code>,
<code class="docutils literal notranslate"><span class="pre">BUCKET_OWNER_READ</span></code>, or <code class="docutils literal notranslate"><span class="pre">BUCKET_OWNER_FULL_CONTROL</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.type</span></code></p></td>
<td><p>Set the type of S3 server-side encryption (SSE) to use. Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>
for no encryption. Other valid values are <code class="docutils literal notranslate"><span class="pre">S3</span></code> for encryption by S3 managed
keys, <code class="docutils literal notranslate"><span class="pre">KMS</span></code> for encryption with a key from the AWS Key Management
Service (KMS), and <code class="docutils literal notranslate"><span class="pre">CUSTOMER</span></code> for encryption with a customer-provided key
from <code class="docutils literal notranslate"><span class="pre">s3.sse.customer-key</span></code>. Note that S3 automatically uses SSE so <code class="docutils literal notranslate"><span class="pre">NONE</span></code>
and <code class="docutils literal notranslate"><span class="pre">S3</span></code> are equivalent. S3-compatible systems might behave differently.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.kms-key-id</span></code></p></td>
<td><p>The identifier of a key in KMS to use for SSE.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.customer-key</span></code></p></td>
<td><p>The 256-bit, base64-encoded AES-256 encryption key to encrypt or decrypt
data from S3 when using the SSE-C mode for SSE with <code class="docutils literal notranslate"><span class="pre">s3.sse.type</span></code> set to
<code class="docutils literal notranslate"><span class="pre">CUSTOMER</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.streaming.part-size</span></code></p></td>
<td><p>Part size for S3 streaming upload. Values between <code class="docutils literal notranslate"><span class="pre">5MB</span></code> and <code class="docutils literal notranslate"><span class="pre">256MB</span></code> are
valid. Defaults to <code class="docutils literal notranslate"><span class="pre">32MB</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.requester-pays</span></code></p></td>
<td><p>Switch to activate billing transfer cost to the requester. Defaults to
<code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code></p></td>
<td><p>Maximum number of connections to S3.  Defaults to <code class="docutils literal notranslate"><span class="pre">500</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.connection-ttl</span></code></p></td>
<td><p>Maximum time <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> allowed to reuse connections in
the connection pool before being replaced.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.connection-max-idle-time</span></code></p></td>
<td><p>Maximum time <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> allowed for connections to
remain idle in the connection pool before being closed.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.socket-connect-timeout</span></code></p></td>
<td><p>Maximum time <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> allowed for socket connection
requests to complete before timing out.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.socket-read-timeout</span></code></p></td>
<td><p>Maximum time <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for socket read operations
before timing out.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.tcp-keep-alive</span></code></p></td>
<td><p>Enable TCP keep alive on created connections. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy</span></code></p></td>
<td><p>URL of a HTTP proxy server to use for connecting to S3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.secure</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to enable HTTPS for the proxy server.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.username</span></code></p></td>
<td><p>Proxy username to use if connecting through a proxy server.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.password</span></code></p></td>
<td><p>Proxy password to use if connecting through a proxy server.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.non-proxy-hosts</span></code></p></td>
<td><p>Hosts list to access without going through the proxy server.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.preemptive-basic-auth</span></code></p></td>
<td><p>Whether to attempt to authenticate preemptively against proxy server
when using base authorization, defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.retry-mode</span></code></p></td>
<td><p>Specifies how the AWS SDK attempts retries. Default value is <code class="docutils literal notranslate"><span class="pre">LEGACY</span></code>.
Other allowed values are <code class="docutils literal notranslate"><span class="pre">STANDARD</span></code> and <code class="docutils literal notranslate"><span class="pre">ADAPTIVE</span></code>. The <code class="docutils literal notranslate"><span class="pre">STANDARD</span></code> mode
includes a standard set of errors that are retried. <code class="docutils literal notranslate"><span class="pre">ADAPTIVE</span></code> mode
includes the functionality of <code class="docutils literal notranslate"><span class="pre">STANDARD</span></code> mode with automatic client-side
throttling.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.max-error-retries</span></code></p></td>
<td><p>Specifies maximum number of retries the client will make on errors.
Defaults to <code class="docutils literal notranslate"><span class="pre">10</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.use-web-identity-token-credentials-provider</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to only use the web identity token credentials provider,
instead of the default providers chain. This can be useful when running
Trino on Amazon EKS and using <a class="reference external" href="https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html">IAM roles for service accounts
(IRSA)</a>
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.application-id</span></code></p></td>
<td><p>Specify the application identifier appended to the <code class="docutils literal notranslate"><span class="pre">User-Agent</span></code> header
for all requests sent to S3. Defaults to <code class="docutils literal notranslate"><span class="pre">Trino</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="authentication">
<h2 id="authentication">Authentication<a class="headerlink" href="file-system-s3.html#authentication" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure the authentication to S3 with access
and secret keys, STS, or an IAM role:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.aws-access-key</span></code></p></td>
<td><p>AWS access key to use for authentication.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.aws-secret-key</span></code></p></td>
<td><p>AWS secret key to use for authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.sts.endpoint</span></code></p></td>
<td><p>The endpoint URL of the AWS Security Token Service to use for authenticating
to S3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.sts.region</span></code></p></td>
<td><p>AWS region of the STS service.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.iam-role</span></code></p></td>
<td><p>ARN of an IAM role to assume when connecting to S3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.role-session-name</span></code></p></td>
<td><p>Role session name to use when connecting to S3. Defaults to
<code class="docutils literal notranslate"><span class="pre">trino-filesystem</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.external-id</span></code></p></td>
<td><p>External ID for the IAM role trust policy when connecting to S3.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="security-mapping">
<h2 id="security-mapping">Security mapping<a class="headerlink" href="file-system-s3.html#security-mapping" title="Link to this heading">#</a></h2>
<p>Trino supports flexible security mapping for S3, allowing for separate
credentials or IAM roles for specific users or S3 locations. The IAM role
for a specific query can be selected from a list of allowed roles by providing
it as an <em>extra credential</em>.</p>
<p>Each security mapping entry may specify one or more match criteria.
If multiple criteria are specified, all criteria must match.
The following match criteria are available:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code>: Regular expression to match against username. Example: <code class="docutils literal notranslate"><span class="pre">alice|bob</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code>: Regular expression to match against any of the groups that the user
belongs to. Example: <code class="docutils literal notranslate"><span class="pre">finance|sales</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">prefix</span></code>: S3 URL prefix. You can specify an entire bucket or a path within a
bucket. The URL must start with <code class="docutils literal notranslate"><span class="pre">s3://</span></code> but also matches for <code class="docutils literal notranslate"><span class="pre">s3a</span></code> or <code class="docutils literal notranslate"><span class="pre">s3n</span></code>.
Example: <code class="docutils literal notranslate"><span class="pre">s3://bucket-name/abc/xyz/</span></code></p></li>
</ul>
<p>The security mapping must provide one or more configuration settings:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">accessKey</span></code> and <code class="docutils literal notranslate"><span class="pre">secretKey</span></code>: AWS access key and secret key. This overrides
any globally configured credentials, such as access key or instance credentials.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">iamRole</span></code>: IAM role to use if no user provided role is specified as an
extra credential. This overrides any globally configured IAM role. This role
is allowed to be specified as an extra credential, although specifying it
explicitly has no effect.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">roleSessionName</span></code>: Optional role session name to use with <code class="docutils literal notranslate"><span class="pre">iamRole</span></code>. This can only
be used when <code class="docutils literal notranslate"><span class="pre">iamRole</span></code> is specified. If <code class="docutils literal notranslate"><span class="pre">roleSessionName</span></code> includes the string
<code class="docutils literal notranslate"><span class="pre">${USER}</span></code>, then the <code class="docutils literal notranslate"><span class="pre">${USER}</span></code> portion of the string is replaced with the
current session’s username. If <code class="docutils literal notranslate"><span class="pre">roleSessionName</span></code> is not specified, it defaults
to <code class="docutils literal notranslate"><span class="pre">trino-session</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allowedIamRoles</span></code>: IAM roles that are allowed to be specified as an extra
credential. This is useful because a particular AWS account may have permissions
to use many roles, but a specific user should only be allowed to use a subset
of those roles.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">kmsKeyId</span></code>: ID of KMS-managed key to be used for client-side encryption.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allowedKmsKeyIds</span></code>: KMS-managed key IDs that are allowed to be specified as an extra
credential. If list contains <code class="docutils literal notranslate"><span class="pre">*</span></code>, then any key can be specified via extra credential.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sseCustomerKey</span></code>: The customer provided key (SSE-C) for server-side encryption.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allowedSseCustomerKey</span></code>: The SSE-C keys that are allowed to be specified as an extra
credential. If list contains <code class="docutils literal notranslate"><span class="pre">*</span></code>, then any key can be specified via extra credential.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">endpoint</span></code>: The S3 storage endpoint server. This optional property can be used
to override S3 endpoints on a per-bucket basis.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">region</span></code>: The S3 region to connect to. This optional property can be used
to override S3 regions on a per-bucket basis.</p></li>
</ul>
<p>The security mapping entries are processed in the order listed in the JSON configuration.
Therefore, specific mappings must be specified before less specific mappings.
For example, the mapping list might have URL prefix <code class="docutils literal notranslate"><span class="pre">s3://abc/xyz/</span></code> followed by
<code class="docutils literal notranslate"><span class="pre">s3://abc/</span></code> to allow different configuration for a specific path within a bucket
than for other paths within the bucket. You can specify the default configuration
by not including any match criteria for the last entry in the list.</p>
<p>In addition to the preceding rules, the default mapping can contain the optional
<code class="docutils literal notranslate"><span class="pre">useClusterDefault</span></code> boolean property set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to use the default S3 configuration.
It cannot be used with any other configuration settings.</p>
<p>If no mapping entry matches and no default is configured, access is denied.</p>
<p>The configuration JSON is read from a file via <code class="docutils literal notranslate"><span class="pre">s3.security-mapping.config-file</span></code>
or from an HTTP endpoint via <code class="docutils literal notranslate"><span class="pre">s3.security-mapping.config-uri</span></code>.</p>
<p>Example JSON configuration:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"mappings"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"prefix"</span><span class="p">:</span><span class="w"> </span><span class="s2">"s3://bucket-name/abc/"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/test_path"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob|charlie"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/test_default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allowedIamRoles"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"arn:aws:iam::123456789101:role/test1"</span><span class="p">,</span>
<span class="w">        </span><span class="s2">"arn:aws:iam::123456789101:role/test2"</span><span class="p">,</span>
<span class="w">        </span><span class="s2">"arn:aws:iam::123456789101:role/test3"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"prefix"</span><span class="p">:</span><span class="w"> </span><span class="s2">"s3://special-bucket/"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"accessKey"</span><span class="p">:</span><span class="w"> </span><span class="s2">"AKIAxxxaccess"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"secretKey"</span><span class="p">:</span><span class="w"> </span><span class="s2">"iXbXxxxsecret"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"prefix"</span><span class="p">:</span><span class="w"> </span><span class="s2">"s3://regional-bucket/"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/regional-user"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"endpoint"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://bucket.vpce-1a2b3c4d-5e6f.s3.us-east-1.vpce.amazonaws.com"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"region"</span><span class="p">:</span><span class="w"> </span><span class="s2">"us-east-1"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"prefix"</span><span class="p">:</span><span class="w"> </span><span class="s2">"s3://encrypted-bucket/"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"kmsKeyId"</span><span class="p">:</span><span class="w"> </span><span class="s2">"kmsKey_10"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test.*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/test_users"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"finance"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/finance_users"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"iamRole"</span><span class="p">:</span><span class="w"> </span><span class="s2">"arn:aws:iam::123456789101:role/default"</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<table id="id1">
<caption><span class="caption-text">Security mapping properties</span><a class="headerlink" href="file-system-s3.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.enabled</span></code></p></td>
<td><p>Activate the security mapping feature. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.
Must be set to <code class="docutils literal notranslate"><span class="pre">true</span></code> for all other properties be used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.config-file</span></code></p></td>
<td><p>Path to the JSON configuration file containing security mappings.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.config-uri</span></code></p></td>
<td><p>HTTP endpoint URI containing security mappings.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.json-pointer</span></code></p></td>
<td><p>A JSON pointer (RFC 6901) to mappings inside the JSON retrieved from the
configuration file or HTTP endpoint. The default is the root of the document.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.iam-role-credential-name</span></code></p></td>
<td><p>The name of the <em>extra credential</em> used to provide the IAM role.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.kms-key-id-credential-name</span></code></p></td>
<td><p>The name of the <em>extra credential</em> used to provide the KMS-managed key ID.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.sse-customer-key-credential-name</span></code></p></td>
<td><p>The name of the <em>extra credential</em> used to provide the server-side encryption with customer-provided keys (SSE-C).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.refresh-period</span></code></p></td>
<td><p>How often to refresh the security mapping configuration, specified as a
<a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a>. By default, the configuration is not refreshed.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">s3.security-mapping.colon-replacement</span></code></p></td>
<td><p>The character or characters to be used instead of a colon character
when specifying an IAM role name as an extra credential.
Any instances of this replacement value in the extra credential value
are converted to a colon.
Choose a value not used in any of your IAM ARNs.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="migration-from-legacy-s3-file-system">
<span id="fs-legacy-s3-migration"></span><h2 id="migration-from-legacy-s3-file-system">Migration from legacy S3 file system<a class="headerlink" href="file-system-s3.html#migration-from-legacy-s3-file-system" title="Link to this heading">#</a></h2>
<p>Trino includes legacy Amazon S3 support to use with a catalog using the Delta
Lake, Hive, Hudi, or Iceberg connectors. Upgrading existing deployments to the
current native implementation is recommended. Legacy support is deprecated and
will be removed.</p>
<p>To migrate a catalog to use the native file system implementation for S3, make
the following edits to your catalog configuration:</p>
<ol class="arabic simple">
<li><p>Add the <code class="docutils literal notranslate"><span class="pre">fs.native-s3.enabled=true</span></code> catalog configuration property.</p></li>
<li><p>Refer to the following table to rename your existing legacy catalog
configuration properties to the corresponding native configuration
properties. Supported configuration values are identical unless otherwise
noted.</p></li>
</ol>
<table>
<colgroup>
<col style="width: 26%"/>
<col style="width: 26%"/>
<col style="width: 48%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Legacy property</p></th>
<th class="head"><p>Native property</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.aws-access-key</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.aws-access-key</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.aws-secret-key</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.aws-secret-key</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.iam-role</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.iam-role</span></code></p></td>
<td><p>Also see <code class="docutils literal notranslate"><span class="pre">s3.role-session-name</span></code> in preceding sections
for more role configuration options.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.external-id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.external-id</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.endpoint</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.endpoint</span></code></p></td>
<td><p>Add the <code class="docutils literal notranslate"><span class="pre">https://</span></code> prefix to make the value a correct URL.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.region</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.region</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sse.enabled</span></code></p></td>
<td><p>None</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.type</span></code> set to the default value of <code class="docutils literal notranslate"><span class="pre">NONE</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">hive.s3.sse.enabled=false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sse.type</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.type</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sse.kms-key-id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.sse.kms-key-id</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.upload-acl-type</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.canned-acl</span></code></p></td>
<td><p>See preceding sections for supported values.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.streaming.part-size</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.streaming.part-size</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.host</span></code>, <code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.port</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy</span></code></p></td>
<td><p>Specify the host and port in one URL, for example <code class="docutils literal notranslate"><span class="pre">localhost:8888</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.protocol</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.secure</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> to enable HTTPS.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.non-proxy-hosts</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.non-proxy-hosts</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.username</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.username</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.password</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.password</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.proxy.preemptive-basic-auth</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.http-proxy.preemptive-basic-auth</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sts.endpoint</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.sts.endpoint</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sts.region</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.sts.region</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.max-error-retries</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.max-error-retries</span></code></p></td>
<td><p>Also see <code class="docutils literal notranslate"><span class="pre">s3.retry-mode</span></code> in preceding sections for more retry behavior
configuration options.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.connect-timeout</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.socket-connect-timeout</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.connect-ttl</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.connection-ttl</span></code></p></td>
<td><p>Also see <code class="docutils literal notranslate"><span class="pre">s3.connection-max-idle-time</span></code> in preceding section for more
connection keep-alive options.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.socket-timeout</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.socket-read-timeout</span></code></p></td>
<td><p>Also see <code class="docutils literal notranslate"><span class="pre">s3.tcp-keep-alive</span></code> in preceding sections for more socket
connection keep-alive options.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.max-connections</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.path-style-access</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.path-style-access</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.s3.signer-type</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">s3.signer-type</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<ol class="arabic simple">
<li><p>Remove the following legacy configuration properties if they exist in your
catalog configuration:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.storage-class</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.signer-class</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.staging-directory</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.pin-client-to-current-region</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.ssl.enabled</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.sse.enabled</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.kms-key-id</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.encryption-materials-provider</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.streaming.enabled</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.max-client-retries</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.max-backoff-time</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.max-retry-time</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.multipart.min-file-size</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.multipart.min-part-size</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3-file-system-type</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">hive.s3.user-agent-prefix</span></code></p></li>
</ul>
</li>
</ol>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="file-system-gcs.html" title="Google Cloud Storage file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Google Cloud Storage file system support </span>
              </div>
            </a>
          
          
            <a href="file-system-local.html" title="Local file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Local file system support </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>