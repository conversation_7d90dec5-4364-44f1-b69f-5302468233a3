<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>HDFS file system support &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="file-system-hdfs.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="File system cache" href="file-system-cache.html" />
    <link rel="prev" title="Local file system support" href="file-system-local.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="file-system-hdfs.html#object-storage/file-system-hdfs" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> HDFS file system support </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="file-system-azure.html" class="md-nav__link">Azure Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-s3.html" class="md-nav__link">S3 file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> HDFS file system support </label>
    
      <a href="file-system-hdfs.html#" class="md-nav__link md-nav__link--active">HDFS file system support</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-hdfs.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#hdfs-impersonation" class="md-nav__link">HDFS impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#hdfs-kerberos-authentication" class="md-nav__link">HDFS Kerberos authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#keytab-files" class="md-nav__link">Keytab files</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#security-configuration-examples" class="md-nav__link">Security configuration examples</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#default-none-authentication-without-impersonation" class="md-nav__link">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#none-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication with impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#kerberos-authentication-without-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#kerberos-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-cache.html" class="md-nav__link">File system cache</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="metastores.html" class="md-nav__link">Metastores</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-hdfs.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#hdfs-impersonation" class="md-nav__link">HDFS impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#hdfs-kerberos-authentication" class="md-nav__link">HDFS Kerberos authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#keytab-files" class="md-nav__link">Keytab files</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#security-configuration-examples" class="md-nav__link">Security configuration examples</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-hdfs.html#default-none-authentication-without-impersonation" class="md-nav__link">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#none-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication with impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#kerberos-authentication-without-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="file-system-hdfs.html#kerberos-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="hdfs-file-system-support">
<h1 id="object-storage-file-system-hdfs--page-root">HDFS file system support<a class="headerlink" href="file-system-hdfs.html#object-storage-file-system-hdfs--page-root" title="Link to this heading">#</a></h1>
<p>Trino includes support to access the <a class="reference external" href="https://hadoop.apache.org/">Hadoop Distributed File System
(HDFS)</a> with a catalog using the Delta Lake, Hive,
Hudi, or Iceberg connectors.</p>
<p>Support for HDFS is not enabled by default, but can be activated by setting the
<code class="docutils literal notranslate"><span class="pre">fs.hadoop.enabled</span></code> property to <code class="docutils literal notranslate"><span class="pre">true</span></code> in your catalog configuration file.</p>
<p>Apache Hadoop HDFS 2.x and 3.x are supported.</p>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="file-system-hdfs.html#general-configuration" title="Link to this heading">#</a></h2>
<p>Use the following properties to configure general aspects of HDFS support:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.hadoop.enabled</span></code></p></td>
<td><p>Activate the support for HDFS access. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>. Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to
use HDFS and enable all other properties.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.config.resources</span></code></p></td>
<td><p>An optional, comma-separated list of HDFS configuration files. These files
must exist on the machines running Trino. For basic setups, Trino configures
the HDFS client automatically and does not require any configuration files.
In some cases, such as when using federated HDFS or NameNode high
availability, it is necessary to specify additional HDFS client options to
access your HDFS cluster in the HDFS XML configuration files and reference
them with this parameter:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.config.resources=/etc/hadoop/conf/core-site.xml
</pre></div>
</div>
<p>Only specify additional configuration files if necessary for your setup, and
reduce the configuration files to have the minimum set of required
properties. Additional properties may cause problems.</p>
</td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.fs.new-directory-permissions</span></code></p></td>
<td><p>Controls the permissions set on new directories created for schemas and
tables. Value must either be <code class="docutils literal notranslate"><span class="pre">skip</span></code> or an octal number, with a leading 0. If
set to <code class="docutils literal notranslate"><span class="pre">skip</span></code>, permissions of newly created directories are not set by
Trino. Defaults to <code class="docutils literal notranslate"><span class="pre">0777</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.fs.new-file-inherit-ownership</span></code></p></td>
<td><p>Flag to determine if new files inherit the ownership information from the
directory. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.verify-checksum</span></code></p></td>
<td><p>Flag to determine if file checksums must be verified. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.ipc-ping-interval</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> between IPC pings from Trino to HDFS.
Defaults to <code class="docutils literal notranslate"><span class="pre">10s</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs-timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for access operations on HDFS.
Defaults to <code class="docutils literal notranslate"><span class="pre">60s</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.connect.timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for connection operations to HDFS.
Defaults to <code class="docutils literal notranslate"><span class="pre">500ms</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.connect.max-retries</span></code></p></td>
<td><p>Maximum number of retries for HDFS connection attempts. Defaults to <code class="docutils literal notranslate"><span class="pre">5</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.key-provider.cache-ttl</span></code></p></td>
<td><p>Caching time <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for the key provider. Defaults
to <code class="docutils literal notranslate"><span class="pre">30min</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.domain-socket-path</span></code></p></td>
<td><p>Path to the UNIX domain socket for the DataNode. The path must exist on each
node. For example, <code class="docutils literal notranslate"><span class="pre">/var/lib/hadoop-hdfs/dn_socket</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.socks-proxy</span></code></p></td>
<td><p>URL for a SOCKS proxy to use for accessing HDFS. For example,
<code class="docutils literal notranslate"><span class="pre">hdfs-master:1180</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.wire-encryption.enabled</span></code></p></td>
<td><p>Enable HDFS wire encryption. In a Kerberized Hadoop cluster that uses HDFS
wire encryption, this must be set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to enable Trino to access HDFS.
Note that using wire encryption may impact query execution performance.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.fs.cache.max-size</span></code></p></td>
<td><p>Maximum number of cached file system objects in the HDFS cache. Defaults to
<code class="docutils literal notranslate"><span class="pre">1000</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.dfs.replication</span></code></p></td>
<td><p>Integer value to set the HDFS replication factor. By default, no value is
set.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="security">
<h2 id="security">Security<a class="headerlink" href="file-system-hdfs.html#security" title="Link to this heading">#</a></h2>
<p>HDFS support includes capabilities for user impersonation and Kerberos
authentication. The following properties are available:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.authentication.type</span></code></p></td>
<td><p>Configure the authentication to use no authentication (<code class="docutils literal notranslate"><span class="pre">NONE</span></code>) or Kerberos
authentication (<code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>). Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.impersonation.enabled</span></code></p></td>
<td><p>Enable HDFS end-user impersonation. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>. See details in
<a class="reference internal" href="file-system-hdfs.html#hdfs-security-impersonation"><span class="std std-ref">HDFS impersonation</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.principal</span></code></p></td>
<td><p>The Kerberos principal Trino uses when connecting to HDFS. Example:
<code class="docutils literal notranslate"><span class="pre">trino-hdfs-superuser/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="cabeb8a3a4a5e7b9afb8bcafb8e7a4a5aeaf8a8f928b879a868fe4898587">[email&#160;protected]</a></span></code> or
<code class="docutils literal notranslate"><span class="pre">trino-hdfs-superuser/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="401f080f1314000518010d100c056e030f0d">[email&#160;protected]</a></span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">_HOST</span></code> placeholder can be used in this property value. When connecting
to HDFS, the Hive connector substitutes in the hostname of the <strong>worker</strong>
node Trino is running on. This is useful if each worker node has its own
Kerberos principal.</p>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.keytab</span></code></p></td>
<td><p>The path to the keytab file that contains a key for the principal specified
by <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.principal</span></code>. This file must be readable by the operating
system user running Trino.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.credential-cache.location</span></code></p></td>
<td><p>The location of the credential-cache with the credentials for the principal
to use to access HDFS. Alternative to <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.keytab</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>The default security configuration does not use authentication when connecting
to a Hadoop cluster (<code class="docutils literal notranslate"><span class="pre">hive.hdfs.authentication.type=NONE</span></code>). All queries are
executed as the OS user who runs the Trino process, regardless of which user
submits the query.</p>
<p>Before running any <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span></code> or <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code> statements for Hive
tables in Trino, you must check that the user Trino is using to access HDFS has
access to the Hive warehouse directory. The Hive warehouse directory is
specified by the configuration variable <code class="docutils literal notranslate"><span class="pre">hive.metastore.warehouse.dir</span></code> in
<code class="docutils literal notranslate"><span class="pre">hive-site.xml</span></code>, and the default value is <code class="docutils literal notranslate"><span class="pre">/user/hive/warehouse</span></code>.</p>
<p>For example, if Trino is running as <code class="docutils literal notranslate"><span class="pre">nobody</span></code>, it accesses
HDFS as <code class="docutils literal notranslate"><span class="pre">nobody</span></code>. You can override this username by setting the
<code class="docutils literal notranslate"><span class="pre">HADOOP_USER_NAME</span></code> system property in the Trino <a class="reference internal" href="../installation/deployment.html#jvm-config"><span class="std std-ref">JVM config</span></a>, replacing
<code class="docutils literal notranslate"><span class="pre">hdfs_user</span></code> with the appropriate username:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-DHADOOP_USER_NAME=hdfs_user
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">hive</span></code> user generally works, since Hive is often started with the <code class="docutils literal notranslate"><span class="pre">hive</span></code>
user and this user has access to the Hive warehouse.</p>
<section id="hdfs-impersonation">
<span id="hdfs-security-impersonation"></span><h3 id="hdfs-impersonation">HDFS impersonation<a class="headerlink" href="file-system-hdfs.html#hdfs-impersonation" title="Link to this heading">#</a></h3>
<p>HDFS impersonation is enabled by adding <code class="docutils literal notranslate"><span class="pre">hive.hdfs.impersonation.enabled=true</span></code>
to the catalog properties file. With this configuration HDFS, Trino can
impersonate the end user who is running the query. This can be used with HDFS
permissions and <abbr title="Access Control Lists">ACLs</abbr> to provide additional
security for data. HDFS permissions and ACLs are explained in the <a class="reference external" href="https://hadoop.apache.org/docs/current/hadoop-project-dist/hadoop-hdfs/HdfsPermissionsGuide.html">HDFS
Permissions
Guide</a>.</p>
<p>To use impersonation, the Hadoop cluster must be configured to allow the user or
principal that Trino is running as to impersonate the users who log in to Trino.
Impersonation in Hadoop is configured in the file <code class="file docutils literal notranslate"><span class="pre">core-site.xml</span></code>. A
complete description of the configuration options is available in the <a class="reference external" href="https://hadoop.apache.org/docs/current/hadoop-project-dist/hadoop-common/Superusers.html#Configurations">Hadoop
documentation</a>.</p>
<p>In the case of a user running a query from the <a class="reference internal" href="../client/cli.html"><span class="doc std std-doc">command line
interface</span></a>, the end user is the username associated with the Trino
CLI process or argument to the optional <code class="docutils literal notranslate"><span class="pre">--user</span></code> option.</p>
</section>
<section id="hdfs-kerberos-authentication">
<span id="hdfs-security-kerberos"></span><h3 id="hdfs-kerberos-authentication">HDFS Kerberos authentication<a class="headerlink" href="file-system-hdfs.html#hdfs-kerberos-authentication" title="Link to this heading">#</a></h3>
<p>To use Trino with a Hadoop cluster that uses Kerberos authentication, you must
configure the catalog in the catalog properties file to work with two services
on the Hadoop cluster:</p>
<ul class="simple">
<li><p>The Hive metastore Thrift service, see <a class="reference internal" href="metastores.html#hive-thrift-metastore-authentication"><span class="std std-ref">Thrift metastore authentication</span></a></p></li>
<li><p>The Hadoop Distributed File System (HDFS), see examples in
<a class="reference internal" href="file-system-hdfs.html#hive-security-kerberos"><span class="std std-ref">KERBEROS authentication without impersonation</span></a> or <a class="reference internal" href="file-system-hdfs.html#hive-security-kerberos-impersonation"><span class="std std-ref">KERBEROS authentication with impersonation</span></a></p></li>
</ul>
<p>Both setups require that Kerberos is configured on each Trino node. Access to
the Trino coordinator must be secured, for example using Kerberos or password
authentication, when using Kerberos authentication to Hadoop services. Failure
to secure access to the Trino coordinator could result in unauthorized access to
sensitive data on the Hadoop cluster. Refer to <a class="reference internal" href="../security.html"><span class="doc">Security</span></a> for further
information, and specifically consider configuring <a class="reference internal" href="../security/kerberos.html"><span class="doc std std-doc">Kerberos authentication</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If your <code class="docutils literal notranslate"><span class="pre">krb5.conf</span></code> location is different from <code class="docutils literal notranslate"><span class="pre">/etc/krb5.conf</span></code> you must set it
explicitly using the <code class="docutils literal notranslate"><span class="pre">java.security.krb5.conf</span></code> JVM property in the <code class="docutils literal notranslate"><span class="pre">jvm.config</span></code>
file. For example, <code class="docutils literal notranslate"><span class="pre">-Djava.security.krb5.conf=/example/path/krb5.conf</span></code>.</p>
</div>
<section id="keytab-files">
<span id="hive-security-additional-keytab"></span><h4 id="keytab-files">Keytab files<a class="headerlink" href="file-system-hdfs.html#keytab-files" title="Link to this heading">#</a></h4>
<p>Keytab files are needed for Kerberos authentication and contain encryption keys
that are used to authenticate principals to the Kerberos <abbr title="Key Distribution Center">KDC</abbr>. These encryption keys must be stored securely; you must
take the same precautions to protect them that you take to protect ssh private
keys.</p>
<p>In particular, access to keytab files must be limited to only the accounts
that must use them to authenticate. In practice, this is the user that
the Trino process runs as. The ownership and permissions on keytab files
must be set to prevent other users from reading or modifying the files.</p>
<p>Keytab files must be distributed to every node running Trino, and  must have the
correct permissions on every node after distributing them.</p>
</section>
</section>
</section>
<section id="security-configuration-examples">
<h2 id="security-configuration-examples">Security configuration examples<a class="headerlink" href="file-system-hdfs.html#security-configuration-examples" title="Link to this heading">#</a></h2>
<p>The following sections describe the configuration properties and values needed
for the various authentication configurations with HDFS.</p>
<section id="default-none-authentication-without-impersonation">
<span id="hive-security-simple"></span><h3 id="default-none-authentication-without-impersonation">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation<a class="headerlink" href="file-system-hdfs.html#default-none-authentication-without-impersonation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.hdfs.authentication.type=NONE
</pre></div>
</div>
<p>The default authentication type for HDFS is <code class="docutils literal notranslate"><span class="pre">NONE</span></code>. When the authentication type
is <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, Trino connects to HDFS using Hadoop’s simple authentication
mechanism. Kerberos is not used.</p>
</section>
<section id="none-authentication-with-impersonation">
<span id="hive-security-simple-impersonation"></span><h3 id="none-authentication-with-impersonation"><code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication with impersonation<a class="headerlink" href="file-system-hdfs.html#none-authentication-with-impersonation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.hdfs.authentication.type=NONE
hive.hdfs.impersonation.enabled=true
</pre></div>
</div>
<p>When using <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication with impersonation, Trino impersonates the user
who is running the query when accessing HDFS. The user Trino is running as must
be allowed to impersonate this user, as discussed in the section
<a class="reference internal" href="file-system-hdfs.html#hdfs-security-impersonation"><span class="std std-ref">HDFS impersonation</span></a>. Kerberos is not used.</p>
</section>
<section id="kerberos-authentication-without-impersonation">
<span id="hive-security-kerberos"></span><h3 id="kerberos-authentication-without-impersonation"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication without impersonation<a class="headerlink" href="file-system-hdfs.html#kerberos-authentication-without-impersonation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.hdfs.authentication.type=KERBEROS
<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="80e8e9f6e5aee8e4e6f3aef4f2e9eeefaef0f2e9eee3e9f0e1ecbdf4f2e9eeefc0c5d8c1cdd0ccc5aec3cfcd">[email&#160;protected]</a>
hive.hdfs.trino.keytab=/etc/trino/trino.keytab
</pre></div>
</div>
<p>When the authentication type is <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>, Trino accesses HDFS as the principal
specified by the <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.principal</span></code> property. Trino authenticates this
principal using the keytab specified by the <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.keytab</span></code> keytab.</p>
</section>
<section id="kerberos-authentication-with-impersonation">
<span id="hive-security-kerberos-impersonation"></span><h3 id="kerberos-authentication-with-impersonation"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation<a class="headerlink" href="file-system-hdfs.html#kerberos-authentication-with-impersonation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.hdfs.authentication.type=KERBEROS
hive.hdfs.impersonation.enabled=true
<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="056d6c73602b6d6163762b71776c6b6a2b75776c6b666c7564693871776c6b6a45405d44485549402b464a48">[email&#160;protected]</a>
hive.hdfs.trino.keytab=/etc/trino/trino.keytab
</pre></div>
</div>
<p>When using <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation, Trino impersonates the
user who is running the query when accessing HDFS. The principal specified by
the <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.principal</span></code> property must be allowed to impersonate the
current Trino user, as discussed in the section <a class="reference internal" href="file-system-hdfs.html#hdfs-security-impersonation"><span class="std std-ref">HDFS impersonation</span></a>. Trino
authenticates <code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.principal</span></code> using the keytab specified by
<code class="docutils literal notranslate"><span class="pre">hive.hdfs.trino.keytab</span></code>.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="file-system-local.html" title="Local file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Local file system support </span>
              </div>
            </a>
          
          
            <a href="file-system-cache.html" title="File system cache"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> File system cache </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>