<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Metastores &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="metastores.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Object storage file formats" href="file-formats.html" />
    <link rel="prev" title="Alluxio file system support" href="file-system-alluxio.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="metastores.html#object-storage/metastores" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Metastores </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="file-system-azure.html" class="md-nav__link">Azure Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-gcs.html" class="md-nav__link">Google Cloud Storage file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-s3.html" class="md-nav__link">S3 file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-local.html" class="md-nav__link">Local file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-hdfs.html" class="md-nav__link">HDFS file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-cache.html" class="md-nav__link">File system cache</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-alluxio.html" class="md-nav__link">Alluxio file system support</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Metastores </label>
    
      <a href="metastores.html#" class="md-nav__link md-nav__link--active">Metastores</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="metastores.html#general-metastore-configuration-properties" class="md-nav__link">General metastore configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#thrift-metastore-configuration-properties" class="md-nav__link">Thrift metastore configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-hive-catalog-configuration-properties" class="md-nav__link">Iceberg-specific Hive catalog configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#thrift-metastore-authentication" class="md-nav__link">Thrift metastore authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#default-none-authentication-without-impersonation" class="md-nav__link">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#kerberos-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#aws-glue-catalog-configuration-properties" class="md-nav__link">AWS Glue catalog configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-glue-catalog-configuration-properties" class="md-nav__link">Iceberg-specific Glue catalog configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-metastores" class="md-nav__link">Iceberg-specific metastores</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#rest-catalog" class="md-nav__link">REST catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#jdbc-catalog" class="md-nav__link">JDBC catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#nessie-catalog" class="md-nav__link">Nessie catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#snowflake-catalog" class="md-nav__link">Snowflake catalog</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#access-tables-with-athena-partition-projection-metadata" class="md-nav__link">Access tables with Athena partition projection metadata</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#configure-metastore-for-avro" class="md-nav__link">Configure metastore for Avro</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-formats.html" class="md-nav__link">Object storage file formats</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="metastores.html#general-metastore-configuration-properties" class="md-nav__link">General metastore configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#thrift-metastore-configuration-properties" class="md-nav__link">Thrift metastore configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-hive-catalog-configuration-properties" class="md-nav__link">Iceberg-specific Hive catalog configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#thrift-metastore-authentication" class="md-nav__link">Thrift metastore authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#default-none-authentication-without-impersonation" class="md-nav__link">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#kerberos-authentication-with-impersonation" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#aws-glue-catalog-configuration-properties" class="md-nav__link">AWS Glue catalog configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-glue-catalog-configuration-properties" class="md-nav__link">Iceberg-specific Glue catalog configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#iceberg-specific-metastores" class="md-nav__link">Iceberg-specific metastores</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="metastores.html#rest-catalog" class="md-nav__link">REST catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#jdbc-catalog" class="md-nav__link">JDBC catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#nessie-catalog" class="md-nav__link">Nessie catalog</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#snowflake-catalog" class="md-nav__link">Snowflake catalog</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="metastores.html#access-tables-with-athena-partition-projection-metadata" class="md-nav__link">Access tables with Athena partition projection metadata</a>
        </li>
        <li class="md-nav__item"><a href="metastores.html#configure-metastore-for-avro" class="md-nav__link">Configure metastore for Avro</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="metastores">
<h1 id="object-storage-metastores--page-root">Metastores<a class="headerlink" href="metastores.html#object-storage-metastores--page-root" title="Link to this heading">#</a></h1>
<p>Object storage access is mediated through a <em>metastore</em>. Metastores provide
information on directory structure, file format, and metadata about the stored
data. Object storage connectors support the use of one or more metastores. A
supported metastore is required to use any object storage connector.</p>
<p>Additional configuration is required in order to access tables with Athena
partition projection metadata or implement first class support for Avro tables.
These requirements are discussed later in this topic.</p>
<section id="general-metastore-configuration-properties">
<span id="general-metastore-properties"></span><h2 id="general-metastore-configuration-properties">General metastore configuration properties<a class="headerlink" href="metastores.html#general-metastore-configuration-properties" title="Link to this heading">#</a></h2>
<p>The following table describes general metastore configuration properties, most
of which are used with either metastore.</p>
<p>At a minimum, each Delta Lake, Hive or Hudi object storage catalog file must set
the <code class="docutils literal notranslate"><span class="pre">hive.metastore</span></code> configuration property to define the type of metastore to
use. Iceberg catalogs instead use the <code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type</span></code> configuration
property to define the type of metastore to use.</p>
<p>Additional configuration properties specific to the Thrift and Glue Metastores
are also available. They are discussed later in this topic.</p>
<table id="id1">
<caption><span class="caption-text">General metastore configuration properties</span><a class="headerlink" href="metastores.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 50%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property Name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore</span></code></p></td>
<td><p>The type of Hive metastore to use. Trino currently supports the default Hive
Thrift metastore (<code class="docutils literal notranslate"><span class="pre">thrift</span></code>), and the AWS Glue Catalog (<code class="docutils literal notranslate"><span class="pre">glue</span></code>) as metadata
sources. You must use this for all object storage catalogs except Iceberg.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">thrift</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type</span></code></p></td>
<td><p>The Iceberg table format manages most metadata in metadata files in the
object storage itself. A small amount of metadata, however, still requires
the use of a metastore. In the Iceberg ecosystem, these smaller metastores
are called Iceberg metadata catalogs, or just catalogs. The examples in each
subsection depict the contents of a Trino catalog file that uses the
Iceberg connector to configures different Iceberg metadata catalogs.</p>
<p>You must set this property in all Iceberg catalog property files. Valid
values are <code class="docutils literal notranslate"><span class="pre">hive_metastore</span></code>, <code class="docutils literal notranslate"><span class="pre">glue</span></code>, <code class="docutils literal notranslate"><span class="pre">jdbc</span></code>, <code class="docutils literal notranslate"><span class="pre">rest</span></code>, <code class="docutils literal notranslate"><span class="pre">nessie</span></code>, and
<code class="docutils literal notranslate"><span class="pre">snowflake</span></code>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">hive_metastore</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache.cache-partitions</span></code></p></td>
<td><p>Enable caching for partition metadata. You can disable caching to avoid
inconsistent behavior that results from it.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache.cache-missing</span></code></p></td>
<td><p>Enable caching the fact that a table is missing to prevent future metastore
calls for that table.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache.cache-missing-partitions</span></code></p></td>
<td><p>Enable caching the fact that a partition is missing to prevent future
metastore calls for that partition.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache.cache-missing-stats</span></code></p></td>
<td><p>Enable caching the fact that table statistics for a specific table are
missing to prevent future metastore calls.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> of how long cached metastore data is considered valid.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-stats-cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> of how long cached metastore statistics are considered valid.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5m</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-cache-maximum-size</span></code></p></td>
<td><p>Maximum number of metastore data objects in the Hive metastore cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">20000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-refresh-interval</span></code></p></td>
<td><p>Asynchronously refresh cached metastore data after access if it is older
than this but is not yet expired, allowing subsequent accesses to see fresh
data.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore-refresh-max-threads</span></code></p></td>
<td><p>Maximum threads used to refresh cached metastore data.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.user-metastore-cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> of how long cached metastore statistics, which are user specific
in user impersonation scenarios, are considered valid.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.user-metastore-cache-maximum-size</span></code></p></td>
<td><p>Maximum number of metastore data objects in the Hive metastore cache,
which are user specific in user impersonation scenarios.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.hide-delta-lake-tables</span></code></p></td>
<td><p>Controls whether to hide Delta Lake tables in table listings. Currently
applies only when using the AWS Glue metastore.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="thrift-metastore-configuration-properties">
<span id="hive-thrift-metastore"></span><h2 id="thrift-metastore-configuration-properties">Thrift metastore configuration properties<a class="headerlink" href="metastores.html#thrift-metastore-configuration-properties" title="Link to this heading">#</a></h2>
<p>In order to use a Hive Thrift metastore, you must configure the metastore with
<code class="docutils literal notranslate"><span class="pre">hive.metastore=thrift</span></code> and provide further details with the following
properties:</p>
<table id="id2">
<caption><span class="caption-text">Thrift metastore configuration properties</span><a class="headerlink" href="metastores.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 50%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.uri</span></code></p></td>
<td><p>The URIs of the Hive metastore to connect to using the Thrift protocol.
If a comma-separated list of URIs is provided, the first URI is used by
default, and the rest of the URIs are fallback metastores. This property
is required. Example: <code class="docutils literal notranslate"><span class="pre">thrift://*********:9083</span></code> or
<code class="docutils literal notranslate"><span class="pre">thrift://*********:9083,thrift://*********:9083</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.username</span></code></p></td>
<td><p>The username Trino uses to access the Hive metastore.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.authentication.type</span></code></p></td>
<td><p>Hive metastore authentication type. Possible values are <code class="docutils literal notranslate"><span class="pre">NONE</span></code> or
<code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.connect-timeout</span></code></p></td>
<td><p>Socket connect timeout for metastore client.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.read-timeout</span></code></p></td>
<td><p>Socket read timeout for metastore client.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.impersonation.enabled</span></code></p></td>
<td><p>Enable Hive metastore end user impersonation.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.use-spark-table-statistics-fallback</span></code></p></td>
<td><p>Enable usage of table statistics generated by Apache Spark when Hive table
statistics are not available.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.delegation-token.cache-ttl</span></code></p></td>
<td><p>Time to live delegation token cache for metastore.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1h</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.delegation-token.cache-maximum-size</span></code></p></td>
<td><p>Delegation token cache maximum size.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.ssl.enabled</span></code></p></td>
<td><p>Use SSL when connecting to metastore.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.ssl.key</span></code></p></td>
<td><p>Path to private key and client certification (key store).</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.ssl.key-password</span></code></p></td>
<td><p>Password for the private key.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.ssl.trust-certificate</span></code></p></td>
<td><p>Path to the server certificate chain (trust store). Required when SSL is
enabled.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.ssl.trust-certificate-password</span></code></p></td>
<td><p>Password for the trust store.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.service.principal</span></code></p></td>
<td><p>The Kerberos principal of the Hive metastore service.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code></p></td>
<td><p>The Kerberos principal that Trino uses when connecting to the Hive metastore
service.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.client.keytab</span></code></p></td>
<td><p>Hive metastore client keytab location.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.delete-files-on-drop</span></code></p></td>
<td><p>Actively delete the files for managed tables when performing drop table or
partition operations, for cases when the metastore does not delete the
files.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.assume-canonical-partition-keys</span></code></p></td>
<td><p>Allow the metastore to assume that the values of partition columns can be
converted to string values. This can lead to performance improvements in
queries which apply filters on the partition columns. Partition keys with a
<code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> type do not get canonicalized.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.socks-proxy</span></code></p></td>
<td><p>SOCKS proxy to use for the Thrift Hive metastore.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.max-retries</span></code></p></td>
<td><p>Maximum number of retry attempts for metastore requests.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">9</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.backoff-scale-factor</span></code></p></td>
<td><p>Scale factor for metastore request retry delay.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2.0</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.max-retry-time</span></code></p></td>
<td><p>Total allowed time limit for a metastore request to be retried.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">30s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.min-backoff-delay</span></code></p></td>
<td><p>Minimum delay between metastore request retries.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.client.max-backoff-delay</span></code></p></td>
<td><p>Maximum delay between metastore request retries.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.txn-lock-max-wait</span></code></p></td>
<td><p>Maximum time to wait to acquire hive transaction lock.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10m</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.catalog-name</span></code></p></td>
<td><p>The term “Hive metastore catalog name” refers to the abstraction concept
within Hive, enabling various systems to connect to distinct, independent
catalogs stored in the metastore. By default, the catalog name in Hive
metastore is set to “hive.” When this configuration property is left empty,
the default catalog of the Hive metastore will be accessed.</p></td>
<td></td>
</tr>
</tbody>
</table>
<section id="iceberg-specific-hive-catalog-configuration-properties">
<span id="iceberg-hive-catalog"></span><h3 id="iceberg-specific-hive-catalog-configuration-properties">Iceberg-specific Hive catalog configuration properties<a class="headerlink" href="metastores.html#iceberg-specific-hive-catalog-configuration-properties" title="Link to this heading">#</a></h3>
<p>When using the Hive catalog, the Iceberg connector supports the same
<a class="reference internal" href="metastores.html#hive-thrift-metastore"><span class="std std-ref">general Thrift metastore configuration properties</span></a>
as previously described with the following additional property:</p>
<table id="id3">
<caption><span class="caption-text">Iceberg Hive catalog configuration property</span><a class="headerlink" href="metastores.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 50%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.hive-catalog.locking-enabled</span></code></p></td>
<td><p>Commit to tables using Hive locks.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Setting <code class="docutils literal notranslate"><span class="pre">iceberg.hive-catalog.locking-enabled=false</span></code> will cause the catalog to
commit to tables without using Hive locks. This should only be set to false if all
following conditions are met:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://issues.apache.org/jira/browse/HIVE-26882">HIVE-26882</a> is available on
the Hive metastore server. Requires version 2.3.10, 4.0.0-beta-1 or later.</p></li>
<li><p><a class="reference external" href="https://issues.apache.org/jira/browse/HIVE-28121">HIVE-28121</a> is available on
the Hive metastore server, if it is backed by MySQL or MariaDB. Requires version
2.3.10, 4.1.0, 4.0.1 or later.</p></li>
<li><p>All other catalogs committing to tables that this catalogs commits to are also
on Iceberg 1.3 or later, and disabled Hive locks on commit.</p></li>
</ul>
</div>
</section>
<section id="thrift-metastore-authentication">
<span id="hive-thrift-metastore-authentication"></span><h3 id="thrift-metastore-authentication">Thrift metastore authentication<a class="headerlink" href="metastores.html#thrift-metastore-authentication" title="Link to this heading">#</a></h3>
<p>In a Kerberized Hadoop cluster, Trino connects to the Hive metastore Thrift
service using <abbr title="Simple Authentication and Security Layer">SASL</abbr> and
authenticates using Kerberos. Kerberos authentication for the metastore is
configured in the connector’s properties file using the following optional
properties:</p>
<table id="id4">
<caption><span class="caption-text">Hive metastore Thrift service authentication properties</span><a class="headerlink" href="metastores.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 55%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property value</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.authentication.type</span></code></p></td>
<td><p>Hive metastore authentication type. One of <code class="docutils literal notranslate"><span class="pre">NONE</span></code> or <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>. When using
the default value of <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, Kerberos authentication is disabled, and no
other properties must be configured.</p>
<p>When set to <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> the Hive connector connects to the Hive metastore
Thrift service using SASL and authenticate using Kerberos.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.thrift.impersonation.enabled</span></code></p></td>
<td><p>Enable Hive metastore end user impersonation. See
<a class="reference internal" href="metastores.html#hive-security-metastore-impersonation"><span class="std std-ref">KERBEROS authentication with impersonation</span></a> for more information.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.service.principal</span></code></p></td>
<td><p>The Kerberos principal of the Hive metastore service. The coordinator uses
this to authenticate the Hive metastore.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">_HOST</span></code> placeholder can be used in this property value. When connecting
to the Hive metastore, the Hive connector substitutes in the hostname of the
<strong>metastore</strong> server it is connecting to. This is useful if the metastore
runs on multiple hosts.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">hive/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="c8a0a1beade5bbadbabeadbae5a0a7bbbc888d90898598848de68b8785">[email&#160;protected]</a></span></code> or <code class="docutils literal notranslate"><span class="pre">hive/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="e8b7a0a7bbbca8adb0a9a5b8a4adc6aba7a5">[email&#160;protected]</a></span></code>.</p>
</td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code></p></td>
<td><p>The Kerberos principal that Trino uses when connecting to the Hive metastore
service.</p>
<p>Example: <code class="docutils literal notranslate"><span class="pre">trino/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="1561677c7b7a38667067637067387b7a717055504d54584559503b565a58">[email&#160;protected]</a></span></code> or <code class="docutils literal notranslate"><span class="pre">trino/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="237c6b6c707763667b626e736f660d606c6e">[email&#160;protected]</a></span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">_HOST</span></code> placeholder can be used in this property value. When connecting
to the Hive metastore, the Hive connector substitutes in the hostname of the
<strong>worker</strong> node Trino is running on. This is useful if each worker node has
its own Kerberos principal.</p>
<p>Unless <a class="reference internal" href="metastores.html#hive-security-metastore-impersonation"><span class="std std-ref">KERBEROS authentication with impersonation</span></a> is enabled, the principal
specified by <code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code> must have sufficient
privileges to remove files and directories within the <code class="docutils literal notranslate"><span class="pre">hive/warehouse</span></code>
directory.</p>
<p><strong>Warning:</strong> If the principal does have sufficient permissions, only the
metadata is removed, and the data continues to consume disk space. This
occurs because the Hive metastore is responsible for deleting the internal
table data. When the metastore is configured to use Kerberos authentication,
all the HDFS operations performed by the metastore are impersonated.
Errors deleting data are silently ignored.</p>
</td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.client.keytab</span></code></p></td>
<td><p>The path to the keytab file that contains a key for the principal
specified by <code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code>. This file must be
readable by the operating system user running Trino.</p></td>
<td></td>
</tr>
</tbody>
</table>
<p>The following sections describe the configuration properties and values needed
for the various authentication configurations needed to use the Hive metastore
Thrift service with the Hive connector.</p>
<section id="default-none-authentication-without-impersonation">
<h4 id="default-none-authentication-without-impersonation">Default <code class="docutils literal notranslate"><span class="pre">NONE</span></code> authentication without impersonation<a class="headerlink" href="metastores.html#default-none-authentication-without-impersonation" title="Link to this heading">#</a></h4>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.metastore.authentication.type=NONE
</pre></div>
</div>
<p>The default authentication type for the Hive metastore is <code class="docutils literal notranslate"><span class="pre">NONE</span></code>. When the
authentication type is <code class="docutils literal notranslate"><span class="pre">NONE</span></code>, Trino connects to an unsecured Hive
metastore. Kerberos is not used.</p>
</section>
<section id="kerberos-authentication-with-impersonation">
<span id="hive-security-metastore-impersonation"></span><h4 id="kerberos-authentication-with-impersonation"><code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> authentication with impersonation<a class="headerlink" href="metastores.html#kerberos-authentication-with-impersonation" title="Link to this heading">#</a></h4>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>hive.metastore.authentication.type=KERBEROS
hive.metastore.thrift.impersonation.enabled=true
hive.metastore.service.principal=hive/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="7f1716091a52121a0b1e0c0b100d1a5217100c0b511a071e120f131a511c10123f3a273e322f333a513c3032">[email&#160;protected]</a>
<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="8fe7e6f9eaa1e2eafbeefcfbe0fdeaa1ece3e6eae1fba1fffde6e1ece6ffeee3b2fbfde6e1e0cfcad7cec2dfc3caa1ccc0c2">[email&#160;protected]</a>
hive.metastore.client.keytab=/etc/trino/hive.keytab
</pre></div>
</div>
<p>When the authentication type for the Hive metastore Thrift service is
<code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>, Trino connects as the Kerberos principal specified by the
property <code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code>. Trino authenticates this
principal using the keytab specified by the <code class="docutils literal notranslate"><span class="pre">hive.metastore.client.keytab</span></code>
property, and verifies that the identity of the metastore matches
<code class="docutils literal notranslate"><span class="pre">hive.metastore.service.principal</span></code>.</p>
<p>When using <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code> Metastore authentication with impersonation, the
principal specified by the <code class="docutils literal notranslate"><span class="pre">hive.metastore.client.principal</span></code> property must be
allowed to impersonate the current Trino user, as discussed in the section
<a class="reference internal" href="file-system-hdfs.html#hdfs-security-impersonation"><span class="std std-ref">HDFS impersonation</span></a>.</p>
<p>Keytab files must be distributed to every node in the Trino cluster.</p>
</section>
</section>
</section>
<section id="aws-glue-catalog-configuration-properties">
<span id="hive-glue-metastore"></span><h2 id="aws-glue-catalog-configuration-properties">AWS Glue catalog configuration properties<a class="headerlink" href="metastores.html#aws-glue-catalog-configuration-properties" title="Link to this heading">#</a></h2>
<p>In order to use an AWS Glue catalog, you must configure your catalog file as
follows:</p>
<p><code class="docutils literal notranslate"><span class="pre">hive.metastore=glue</span></code> and provide further details with the following
properties:</p>
<table id="id5">
<caption><span class="caption-text">AWS Glue catalog configuration properties</span><a class="headerlink" href="metastores.html#id5" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 50%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property Name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.region</span></code></p></td>
<td><p>AWS region of the Glue Catalog. This is required when not running in EC2, or
when the catalog is in a different region. Example: <code class="docutils literal notranslate"><span class="pre">us-east-1</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.endpoint-url</span></code></p></td>
<td><p>Glue API endpoint URL (optional). Example:
<code class="docutils literal notranslate"><span class="pre">https://glue.us-east-1.amazonaws.com</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.sts.region</span></code></p></td>
<td><p>AWS region of the STS service to authenticate with. This is required when
running in a GovCloud region. Example: <code class="docutils literal notranslate"><span class="pre">us-gov-east-1</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.sts.endpoint</span></code></p></td>
<td><p>STS endpoint URL to use when authenticating to Glue (optional). Example:
<code class="docutils literal notranslate"><span class="pre">https://sts.us-gov-east-1.amazonaws.com</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.pin-client-to-current-region</span></code></p></td>
<td><p>Pin Glue requests to the same region as the EC2 instance where Trino is
running.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.max-connections</span></code></p></td>
<td><p>Max number of concurrent connections to Glue.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">30</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.max-error-retries</span></code></p></td>
<td><p>Maximum number of error retries for the Glue client.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.default-warehouse-dir</span></code></p></td>
<td><p>Default warehouse directory for schemas created without an explicit
<code class="docutils literal notranslate"><span class="pre">location</span></code> property.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.use-web-identity-token-credentials-provider</span></code></p></td>
<td><p>If you are running Trino on Amazon EKS, and authenticate using a Kubernetes
service account, you can set this property to <code class="docutils literal notranslate"><span class="pre">true</span></code>. Setting to <code class="docutils literal notranslate"><span class="pre">true</span></code> forces
Trino to not try using different credential providers from the default credential
provider chain, and instead directly use credentials from the service account.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.aws-access-key</span></code></p></td>
<td><p>AWS access key to use to connect to the Glue Catalog. If specified along
with <code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.aws-secret-key</span></code>, this parameter takes precedence
over <code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.iam-role</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.aws-secret-key</span></code></p></td>
<td><p>AWS secret key to use to connect to the Glue Catalog. If specified along
with <code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.aws-access-key</span></code>, this parameter takes precedence
over <code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.iam-role</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.catalogid</span></code></p></td>
<td><p>The ID of the Glue Catalog in which the metadata database resides.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.iam-role</span></code></p></td>
<td><p>ARN of an IAM role to assume when connecting to the Glue Catalog.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.external-id</span></code></p></td>
<td><p>External ID for the IAM role trust policy when connecting to the Glue
Catalog.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.partitions-segments</span></code></p></td>
<td><p>Number of segments for partitioned Glue tables.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hive.metastore.glue.skip-archive</span></code></p></td>
<td><p>AWS Glue has the ability to archive older table versions and a user can
roll back the table to any historical version if needed. By default, the
Hive Connector backed by Glue will not skip the archival of older table
versions.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
<section id="iceberg-specific-glue-catalog-configuration-properties">
<span id="iceberg-glue-catalog"></span><h3 id="iceberg-specific-glue-catalog-configuration-properties">Iceberg-specific Glue catalog configuration properties<a class="headerlink" href="metastores.html#iceberg-specific-glue-catalog-configuration-properties" title="Link to this heading">#</a></h3>
<p>When using the Glue catalog, the Iceberg connector supports the same
<a class="reference internal" href="metastores.html#hive-glue-metastore"><span class="std std-ref">general Glue configuration properties</span></a> as previously
described with the following additional property:</p>
<table id="id6">
<caption><span class="caption-text">Iceberg Glue catalog configuration property</span><a class="headerlink" href="metastores.html#id6" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 50%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.glue.cache-table-metadata</span></code></p></td>
<td><p>While updating the table in AWS Glue, store the table metadata with the
purpose of accelerating <code class="docutils literal notranslate"><span class="pre">information_schema.columns</span></code> and
<code class="docutils literal notranslate"><span class="pre">system.metadata.table_comments</span></code> queries.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="iceberg-specific-metastores">
<h2 id="iceberg-specific-metastores">Iceberg-specific metastores<a class="headerlink" href="metastores.html#iceberg-specific-metastores" title="Link to this heading">#</a></h2>
<p>The Iceberg table format manages most metadata in metadata files in the object
storage itself. A small amount of metadata, however, still requires the use of a
metastore. In the Iceberg ecosystem, these smaller metastores are called Iceberg
metadata catalogs, or just catalogs.</p>
<p>You can use a general metastore such as an HMS or AWS Glue, or you can use the
Iceberg-specific REST, Nessie or JDBC metadata catalogs, as discussed in this
section.</p>
<section id="rest-catalog">
<span id="iceberg-rest-catalog"></span><h3 id="rest-catalog">REST catalog<a class="headerlink" href="metastores.html#rest-catalog" title="Link to this heading">#</a></h3>
<p>In order to use the Iceberg REST catalog, configure the catalog type
with <code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type=rest</span></code>, and provide further details with the
following properties:</p>
<table id="id7">
<caption><span class="caption-text">Iceberg REST catalog configuration properties</span><a class="headerlink" href="metastores.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.uri</span></code></p></td>
<td><p>REST server API endpoint URI (required). Example:
<code class="docutils literal notranslate"><span class="pre">http://iceberg-with-rest:8181</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.prefix</span></code></p></td>
<td><p>The prefix for the resource path to use with the REST catalog server (optional).
Example: <code class="docutils literal notranslate"><span class="pre">dev</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.warehouse</span></code></p></td>
<td><p>Warehouse identifier/location for the catalog (optional). Example:
<code class="docutils literal notranslate"><span class="pre">s3://my_bucket/warehouse_location</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.security</span></code></p></td>
<td><p>The type of security to use (default: <code class="docutils literal notranslate"><span class="pre">NONE</span></code>).  <code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code> requires either a
<code class="docutils literal notranslate"><span class="pre">token</span></code> or <code class="docutils literal notranslate"><span class="pre">credential</span></code>. Example: <code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.session</span></code></p></td>
<td><p>Session information included when communicating with the REST Catalog.
Options are <code class="docutils literal notranslate"><span class="pre">NONE</span></code> or <code class="docutils literal notranslate"><span class="pre">USER</span></code> (default: <code class="docutils literal notranslate"><span class="pre">NONE</span></code>).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.session-timeout</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> to keep authentication session in cache. Defaults to <code class="docutils literal notranslate"><span class="pre">1h</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.oauth2.token</span></code></p></td>
<td><p>The bearer token used for interactions with the server. A <code class="docutils literal notranslate"><span class="pre">token</span></code> or
<code class="docutils literal notranslate"><span class="pre">credential</span></code> is required for <code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code> security. Example: <code class="docutils literal notranslate"><span class="pre">AbCdEf123456</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.oauth2.credential</span></code></p></td>
<td><p>The credential to exchange for a token in the OAuth2 client credentials flow
with the server. A <code class="docutils literal notranslate"><span class="pre">token</span></code> or <code class="docutils literal notranslate"><span class="pre">credential</span></code> is required for <code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code>
security. Example: <code class="docutils literal notranslate"><span class="pre">AbCdEf123456</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.oauth2.scope</span></code></p></td>
<td><p>Scope to be used when communicating with the REST Catalog. Applicable only
when using <code class="docutils literal notranslate"><span class="pre">credential</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.oauth2.server-uri</span></code></p></td>
<td><p>The endpoint to retrieve access token from OAuth2 Server.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.oauth2.token-refresh-enabled</span></code></p></td>
<td><p>Controls whether a token should be refreshed if information about its expiration time is available.
Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.vended-credentials-enabled</span></code></p></td>
<td><p>Use credentials provided by the REST backend for file system access.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.nested-namespace-enabled</span></code></p></td>
<td><p>Support querying objects under nested namespace.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.case-insensitive-name-matching</span></code></p></td>
<td><p>Match namespace, table, and view names case insensitively. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.rest-catalog.case-insensitive-name-matching.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which case-insensitive namespace, table,
and view names are cached. Defaults to <code class="docutils literal notranslate"><span class="pre">1m</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>The following example shows a minimal catalog configuration using an Iceberg
REST metadata catalog:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">iceberg</span>
<span class="na">iceberg.catalog.type</span><span class="o">=</span><span class="s">rest</span>
<span class="na">iceberg.rest-catalog.uri</span><span class="o">=</span><span class="s">http://iceberg-with-rest:8181</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">iceberg.security</span></code> must be <code class="docutils literal notranslate"><span class="pre">read_only</span></code> when connecting to Databricks Unity catalog
using an Iceberg REST catalog:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">iceberg</span>
<span class="na">iceberg.catalog.type</span><span class="o">=</span><span class="s">rest</span>
<span class="na">iceberg.rest-catalog.uri</span><span class="o">=</span><span class="s">https://dbc-12345678-9999.cloud.databricks.com/api/2.1/unity-catalog/iceberg</span>
<span class="na">iceberg.security</span><span class="o">=</span><span class="s">read_only</span>
<span class="na">iceberg.rest-catalog.security</span><span class="o">=</span><span class="s">OAUTH2</span>
<span class="na">iceberg.rest-catalog.oauth2.token</span><span class="o">=</span><span class="s">***</span>
</pre></div>
</div>
<p>The REST catalog supports <a class="reference internal" href="../language/sql-support.html#sql-view-management"><span class="std std-ref">view management</span></a>
using the <a class="reference external" href="https://iceberg.apache.org/view-spec/">Iceberg View specification</a>.</p>
<p>The REST catalog does not support <a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">materialized view management</span></a>.</p>
</section>
<section id="jdbc-catalog">
<span id="iceberg-jdbc-catalog"></span><h3 id="jdbc-catalog">JDBC catalog<a class="headerlink" href="metastores.html#jdbc-catalog" title="Link to this heading">#</a></h3>
<p>The Iceberg JDBC catalog is supported for the Iceberg connector.  At a minimum,
<code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.driver-class</span></code>, <code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.connection-url</span></code>,
<code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.default-warehouse-dir</span></code>, and
<code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.catalog-name</span></code> must be configured. When using any
database besides PostgreSQL, a JDBC driver jar file must be placed in the plugin
directory.</p>
<table id="id8">
<caption><span class="caption-text">JDBC catalog configuration properties</span><a class="headerlink" href="metastores.html#id8" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.driver-class</span></code></p></td>
<td><p>JDBC driver class name.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.connection-url</span></code></p></td>
<td><p>The URI to connect to the JDBC server.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.connection-user</span></code></p></td>
<td><p>Username for JDBC client.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.connection-password</span></code></p></td>
<td><p>Password for JDBC client.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.catalog-name</span></code></p></td>
<td><p>Iceberg JDBC metastore catalog name.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.default-warehouse-dir</span></code></p></td>
<td><p>The default warehouse directory to use for JDBC.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.schema-version</span></code></p></td>
<td><p>JDBC catalog schema version.
Valid values are <code class="docutils literal notranslate"><span class="pre">V0</span></code> or <code class="docutils literal notranslate"><span class="pre">V1</span></code>. Defaults to <code class="docutils literal notranslate"><span class="pre">V1</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.jdbc-catalog.retryable-status-codes</span></code></p></td>
<td><p>On connection error to JDBC metastore, retry if
it is one of these JDBC status codes.
Valid value is a comma-separated list of status codes.
Note: JDBC catalog always retries the following status
codes: <code class="docutils literal notranslate"><span class="pre">08000,08003,08006,08007,40001</span></code>. Specify only
additional codes (such as <code class="docutils literal notranslate"><span class="pre">57000,57P03,57P04</span></code> if using
PostgreSQL driver) here.</p></td>
</tr>
</tbody>
</table>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The JDBC catalog may have compatibility issues if Iceberg introduces breaking
changes in the future. Consider the <a class="reference internal" href="metastores.html#iceberg-rest-catalog"><span class="std std-ref">REST catalog</span></a> as an alternative solution.</p>
<p>The JDBC catalog requires the metadata tables to already exist.
Refer to <a class="reference external" href="https://github.com/apache/iceberg/blob/main/core/src/main/java/org/apache/iceberg/jdbc/JdbcUtil.java">Iceberg repository</a>
for creating those tables.</p>
</div>
<p>The following example shows a minimal catalog configuration using an
Iceberg JDBC metadata catalog:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=iceberg
iceberg.catalog.type=jdbc
iceberg.jdbc-catalog.catalog-name=test
iceberg.jdbc-catalog.driver-class=org.postgresql.Driver
iceberg.jdbc-catalog.connection-url=*******************************************
iceberg.jdbc-catalog.connection-user=admin
iceberg.jdbc-catalog.connection-password=test
iceberg.jdbc-catalog.default-warehouse-dir=s3://bucket
</pre></div>
</div>
<p>The JDBC catalog does not support <a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">materialized view management</span></a>.</p>
</section>
<section id="nessie-catalog">
<span id="iceberg-nessie-catalog"></span><h3 id="nessie-catalog">Nessie catalog<a class="headerlink" href="metastores.html#nessie-catalog" title="Link to this heading">#</a></h3>
<p>In order to use a Nessie catalog, configure the catalog type with
<code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type=nessie</span></code> and provide further details with the following
properties:</p>
<table id="id9">
<caption><span class="caption-text">Nessie catalog configuration properties</span><a class="headerlink" href="metastores.html#id9" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.uri</span></code></p></td>
<td><p>Nessie API endpoint URI (required). Example:
<code class="docutils literal notranslate"><span class="pre">https://localhost:19120/api/v2</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.ref</span></code></p></td>
<td><p>The branch/tag to use for Nessie. Defaults to <code class="docutils literal notranslate"><span class="pre">main</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.default-warehouse-dir</span></code></p></td>
<td><p>Default warehouse directory for schemas created without an explicit
<code class="docutils literal notranslate"><span class="pre">location</span></code> property. Example: <code class="docutils literal notranslate"><span class="pre">/tmp</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.read-timeout</span></code></p></td>
<td><p>The read timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for requests to the Nessie
server. Defaults to <code class="docutils literal notranslate"><span class="pre">25s</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.connection-timeout</span></code></p></td>
<td><p>The connection timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for connection
requests to the Nessie server. Defaults to <code class="docutils literal notranslate"><span class="pre">5s</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.enable-compression</span></code></p></td>
<td><p>Configure whether compression should be enabled or not for requests to the
Nessie server. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.authentication.type</span></code></p></td>
<td><p>The authentication type to use. Available value is <code class="docutils literal notranslate"><span class="pre">BEARER</span></code>. Defaults to no
authentication.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.authentication.token</span></code></p></td>
<td><p>The token to use with <code class="docutils literal notranslate"><span class="pre">BEARER</span></code> authentication. Example:
<code class="docutils literal notranslate"><span class="pre">SXVLUXUhIExFQ0tFUiEK</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.client-api-version</span></code></p></td>
<td><p>Optional version of the Client API version to use. By default it is inferred from the <code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.uri</span></code> value.
Valid values are <code class="docutils literal notranslate"><span class="pre">V1</span></code> or <code class="docutils literal notranslate"><span class="pre">V2</span></code>.</p></td>
</tr>
</tbody>
</table>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=iceberg
iceberg.catalog.type=nessie
iceberg.nessie-catalog.uri=https://localhost:19120/api/v2
iceberg.nessie-catalog.default-warehouse-dir=/tmp
</pre></div>
</div>
<p>The Nessie catalog does not support <a class="reference internal" href="../language/sql-support.html#sql-view-management"><span class="std std-ref">view management</span></a> or
<a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">materialized view management</span></a>.</p>
</section>
<section id="snowflake-catalog">
<span id="iceberg-snowflake-catalog"></span><h3 id="snowflake-catalog">Snowflake catalog<a class="headerlink" href="metastores.html#snowflake-catalog" title="Link to this heading">#</a></h3>
<p>In order to use a Snowflake catalog, configure the catalog type with
<code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type=snowflake</span></code> and provide further details with the following
properties:</p>
<table id="id10">
<caption><span class="caption-text">Snowflake catalog configuration properties</span><a class="headerlink" href="metastores.html#id10" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.snowflake-catalog.account-uri</span></code></p></td>
<td><p>Snowflake JDBC account URI (required). Example:
<code class="docutils literal notranslate"><span class="pre">********************************************************</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.snowflake-catalog.user</span></code></p></td>
<td><p>Snowflake user (required).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.snowflake-catalog.password</span></code></p></td>
<td><p>Snowflake password (required).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.snowflake-catalog.database</span></code></p></td>
<td><p>Snowflake database name (required).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.snowflake-catalog.role</span></code></p></td>
<td><p>Snowflake role name</p></td>
</tr>
</tbody>
</table>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=iceberg
iceberg.catalog.type=snowflake
iceberg.snowflake-catalog.account-uri=*********************************************************
iceberg.snowflake-catalog.user=user
iceberg.snowflake-catalog.password=secret
iceberg.snowflake-catalog.database=db
</pre></div>
</div>
<p>When using the Snowflake catalog, data management tasks such as creating tables,
must be performed in Snowflake because using the catalog from external systems
like Trino only supports <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries and other <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operations</span></a>.</p>
<p>Additionally, the <a class="reference external" href="https://docs.snowflake.com/en/sql-reference/sql/create-iceberg-table-snowflake">Snowflake-created Iceberg
tables</a>
do not expose partitioning information, which prevents efficient parallel reads
and therefore can have significant negative performance implications.</p>
<p>The Snowflake catalog does not support <a class="reference internal" href="../language/sql-support.html#sql-view-management"><span class="std std-ref">view management</span></a> or
<a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">materialized view management</span></a>.</p>
<p>Further information is available in the <a class="reference external" href="https://docs.snowflake.com/en/user-guide/tables-iceberg-catalog">Snowflake catalog
documentation</a>.</p>
</section>
</section>
<section id="access-tables-with-athena-partition-projection-metadata">
<span id="partition-projection"></span><h2 id="access-tables-with-athena-partition-projection-metadata">Access tables with Athena partition projection metadata<a class="headerlink" href="metastores.html#access-tables-with-athena-partition-projection-metadata" title="Link to this heading">#</a></h2>
<p><a class="reference external" href="https://docs.aws.amazon.com/athena/latest/ug/partition-projection.html">Partition projection</a>
is a feature of AWS Athena often used to speed up query processing with highly
partitioned tables when using the Hive connector.</p>
<p>Trino supports partition projection table properties stored in the Hive
metastore or Glue catalog, and it reimplements this functionality. Currently,
there is a limitation in comparison to AWS Athena for date projection, as it
only supports intervals of <code class="docutils literal notranslate"><span class="pre">DAYS</span></code>, <code class="docutils literal notranslate"><span class="pre">HOURS</span></code>, <code class="docutils literal notranslate"><span class="pre">MINUTES</span></code>, and <code class="docutils literal notranslate"><span class="pre">SECONDS</span></code>.</p>
<p>If there are any compatibility issues blocking access to a requested table when
partition projection is enabled, set the
<code class="docutils literal notranslate"><span class="pre">partition_projection_ignore</span></code> table property to <code class="docutils literal notranslate"><span class="pre">true</span></code> for a table to bypass
any errors.</p>
<p>Refer to <a class="reference internal" href="../connector/hive.html#hive-table-properties"><span class="std std-ref">Table properties</span></a> and <a class="reference internal" href="../connector/hive.html#hive-column-properties"><span class="std std-ref">Column properties</span></a> for
configuration of partition projection.</p>
</section>
<section id="configure-metastore-for-avro">
<h2 id="configure-metastore-for-avro">Configure metastore for Avro<a class="headerlink" href="metastores.html#configure-metastore-for-avro" title="Link to this heading">#</a></h2>
<p>For catalogs using the Hive connector, you must add the following property
definition to the Hive metastore configuration file <code class="docutils literal notranslate"><span class="pre">hive-site.xml</span></code> and
restart the metastore service to enable first-class support for Avro tables when
using Hive 3.x:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;property&gt;</span>
<span class="w">     </span><span class="cm">&lt;!-- https://community.hortonworks.com/content/supportkb/247055/errorjavalangunsupportedoperationexception-storage.html --&gt;</span>
<span class="w">     </span><span class="nt">&lt;name&gt;</span>metastore.storage.schema.reader.impl<span class="nt">&lt;/name&gt;</span>
<span class="w">     </span><span class="nt">&lt;value&gt;</span>org.apache.hadoop.hive.metastore.SerDeStorageSchemaReader<span class="nt">&lt;/value&gt;</span>
<span class="w"> </span><span class="nt">&lt;/property&gt;</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="file-system-alluxio.html" title="Alluxio file system support"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Alluxio file system support </span>
              </div>
            </a>
          
          
            <a href="file-formats.html" title="Object storage file formats"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Object storage file formats </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>