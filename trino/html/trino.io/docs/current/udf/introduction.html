<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Introduction to UDFs &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="introduction.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FUNCTION" href="function.html" />
    <link rel="prev" title="User-defined functions" href="../udf.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="introduction.html#udf/introduction" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Introduction to UDFs </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Introduction to UDFs </label>
    
      <a href="introduction.html#" class="md-nav__link md-nav__link--active">Introduction to UDFs</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="introduction.html#udf-declaration" class="md-nav__link">UDF declaration</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#inline-user-defined-functions" class="md-nav__link">Inline user-defined functions</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#catalog-user-defined-functions" class="md-nav__link">Catalog user-defined functions</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#sql-environment-configuration-for-udfs" class="md-nav__link">SQL environment configuration for UDFs</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#recommendations" class="md-nav__link">Recommendations</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="function.html" class="md-nav__link">FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL user-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="python.html" class="md-nav__link">Python user-defined functions</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="introduction.html#udf-declaration" class="md-nav__link">UDF declaration</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#inline-user-defined-functions" class="md-nav__link">Inline user-defined functions</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#catalog-user-defined-functions" class="md-nav__link">Catalog user-defined functions</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#sql-environment-configuration-for-udfs" class="md-nav__link">SQL environment configuration for UDFs</a>
        </li>
        <li class="md-nav__item"><a href="introduction.html#recommendations" class="md-nav__link">Recommendations</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="introduction-to-udfs">
<h1 id="udf-introduction--page-root">Introduction to UDFs<a class="headerlink" href="introduction.html#udf-introduction--page-root" title="Link to this heading">#</a></h1>
<p>A user-defined function (UDF) is a custom function authored by a user of Trino
in a client application. UDFs are scalar functions that return a single output
value, similar to <a class="reference internal" href="../functions.html"><span class="doc std std-doc">built-in functions</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Custom functions can alternatively be written in Java and deployed as a
plugin. Details are available in the <a class="reference internal" href="../develop/functions.html"><span class="doc std std-doc">developer guide</span></a>.</p>
</div>
<section id="udf-declaration">
<span id="id1"></span><h2 id="udf-declaration">UDF declaration<a class="headerlink" href="introduction.html#udf-declaration" title="Link to this heading">#</a></h2>
<p>Declare the UDF with the SQL <a class="reference internal" href="function.html"><span class="doc std std-doc">FUNCTION</span></a> keyword and the supported
statements for <a class="reference internal" href="sql.html"><span class="doc std std-doc">SQL user-defined functions</span></a> or <a class="reference internal" href="python.html"><span class="doc std std-doc">Python user-defined functions</span></a>.</p>
<p>A UDF can be declared as an <a class="reference internal" href="introduction.html#udf-inline"><span class="std std-ref">inline UDF</span></a> to be used in the current
query, or declared as a <a class="reference internal" href="introduction.html#udf-catalog"><span class="std std-ref">catalog UDF</span></a> to be used in any future
query.</p>
</section>
<section id="inline-user-defined-functions">
<span id="udf-inline"></span><h2 id="inline-user-defined-functions">Inline user-defined functions<a class="headerlink" href="introduction.html#inline-user-defined-functions" title="Link to this heading">#</a></h2>
<p>An inline user-defined function (inline UDF) declares and uses the UDF within a
query processing context. The UDF is declared in a <code class="docutils literal notranslate"><span class="pre">WITH</span></code> block before the
query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="w">  </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">doubleup</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="w">    </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">integer</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">doubleup</span><span class="p">(</span><span class="mi">21</span><span class="p">);</span>
<span class="c1">-- 42</span>
</pre></div>
</div>
<p>Inline UDF names must follow SQL identifier naming conventions, and cannot
contain <code class="docutils literal notranslate"><span class="pre">.</span></code> characters.</p>
<p>The UDF declaration is only valid within the context of the query. A separate
later invocation of the UDF is not possible. If this is desired, use a <a class="reference internal" href="introduction.html#udf-catalog"><span class="std std-ref">catalog
UDF</span></a>.</p>
<p>Multiple inline UDF declarations are comma-separated, and can include UDFs
calling each other, as long as a called UDF is declared before the first
invocation.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="w">  </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">doubleup</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="w">    </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">integer</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">doubleupplusone</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="w">    </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">integer</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">doubleup</span><span class="p">(</span><span class="n">x</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">doubleupplusone</span><span class="p">(</span><span class="mi">21</span><span class="p">);</span>
<span class="c1">-- 43</span>
</pre></div>
</div>
<p>Note that inline UDFs can mask and override the meaning of a built-in function:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="w">  </span><span class="k">FUNCTION</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="w">    </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">integer</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="o">-</span><span class="mi">10</span><span class="p">);</span><span class="w"> </span><span class="c1">-- -20, not 10!</span>
</pre></div>
</div>
</section>
<section id="catalog-user-defined-functions">
<span id="udf-catalog"></span><h2 id="catalog-user-defined-functions">Catalog user-defined functions<a class="headerlink" href="introduction.html#catalog-user-defined-functions" title="Link to this heading">#</a></h2>
<p>You can store a UDF in the context of a catalog, if the connector used in the
catalog supports UDF storage. The following connectors support catalog UDF
storage:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../connector/hive.html"><span class="doc std std-doc">Hive connector</span></a></p></li>
<li><p><a class="reference internal" href="../connector/memory.html"><span class="doc std std-doc">Memory connector</span></a></p></li>
</ul>
<p>In this scenario, the following commands can be used:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-function.html"><span class="doc std std-doc">CREATE FUNCTION</span></a> to create and store a UDF.</p></li>
<li><p><a class="reference internal" href="../sql/drop-function.html"><span class="doc std std-doc">DROP FUNCTION</span></a> to remove a UDF.</p></li>
<li><p><a class="reference internal" href="../sql/show-functions.html"><span class="doc std std-doc">SHOW FUNCTIONS</span></a> to display a list of UDFs in a catalog.</p></li>
</ul>
<p>Catalog UDFs must use a name that combines the catalog name and schema name with
the UDF name, such as <code class="docutils literal notranslate"><span class="pre">example.default.power</span></code> for the <code class="docutils literal notranslate"><span class="pre">power</span></code> UDF in the
<code class="docutils literal notranslate"><span class="pre">default</span></code> schema of the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog.</p>
<p>Invocation must use the fully qualified name, such as <code class="docutils literal notranslate"><span class="pre">example.default.power</span></code>.</p>
</section>
<section id="sql-environment-configuration-for-udfs">
<span id="udf-sql-environment"></span><h2 id="sql-environment-configuration-for-udfs">SQL environment configuration for UDFs<a class="headerlink" href="introduction.html#sql-environment-configuration-for-udfs" title="Link to this heading">#</a></h2>
<p>Configuration of the <code class="docutils literal notranslate"><span class="pre">sql.default-function-catalog</span></code> and
<code class="docutils literal notranslate"><span class="pre">sql.default-function-schema</span></code> <a class="reference internal" href="../admin/properties-sql-environment.html"><span class="doc std std-doc">SQL environment properties</span></a> allows you
to set the default storage for UDFs. The catalog and schema must be added to the
<code class="docutils literal notranslate"><span class="pre">sql.path</span></code> as well. This enables users to call UDFs and perform all
<a class="reference internal" href="../language/sql-support.html#udf-management"><span class="std std-ref">User-defined function management</span></a> without specifying the full path to the UDF.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Use the <a class="reference internal" href="../connector/memory.html"><span class="doc std std-doc">Memory connector</span></a> in a catalog for simple storing and
testing of your UDFs.</p>
</div>
</section>
<section id="recommendations">
<h2 id="recommendations">Recommendations<a class="headerlink" href="introduction.html#recommendations" title="Link to this heading">#</a></h2>
<p>Processing UDFs can potentially be resource intensive on the cluster in
terms of memory and processing. Take the following considerations into account
when writing and running UDFs:</p>
<ul class="simple">
<li><p>Some checks for the runtime behavior of queries, and therefore UDF processing,
are in place. For example, if a query takes longer to process than a hardcoded
threshold, processing is automatically terminated.</p></li>
<li><p>Avoid creation of arrays in a looping construct. Each iteration creates a
separate new array with all items and copies the data for each modification,
leaving the prior array in memory for automated clean up later. Use a <a class="reference internal" href="../functions/lambda.html"><span class="doc std std-doc">lambda
expression</span></a> instead of the loop.</p></li>
<li><p>Avoid concatenating strings in a looping construct. Each iteration creates a
separate new string and copying the old string for each modification, leaving
the prior string in memory for automated clean up later. Use a <a class="reference internal" href="../functions/lambda.html"><span class="doc std std-doc">lambda
expression</span></a> instead of the loop.</p></li>
<li><p>Most UDFs should declare the <code class="docutils literal notranslate"><span class="pre">RETURNS</span> <span class="pre">NULL</span> <span class="pre">ON</span> <span class="pre">NULL</span> <span class="pre">INPUT</span></code> characteristics
unless the code has some special handling for null values. You must declare
this explicitly since <code class="docutils literal notranslate"><span class="pre">CALLED</span> <span class="pre">ON</span> <span class="pre">NULL</span> <span class="pre">INPUT</span></code> is the default characteristic.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../udf.html" title="User-defined functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> User-defined functions </span>
              </div>
            </a>
          
          
            <a href="function.html" title="FUNCTION"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> FUNCTION </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>