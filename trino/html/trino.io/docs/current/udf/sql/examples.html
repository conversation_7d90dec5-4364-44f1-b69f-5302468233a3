<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Example SQL UDFs &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/trino.css@v=b5fc78e7.css" />
    <script src="../../_static/jquery.js@v=5d32c60e"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../../_static/doctools.js@v=9a2dae69"></script>
    <script src="../../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="examples.html" />
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="BEGIN" href="begin.html" />
    <link rel="prev" title="SQL user-defined functions" href="../sql.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="examples.html#udf/sql/examples" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Example SQL UDFs </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../../versions.json",
        target_loc = "../../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../udf.html" class="md-nav__link">User-defined functions</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="../introduction.html" class="md-nav__link">Introduction to UDFs</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../function.html" class="md-nav__link">FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL user-defined functions</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Example SQL UDFs </label>
    
      <a href="examples.html#" class="md-nav__link md-nav__link--active">Example SQL UDFs</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="examples.html#inline-and-catalog-udfs" class="md-nav__link">Inline and catalog UDFs</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#declaration-examples" class="md-nav__link">Declaration examples</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#conditional-flows" class="md-nav__link">Conditional flows</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#fibonacci-example" class="md-nav__link">Fibonacci example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#labels-and-loops" class="md-nav__link">Labels and loops</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#sql-udfs-and-built-in-functions" class="md-nav__link">SQL UDFs and built-in functions</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#optional-parameter-example" class="md-nav__link">Optional parameter example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#date-string-parsing-example" class="md-nav__link">Date string parsing example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#human-readable-days" class="md-nav__link">Human-readable days</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#truncating-long-strings" class="md-nav__link">Truncating long strings</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#formatting-bytes" class="md-nav__link">Formatting bytes</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#charts" class="md-nav__link">Charts</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#top-n" class="md-nav__link">Top-N</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="begin.html" class="md-nav__link">BEGIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="case.html" class="md-nav__link">CASE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="declare.html" class="md-nav__link">DECLARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="if.html" class="md-nav__link">IF</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iterate.html" class="md-nav__link">ITERATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="leave.html" class="md-nav__link">LEAVE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loop.html" class="md-nav__link">LOOP</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="repeat.html" class="md-nav__link">REPEAT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="return.html" class="md-nav__link">RETURN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set.html" class="md-nav__link">SET</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="while.html" class="md-nav__link">WHILE</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../python.html" class="md-nav__link">Python user-defined functions</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="examples.html#inline-and-catalog-udfs" class="md-nav__link">Inline and catalog UDFs</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#declaration-examples" class="md-nav__link">Declaration examples</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#conditional-flows" class="md-nav__link">Conditional flows</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#fibonacci-example" class="md-nav__link">Fibonacci example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#labels-and-loops" class="md-nav__link">Labels and loops</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#sql-udfs-and-built-in-functions" class="md-nav__link">SQL UDFs and built-in functions</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#optional-parameter-example" class="md-nav__link">Optional parameter example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#date-string-parsing-example" class="md-nav__link">Date string parsing example</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#human-readable-days" class="md-nav__link">Human-readable days</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#truncating-long-strings" class="md-nav__link">Truncating long strings</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#formatting-bytes" class="md-nav__link">Formatting bytes</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#charts" class="md-nav__link">Charts</a>
        </li>
        <li class="md-nav__item"><a href="examples.html#top-n" class="md-nav__link">Top-N</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="example-sql-udfs">
<h1 id="udf-sql-examples--page-root">Example SQL UDFs<a class="headerlink" href="examples.html#udf-sql-examples--page-root" title="Link to this heading">#</a></h1>
<p>After learning about <a class="reference internal" href="../sql.html"><span class="doc std std-doc">SQL user-defined functions</span></a>, the following sections show numerous examples
of valid SQL UDFs. The UDFs are suitable as <a class="reference internal" href="../introduction.html#udf-inline"><span class="std std-ref">Inline user-defined functions</span></a> or <a class="reference internal" href="../introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>,
after adjusting the name and the example invocations.</p>
<p>The examples combine numerous supported statements. Refer to the specific
statement documentation for further details:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../function.html"><span class="doc std std-doc">FUNCTION</span></a> for general UDF declaration</p></li>
<li><p><a class="reference internal" href="begin.html"><span class="doc std std-doc">BEGIN</span></a> and <a class="reference internal" href="declare.html"><span class="doc std std-doc">DECLARE</span></a> for SQL UDF blocks</p></li>
<li><p><a class="reference internal" href="set.html"><span class="doc std std-doc">SET</span></a> for assigning values to variables</p></li>
<li><p><a class="reference internal" href="return.html"><span class="doc std std-doc">RETURN</span></a> for returning results</p></li>
<li><p><a class="reference internal" href="case.html"><span class="doc std std-doc">CASE</span></a> and <a class="reference internal" href="if.html"><span class="doc std std-doc">IF</span></a> for conditional flows</p></li>
<li><p><a class="reference internal" href="loop.html"><span class="doc std std-doc">LOOP</span></a>, <a class="reference internal" href="repeat.html"><span class="doc std std-doc">REPEAT</span></a>, and <a class="reference internal" href="while.html"><span class="doc std std-doc">WHILE</span></a> for looping constructs</p></li>
<li><p><a class="reference internal" href="iterate.html"><span class="doc std std-doc">ITERATE</span></a> and <a class="reference internal" href="leave.html"><span class="doc std std-doc">LEAVE</span></a> for flow control</p></li>
</ul>
<section id="inline-and-catalog-udfs">
<h2 id="inline-and-catalog-udfs">Inline and catalog UDFs<a class="headerlink" href="examples.html#inline-and-catalog-udfs" title="Link to this heading">#</a></h2>
<p>The following section shows the differences in usage with inline and catalog
UDFs with a simple SQL UDF example. The same pattern applies to all other
following sections.</p>
<p>A very simple SQL UDF that returns a static value without requiring any input:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">answer</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">BIGINT</span>
<span class="k">RETURN</span><span class="w"> </span><span class="mi">42</span>
</pre></div>
</div>
<p>A full example of this UDF as inline UDF and usage in a string concatenation
with a cast:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="w">  </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">answer</span><span class="p">()</span>
<span class="w">  </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">BIGINT</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="mi">42</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'The answer is '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">answer</span><span class="p">()</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">);</span>
<span class="c1">-- The answer is 42</span>
</pre></div>
</div>
<p>Provided the catalog <code class="docutils literal notranslate"><span class="pre">example</span></code> supports UDF storage in the <code class="docutils literal notranslate"><span class="pre">default</span></code> schema, you
can use the following:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">answer</span><span class="p">()</span>
<span class="w">  </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">BIGINT</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="mi">42</span><span class="p">;</span>
</pre></div>
</div>
<p>With the UDF stored in the catalog, you can run the UDF multiple times without
repeated definition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">answer</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span><span class="w"> </span><span class="c1">-- 43</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'The answer is '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">answer</span><span class="p">()</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">);</span><span class="w"> </span><span class="c1">-- The answer is 42</span>
</pre></div>
</div>
<p>Alternatively, you can configure the SQL PATH in the <a class="reference internal" href="../../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a> to a
catalog and schema that support UDF storage:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">sql.default-function-catalog</span><span class="o">=</span><span class="s">example</span>
<span class="na">sql.default-function-schema</span><span class="o">=</span><span class="s">default</span>
<span class="na">sql.path</span><span class="o">=</span><span class="s">example.default</span>
</pre></div>
</div>
<p>Now you can manage UDFs without the full path:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">answer</span><span class="p">()</span>
<span class="w">  </span><span class="k">RETURNS</span><span class="w"> </span><span class="nb">BIGINT</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="mi">42</span><span class="p">;</span>
</pre></div>
</div>
<p>UDF invocation works without the full path:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">answer</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span><span class="w"> </span><span class="c1">-- 47</span>
</pre></div>
</div>
</section>
<section id="declaration-examples">
<h2 id="declaration-examples">Declaration examples<a class="headerlink" href="examples.html#declaration-examples" title="Link to this heading">#</a></h2>
<p>The result of calling the UDF <code class="docutils literal notranslate"><span class="pre">answer()</span></code> is always identical, so you can
declare it as deterministic, and add some other information:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">answer</span><span class="p">()</span>
<span class="k">LANGUAGE</span><span class="w"> </span><span class="k">SQL</span>
<span class="k">DETERMINISTIC</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">BIGINT</span>
<span class="k">COMMENT</span><span class="w"> </span><span class="s1">'Provide the answer to the question about life, the universe, and everything.'</span>
<span class="k">RETURN</span><span class="w"> </span><span class="mi">42</span>
</pre></div>
</div>
<p>The comment and other information about the UDF is visible in the output of
<a class="reference internal" href="../../sql/show-functions.html"><span class="doc std std-doc">SHOW FUNCTIONS</span></a>.</p>
<p>A simple UDF that returns a greeting back to the input string <code class="docutils literal notranslate"><span class="pre">fullname</span></code>
concatenating two strings and the input value:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">hello</span><span class="p">(</span><span class="n">fullname</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">RETURN</span><span class="w"> </span><span class="s1">'Hello, '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">fullname</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">'!'</span>
</pre></div>
</div>
<p>Following is an example invocation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">hello</span><span class="p">(</span><span class="s1">'Jane Doe'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- Hello, Jane Doe!</span>
</pre></div>
</div>
<p>A first example UDF, that uses multiple statements in a <code class="docutils literal notranslate"><span class="pre">BEGIN</span></code> block. It
calculates the result of a multiplication of the input integer with <code class="docutils literal notranslate"><span class="pre">99</span></code>. The
<code class="docutils literal notranslate"><span class="pre">bigint</span></code> data type is used for all variables and values. The value of integer
<code class="docutils literal notranslate"><span class="pre">99</span></code> is cast to <code class="docutils literal notranslate"><span class="pre">bigint</span></code> in the default value assignment for the variable <code class="docutils literal notranslate"><span class="pre">x</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">times_ninety_nine</span><span class="p">(</span><span class="n">a</span><span class="w"> </span><span class="nb">bigint</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">bigint</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="nb">bigint</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="mi">99</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">bigint</span><span class="p">);</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">a</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>Following is an example invocation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">times_ninety_nine</span><span class="p">(</span><span class="k">CAST</span><span class="p">(</span><span class="mi">2</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">bigint</span><span class="p">));</span><span class="w"> </span><span class="c1">-- 198</span>
</pre></div>
</div>
</section>
<section id="conditional-flows">
<h2 id="conditional-flows">Conditional flows<a class="headerlink" href="examples.html#conditional-flows" title="Link to this heading">#</a></h2>
<p>A first example of conditional flow control in a SQL UDF using the <code class="docutils literal notranslate"><span class="pre">CASE</span></code>
statement. The simple <code class="docutils literal notranslate"><span class="pre">bigint</span></code> input value is compared to a number of values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="n">a</span><span class="w"> </span><span class="nb">bigint</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">varchar</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">CASE</span><span class="w"> </span><span class="n">a</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'zero'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'one'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'ten'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">20</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'twenty'</span><span class="p">;</span>
<span class="w">    </span><span class="k">ELSE</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'other'</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="k">CASE</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>Following are a couple of example invocations with result and explanation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- zero</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- one</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- other (from else clause)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">10</span><span class="p">);</span><span class="w"> </span><span class="c1">-- ten</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">11</span><span class="p">);</span><span class="w"> </span><span class="c1">-- other (from else clause)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">20</span><span class="p">);</span><span class="w"> </span><span class="c1">-- twenty</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="mi">100</span><span class="p">);</span><span class="w"> </span><span class="c1">-- other (from else clause)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="k">null</span><span class="p">);</span><span class="w"> </span><span class="c1">-- null .. but really??</span>
</pre></div>
</div>
<p>A second example of a SQL UDF with a <code class="docutils literal notranslate"><span class="pre">CASE</span></code> statement, this time with two
parameters, showcasing the importance of the order of the conditions:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="n">a</span><span class="w"> </span><span class="nb">bigint</span><span class="p">,</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="nb">bigint</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">varchar</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">CASE</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'zero'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'one'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="w"> </span><span class="s1">'10.0'</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'ten'</span><span class="p">;</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">20</span><span class="p">.</span><span class="mi">0</span><span class="n">E0</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'twenty'</span><span class="p">;</span>
<span class="w">    </span><span class="k">ELSE</span><span class="w"> </span><span class="k">RETURN</span><span class="w"> </span><span class="s1">'other'</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="k">CASE</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>Following are a couple of example invocations with result and explanation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- zero</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- one</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- zero (not one since the second check is never reached)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- one (not ten since the third check is never reached)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- ten</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="mi">20</span><span class="p">);</span><span class="w"> </span><span class="c1">-- ten (not twenty)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">20</span><span class="p">);</span><span class="w"> </span><span class="c1">-- zero (not twenty)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">20</span><span class="p">);</span><span class="w"> </span><span class="c1">-- twenty</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">search_case</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">21</span><span class="p">);</span><span class="w"> </span><span class="c1">-- other</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">simple_case</span><span class="p">(</span><span class="k">null</span><span class="p">,</span><span class="k">null</span><span class="p">);</span><span class="w"> </span><span class="c1">-- null .. but really??</span>
</pre></div>
</div>
</section>
<section id="fibonacci-example">
<h2 id="fibonacci-example">Fibonacci example<a class="headerlink" href="examples.html#fibonacci-example" title="Link to this heading">#</a></h2>
<p>This SQL UDF calculates the <code class="docutils literal notranslate"><span class="pre">n</span></code>-th value in the Fibonacci series, in which each
number is the sum of the two preceding ones. The two initial values are set to
<code class="docutils literal notranslate"><span class="pre">1</span></code> as the defaults for <code class="docutils literal notranslate"><span class="pre">a</span></code> and <code class="docutils literal notranslate"><span class="pre">b</span></code>. The UDF uses an <code class="docutils literal notranslate"><span class="pre">IF</span></code> statement condition to
return <code class="docutils literal notranslate"><span class="pre">1</span></code> for all input values of <code class="docutils literal notranslate"><span class="pre">2</span></code> or less. The <code class="docutils literal notranslate"><span class="pre">WHILE</span></code> block then starts to
calculate each number in the series, starting with <code class="docutils literal notranslate"><span class="pre">a=1</span></code> and <code class="docutils literal notranslate"><span class="pre">b=1</span></code> and iterates
until it reaches the <code class="docutils literal notranslate"><span class="pre">n</span></code>-th position. In each iteration it sets <code class="docutils literal notranslate"><span class="pre">a</span></code> and <code class="docutils literal notranslate"><span class="pre">b</span></code> for
the preceding to values, so it can calculate the sum, and finally return it.
Note that processing the UDF takes longer and longer with higher <code class="docutils literal notranslate"><span class="pre">n</span></code> values, and
the result is deterministic:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="n">n</span><span class="w"> </span><span class="nb">bigint</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">bigint</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">a</span><span class="p">,</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="nb">bigint</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="k">c</span><span class="w"> </span><span class="nb">bigint</span><span class="p">;</span>
<span class="w">  </span><span class="k">IF</span><span class="w"> </span><span class="n">n</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">  </span><span class="n">WHILE</span><span class="w"> </span><span class="n">n</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">DO</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">n</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">n</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="k">c</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">b</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">b</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">c</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="n">WHILE</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="k">c</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>Following are a couple of example invocations with result and explanation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">3</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 3</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 5</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">6</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 8</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">7</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 13</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">fib</span><span class="p">(</span><span class="mi">8</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 21</span>
</pre></div>
</div>
</section>
<section id="labels-and-loops">
<h2 id="labels-and-loops">Labels and loops<a class="headerlink" href="examples.html#labels-and-loops" title="Link to this heading">#</a></h2>
<p>This SQL UDF uses the <code class="docutils literal notranslate"><span class="pre">top</span></code> label to name the <code class="docutils literal notranslate"><span class="pre">WHILE</span></code> block, and then controls
the flow with conditional statements, <code class="docutils literal notranslate"><span class="pre">ITERATE</span></code>, and <code class="docutils literal notranslate"><span class="pre">LEAVE</span></code>. For the values of
<code class="docutils literal notranslate"><span class="pre">a=1</span></code> and <code class="docutils literal notranslate"><span class="pre">a=2</span></code> in the first two iterations of the loop the <code class="docutils literal notranslate"><span class="pre">ITERATE</span></code> call moves
the flow up to <code class="docutils literal notranslate"><span class="pre">top</span></code> before <code class="docutils literal notranslate"><span class="pre">b</span></code> is ever increased. Then <code class="docutils literal notranslate"><span class="pre">b</span></code> is increased for the
values <code class="docutils literal notranslate"><span class="pre">a=3</span></code>, <code class="docutils literal notranslate"><span class="pre">a=4</span></code>, <code class="docutils literal notranslate"><span class="pre">a=5</span></code>, <code class="docutils literal notranslate"><span class="pre">a=6</span></code>, and <code class="docutils literal notranslate"><span class="pre">a=7</span></code>, resulting in <code class="docutils literal notranslate"><span class="pre">b=5</span></code>. The <code class="docutils literal notranslate"><span class="pre">LEAVE</span></code>
call then causes the exit of the block before a is increased further to <code class="docutils literal notranslate"><span class="pre">10</span></code> and
therefore the result of the UDF is <code class="docutils literal notranslate"><span class="pre">5</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">labels</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">bigint</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">a</span><span class="p">,</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="nb">int</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="n">top</span><span class="p">:</span><span class="w"> </span><span class="n">WHILE</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="k">DO</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">ITERATE</span><span class="w"> </span><span class="n">top</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">6</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="n">LEAVE</span><span class="w"> </span><span class="n">top</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="n">WHILE</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">b</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>This SQL UDF implements calculating the <code class="docutils literal notranslate"><span class="pre">n</span></code> to the power of <code class="docutils literal notranslate"><span class="pre">p</span></code> by repeated
multiplication and keeping track of the number of multiplications performed.
Note that this SQL UDF does not return the correct <code class="docutils literal notranslate"><span class="pre">0</span></code> for <code class="docutils literal notranslate"><span class="pre">p=0</span></code> since the <code class="docutils literal notranslate"><span class="pre">top</span></code>
block is merely escaped and the value of <code class="docutils literal notranslate"><span class="pre">n</span></code> is returned. The same incorrect
behavior happens for negative values of <code class="docutils literal notranslate"><span class="pre">p</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="n">n</span><span class="w"> </span><span class="nb">int</span><span class="p">,</span><span class="w"> </span><span class="n">p</span><span class="w"> </span><span class="nb">int</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">int</span>
<span class="w">  </span><span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="nb">int</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">n</span><span class="p">;</span>
<span class="w">    </span><span class="n">top</span><span class="p">:</span><span class="w"> </span><span class="n">LOOP</span>
<span class="w">      </span><span class="k">IF</span><span class="w"> </span><span class="n">p</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="n">LEAVE</span><span class="w"> </span><span class="n">top</span><span class="p">;</span>
<span class="w">      </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">n</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">p</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">p</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="n">LOOP</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">r</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span>
</pre></div>
</div>
<p>Following are a couple of example invocations with result and explanation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 4</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">8</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 256</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 256</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 3, which is wrong</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">power</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 3, which is wrong</span>
</pre></div>
</div>
<p>This SQL UDF returns <code class="docutils literal notranslate"><span class="pre">7</span></code> as a result of the increase of <code class="docutils literal notranslate"><span class="pre">b</span></code> in the loop from
<code class="docutils literal notranslate"><span class="pre">a=3</span></code> to <code class="docutils literal notranslate"><span class="pre">a=10</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">test_repeat_continue</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">bigint</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="nb">int</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="nb">int</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="n">top</span><span class="p">:</span><span class="w"> </span><span class="n">REPEAT</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">ITERATE</span><span class="w"> </span><span class="n">top</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">  </span><span class="k">UNTIL</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">10</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="n">REPEAT</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">b</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>This SQL UDF returns <code class="docutils literal notranslate"><span class="pre">2</span></code> and shows that labels can be repeated and label usage
within a block refers to the label of that block:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">test</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">int</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="nb">int</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="n">abc</span><span class="p">:</span><span class="w"> </span><span class="n">LOOP</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="n">LEAVE</span><span class="w"> </span><span class="n">abc</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="n">LOOP</span><span class="p">;</span>
<span class="w">  </span><span class="n">abc</span><span class="p">:</span><span class="w"> </span><span class="n">LOOP</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="n">LEAVE</span><span class="w"> </span><span class="n">abc</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="n">LOOP</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">r</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
</section>
<section id="sql-udfs-and-built-in-functions">
<h2 id="sql-udfs-and-built-in-functions">SQL UDFs and built-in functions<a class="headerlink" href="examples.html#sql-udfs-and-built-in-functions" title="Link to this heading">#</a></h2>
<p>This SQL UDF shows that multiple data types and built-in functions like
<code class="docutils literal notranslate"><span class="pre">length()</span></code> and <code class="docutils literal notranslate"><span class="pre">cardinality()</span></code> can be used in a UDF. The two nested <code class="docutils literal notranslate"><span class="pre">BEGIN</span></code>
blocks also show how variable names are local within these blocks <code class="docutils literal notranslate"><span class="pre">x</span></code>, but the
global <code class="docutils literal notranslate"><span class="pre">r</span></code> from the top-level block can be accessed in the nested blocks:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">test</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">bigint</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="nb">bigint</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">'hello'</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>
<span class="w">  </span><span class="k">END</span><span class="p">;</span>
<span class="w">  </span><span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="nb">array</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="nb">array</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">];</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">r</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="k">cardinality</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>
<span class="w">  </span><span class="k">END</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">r</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
</section>
<section id="optional-parameter-example">
<h2 id="optional-parameter-example">Optional parameter example<a class="headerlink" href="examples.html#optional-parameter-example" title="Link to this heading">#</a></h2>
<p>UDFs can invoke other UDFs and other functions. The full signature of a UDF is
composed of the UDF name and parameters, and determines the exact UDF to use.
You can declare multiple UDFs with the same name, but with a different number of
arguments or different argument types. One example use case is to implement an
optional parameter.</p>
<p>The following SQL UDF truncates a string to the specified length including three
dots at the end of the output:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">varchar</span><span class="p">,</span><span class="w"> </span><span class="k">length</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">varchar</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">IF</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="k">length</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="k">substring</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="k">length</span><span class="o">-</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">'...'</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="k">input</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>Following are example invocations and output:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="s1">'A long string that will be shortened'</span><span class="p">,</span><span class="mi">15</span><span class="p">);</span>
<span class="c1">-- A long strin...</span>
<span class="k">SELECT</span><span class="w">	</span><span class="n">dots</span><span class="p">(</span><span class="s1">'A short string'</span><span class="p">,</span><span class="mi">15</span><span class="p">);</span>
<span class="c1">-- A short string</span>
</pre></div>
</div>
<p>If you want to provide a UDF with the same name, but without the parameter
for length, you can create another UDF that invokes the preceding UDF:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">varchar</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">varchar</span>
<span class="k">RETURN</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="mi">15</span><span class="p">);</span>
</pre></div>
</div>
<p>You can now use both UDFs. When the length parameter is omitted, the default
value from the second declaration is used.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="s1">'A long string that will be shortened'</span><span class="p">,</span><span class="mi">15</span><span class="p">);</span>
<span class="c1">-- A long strin...</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="s1">'A long string that will be shortened'</span><span class="p">);</span>
<span class="c1">-- A long strin...</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">dots</span><span class="p">(</span><span class="s1">'A long string that will be shortened'</span><span class="p">,</span><span class="mi">20</span><span class="p">);</span>
<span class="c1">-- A long string tha...</span>
</pre></div>
</div>
</section>
<section id="date-string-parsing-example">
<h2 id="date-string-parsing-example">Date string parsing example<a class="headerlink" href="examples.html#date-string-parsing-example" title="Link to this heading">#</a></h2>
<p>This example SQL UDF parses a date string of type <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> into <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code>. Date strings are commonly represented by ISO 8601 standard, such as
<code class="docutils literal notranslate"><span class="pre">2023-12-01</span></code>, <code class="docutils literal notranslate"><span class="pre">2023-12-01T23</span></code>. Date strings are also often represented in the
<code class="docutils literal notranslate"><span class="pre">YYYYmmdd</span></code> and <code class="docutils literal notranslate"><span class="pre">YYYYmmddHH</span></code> format, such as <code class="docutils literal notranslate"><span class="pre">20230101</span></code> and <code class="docutils literal notranslate"><span class="pre">2023010123</span></code>. Hive
tables can use this format to represent day and hourly partitions, for example
<code class="docutils literal notranslate"><span class="pre">/day=20230101</span></code>, <code class="docutils literal notranslate"><span class="pre">/hour=2023010123</span></code>.</p>
<p>This UDF parses date strings in a best-effort fashion and can be used as a
replacement for date string manipulation functions such as <code class="docutils literal notranslate"><span class="pre">date</span></code>, <code class="docutils literal notranslate"><span class="pre">date_parse</span></code>,
<code class="docutils literal notranslate"><span class="pre">from_iso8601_date</span></code>,  and <code class="docutils literal notranslate"><span class="pre">from_iso8601_timestamp</span></code>.</p>
<p>Note that the UDF defaults the time value to <code class="docutils literal notranslate"><span class="pre">00:00:00.000</span></code> and the time
zone to the session time zone:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="n">date_string</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">IF</span><span class="w"> </span><span class="n">date_string</span><span class="w"> </span><span class="k">like</span><span class="w"> </span><span class="s1">'%-%'</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="c1">-- ISO 8601</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">from_iso8601_timestamp</span><span class="p">(</span><span class="n">date_string</span><span class="p">);</span>
<span class="w">  </span><span class="n">ELSEIF</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="n">date_string</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">8</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="c1">-- YYYYmmdd</span>
<span class="w">      </span><span class="k">RETURN</span><span class="w"> </span><span class="n">date_parse</span><span class="p">(</span><span class="n">date_string</span><span class="p">,</span><span class="w"> </span><span class="s1">'%Y%m%d'</span><span class="p">);</span>
<span class="w">  </span><span class="n">ELSEIF</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="n">date_string</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="c1">-- YYYYmmddHH</span>
<span class="w">      </span><span class="k">RETURN</span><span class="w"> </span><span class="n">date_parse</span><span class="p">(</span><span class="n">date_string</span><span class="p">,</span><span class="w"> </span><span class="s1">'%Y%m%d%H'</span><span class="p">);</span>
<span class="w">  </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
<span class="k">END</span>
</pre></div>
</div>
<p>Following are a couple of example invocations with result and explanation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'2023-01-01'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2023-01-01 00:00:00.000 UTC (using the ISO 8601 format)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'2023-01-01T23'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2023-01-01 23:00:00.000 UTC (using the ISO 8601 format)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'2023-01-01T23:23:23'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2023-01-01 23:23:23.000 UTC (using the ISO 8601 format)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'20230101'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2023-01-01 00:00:00.000 UTC (using the YYYYmmdd format)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'2023010123'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2023-01-01 23:00:00.000 UTC (using the YYYYmmddHH format)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="k">NULL</span><span class="p">);</span><span class="w"> </span><span class="c1">-- NULL (handles NULL string)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">from_date_string</span><span class="p">(</span><span class="s1">'abc'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- NULL (not matched to any format)</span>
</pre></div>
</div>
</section>
<section id="human-readable-days">
<h2 id="human-readable-days">Human-readable days<a class="headerlink" href="examples.html#human-readable-days" title="Link to this heading">#</a></h2>
<p>Trino includes a built-in function called <a class="reference internal" href="../../functions/datetime.html#human_readable_seconds" title="human_readable_seconds"><code class="xref py py-func docutils literal notranslate"><span class="pre">human_readable_seconds()</span></code></a> that
formats a number of seconds into a string:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">human_readable_seconds</span><span class="p">(</span><span class="mi">134823</span><span class="p">);</span>
<span class="c1">-- 1 day, 13 hours, 27 minutes, 3 seconds</span>
</pre></div>
</div>
<p>The example SQL UDF <code class="docutils literal notranslate"><span class="pre">hrd</span></code> formats a number of days into a human-readable text
that provides the approximate number of years and months:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="n">d</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="k">default</span><span class="w"> </span><span class="s1">'About '</span><span class="p">;</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="nb">real</span><span class="p">;</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="nb">real</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">truncate</span><span class="p">(</span><span class="n">d</span><span class="o">/</span><span class="mi">365</span><span class="p">);</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">then</span>
<span class="w">        </span><span class="k">SET</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">format</span><span class="p">(</span><span class="s1">'%1.0f'</span><span class="p">,</span><span class="w"> </span><span class="n">years</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' year'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">SET</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">'s'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">d</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">d</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">365</span><span class="w"> </span><span class="p">;</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">truncate</span><span class="p">(</span><span class="n">d</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">30</span><span class="p">);</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">SET</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' and '</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">set</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">format</span><span class="p">(</span><span class="s1">'%1.0f'</span><span class="p">,</span><span class="w"> </span><span class="n">months</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' month'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">SET</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">'s'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="n">years</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">months</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">SET</span><span class="w"> </span><span class="n">answer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'Less than 1 month'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">answer</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>The following examples show the output for a range of values under one month,
under one year, and various larger values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">10</span><span class="p">);</span><span class="w"> </span><span class="c1">-- Less than 1 month</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">95</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 3 months</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">400</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 1 year and 1 month</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">369</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 1 year</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">800</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 2 years and 2 months</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">1100</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 3 years</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">hrd</span><span class="p">(</span><span class="mi">5000</span><span class="p">);</span><span class="w"> </span><span class="c1">-- About 13 years and 8 months</span>
</pre></div>
</div>
<p>Improvements of the SQL UDF could include the following modifications:</p>
<ul class="simple">
<li><p>Take into account that one month equals 30.4375 days.</p></li>
<li><p>Take into account that one year equals 365.25 days.</p></li>
<li><p>Add weeks to the output.</p></li>
<li><p>Expand to cover decades, centuries, and millennia.</p></li>
</ul>
</section>
<section id="truncating-long-strings">
<h2 id="truncating-long-strings">Truncating long strings<a class="headerlink" href="examples.html#truncating-long-strings" title="Link to this heading">#</a></h2>
<p>This example SQL UDF <code class="docutils literal notranslate"><span class="pre">strtrunc</span></code> truncates strings longer than 60 characters,
leaving the first 30 and the last 25 characters, and cutting out extra
characters in the middle:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">strtrunc</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">RETURN</span>
<span class="w">    </span><span class="k">CASE</span><span class="w"> </span><span class="k">WHEN</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">60</span>
<span class="w">    </span><span class="k">THEN</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' ... '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">25</span><span class="p">)</span>
<span class="w">    </span><span class="k">ELSE</span><span class="w"> </span><span class="k">input</span>
<span class="w">    </span><span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding declaration is very compact and consists of only one complex
statement with a <a class="reference internal" href="../../functions/conditional.html#case-expression"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">CASE</span></code> expression</span></a> and multiple function
calls. It can therefore define the complete logic in the <code class="docutils literal notranslate"><span class="pre">RETURN</span></code> clause.</p>
<p>The following statement shows the same capability within the SQL UDF itself.
Note the duplicate <code class="docutils literal notranslate"><span class="pre">RETURN</span></code> inside and outside the <code class="docutils literal notranslate"><span class="pre">CASE</span></code> statement and the
required <code class="docutils literal notranslate"><span class="pre">END</span> <span class="pre">CASE;</span></code>. The second <code class="docutils literal notranslate"><span class="pre">RETURN</span></code> statement is required, because a SQL
UDF must end with a <code class="docutils literal notranslate"><span class="pre">RETURN</span></code> statement. As a result the <code class="docutils literal notranslate"><span class="pre">ELSE</span></code> clause can be
omitted:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">strtrunc</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">CASE</span><span class="w"> </span><span class="k">WHEN</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">60</span>
<span class="w">    </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">RETURN</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' ... '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">25</span><span class="p">);</span>
<span class="w">    </span><span class="k">ELSE</span>
<span class="w">        </span><span class="k">RETURN</span><span class="w"> </span><span class="k">input</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">CASE</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="k">input</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>The next example changes over from a <code class="docutils literal notranslate"><span class="pre">CASE</span></code> to an <code class="docutils literal notranslate"><span class="pre">IF</span></code> statement, and avoids the
duplicate <code class="docutils literal notranslate"><span class="pre">RETURN</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">strtrunc</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">RETURN</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">' ... '</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="k">input</span><span class="p">,</span><span class="w"> </span><span class="k">length</span><span class="p">(</span><span class="k">input</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">25</span><span class="p">);</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="k">input</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>All the preceding examples create the same output. Following is an example query
which generates long strings to truncate:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="k">data</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="k">substring</span><span class="p">(</span><span class="s1">'strtrunc truncates strings longer than 60 characters,</span>
<span class="s1">     leaving the prefix and suffix visible'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">num</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">value</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">40</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">80</span><span class="p">,</span><span class="w"> </span><span class="n">step</span><span class="o">=&gt;</span><span class="mi">5</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">num</span><span class="p">)</span>
<span class="p">)</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="k">data</span><span class="p">.</span><span class="n">value</span>
<span class="w">  </span><span class="p">,</span><span class="w"> </span><span class="n">strtrunc</span><span class="p">(</span><span class="k">data</span><span class="p">.</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">truncated</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">data</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">data</span><span class="p">.</span><span class="n">value</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding query produces the following output with all variants of the SQL
UDF:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="w">                                      </span><span class="n">value</span><span class="w">                                       </span><span class="o">|</span><span class="w">                           </span><span class="n">truncated</span>
<span class="c1">----------------------------------------------------------------------------------+---------------------------------------------------------------</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">6</span><span class="w">                                         </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">6</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">cha</span><span class="w">                                    </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">cha</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characte</span><span class="w">                               </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characte</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">l</span><span class="w">                          </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">l</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leavin</span><span class="w">                     </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leavin</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w">                </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">lon</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">pref</span><span class="w">           </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">lon</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="n">aracters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">pref</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">prefix</span><span class="w"> </span><span class="n">an</span><span class="w">      </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">lon</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="n">ers</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">prefix</span><span class="w"> </span><span class="n">an</span>
<span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">longer</span><span class="w"> </span><span class="k">than</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="n">characters</span><span class="p">,</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">prefix</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">suf</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="n">strtrunc</span><span class="w"> </span><span class="n">truncates</span><span class="w"> </span><span class="n">strings</span><span class="w"> </span><span class="n">lon</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="n">leaving</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">prefix</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">suf</span>
</pre></div>
</div>
<p>A possible improvement is to introduce parameters for the total length.</p>
</section>
<section id="formatting-bytes">
<h2 id="formatting-bytes">Formatting bytes<a class="headerlink" href="examples.html#formatting-bytes" title="Link to this heading">#</a></h2>
<p>Trino includes a built-in <code class="docutils literal notranslate"><span class="pre">format_number()</span></code> function. However, it is using units
that do not work well with bytes. The following <code class="docutils literal notranslate"><span class="pre">format_data_size</span></code> SQL UDF can
format large values of bytes into a human-readable string:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">format_data_size</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="w">  </span><span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="n">DOUBLE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">);</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="k">result</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">;</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="nb">INT</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">1024</span><span class="p">;</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">'B'</span><span class="p">;</span>
<span class="w">    </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">format</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'kB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'MB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'GB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'TB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'PB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'EB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'ZB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">base</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">base</span><span class="p">;</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'YB'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'%.2f'</span><span class="p">;</span>
<span class="w">    </span><span class="n">ELSEIF</span><span class="w"> </span><span class="k">abs</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'%.1f'</span><span class="p">;</span>
<span class="w">    </span><span class="k">ELSE</span>
<span class="w">      </span><span class="k">SET</span><span class="w"> </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'%.0f'</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="n">format</span><span class="p">(</span><span class="n">format</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">unit</span><span class="p">;</span>
<span class="w">  </span><span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>Below is a query that shows how it formats a wide range of values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="k">data</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">pow</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">p</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">num</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">18</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="w">    </span><span class="k">UNION</span><span class="w"> </span><span class="k">ALL</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="o">-</span><span class="k">CAST</span><span class="p">(</span><span class="n">pow</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">p</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">num</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">18</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="p">)</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="k">data</span><span class="p">.</span><span class="n">num</span>
<span class="w">  </span><span class="p">,</span><span class="w"> </span><span class="n">format_data_size</span><span class="p">(</span><span class="k">data</span><span class="p">.</span><span class="n">num</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">formatted</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">data</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">data</span><span class="p">.</span><span class="n">num</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding query produces the following output:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="w">         </span><span class="n">num</span><span class="w">          </span><span class="o">|</span><span class="w"> </span><span class="n">formatted</span>
<span class="c1">----------------------+-----------</span>
<span class="w"> </span><span class="o">-</span><span class="mi">1000000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">888</span><span class="n">PB</span>
<span class="w">  </span><span class="o">-</span><span class="mi">100000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">88</span><span class="p">.</span><span class="mi">8</span><span class="n">PB</span>
<span class="w">   </span><span class="o">-</span><span class="mi">10000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">8</span><span class="p">.</span><span class="mi">88</span><span class="n">PB</span>
<span class="w">    </span><span class="o">-</span><span class="mi">1000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">909</span><span class="n">TB</span>
<span class="w">     </span><span class="o">-</span><span class="mi">100000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">90</span><span class="p">.</span><span class="mi">9</span><span class="n">TB</span>
<span class="w">      </span><span class="o">-</span><span class="mi">10000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">9</span><span class="p">.</span><span class="mi">09</span><span class="n">TB</span>
<span class="w">       </span><span class="o">-</span><span class="mi">1000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">931</span><span class="n">GB</span>
<span class="w">        </span><span class="o">-</span><span class="mi">100000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">93</span><span class="p">.</span><span class="mi">1</span><span class="n">GB</span>
<span class="w">         </span><span class="o">-</span><span class="mi">10000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">9</span><span class="p">.</span><span class="mi">31</span><span class="n">GB</span>
<span class="w">          </span><span class="o">-</span><span class="mi">1000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">954</span><span class="n">MB</span>
<span class="w">           </span><span class="o">-</span><span class="mi">100000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">95</span><span class="p">.</span><span class="mi">4</span><span class="n">MB</span>
<span class="w">            </span><span class="o">-</span><span class="mi">10000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">9</span><span class="p">.</span><span class="mi">54</span><span class="n">MB</span>
<span class="w">             </span><span class="o">-</span><span class="mi">1000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">977</span><span class="n">kB</span>
<span class="w">              </span><span class="o">-</span><span class="mi">100000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">97</span><span class="p">.</span><span class="mi">7</span><span class="n">kB</span>
<span class="w">               </span><span class="o">-</span><span class="mi">10000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">9</span><span class="p">.</span><span class="mi">77</span><span class="n">kB</span>
<span class="w">                </span><span class="o">-</span><span class="mi">1000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">1000</span><span class="n">B</span>
<span class="w">                 </span><span class="o">-</span><span class="mi">100</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">100</span><span class="n">B</span>
<span class="w">                  </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">.</span><span class="mi">0</span><span class="n">B</span>
<span class="w">                    </span><span class="mi">0</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">00</span><span class="n">B</span>
<span class="w">                   </span><span class="mi">10</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">10</span><span class="p">.</span><span class="mi">0</span><span class="n">B</span>
<span class="w">                  </span><span class="mi">100</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">100</span><span class="n">B</span>
<span class="w">                 </span><span class="mi">1000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">1000</span><span class="n">B</span>
<span class="w">                </span><span class="mi">10000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">9</span><span class="p">.</span><span class="mi">77</span><span class="n">kB</span>
<span class="w">               </span><span class="mi">100000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">97</span><span class="p">.</span><span class="mi">7</span><span class="n">kB</span>
<span class="w">              </span><span class="mi">1000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">977</span><span class="n">kB</span>
<span class="w">             </span><span class="mi">10000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">9</span><span class="p">.</span><span class="mi">54</span><span class="n">MB</span>
<span class="w">            </span><span class="mi">100000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">95</span><span class="p">.</span><span class="mi">4</span><span class="n">MB</span>
<span class="w">           </span><span class="mi">1000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">954</span><span class="n">MB</span>
<span class="w">          </span><span class="mi">10000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">9</span><span class="p">.</span><span class="mi">31</span><span class="n">GB</span>
<span class="w">         </span><span class="mi">100000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">93</span><span class="p">.</span><span class="mi">1</span><span class="n">GB</span>
<span class="w">        </span><span class="mi">1000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">931</span><span class="n">GB</span>
<span class="w">       </span><span class="mi">10000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">9</span><span class="p">.</span><span class="mi">09</span><span class="n">TB</span>
<span class="w">      </span><span class="mi">100000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">90</span><span class="p">.</span><span class="mi">9</span><span class="n">TB</span>
<span class="w">     </span><span class="mi">1000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">909</span><span class="n">TB</span>
<span class="w">    </span><span class="mi">10000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">8</span><span class="p">.</span><span class="mi">88</span><span class="n">PB</span>
<span class="w">   </span><span class="mi">100000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">88</span><span class="p">.</span><span class="mi">8</span><span class="n">PB</span>
<span class="w">  </span><span class="mi">1000000000000000000</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="mi">888</span><span class="n">PB</span>
</pre></div>
</div>
</section>
<section id="charts">
<h2 id="charts">Charts<a class="headerlink" href="examples.html#charts" title="Link to this heading">#</a></h2>
<p>Trino already has a built-in <code class="docutils literal notranslate"><span class="pre">bar()</span></code> <a class="reference internal" href="../../functions/color.html"><span class="doc std std-doc">color function</span></a>, but it
is using ANSI escape codes to output colors, and thus is only usable for
displaying results in a terminal. The following example shows a similar SQL UDF
that only uses ASCII characters:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">ascii_bar</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">max_width</span><span class="w"> </span><span class="n">DOUBLE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">40</span><span class="p">.</span><span class="mi">0</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">array_join</span><span class="p">(</span>
<span class="w">    </span><span class="n">repeat</span><span class="p">(</span><span class="s1">'█'</span><span class="p">,</span>
<span class="w">        </span><span class="n">greatest</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">floor</span><span class="p">(</span><span class="n">max_width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">integer</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mi">1</span><span class="p">)),</span><span class="w"> </span><span class="s1">''</span><span class="p">)</span>
<span class="w">        </span><span class="o">||</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">' '</span><span class="p">,</span><span class="w"> </span><span class="s1">'▏'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▎'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▍'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▌'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▋'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▊'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▉'</span><span class="p">,</span><span class="w"> </span><span class="s1">'█'</span><span class="p">]</span>
<span class="w">        </span><span class="p">[</span><span class="k">cast</span><span class="p">((</span><span class="n">value</span><span class="w"> </span><span class="o">%</span><span class="w"> </span><span class="p">(</span><span class="k">cast</span><span class="p">(</span><span class="mi">1</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">double</span><span class="p">)</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">max_width</span><span class="p">))</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">max_width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">8</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">int</span><span class="p">)];</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>It can be used to visualize a value:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="k">data</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span>
<span class="w">        </span><span class="k">cast</span><span class="p">(</span><span class="n">s</span><span class="p">.</span><span class="n">num</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">double</span><span class="p">)</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">x</span><span class="p">,</span>
<span class="w">        </span><span class="n">sin</span><span class="p">(</span><span class="k">cast</span><span class="p">(</span><span class="n">s</span><span class="p">.</span><span class="n">num</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">double</span><span class="p">)</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">y</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">314</span><span class="p">,</span><span class="w"> </span><span class="n">step</span><span class="o">=&gt;</span><span class="mi">10</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">num</span><span class="p">)</span>
<span class="p">)</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="k">data</span><span class="p">.</span><span class="n">x</span><span class="p">,</span>
<span class="w">    </span><span class="n">round</span><span class="p">(</span><span class="k">data</span><span class="p">.</span><span class="n">y</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">y</span><span class="p">,</span>
<span class="w">    </span><span class="n">ascii_bar</span><span class="p">(</span><span class="k">data</span><span class="p">.</span><span class="n">y</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">chart</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">data</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">data</span><span class="p">.</span><span class="n">x</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding query produces the following output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>  x  |   y    |                  chart
-----+--------+-----------------------------------------
 0.0 |    0.0 |
 0.1 | 0.0998 | ███
 0.2 | 0.1987 | ███████
 0.3 | 0.2955 | ██████████▉
 0.4 | 0.3894 | ██████████████▋
 0.5 | 0.4794 | ██████████████████▏
 0.6 | 0.5646 | █████████████████████▋
 0.7 | 0.6442 | ████████████████████████▊
 0.8 | 0.7174 | ███████████████████████████▊
 0.9 | 0.7833 | ██████████████████████████████▍
 1.0 | 0.8415 | ████████████████████████████████▋
 1.1 | 0.8912 | ██████████████████████████████████▋
 1.2 |  0.932 | ████████████████████████████████████▎
 1.3 | 0.9636 | █████████████████████████████████████▌
 1.4 | 0.9854 | ██████████████████████████████████████▍
 1.5 | 0.9975 | ██████████████████████████████████████▉
 1.6 | 0.9996 | ███████████████████████████████████████
 1.7 | 0.9917 | ██████████████████████████████████████▋
 1.8 | 0.9738 | ██████████████████████████████████████
 1.9 | 0.9463 | ████████████████████████████████████▉
 2.0 | 0.9093 | ███████████████████████████████████▍
 2.1 | 0.8632 | █████████████████████████████████▌
 2.2 | 0.8085 | ███████████████████████████████▍
 2.3 | 0.7457 | ████████████████████████████▉
 2.4 | 0.6755 | ██████████████████████████
 2.5 | 0.5985 | ███████████████████████
 2.6 | 0.5155 | ███████████████████▋
 2.7 | 0.4274 | ████████████████▏
 2.8 |  0.335 | ████████████▍
 2.9 | 0.2392 | ████████▋
 3.0 | 0.1411 | ████▋
 3.1 | 0.0416 | ▋
</pre></div>
</div>
<p>It is also possible to draw more compacted charts. Following is a SQL UDF
drawing vertical bars:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">vertical_bar</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">RETURN</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">' '</span><span class="p">,</span><span class="w"> </span><span class="s1">'▁'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▂'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▃'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▄'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▅'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▆'</span><span class="p">,</span><span class="w"> </span><span class="s1">'▇'</span><span class="p">,</span><span class="w"> </span><span class="s1">'█'</span><span class="p">][</span><span class="k">cast</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">8</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">int</span><span class="p">)];</span>
</pre></div>
</div>
<p>It can be used to draw a distribution of values, in a single column:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="n">measurements</span><span class="p">(</span><span class="n">sensor_id</span><span class="p">,</span><span class="w"> </span><span class="n">recorded_at</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">VALUES</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-01'</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-03'</span><span class="p">,</span><span class="w"> </span><span class="mi">7</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-04'</span><span class="p">,</span><span class="w"> </span><span class="mi">15</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-05'</span><span class="p">,</span><span class="w"> </span><span class="mi">14</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-08'</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-09'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-10'</span><span class="p">,</span><span class="w"> </span><span class="mi">7</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-11'</span><span class="p">,</span><span class="w"> </span><span class="mi">8</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-03'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-04'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-05'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">.</span><span class="mi">5</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-07'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">.</span><span class="mi">75</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-09'</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-10'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">.</span><span class="mi">5</span><span class="p">)</span>
<span class="w">      </span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-11'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">)</span>
<span class="p">),</span>
<span class="n">days</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_add</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">num</span><span class="p">,</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2023-01-01'</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">day</span>
<span class="w">    </span><span class="c1">-- table function arguments need to be constant but range could be calculated</span>
<span class="w">    </span><span class="c1">-- using: SELECT date_diff('day', max(recorded_at), min(recorded_at)) FROM measurements</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">10</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">num</span><span class="p">)</span>
<span class="p">),</span>
<span class="n">sensors</span><span class="p">(</span><span class="n">id</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="s1">'A'</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="s1">'B'</span><span class="p">)),</span>
<span class="n">normalized</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span>
<span class="w">        </span><span class="n">sensors</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">sensor_id</span><span class="p">,</span>
<span class="w">        </span><span class="n">days</span><span class="p">.</span><span class="k">day</span><span class="p">,</span>
<span class="w">        </span><span class="n">value</span><span class="p">,</span>
<span class="w">        </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="k">max</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="p">(</span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">sensor_id</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">normalized</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="n">days</span>
<span class="w">    </span><span class="k">CROSS</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">sensors</span>
<span class="w">    </span><span class="k">LEFT</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">measurements</span><span class="w"> </span><span class="n">m</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">day</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">recorded_at</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">m</span><span class="p">.</span><span class="n">sensor_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">sensors</span><span class="p">.</span><span class="n">id</span>
<span class="p">)</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="n">sensor_id</span><span class="p">,</span>
<span class="w">    </span><span class="k">min</span><span class="p">(</span><span class="k">day</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">start</span><span class="p">,</span>
<span class="w">    </span><span class="k">max</span><span class="p">(</span><span class="k">day</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">stop</span><span class="p">,</span>
<span class="w">    </span><span class="k">count</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">num_values</span><span class="p">,</span>
<span class="w">    </span><span class="k">min</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">min_value</span><span class="p">,</span>
<span class="w">    </span><span class="k">max</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">max_value</span><span class="p">,</span>
<span class="w">    </span><span class="k">avg</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">avg_value</span><span class="p">,</span>
<span class="w">    </span><span class="n">array_join</span><span class="p">(</span><span class="n">array_agg</span><span class="p">(</span><span class="k">coalesce</span><span class="p">(</span><span class="n">vertical_bar</span><span class="p">(</span><span class="n">normalized</span><span class="p">),</span><span class="w"> </span><span class="s1">' '</span><span class="p">)</span><span class="w"> </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">day</span><span class="p">),</span>
<span class="w">     </span><span class="s1">''</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">distribution</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">normalized</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">sensor_id</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span>
<span class="k">GROUP</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">sensor_id</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">sensor_id</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding query produces the following output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> sensor_id |   start    |    stop    | num_values | min_value | max_value | avg_value | distribution
-----------+------------+------------+------------+-----------+-----------+-----------+--------------
 A         | 2023-01-01 | 2023-01-11 |          8 |      1.00 |     15.00 |      8.38 | ▃ ▄█▇  ▅▁▄▄
 B         | 2023-01-01 | 2023-01-11 |          7 |      1.00 |      4.00 |      2.39 |   ▄▆▅ ▆ █▃▂
</pre></div>
</div>
</section>
<section id="top-n">
<h2 id="top-n">Top-N<a class="headerlink" href="examples.html#top-n" title="Link to this heading">#</a></h2>
<p>Trino already has a built-in <a class="reference internal" href="../../functions/aggregate.html"><span class="doc std std-doc">aggregate function</span></a> called
<code class="docutils literal notranslate"><span class="pre">approx_most_frequent()</span></code> that can calculate the most frequently occurring
values. It returns a map with values as keys and number of occurrences as
values. Maps are not ordered, so when displayed, the entries can change places
on subsequent runs of the same query, and readers must still compare all
frequencies to find the one most frequent value. The following is a SQL UDF that
returns ordered results as a string:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">format_topn</span><span class="p">(</span><span class="k">input</span><span class="w"> </span><span class="k">map</span><span class="o">&lt;</span><span class="nb">varchar</span><span class="p">,</span><span class="w"> </span><span class="nb">bigint</span><span class="o">&gt;</span><span class="p">)</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="k">NOT</span><span class="w"> </span><span class="k">DETERMINISTIC</span>
<span class="k">BEGIN</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">freq_separator</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">'='</span><span class="p">;</span>
<span class="w">  </span><span class="k">DECLARE</span><span class="w"> </span><span class="n">entry_separator</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">', '</span><span class="p">;</span>
<span class="w">  </span><span class="k">RETURN</span><span class="w"> </span><span class="n">array_join</span><span class="p">(</span><span class="k">transform</span><span class="p">(</span>
<span class="w">    </span><span class="n">reverse</span><span class="p">(</span><span class="n">array_sort</span><span class="p">(</span><span class="k">transform</span><span class="p">(</span>
<span class="w">      </span><span class="k">transform</span><span class="p">(</span>
<span class="w">        </span><span class="n">map_entries</span><span class="p">(</span><span class="k">input</span><span class="p">),</span>
<span class="w">          </span><span class="n">r</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="n">r</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">row</span><span class="p">(</span><span class="k">key</span><span class="w"> </span><span class="nb">varchar</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="nb">bigint</span><span class="p">))</span>
<span class="w">      </span><span class="p">),</span>
<span class="w">      </span><span class="n">r</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="k">row</span><span class="p">(</span><span class="n">r</span><span class="p">.</span><span class="n">value</span><span class="p">,</span><span class="w"> </span><span class="n">r</span><span class="p">.</span><span class="k">key</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">row</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="nb">bigint</span><span class="p">,</span><span class="w"> </span><span class="k">key</span><span class="w"> </span><span class="nb">varchar</span><span class="p">)))</span>
<span class="w">    </span><span class="p">)),</span>
<span class="w">    </span><span class="n">r</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">r</span><span class="p">.</span><span class="k">key</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">freq_separator</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="n">r</span><span class="p">.</span><span class="n">value</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">)),</span>
<span class="w">    </span><span class="n">entry_separator</span><span class="p">);</span>
<span class="k">END</span><span class="p">;</span>
</pre></div>
</div>
<p>Following is an example query to count generated strings:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span>
<span class="k">data</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="n">lpad</span><span class="p">(</span><span class="s1">''</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="n">chr</span><span class="p">(</span><span class="mi">65</span><span class="o">+</span><span class="p">(</span><span class="n">s</span><span class="p">.</span><span class="n">num</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">3</span><span class="p">)))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">value</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">table</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span><span class="k">start</span><span class="o">=&gt;</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">stop</span><span class="o">=&gt;</span><span class="mi">10</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">s</span><span class="p">(</span><span class="n">num</span><span class="p">)</span>
<span class="p">),</span>
<span class="n">aggregated</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">SELECT</span>
<span class="w">        </span><span class="n">array_agg</span><span class="p">(</span><span class="k">data</span><span class="p">.</span><span class="n">value</span><span class="w"> </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">data</span><span class="p">.</span><span class="n">value</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">all_values</span><span class="p">,</span>
<span class="w">        </span><span class="n">approx_most_frequent</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="k">data</span><span class="p">.</span><span class="n">value</span><span class="p">,</span><span class="w"> </span><span class="mi">1000</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">top3</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="k">data</span>
<span class="p">)</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="n">a</span><span class="p">.</span><span class="n">all_values</span><span class="p">,</span>
<span class="w">    </span><span class="n">a</span><span class="p">.</span><span class="n">top3</span><span class="p">,</span>
<span class="w">    </span><span class="n">format_topn</span><span class="p">(</span><span class="n">a</span><span class="p">.</span><span class="n">top3</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">top3_formatted</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">aggregated</span><span class="w"> </span><span class="n">a</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding query produces the following result:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                     all_values                     |         top3          |    top3_formatted
----------------------------------------------------+-----------------------+---------------------
 [AAA, AAA, BBB, BBB, BBB, CCC, CCC, CCC, DDD, DDD] | {AAA=2, CCC=3, BBB=3} | CCC=3, BBB=3, AAA=2
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../sql.html" title="SQL user-defined functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> SQL user-defined functions </span>
              </div>
            </a>
          
          
            <a href="begin.html" title="BEGIN"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> BEGIN </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>