<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Row pattern recognition in window structures &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="pattern-recognition-in-window.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Developer guide" href="../develop.html" />
    <link rel="prev" title="VALUES" href="values.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="pattern-recognition-in-window.html#sql/pattern-recognition-in-window" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Row pattern recognition in window structures </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="alter-materialized-view.html" class="md-nav__link">ALTER MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-schema.html" class="md-nav__link">ALTER SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-table.html" class="md-nav__link">ALTER TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-view.html" class="md-nav__link">ALTER VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="analyze.html" class="md-nav__link">ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="call.html" class="md-nav__link">CALL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comment.html" class="md-nav__link">COMMENT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="commit.html" class="md-nav__link">COMMIT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-catalog.html" class="md-nav__link">CREATE CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-function.html" class="md-nav__link">CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-materialized-view.html" class="md-nav__link">CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-role.html" class="md-nav__link">CREATE ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-schema.html" class="md-nav__link">CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table.html" class="md-nav__link">CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table-as.html" class="md-nav__link">CREATE TABLE AS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-view.html" class="md-nav__link">CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deallocate-prepare.html" class="md-nav__link">DEALLOCATE PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delete.html" class="md-nav__link">DELETE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deny.html" class="md-nav__link">DENY</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe.html" class="md-nav__link">DESCRIBE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-input.html" class="md-nav__link">DESCRIBE INPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-output.html" class="md-nav__link">DESCRIBE OUTPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-catalog.html" class="md-nav__link">DROP CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-function.html" class="md-nav__link">DROP FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-materialized-view.html" class="md-nav__link">DROP MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-role.html" class="md-nav__link">DROP ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-schema.html" class="md-nav__link">DROP SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-table.html" class="md-nav__link">DROP TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-view.html" class="md-nav__link">DROP VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute.html" class="md-nav__link">EXECUTE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute-immediate.html" class="md-nav__link">EXECUTE IMMEDIATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain.html" class="md-nav__link">EXPLAIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain-analyze.html" class="md-nav__link">EXPLAIN ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant.html" class="md-nav__link">GRANT privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant-roles.html" class="md-nav__link">GRANT role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">INSERT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="match-recognize.html" class="md-nav__link">MATCH_RECOGNIZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="merge.html" class="md-nav__link">MERGE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prepare.html" class="md-nav__link">PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="refresh-materialized-view.html" class="md-nav__link">REFRESH MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session.html" class="md-nav__link">RESET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session-authorization.html" class="md-nav__link">RESET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke.html" class="md-nav__link">REVOKE privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke-roles.html" class="md-nav__link">REVOKE role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="rollback.html" class="md-nav__link">ROLLBACK</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="select.html" class="md-nav__link">SELECT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-path.html" class="md-nav__link">SET PATH</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-role.html" class="md-nav__link">SET ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session.html" class="md-nav__link">SET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session-authorization.html" class="md-nav__link">SET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-time-zone.html" class="md-nav__link">SET TIME ZONE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-catalogs.html" class="md-nav__link">SHOW CATALOGS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-columns.html" class="md-nav__link">SHOW COLUMNS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-function.html" class="md-nav__link">SHOW CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-materialized-view.html" class="md-nav__link">SHOW CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-schema.html" class="md-nav__link">SHOW CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-table.html" class="md-nav__link">SHOW CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-view.html" class="md-nav__link">SHOW CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-functions.html" class="md-nav__link">SHOW FUNCTIONS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-grants.html" class="md-nav__link">SHOW GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-role-grants.html" class="md-nav__link">SHOW ROLE GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-roles.html" class="md-nav__link">SHOW ROLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-schemas.html" class="md-nav__link">SHOW SCHEMAS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-session.html" class="md-nav__link">SHOW SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-stats.html" class="md-nav__link">SHOW STATS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-tables.html" class="md-nav__link">SHOW TABLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="start-transaction.html" class="md-nav__link">START TRANSACTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="truncate.html" class="md-nav__link">TRUNCATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="update.html" class="md-nav__link">UPDATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="use.html" class="md-nav__link">USE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="values.html" class="md-nav__link">VALUES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Row pattern recognition in window structures </label>
    
      <a href="pattern-recognition-in-window.html#" class="md-nav__link md-nav__link--active">Row pattern recognition in window structures</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#window-with-row-pattern-recognition" class="md-nav__link">Window with row pattern recognition</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#description-of-the-pattern-recognition-clauses" class="md-nav__link">Description of the pattern recognition clauses</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#processing-input-with-row-pattern-recognition" class="md-nav__link">Processing input with row pattern recognition</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#empty-matches-and-unmatched-rows" class="md-nav__link">Empty matches and unmatched rows</a>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#window-with-row-pattern-recognition" class="md-nav__link">Window with row pattern recognition</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#description-of-the-pattern-recognition-clauses" class="md-nav__link">Description of the pattern recognition clauses</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#processing-input-with-row-pattern-recognition" class="md-nav__link">Processing input with row pattern recognition</a>
        </li>
        <li class="md-nav__item"><a href="pattern-recognition-in-window.html#empty-matches-and-unmatched-rows" class="md-nav__link">Empty matches and unmatched rows</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="row-pattern-recognition-in-window-structures">
<h1 id="sql-pattern-recognition-in-window--page-root">Row pattern recognition in window structures<a class="headerlink" href="pattern-recognition-in-window.html#sql-pattern-recognition-in-window--page-root" title="Link to this heading">#</a></h1>
<p>A window structure can be defined in the <code class="docutils literal notranslate"><span class="pre">WINDOW</span></code> clause or in the <code class="docutils literal notranslate"><span class="pre">OVER</span></code>
clause of a window operation. In both cases, the window specification can
include row pattern recognition clauses. They are part of the window frame. The
syntax and semantics of row pattern recognition in window are similar to those
of the <a class="reference internal" href="match-recognize.html"><span class="doc">MATCH_RECOGNIZE</span></a> clause.</p>
<p>This section explains the details of row pattern recognition in window
structures, and highlights the similarities and the differences between both
pattern recognition mechanisms.</p>
<section id="window-with-row-pattern-recognition">
<h2 id="window-with-row-pattern-recognition">Window with row pattern recognition<a class="headerlink" href="pattern-recognition-in-window.html#window-with-row-pattern-recognition" title="Link to this heading">#</a></h2>
<p><strong>Window specification:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>(
[ existing_window_name ]
[ PARTITION BY column [, ...] ]
[ ORDER BY column [, ...] ]
[ window_frame ]
)
</pre></div>
</div>
<p><strong>Window frame:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[ MEASURES measure_definition [, ...] ]
frame_extent
[ AFTER MATCH skip_to ]
[ INITIAL | SEEK ]
[ PATTERN ( row_pattern ) ]
[ SUBSET subset_definition [, ...] ]
[ DEFINE variable_definition [, ...] ]
</pre></div>
</div>
<p>Generally, a window frame specifies the <code class="docutils literal notranslate"><span class="pre">frame_extent</span></code>, which defines the
“sliding window” of rows to be processed by a window function. It can be
defined in terms of <code class="docutils literal notranslate"><span class="pre">ROWS</span></code>, <code class="docutils literal notranslate"><span class="pre">RANGE</span></code> or <code class="docutils literal notranslate"><span class="pre">GROUPS</span></code>.</p>
<p>A window frame with row pattern recognition involves many other syntactical
components, mandatory or optional, and enforces certain limitations on the
<code class="docutils literal notranslate"><span class="pre">frame_extent</span></code>.</p>
<p><strong>Window frame with row pattern recognition:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[ MEASURES measure_definition [, ...] ]
ROWS BETWEEN CURRENT ROW AND frame_end
[ AFTER MATCH skip_to ]
[ INITIAL | SEEK ]
PATTERN ( row_pattern )
[ SUBSET subset_definition [, ...] ]
DEFINE variable_definition [, ...]
</pre></div>
</div>
</section>
<section id="description-of-the-pattern-recognition-clauses">
<h2 id="description-of-the-pattern-recognition-clauses">Description of the pattern recognition clauses<a class="headerlink" href="pattern-recognition-in-window.html#description-of-the-pattern-recognition-clauses" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">frame_extent</span></code> with row pattern recognition must be defined in terms of
<code class="docutils literal notranslate"><span class="pre">ROWS</span></code>. The frame start must be at the <code class="docutils literal notranslate"><span class="pre">CURRENT</span> <span class="pre">ROW</span></code>, which limits the
allowed frame extent values to the following:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ROWS</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">CURRENT</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="k">CURRENT</span><span class="w"> </span><span class="k">ROW</span>

<span class="k">ROWS</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">CURRENT</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="o">&lt;</span><span class="n">expression</span><span class="o">&gt;</span><span class="w"> </span><span class="n">FOLLOWING</span>

<span class="k">ROWS</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">CURRENT</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">UNBOUNDED</span><span class="w"> </span><span class="n">FOLLOWING</span>
</pre></div>
</div>
<p>For every input row processed by the window, the portion of rows enclosed by
the <code class="docutils literal notranslate"><span class="pre">frame_extent</span></code> limits the search area for row pattern recognition. Unlike
in <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code>, where the pattern search can explore all rows until the
partition end, and all rows of the partition are available for computations, in
window structures the pattern matching can neither match rows nor retrieve
input values outside the frame.</p>
<p>Besides the <code class="docutils literal notranslate"><span class="pre">frame_extent</span></code>, pattern matching requires the <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> and
<code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clauses.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> clause specifies a row pattern, which is a form of a regular
expression with some syntactical extensions. The row pattern syntax is similar
to the <a class="reference internal" href="match-recognize.html#row-pattern-syntax"><span class="std std-ref">row pattern syntax in MATCH_RECOGNIZE</span></a>.
However, the anchor patterns <code class="docutils literal notranslate"><span class="pre">^</span></code> and <code class="docutils literal notranslate"><span class="pre">$</span></code> are not allowed in a window
specification.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause defines the row pattern primary variables in terms of
boolean conditions that must be satisfied. It is similar to the
<a class="reference internal" href="match-recognize.html#row-pattern-variable-definitions"><span class="std std-ref">DEFINE clause of MATCH_RECOGNIZE</span></a>.
The only difference is that the window syntax does not support the
<code class="docutils literal notranslate"><span class="pre">MATCH_NUMBER</span></code> function.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause is syntactically similar to the
<a class="reference internal" href="match-recognize.html#row-pattern-measures"><span class="std std-ref">MEASURES clause of MATCH_RECOGNIZE</span></a>. The only
limitation is that the <code class="docutils literal notranslate"><span class="pre">MATCH_NUMBER</span></code> function is not allowed. However, the
semantics of this clause differs between <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code> and window.
While in <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code> every measure produces an output column, the
measures in window should be considered as <strong>definitions</strong> associated with the
window structure. They can be called over the window, in the same manner as
regular window functions:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">cust_key</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="n">w</span><span class="p">,</span><span class="w"> </span><span class="n">label</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="n">w</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="n">orders</span>
<span class="w">    </span><span class="n">WINDOW</span><span class="w"> </span><span class="n">w</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                 </span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">cust_key</span>
<span class="w">                 </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">order_date</span>
<span class="w">                 </span><span class="n">MEASURES</span>
<span class="w">                        </span><span class="n">RUNNING</span><span class="w"> </span><span class="k">LAST</span><span class="p">(</span><span class="n">total_price</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">value</span><span class="p">,</span>
<span class="w">                        </span><span class="n">CLASSIFIER</span><span class="p">()</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">label</span>
<span class="w">                 </span><span class="k">ROWS</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">CURRENT</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">UNBOUNDED</span><span class="w"> </span><span class="n">FOLLOWING</span>
<span class="w">                 </span><span class="n">PATTERN</span><span class="w"> </span><span class="p">(</span><span class="n">A</span><span class="w"> </span><span class="n">B</span><span class="o">+</span><span class="w"> </span><span class="k">C</span><span class="o">+</span><span class="p">)</span>
<span class="w">                 </span><span class="n">DEFINE</span>
<span class="w">                        </span><span class="n">B</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">value</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">PREV</span><span class="w"> </span><span class="p">(</span><span class="n">B</span><span class="p">.</span><span class="n">value</span><span class="p">),</span>
<span class="w">                        </span><span class="k">C</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">C</span><span class="p">.</span><span class="n">value</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">PREV</span><span class="w"> </span><span class="p">(</span><span class="k">C</span><span class="p">.</span><span class="n">value</span><span class="p">)</span>
<span class="w">                </span><span class="p">)</span>
</pre></div>
</div>
<p>Measures defined in a window can be referenced in the <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> clause and in
the <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause of the enclosing query.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> and <code class="docutils literal notranslate"><span class="pre">FINAL</span></code> keywords are allowed in the <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause.
They can precede a logical navigation function <code class="docutils literal notranslate"><span class="pre">FIRST</span></code> or <code class="docutils literal notranslate"><span class="pre">LAST</span></code>, or an
aggregate function. However, they have no effect. Every computation is
performed from the position of the final row of the match, so the semantics is
effectively <code class="docutils literal notranslate"><span class="pre">FINAL</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> clause has the same syntax as the
<a class="reference internal" href="match-recognize.html#after-match-skip"><span class="std std-ref">AFTER MATCH SKIP clause of MATCH_RECOGNIZE</span></a>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">INITIAL</span></code> or <code class="docutils literal notranslate"><span class="pre">SEEK</span></code> modifier is specific to row pattern recognition in
window. With <code class="docutils literal notranslate"><span class="pre">INITIAL</span></code>, which is the default, the pattern match for an input
row can only be found starting from that row. With <code class="docutils literal notranslate"><span class="pre">SEEK</span></code>, if there is no
match starting from the current row, the engine tries to find a match starting
from subsequent rows within the frame. As a result, it is possible to associate
an input row with a match which is detached from that row.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">SUBSET</span></code> clause is used to define <a class="reference internal" href="match-recognize.html#row-pattern-union-variables"><span class="std std-ref">union variables</span></a> as sets of primary pattern variables. You can
use union variables to refer to a set of rows matched to any primary pattern
variable from the subset:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">SUBSET</span><span class="w"> </span><span class="n">U</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">A</span><span class="p">,</span><span class="w"> </span><span class="n">B</span><span class="p">)</span>
</pre></div>
</div>
<p>The following expression returns the <code class="docutils literal notranslate"><span class="pre">total_price</span></code> value from the last row
matched to either <code class="docutils literal notranslate"><span class="pre">A</span></code> or <code class="docutils literal notranslate"><span class="pre">B</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="n">U</span><span class="p">.</span><span class="n">total_price</span><span class="p">)</span>
</pre></div>
</div>
<p>If you want to refer to all rows of the match, there is no need to define a
<code class="docutils literal notranslate"><span class="pre">SUBSET</span></code> containing all pattern variables. There is an implicit <em>universal
pattern variable</em> applied to any non prefixed column name and any
<code class="docutils literal notranslate"><span class="pre">CLASSIFIER</span></code> call without an argument. The following expression returns the
<code class="docutils literal notranslate"><span class="pre">total_price</span></code> value from the last matched row:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="n">total_price</span><span class="p">)</span>
</pre></div>
</div>
<p>The following call returns the primary pattern variable of the first matched
row:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FIRST</span><span class="p">(</span><span class="n">CLASSIFIER</span><span class="p">())</span>
</pre></div>
</div>
<p>In window, unlike in <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code>, you cannot specify <code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code> or <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>. This is because all calls over window,
whether they are regular window functions or measures, must comply with the
window semantics. A call over window is supposed to produce exactly one output
row for every input row. And so, the output mode of pattern recognition in
window is a combination of <code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code> and <code class="docutils literal notranslate"><span class="pre">WITH</span> <span class="pre">UNMATCHED</span> <span class="pre">ROWS</span></code>.</p>
</section>
<section id="processing-input-with-row-pattern-recognition">
<h2 id="processing-input-with-row-pattern-recognition">Processing input with row pattern recognition<a class="headerlink" href="pattern-recognition-in-window.html#processing-input-with-row-pattern-recognition" title="Link to this heading">#</a></h2>
<p>Pattern recognition in window processes input rows in two different cases:</p>
<ul>
<li><p>upon a row pattern measure call over the window:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">some_measure</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="n">w</span>
</pre></div>
</div>
</li>
<li><p>upon a window function call over the window:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">sum</span><span class="p">(</span><span class="n">total_price</span><span class="p">)</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="n">w</span>
</pre></div>
</div>
</li>
</ul>
<p>The output row produced for each input row, consists of:</p>
<ul class="simple">
<li><p>all values from the input row</p></li>
<li><p>the value of the called measure or window function, computed with respect to
the pattern match associated with the row</p></li>
</ul>
<p>Processing the input can be described as the following sequence of steps:</p>
<ul>
<li><p>Partition the input data accordingly to <code class="docutils literal notranslate"><span class="pre">PARTITION</span> <span class="pre">BY</span></code></p></li>
<li><p>Order each partition by the <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> expressions</p></li>
<li><dl class="simple myst">
<dt>For every row of the ordered partition:</dt><dd><dl class="simple myst">
<dt>If the row is ‘skipped’ by a match of some previous row:</dt><dd><ul class="simple">
<li><p>For a measure, produce a one-row output as for an unmatched row</p></li>
<li><p>For a window function, evaluate the function over an empty frame
and produce a one-row output</p></li>
</ul>
</dd>
<dt>Otherwise:</dt><dd><ul class="simple">
<li><p>Determine the frame extent</p></li>
<li><p>Try match the row pattern starting from the current row within
the frame extent</p></li>
<li><p>If no match is found, and <code class="docutils literal notranslate"><span class="pre">SEEK</span></code> is specified, try to find a match
starting from subsequent rows within the frame extent</p></li>
</ul>
<dl class="simple myst">
<dt>If no match is found:</dt><dd><ul class="simple">
<li><p>For a measure, produce a one-row output for an unmatched row</p></li>
<li><p>For a window function, evaluate the function over an empty
frame and produce a one-row output</p></li>
</ul>
</dd>
<dt>Otherwise:</dt><dd><ul class="simple">
<li><p>For a measure, produce a one-row output for the match</p></li>
<li><p>For a window function, evaluate the function over a frame
limited to the matched rows sequence and produce a one-row
output</p></li>
<li><p>Evaluate the <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> clause, and mark the ‘skipped’
rows</p></li>
</ul>
</dd>
</dl>
</dd>
</dl>
</dd>
</dl>
</li>
</ul>
</section>
<section id="empty-matches-and-unmatched-rows">
<h2 id="empty-matches-and-unmatched-rows">Empty matches and unmatched rows<a class="headerlink" href="pattern-recognition-in-window.html#empty-matches-and-unmatched-rows" title="Link to this heading">#</a></h2>
<p>If no match can be associated with a particular input row, the row is
<em>unmatched</em>. This happens when no match can be found for the row. This also
happens when no match is attempted for the row, because it is skipped by the
<code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> clause of some preceding row. For an unmatched row,
every row pattern measure is <code class="docutils literal notranslate"><span class="pre">null</span></code>. Every window function is evaluated over
an empty frame.</p>
<p>An <em>empty match</em> is a successful match which does not involve any pattern
variables. In other words, an empty match does not contain any rows. If an
empty match is associated with an input row, every row pattern measure for that
row is evaluated over an empty sequence of rows. All navigation operations and
the <code class="docutils literal notranslate"><span class="pre">CLASSIFIER</span></code> function return <code class="docutils literal notranslate"><span class="pre">null</span></code>. Every window function is evaluated
over an empty frame.</p>
<p>In most cases, the results for empty matches and unmatched rows are the same.
A constant measure can be helpful to distinguish between them:</p>
<p>The following call returns <code class="docutils literal notranslate"><span class="pre">'matched'</span></code> for every matched row, including empty
matches, and <code class="docutils literal notranslate"><span class="pre">null</span></code> for every unmatched row:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">matched</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="p">(</span>
<span class="w">              </span><span class="p">...</span>
<span class="w">              </span><span class="n">MEASURES</span><span class="w"> </span><span class="s1">'matched'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">matched</span>
<span class="w">              </span><span class="p">...</span>
<span class="w">             </span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="values.html" title="VALUES"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> VALUES </span>
              </div>
            </a>
          
          
            <a href="../develop.html" title="Developer guide"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Developer guide </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>