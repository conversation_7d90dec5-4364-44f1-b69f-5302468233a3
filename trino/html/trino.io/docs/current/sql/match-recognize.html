<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>MATCH_RECOGNIZE &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="match-recognize.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="MERGE" href="merge.html" />
    <link rel="prev" title="INSERT" href="insert.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="match-recognize.html#sql/match-recognize" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> MATCH_RECOGNIZE </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="alter-materialized-view.html" class="md-nav__link">ALTER MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-schema.html" class="md-nav__link">ALTER SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-table.html" class="md-nav__link">ALTER TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-view.html" class="md-nav__link">ALTER VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="analyze.html" class="md-nav__link">ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="call.html" class="md-nav__link">CALL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comment.html" class="md-nav__link">COMMENT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="commit.html" class="md-nav__link">COMMIT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-catalog.html" class="md-nav__link">CREATE CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-function.html" class="md-nav__link">CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-materialized-view.html" class="md-nav__link">CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-role.html" class="md-nav__link">CREATE ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-schema.html" class="md-nav__link">CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table.html" class="md-nav__link">CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table-as.html" class="md-nav__link">CREATE TABLE AS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-view.html" class="md-nav__link">CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deallocate-prepare.html" class="md-nav__link">DEALLOCATE PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delete.html" class="md-nav__link">DELETE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deny.html" class="md-nav__link">DENY</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe.html" class="md-nav__link">DESCRIBE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-input.html" class="md-nav__link">DESCRIBE INPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-output.html" class="md-nav__link">DESCRIBE OUTPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-catalog.html" class="md-nav__link">DROP CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-function.html" class="md-nav__link">DROP FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-materialized-view.html" class="md-nav__link">DROP MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-role.html" class="md-nav__link">DROP ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-schema.html" class="md-nav__link">DROP SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-table.html" class="md-nav__link">DROP TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-view.html" class="md-nav__link">DROP VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute.html" class="md-nav__link">EXECUTE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute-immediate.html" class="md-nav__link">EXECUTE IMMEDIATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain.html" class="md-nav__link">EXPLAIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain-analyze.html" class="md-nav__link">EXPLAIN ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant.html" class="md-nav__link">GRANT privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant-roles.html" class="md-nav__link">GRANT role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">INSERT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> MATCH_RECOGNIZE </label>
    
      <a href="match-recognize.html#" class="md-nav__link md-nav__link--active">MATCH_RECOGNIZE</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="match-recognize.html#synopsis" class="md-nav__link">Synopsis</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#description" class="md-nav__link">Description</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#example" class="md-nav__link">Example</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partitioning-and-ordering" class="md-nav__link">Partitioning and ordering</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-measures" class="md-nav__link">Row pattern measures</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#rows-per-match" class="md-nav__link">Rows per match</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#after-match-skip" class="md-nav__link">After match skip</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-syntax" class="md-nav__link">Row pattern syntax</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#concatenation" class="md-nav__link">concatenation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#alternation" class="md-nav__link">alternation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#permutation" class="md-nav__link">permutation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#grouping" class="md-nav__link">grouping</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partition-start-anchor" class="md-nav__link">partition start anchor</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partition-end-anchor" class="md-nav__link">partition end anchor</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#empty-pattern" class="md-nav__link">empty pattern</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#exclusion-syntax" class="md-nav__link">exclusion syntax</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#quantifiers" class="md-nav__link">quantifiers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-union-variables" class="md-nav__link">Row pattern union variables</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-variable-definitions" class="md-nav__link">Row pattern variable definitions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-recognition-expressions" class="md-nav__link">Row pattern recognition expressions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#pattern-variable-references" class="md-nav__link">pattern variable references</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#classifier-function" class="md-nav__link">classifier function</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#match-number-function" class="md-nav__link">match_number function</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#logical-navigation-functions" class="md-nav__link">logical navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#physical-navigation-functions" class="md-nav__link">physical navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#nesting-of-navigation-functions" class="md-nav__link">nesting of navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#aggregate-functions" class="md-nav__link">Aggregate functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#aggregation-arguments" class="md-nav__link">Aggregation arguments</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#nesting-of-aggregate-functions" class="md-nav__link">Nesting of aggregate functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#usage-of-the-classifier-and-match-number-functions" class="md-nav__link">Usage of the <code class="docutils literal notranslate"><span class="pre">classifier</span></code> and <code class="docutils literal notranslate"><span class="pre">match_number</span></code> functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-count-aggregation" class="md-nav__link">Row pattern count aggregation</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#running-and-final-semantics" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> and <code class="docutils literal notranslate"><span class="pre">FINAL</span></code> semantics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#evaluating-expressions-in-empty-matches-and-unmatched-rows" class="md-nav__link">Evaluating expressions in empty matches and unmatched rows</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="merge.html" class="md-nav__link">MERGE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prepare.html" class="md-nav__link">PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="refresh-materialized-view.html" class="md-nav__link">REFRESH MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session.html" class="md-nav__link">RESET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session-authorization.html" class="md-nav__link">RESET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke.html" class="md-nav__link">REVOKE privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke-roles.html" class="md-nav__link">REVOKE role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="rollback.html" class="md-nav__link">ROLLBACK</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="select.html" class="md-nav__link">SELECT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-path.html" class="md-nav__link">SET PATH</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-role.html" class="md-nav__link">SET ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session.html" class="md-nav__link">SET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session-authorization.html" class="md-nav__link">SET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-time-zone.html" class="md-nav__link">SET TIME ZONE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-catalogs.html" class="md-nav__link">SHOW CATALOGS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-columns.html" class="md-nav__link">SHOW COLUMNS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-function.html" class="md-nav__link">SHOW CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-materialized-view.html" class="md-nav__link">SHOW CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-schema.html" class="md-nav__link">SHOW CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-table.html" class="md-nav__link">SHOW CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-view.html" class="md-nav__link">SHOW CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-functions.html" class="md-nav__link">SHOW FUNCTIONS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-grants.html" class="md-nav__link">SHOW GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-role-grants.html" class="md-nav__link">SHOW ROLE GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-roles.html" class="md-nav__link">SHOW ROLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-schemas.html" class="md-nav__link">SHOW SCHEMAS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-session.html" class="md-nav__link">SHOW SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-stats.html" class="md-nav__link">SHOW STATS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-tables.html" class="md-nav__link">SHOW TABLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="start-transaction.html" class="md-nav__link">START TRANSACTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="truncate.html" class="md-nav__link">TRUNCATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="update.html" class="md-nav__link">UPDATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="use.html" class="md-nav__link">USE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="values.html" class="md-nav__link">VALUES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pattern-recognition-in-window.html" class="md-nav__link">Row pattern recognition in window structures</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="match-recognize.html#synopsis" class="md-nav__link">Synopsis</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#description" class="md-nav__link">Description</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#example" class="md-nav__link">Example</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partitioning-and-ordering" class="md-nav__link">Partitioning and ordering</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-measures" class="md-nav__link">Row pattern measures</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#rows-per-match" class="md-nav__link">Rows per match</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#after-match-skip" class="md-nav__link">After match skip</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-syntax" class="md-nav__link">Row pattern syntax</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#concatenation" class="md-nav__link">concatenation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#alternation" class="md-nav__link">alternation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#permutation" class="md-nav__link">permutation</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#grouping" class="md-nav__link">grouping</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partition-start-anchor" class="md-nav__link">partition start anchor</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#partition-end-anchor" class="md-nav__link">partition end anchor</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#empty-pattern" class="md-nav__link">empty pattern</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#exclusion-syntax" class="md-nav__link">exclusion syntax</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#quantifiers" class="md-nav__link">quantifiers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-union-variables" class="md-nav__link">Row pattern union variables</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-variable-definitions" class="md-nav__link">Row pattern variable definitions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-recognition-expressions" class="md-nav__link">Row pattern recognition expressions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#pattern-variable-references" class="md-nav__link">pattern variable references</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#classifier-function" class="md-nav__link">classifier function</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#match-number-function" class="md-nav__link">match_number function</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#logical-navigation-functions" class="md-nav__link">logical navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#physical-navigation-functions" class="md-nav__link">physical navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#nesting-of-navigation-functions" class="md-nav__link">nesting of navigation functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#aggregate-functions" class="md-nav__link">Aggregate functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="match-recognize.html#aggregation-arguments" class="md-nav__link">Aggregation arguments</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#nesting-of-aggregate-functions" class="md-nav__link">Nesting of aggregate functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#usage-of-the-classifier-and-match-number-functions" class="md-nav__link">Usage of the <code class="docutils literal notranslate"><span class="pre">classifier</span></code> and <code class="docutils literal notranslate"><span class="pre">match_number</span></code> functions</a>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#row-pattern-count-aggregation" class="md-nav__link">Row pattern count aggregation</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#running-and-final-semantics" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> and <code class="docutils literal notranslate"><span class="pre">FINAL</span></code> semantics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="match-recognize.html#evaluating-expressions-in-empty-matches-and-unmatched-rows" class="md-nav__link">Evaluating expressions in empty matches and unmatched rows</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="match-recognize">
<h1 id="sql-match-recognize--page-root">MATCH_RECOGNIZE<a class="headerlink" href="match-recognize.html#sql-match-recognize--page-root" title="Link to this heading">#</a></h1>
<section id="synopsis">
<h2 id="synopsis">Synopsis<a class="headerlink" href="match-recognize.html#synopsis" title="Link to this heading">#</a></h2>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>MATCH_RECOGNIZE (
  [ PARTITION BY column [, ...] ]
  [ ORDER BY column [, ...] ]
  [ MEASURES measure_definition [, ...] ]
  [ rows_per_match ]
  [ AFTER MATCH skip_to ]
  PATTERN ( row_pattern )
  [ SUBSET subset_definition [, ...] ]
  DEFINE variable_definition [, ...]
  )
</pre></div>
</div>
</section>
<section id="description">
<h2 id="description">Description<a class="headerlink" href="match-recognize.html#description" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code> clause is an optional subclause of the <code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause.
It is used to detect patterns in a set of rows. Patterns of interest are
specified using row pattern syntax based on regular expressions. The input to
pattern matching is a table, a view or a subquery. For each detected match, one
or more rows are returned. They contain requested information about the match.</p>
<p>Row pattern matching is a powerful tool when analyzing complex sequences of
events. The following examples show some of the typical use cases:</p>
<ul class="simple">
<li><p>in trade applications, tracking trends or identifying customers with specific
behavioral patterns</p></li>
<li><p>in shipping applications, tracking packages through all possible valid paths,</p></li>
<li><p>in financial applications, detecting unusual incidents, which might signal
fraud</p></li>
</ul>
</section>
<section id="example">
<h2 id="example">Example<a class="headerlink" href="match-recognize.html#example" title="Link to this heading">#</a></h2>
<p>In the following example, the pattern describes a V-shape over the
<code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column. A match is found whenever orders made by a customer
first decrease in price, and then increase past the starting point:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="n">MATCH_RECOGNIZE</span><span class="p">(</span>
<span class="w">     </span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">custkey</span>
<span class="w">     </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">orderdate</span>
<span class="w">     </span><span class="n">MEASURES</span>
<span class="w">              </span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">starting_price</span><span class="p">,</span>
<span class="w">              </span><span class="k">LAST</span><span class="p">(</span><span class="n">B</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">bottom_price</span><span class="p">,</span>
<span class="w">              </span><span class="k">LAST</span><span class="p">(</span><span class="n">U</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">top_price</span>
<span class="w">     </span><span class="n">ONE</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span>
<span class="w">     </span><span class="k">AFTER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="n">SKIP</span><span class="w"> </span><span class="n">PAST</span><span class="w"> </span><span class="k">LAST</span><span class="w"> </span><span class="k">ROW</span>
<span class="w">     </span><span class="n">PATTERN</span><span class="w"> </span><span class="p">(</span><span class="n">A</span><span class="w"> </span><span class="n">B</span><span class="o">+</span><span class="w"> </span><span class="k">C</span><span class="o">+</span><span class="w"> </span><span class="n">D</span><span class="o">+</span><span class="p">)</span>
<span class="w">     </span><span class="n">SUBSET</span><span class="w"> </span><span class="n">U</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="k">C</span><span class="p">,</span><span class="w"> </span><span class="n">D</span><span class="p">)</span>
<span class="w">     </span><span class="n">DEFINE</span>
<span class="w">              </span><span class="n">B</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">PREV</span><span class="p">(</span><span class="n">totalprice</span><span class="p">),</span>
<span class="w">              </span><span class="k">C</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">PREV</span><span class="p">(</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span>
<span class="w">              </span><span class="n">D</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">PREV</span><span class="p">(</span><span class="n">totalprice</span><span class="p">)</span>
<span class="w">     </span><span class="p">)</span>
</pre></div>
</div>
<p>In the following sections, all subclauses of the <code class="docutils literal notranslate"><span class="pre">MATCH_RECOGNIZE</span></code> clause are
explained with this example query.</p>
</section>
<section id="partitioning-and-ordering">
<h2 id="partitioning-and-ordering">Partitioning and ordering<a class="headerlink" href="match-recognize.html#partitioning-and-ordering" title="Link to this heading">#</a></h2>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">custkey</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">PARTITION</span> <span class="pre">BY</span></code> clause allows you to break up the input table into
separate sections, that are independently processed for pattern matching.
Without a partition declaration, the whole input table is used. This behavior
is analogous to the semantics of <code class="docutils literal notranslate"><span class="pre">PARTITION</span> <span class="pre">BY</span></code> clause in <a class="reference internal" href="select.html#window-clause"><span class="std std-ref">window specification</span></a>. In the example, the <code class="docutils literal notranslate"><span class="pre">orders</span></code> table is
partitioned by the <code class="docutils literal notranslate"><span class="pre">custkey</span></code> value, so that pattern matching is performed for
all orders of a specific customer independently from orders of other
customers.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">orderdate</span>
</pre></div>
</div>
<p>The optional <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause is generally useful to allow matching on an
ordered data set. For example, sorting the input by <code class="docutils literal notranslate"><span class="pre">orderdate</span></code> allows for
matching on a trend of changes over time.</p>
</section>
<section id="row-pattern-measures">
<span id="id1"></span><h2 id="row-pattern-measures">Row pattern measures<a class="headerlink" href="match-recognize.html#row-pattern-measures" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause allows to specify what information is retrieved from a
matched sequence of rows.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>MEASURES measure_expression AS measure_name [, ...]
</pre></div>
</div>
<p>A measure expression is a scalar expression whose value is computed based on a
match. In the example, three row pattern measures are specified:</p>
<p><code class="docutils literal notranslate"><span class="pre">A.totalprice</span> <span class="pre">AS</span> <span class="pre">starting_price</span></code> returns the price in the first row of the
match, which is the only row associated with <code class="docutils literal notranslate"><span class="pre">A</span></code> according to the pattern.</p>
<p><code class="docutils literal notranslate"><span class="pre">LAST(B.totalprice)</span> <span class="pre">AS</span> <span class="pre">bottom_price</span></code> returns the lowest price (corresponding
to the bottom of the “V” in the pattern). It is the price in the last row
associated with <code class="docutils literal notranslate"><span class="pre">B</span></code>, which is the last row of the descending section.</p>
<p><code class="docutils literal notranslate"><span class="pre">LAST(U.totalprice)</span> <span class="pre">AS</span> <span class="pre">top_price</span></code> returns the highest price in the match. It
is the price in the last row associated with <code class="docutils literal notranslate"><span class="pre">C</span></code> or <code class="docutils literal notranslate"><span class="pre">D</span></code>, which is also the
final row of the match.</p>
<p>Measure expressions can refer to the columns of the input table. They also
allow special syntax to combine the input information with the details of the
match (see <a class="reference internal" href="match-recognize.html#pattern-recognition-expressions"><span class="std std-ref">Row pattern recognition expressions</span></a>).</p>
<p>Each measure defines an output column of the pattern recognition. The column
can be referenced with the <code class="docutils literal notranslate"><span class="pre">measure_name</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause is optional. When no measures are specified, certain
input columns (depending on <a class="reference internal" href="match-recognize.html#rows-per-match"><span class="std std-ref">ROWS PER MATCH</span></a> clause) are
the output of the pattern recognition.</p>
</section>
<section id="rows-per-match">
<span id="id2"></span><h2 id="rows-per-match">Rows per match<a class="headerlink" href="match-recognize.html#rows-per-match" title="Link to this heading">#</a></h2>
<p>This clause can be used to specify the quantity of output rows. There are two
main options:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">ONE</span><span class="w"> </span><span class="k">ROW</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span>
</pre></div>
</div>
<p>and</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALL</span><span class="w"> </span><span class="k">ROWS</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code> is the default option. For every match, a single row of
output is produced. Output consists of <code class="docutils literal notranslate"><span class="pre">PARTITION</span> <span class="pre">BY</span></code> columns and measures.
The output is also produced for empty matches, based on their starting rows.
Rows that are unmatched (that is, neither included in some non-empty match, nor
being the starting row of an empty match), are not included in the output.</p>
<p>For <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>, every row of a match produces an output row, unless
it is excluded from the output by the <a class="reference internal" href="match-recognize.html#exclusion-syntax"><span class="std std-ref">exclusion syntax</span></a>. Output consists
of <code class="docutils literal notranslate"><span class="pre">PARTITION</span> <span class="pre">BY</span></code> columns, <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> columns, measures and remaining
columns from the input table. By default, empty matches are shown and unmatched
rows are skipped, similarly as with the <code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code> option. However,
this behavior can be changed by modifiers:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALL</span><span class="w"> </span><span class="k">ROWS</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="k">SHOW</span><span class="w"> </span><span class="n">EMPTY</span><span class="w"> </span><span class="n">MATCHES</span>
</pre></div>
</div>
<p>shows empty matches and skips unmatched rows, like the default.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALL</span><span class="w"> </span><span class="k">ROWS</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="n">OMIT</span><span class="w"> </span><span class="n">EMPTY</span><span class="w"> </span><span class="n">MATCHES</span>
</pre></div>
</div>
<p>excludes empty matches from the output.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALL</span><span class="w"> </span><span class="k">ROWS</span><span class="w"> </span><span class="n">PER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="n">UNMATCHED</span><span class="w"> </span><span class="k">ROWS</span>
</pre></div>
</div>
<p>shows empty matches and produces additional output row for each unmatched row.</p>
<p>There are special rules for computing row pattern measures for empty matches
and unmatched rows. They are explained in
<a class="reference internal" href="match-recognize.html#empty-matches-and-unmatched-rows"><span class="std std-ref">Evaluating expressions in empty matches and unmatched rows</span></a>.</p>
<p>Unmatched rows can only occur when the pattern does not allow an empty match.
Otherwise, they are considered as starting rows of empty matches. The option
<code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span> <span class="pre">WITH</span> <span class="pre">UNMATCHED</span> <span class="pre">ROWS</span></code> is recommended when pattern
recognition is expected to pass all input rows, and it is not certain whether
the pattern allows an empty match.</p>
</section>
<section id="after-match-skip">
<span id="id3"></span><h2 id="after-match-skip">After match skip<a class="headerlink" href="match-recognize.html#after-match-skip" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> clause specifies where pattern matching resumes after
a non-empty match is found.</p>
<p>The default option is:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">AFTER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="n">SKIP</span><span class="w"> </span><span class="n">PAST</span><span class="w"> </span><span class="k">LAST</span><span class="w"> </span><span class="k">ROW</span>
</pre></div>
</div>
<p>With this option, pattern matching starts from the row after the last row of
the match. Overlapping matches are not detected.</p>
<p>With the following option, pattern matching starts from the second row of the
match:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">AFTER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="n">SKIP</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="k">NEXT</span><span class="w"> </span><span class="k">ROW</span>
</pre></div>
</div>
<p>In the example, if a V-shape is detected, further overlapping matches are
found, starting from consecutive rows on the descending slope of the “V”.
Skipping to the next row is the default behavior after detecting an empty match
or unmatched row.</p>
<p>The following <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> options allow to resume pattern matching
based on the components of the pattern. Pattern matching starts from the last
(default) or first row matched to a certain row pattern variable. It can be
either a primary pattern variable (they are explained in
<a class="reference internal" href="match-recognize.html#row-pattern-syntax"><span class="std std-ref">Row pattern syntax</span></a>) or a
<a class="reference internal" href="match-recognize.html#row-pattern-union-variables"><span class="std std-ref">union variable</span></a>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">AFTER</span><span class="w"> </span><span class="k">MATCH</span><span class="w"> </span><span class="n">SKIP</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="k">FIRST</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">LAST</span><span class="w"> </span><span class="p">]</span><span class="w"> </span><span class="n">pattern_variable</span>
</pre></div>
</div>
<p>It is forbidden to skip to the first row of the current match, because it
results in an infinite loop. For example specifying <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">TO</span> <span class="pre">A</span></code>
fails, because <code class="docutils literal notranslate"><span class="pre">A</span></code> is the first element of the pattern, and jumping back to
it creates an infinite loop. Similarly, skipping to a pattern variable which is
not present in the match causes failure.</p>
<p>All other options than the default <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">PAST</span> <span class="pre">LAST</span> <span class="pre">ROW</span></code> allow
detection of overlapping matches. The combination of <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span> <span class="pre">WITH</span> <span class="pre">UNMATCHED</span> <span class="pre">ROWS</span></code> with <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">PAST</span> <span class="pre">LAST</span> <span class="pre">ROW</span></code> is the only
configuration that guarantees exactly one output row for each input row.</p>
</section>
<section id="row-pattern-syntax">
<span id="id4"></span><h2 id="row-pattern-syntax">Row pattern syntax<a class="headerlink" href="match-recognize.html#row-pattern-syntax" title="Link to this heading">#</a></h2>
<p>Row pattern is a form of a regular expression with some syntactical extensions
specific to row pattern recognition. It is specified in the <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code>
clause:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">PATTERN</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="n">row_pattern</span><span class="w"> </span><span class="p">)</span>
</pre></div>
</div>
<p>The basic element of row pattern is a primary pattern variable. Like pattern
matching in character strings searches for characters, pattern matching in row
sequences searches for rows which can be “labeled” with certain primary pattern
variables. A primary pattern variable has a form of an identifier and is
<a class="reference internal" href="match-recognize.html#row-pattern-variable-definitions"><span class="std std-ref">defined</span></a> by a boolean condition. This
condition determines whether a particular input row can be mapped to this
variable and take part in the match.</p>
<p>In the example <code class="docutils literal notranslate"><span class="pre">PATTERN</span> <span class="pre">(A</span> <span class="pre">B+</span> <span class="pre">C+</span> <span class="pre">D+)</span></code>, there are four primary pattern
variables: <code class="docutils literal notranslate"><span class="pre">A</span></code>, <code class="docutils literal notranslate"><span class="pre">B</span></code>, <code class="docutils literal notranslate"><span class="pre">C</span></code>, and <code class="docutils literal notranslate"><span class="pre">D</span></code>.</p>
<p>Row pattern syntax includes the following usage:</p>
<section id="concatenation">
<h3 id="concatenation">concatenation<a class="headerlink" href="match-recognize.html#concatenation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>A B+ C+ D+
</pre></div>
</div>
<p>It is a sequence of components without operators between them. All components
are matched in the same order as they are specified.</p>
</section>
<section id="alternation">
<h3 id="alternation">alternation<a class="headerlink" href="match-recognize.html#alternation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>A | B | C
</pre></div>
</div>
<p>It is a sequence of components separated by <code class="docutils literal notranslate"><span class="pre">|</span></code>. Exactly one of the
components is matched. In case when multiple components can be matched, the
leftmost matching component is chosen.</p>
</section>
<section id="permutation">
<span id="permute-function"></span><h3 id="permutation">permutation<a class="headerlink" href="match-recognize.html#permutation" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PERMUTE(A, B, C)
</pre></div>
</div>
<p>It is equivalent to alternation of all permutations of its components. All
components are matched in some order. If multiple matches are possible for
different orderings of the components, the match is chosen based on the
lexicographical order established by the order of components in the <code class="docutils literal notranslate"><span class="pre">PERMUTE</span></code>
list. In the above example, the most preferred option is <code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">B</span> <span class="pre">C</span></code>, and the
least preferred option is <code class="docutils literal notranslate"><span class="pre">C</span> <span class="pre">B</span> <span class="pre">A</span></code>.</p>
</section>
<section id="grouping">
<h3 id="grouping">grouping<a class="headerlink" href="match-recognize.html#grouping" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>(A B C)
</pre></div>
</div>
</section>
<section id="partition-start-anchor">
<h3 id="partition-start-anchor">partition start anchor<a class="headerlink" href="match-recognize.html#partition-start-anchor" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>^
</pre></div>
</div>
</section>
<section id="partition-end-anchor">
<h3 id="partition-end-anchor">partition end anchor<a class="headerlink" href="match-recognize.html#partition-end-anchor" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$
</pre></div>
</div>
</section>
<section id="empty-pattern">
<h3 id="empty-pattern">empty pattern<a class="headerlink" href="match-recognize.html#empty-pattern" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>()
</pre></div>
</div>
</section>
<section id="exclusion-syntax">
<span id="id5"></span><h3 id="exclusion-syntax">exclusion syntax<a class="headerlink" href="match-recognize.html#exclusion-syntax" title="Link to this heading">#</a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{- row_pattern -}
</pre></div>
</div>
<p>Exclusion syntax is used to specify portions of the match to exclude from the
output. It is useful in combination with the <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code> option,
when only certain sections of the match are interesting.</p>
<p>If you change the example to use <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>, and the pattern is
modified to <code class="docutils literal notranslate"><span class="pre">PATTERN</span> <span class="pre">(A</span> <span class="pre">{-</span> <span class="pre">B+</span> <span class="pre">C+</span> <span class="pre">-}</span> <span class="pre">D+)</span></code>, the result consists of the initial
matched row and the trailing section of rows.</p>
<p>Specifying pattern exclusions does not affect the computation of expressions in
<code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> and <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clauses. Exclusions also do not affect pattern
matching. They have the same semantics as regular grouping with parentheses.</p>
<p>It is forbidden to specify pattern exclusions with the option <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span> <span class="pre">WITH</span> <span class="pre">UNMATCHED</span> <span class="pre">ROWS</span></code>.</p>
</section>
<section id="quantifiers">
<h3 id="quantifiers">quantifiers<a class="headerlink" href="match-recognize.html#quantifiers" title="Link to this heading">#</a></h3>
<p>Pattern quantifiers allow to specify the desired number of repetitions of a
sub-pattern in a match. They are appended after the relevant pattern
component:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">A</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="n">B</span><span class="p">)</span><span class="o">*</span>
</pre></div>
</div>
<p>There are following row pattern quantifiers:</p>
<ul class="simple">
<li><p>zero or more repetitions:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>*
</pre></div>
</div>
<ul class="simple">
<li><p>one or more repetitions:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>+
</pre></div>
</div>
<ul class="simple">
<li><p>zero or one repetition:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>?
</pre></div>
</div>
<ul class="simple">
<li><p>exact number of repetitions, specified by a non-negative integer number:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{n}
</pre></div>
</div>
<ul class="simple">
<li><p>number of repetitions ranging between bounds, specified by non-negative
integer numbers:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{m, n}
</pre></div>
</div>
<p>Specifying bounds is optional. If the left bound is omitted, it defaults to
<code class="docutils literal notranslate"><span class="pre">0</span></code>. So, <code class="docutils literal notranslate"><span class="pre">{,</span> <span class="pre">5}</span></code> can be described as “between zero and five repetitions”.
If the right bound is omitted, the number of accepted repetitions is unbounded.
So, <code class="docutils literal notranslate"><span class="pre">{5,</span> <span class="pre">}</span></code> can be described as “at least five repetitions”. Also, <code class="docutils literal notranslate"><span class="pre">{,}</span></code> is
equivalent to <code class="docutils literal notranslate"><span class="pre">*</span></code>.</p>
<p>Quantifiers are greedy by default. It means that higher number of repetitions
is preferred over lower number. This behavior can be changed to reluctant by
appending <code class="docutils literal notranslate"><span class="pre">?</span></code> immediately after the quantifier. With <code class="docutils literal notranslate"><span class="pre">{3,</span> <span class="pre">5}</span></code>, 3
repetitions is the least desired option and 5 repetitions – the most desired.
With <code class="docutils literal notranslate"><span class="pre">{3,</span> <span class="pre">5}?</span></code>, 3 repetitions are most desired. Similarly, <code class="docutils literal notranslate"><span class="pre">?</span></code> prefers 1
repetition, while <code class="docutils literal notranslate"><span class="pre">??</span></code> prefers 0 repetitions.</p>
</section>
</section>
<section id="row-pattern-union-variables">
<span id="id6"></span><h2 id="row-pattern-union-variables">Row pattern union variables<a class="headerlink" href="match-recognize.html#row-pattern-union-variables" title="Link to this heading">#</a></h2>
<p>As explained in <a class="reference internal" href="match-recognize.html#row-pattern-syntax"><span class="std std-ref">Row pattern syntax</span></a>, primary pattern variables are the
basic elements of row pattern. In addition to primary pattern variables, you
can define union variables. They are introduced in the <code class="docutils literal notranslate"><span class="pre">SUBSET</span></code> clause:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">SUBSET</span><span class="w"> </span><span class="n">U</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="k">C</span><span class="p">,</span><span class="w"> </span><span class="n">D</span><span class="p">),</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
<p>In the preceding example, union variable <code class="docutils literal notranslate"><span class="pre">U</span></code> is defined as union of primary
variables <code class="docutils literal notranslate"><span class="pre">C</span></code> and <code class="docutils literal notranslate"><span class="pre">D</span></code>. Union variables are useful in <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code>,
<code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> and <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span></code> clauses. They allow you to refer to set of
rows matched to either primary variable from a subset.</p>
<p>With the pattern: <code class="docutils literal notranslate"><span class="pre">PATTERN((A</span> <span class="pre">|</span> <span class="pre">B){5}</span> <span class="pre">C+)</span></code> it cannot be determined upfront if
the match contains any <code class="docutils literal notranslate"><span class="pre">A</span></code> or any <code class="docutils literal notranslate"><span class="pre">B</span></code>. A union variable can be used to
access the last row matched to either <code class="docutils literal notranslate"><span class="pre">A</span></code> or <code class="docutils literal notranslate"><span class="pre">B</span></code>. Define <code class="docutils literal notranslate"><span class="pre">SUBSET</span> <span class="pre">U</span> <span class="pre">=</span> <span class="pre">(A,</span> <span class="pre">B)</span></code>, and the expression <code class="docutils literal notranslate"><span class="pre">LAST(U.totalprice)</span></code> returns the value of the
<code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column from the last row mapped to either <code class="docutils literal notranslate"><span class="pre">A</span></code> or <code class="docutils literal notranslate"><span class="pre">B</span></code>. Also,
<code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">TO</span> <span class="pre">LAST</span> <span class="pre">A</span></code> or <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">TO</span> <span class="pre">LAST</span> <span class="pre">B</span></code> can result in
failure if <code class="docutils literal notranslate"><span class="pre">A</span></code> or <code class="docutils literal notranslate"><span class="pre">B</span></code> is not present in the match. <code class="docutils literal notranslate"><span class="pre">AFTER</span> <span class="pre">MATCH</span> <span class="pre">SKIP</span> <span class="pre">TO</span> <span class="pre">LAST</span> <span class="pre">U</span></code> does not fail.</p>
</section>
<section id="row-pattern-variable-definitions">
<span id="id7"></span><h2 id="row-pattern-variable-definitions">Row pattern variable definitions<a class="headerlink" href="match-recognize.html#row-pattern-variable-definitions" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause is where row pattern primary variables are defined. Each
variable is associated with a boolean condition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">DEFINE</span><span class="w"> </span><span class="n">B</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">PREV</span><span class="p">(</span><span class="n">totalprice</span><span class="p">),</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
<p>During pattern matching, when a certain variable is considered for the next
step of the match, the boolean condition is evaluated in context of the current
match. If the result is <code class="docutils literal notranslate"><span class="pre">true</span></code>, then the current row, “labeled” with the
variable, becomes part of the match.</p>
<p>In the preceding example, assume that the pattern allows to match <code class="docutils literal notranslate"><span class="pre">B</span></code> at some
point. There are some rows already matched to some pattern variables. Now,
variable <code class="docutils literal notranslate"><span class="pre">B</span></code> is being considered for the current row. Before the match is
made, the defining condition for <code class="docutils literal notranslate"><span class="pre">B</span></code> is evaluated. In this example, it is
only true if the value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column in the current row is lower
than <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> in the preceding row.</p>
<p>The mechanism of matching variables to rows shows the difference between
pattern matching in row sequences and regular expression matching in text. In
text, characters remain constantly in their positions. In row pattern matching,
a row can be mapped to different variables in different matches, depending on
the preceding part of the match, and even on the match number.</p>
<p>It is not required that every primary variable has a definition in the
<code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause. Variables not mentioned in the <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause are
implicitly associated with <code class="docutils literal notranslate"><span class="pre">true</span></code> condition, which means that they can be
matched to every row.</p>
<p>Boolean expressions in the <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause allow the same special syntax as
expressions in the <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause. Details are explained in
<a class="reference internal" href="match-recognize.html#pattern-recognition-expressions"><span class="std std-ref">Row pattern recognition expressions</span></a>.</p>
</section>
<section id="row-pattern-recognition-expressions">
<span id="pattern-recognition-expressions"></span><h2 id="row-pattern-recognition-expressions">Row pattern recognition expressions<a class="headerlink" href="match-recognize.html#row-pattern-recognition-expressions" title="Link to this heading">#</a></h2>
<p>Expressions in <a class="reference internal" href="match-recognize.html#row-pattern-measures"><span class="std std-ref">MEASURES</span></a> and
<a class="reference internal" href="match-recognize.html#row-pattern-variable-definitions"><span class="std std-ref">DEFINE</span></a> clauses are scalar expressions
evaluated over rows of the input table. They support special syntax, specific
to pattern recognition context. They can combine input information with the
information about the current match. Special syntax allows to access pattern
variables assigned to rows, browse rows based on how they are matched, and
refer to the sequential number of the match.</p>
<section id="pattern-variable-references">
<h3 id="pattern-variable-references">pattern variable references<a class="headerlink" href="match-recognize.html#pattern-variable-references" title="Link to this heading">#</a></h3>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span>

<span class="n">U</span><span class="p">.</span><span class="n">orderdate</span>

<span class="n">orderstatus</span>
</pre></div>
</div>
<p>A column name prefixed with a pattern variable refers to values of this column
in all rows matched to this variable, or to any variable from the subset in
case of union variable. If a column name is not prefixed, it is considered as
prefixed with the <code class="docutils literal notranslate"><span class="pre">universal</span> <span class="pre">pattern</span> <span class="pre">variable</span></code>, defined as union of all
primary pattern variables. In other words, a non-prefixed column name refers to
all rows of the current match.</p>
<p>It is forbidden to prefix a column name with a table name in the pattern
recognition context.</p>
</section>
<section id="classifier-function">
<span id="id8"></span><h3 id="classifier-function">classifier function<a class="headerlink" href="match-recognize.html#classifier-function" title="Link to this heading">#</a></h3>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">CLASSIFIER</span><span class="p">()</span>

<span class="n">CLASSIFIER</span><span class="p">(</span><span class="n">A</span><span class="p">)</span>

<span class="n">CLASSIFIER</span><span class="p">(</span><span class="n">U</span><span class="p">)</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">classifier</span></code> function returns the primary pattern variable associated
with the row. The return type is <code class="docutils literal notranslate"><span class="pre">varchar</span></code>. The optional argument is a
pattern variable. It limits the rows of interest, the same way as with prefixed
column references. The <code class="docutils literal notranslate"><span class="pre">classifier</span></code> function is particularly useful with a
union variable as the argument. It allows you to determine which variable from
the subset actually matched.</p>
</section>
<section id="match-number-function">
<span id="id9"></span><h3 id="match-number-function">match_number function<a class="headerlink" href="match-recognize.html#match-number-function" title="Link to this heading">#</a></h3>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">MATCH_NUMBER</span><span class="p">()</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">match_number</span></code> function returns the sequential number of the match within
partition, starting from <code class="docutils literal notranslate"><span class="pre">1</span></code>. Empty matches are assigned sequential numbers
as well as non-empty matches. The return type is <code class="docutils literal notranslate"><span class="pre">bigint</span></code>.</p>
</section>
<section id="logical-navigation-functions">
<span id="id10"></span><h3 id="logical-navigation-functions">logical navigation functions<a class="headerlink" href="match-recognize.html#logical-navigation-functions" title="Link to this heading">#</a></h3>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">FIRST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>In the above example, the <code class="docutils literal notranslate"><span class="pre">first</span></code> function navigates to the first row matched
to pattern variable <code class="docutils literal notranslate"><span class="pre">A</span></code>, and then searches forward until it finds two more
occurrences of variable <code class="docutils literal notranslate"><span class="pre">A</span></code> within the match. The result is the value of the
<code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column in that row.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>In the above example, the <code class="docutils literal notranslate"><span class="pre">last</span></code> function navigates to the last row matched
to pattern variable <code class="docutils literal notranslate"><span class="pre">A</span></code>, and then searches backwards until it finds two more
occurrences of variable <code class="docutils literal notranslate"><span class="pre">A</span></code> within the match. The result is the value of the
<code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column in that row.</p>
<p>With the <code class="docutils literal notranslate"><span class="pre">first</span></code> and <code class="docutils literal notranslate"><span class="pre">last</span></code> functions the result is <code class="docutils literal notranslate"><span class="pre">null</span></code>, if the
searched row is not found in the mach.</p>
<p>The second argument is optional. The default value is <code class="docutils literal notranslate"><span class="pre">0</span></code>, which means that
by default these functions navigate to the first or last row of interest. If
specified, the second argument must be a non-negative integer number.</p>
</section>
<section id="physical-navigation-functions">
<span id="id11"></span><h3 id="physical-navigation-functions">physical navigation functions<a class="headerlink" href="match-recognize.html#physical-navigation-functions" title="Link to this heading">#</a></h3>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">PREV</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>In the above example, the <code class="docutils literal notranslate"><span class="pre">prev</span></code> function navigates to the last row matched
to pattern variable <code class="docutils literal notranslate"><span class="pre">A</span></code>, and then searches two rows backward. The result is
the value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column in that row.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">NEXT</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>In the above example, the <code class="docutils literal notranslate"><span class="pre">next</span></code> function navigates to the last row matched
to pattern variable <code class="docutils literal notranslate"><span class="pre">A</span></code>, and then searches two rows forward. The result is
the value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column in that row.</p>
<p>With the <code class="docutils literal notranslate"><span class="pre">prev</span></code> and <code class="docutils literal notranslate"><span class="pre">next</span></code> functions, it is possible to navigate and
retrieve values outside the match. If the navigation goes beyond partition
bounds, the result is <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
<p>The second argument is optional. The default value is <code class="docutils literal notranslate"><span class="pre">1</span></code>, which means that
by default these functions navigate to previous or next row. If specified, the
second argument must be a non-negative integer number.</p>
</section>
<section id="nesting-of-navigation-functions">
<h3 id="nesting-of-navigation-functions">nesting of navigation functions<a class="headerlink" href="match-recognize.html#nesting-of-navigation-functions" title="Link to this heading">#</a></h3>
<p>It is possible to nest logical navigation functions within physical navigation
functions:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">PREV</span><span class="p">(</span><span class="k">FIRST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">),</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
</pre></div>
</div>
<p>In case of nesting, first the logical navigation is performed. It establishes
the starting row for the physical navigation. When both navigation operations
succeed, the value is retrieved from the designated row.</p>
<p>Pattern navigation functions require at least one column reference or
<code class="docutils literal notranslate"><span class="pre">classifier</span></code> function inside of their first argument. The following examples
are correct:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="ss">"pattern_variable_"</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">CLASSIFIER</span><span class="p">())</span>

<span class="k">NEXT</span><span class="p">(</span><span class="n">U</span><span class="p">.</span><span class="n">totalprice</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">10</span><span class="p">)</span>
</pre></div>
</div>
<p>This is incorrect:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>It is also required that all column references and all <code class="docutils literal notranslate"><span class="pre">classifier</span></code> calls
inside a pattern navigation function are consistent in referred pattern
variables. They must all refer either to the same primary variable, the same
union variable, or to the implicit universal pattern variable. The following
examples are correct:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="n">CLASSIFIER</span><span class="p">()</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">10</span><span class="p">)</span><span class="w"> </span><span class="cm">/* universal pattern variable */</span>

<span class="k">LAST</span><span class="p">(</span><span class="n">CLASSIFIER</span><span class="p">(</span><span class="n">U</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="n">U</span><span class="p">.</span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">10</span><span class="p">)</span><span class="w"> </span><span class="cm">/* pattern variable U */</span>
</pre></div>
</div>
<p>This is incorrect:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">LAST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="aggregate-functions">
<h3 id="aggregate-functions">Aggregate functions<a class="headerlink" href="match-recognize.html#aggregate-functions" title="Link to this heading">#</a></h3>
<p>It is allowed to use aggregate functions in a row pattern recognition context.
Aggregate functions are evaluated over all rows of the current match or over a
subset of rows based on the matched pattern variables. The
<a class="reference internal" href="match-recognize.html#running-and-final"><span class="std std-ref">running and final semantics</span></a> are supported, with
<code class="docutils literal notranslate"><span class="pre">running</span></code> as the default.</p>
<p>The following expression returns the average value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column
for all rows matched to pattern variable <code class="docutils literal notranslate"><span class="pre">A</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">avg</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>
</pre></div>
</div>
<p>The following expression returns the average value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column
for all rows matched to pattern variables from subset <code class="docutils literal notranslate"><span class="pre">U</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">avg</span><span class="p">(</span><span class="n">U</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>
</pre></div>
</div>
<p>The following expression returns the average value of the <code class="docutils literal notranslate"><span class="pre">totalprice</span></code> column
for all rows of the match:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">avg</span><span class="p">(</span><span class="n">totalprice</span><span class="p">)</span>
</pre></div>
</div>
<section id="aggregation-arguments">
<h4 id="aggregation-arguments">Aggregation arguments<a class="headerlink" href="match-recognize.html#aggregation-arguments" title="Link to this heading">#</a></h4>
<p>In case when the aggregate function has multiple arguments, it is required that
all arguments refer consistently to the same set of rows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">max_by</span><span class="p">(</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="n">tax</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows of the match */</span>

<span class="n">max_by</span><span class="p">(</span><span class="n">CLASSIFIER</span><span class="p">(</span><span class="n">A</span><span class="p">),</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">tax</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows matched to A */</span>
</pre></div>
</div>
<p>This is incorrect:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">max_by</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="n">tax</span><span class="p">)</span>

<span class="n">max_by</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">tax</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">B</span><span class="p">.</span><span class="n">tax</span><span class="p">)</span>
</pre></div>
</div>
<p>If an aggregate argument does not contain any column reference or
<code class="docutils literal notranslate"><span class="pre">classifier</span></code> function, it does not refer to any pattern variable. In such a
case other aggregate arguments determine the set of rows to aggregate over. If
none of the arguments contains a pattern variable reference, the universal row
pattern variable is implicit. This means that the aggregate function applies to
all rows of the match:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">count</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows of the match */</span>

<span class="n">min_by</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows of the match */</span>

<span class="n">min_by</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows of the match */</span>

<span class="n">min_by</span><span class="p">(</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows of the match */</span>

<span class="n">min_by</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows matched to A */</span>

<span class="n">max_by</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="cm">/* aggregate over all rows matched to A */</span>
</pre></div>
</div>
</section>
<section id="nesting-of-aggregate-functions">
<h4 id="nesting-of-aggregate-functions">Nesting of aggregate functions<a class="headerlink" href="match-recognize.html#nesting-of-aggregate-functions" title="Link to this heading">#</a></h4>
<p>Aggregate function arguments must not contain pattern navigation functions.
Similarly, aggregate functions cannot be nested in pattern navigation
functions.</p>
</section>
<section id="usage-of-the-classifier-and-match-number-functions">
<h4 id="usage-of-the-classifier-and-match-number-functions">Usage of the <code class="docutils literal notranslate"><span class="pre">classifier</span></code> and <code class="docutils literal notranslate"><span class="pre">match_number</span></code> functions<a class="headerlink" href="match-recognize.html#usage-of-the-classifier-and-match-number-functions" title="Link to this heading">#</a></h4>
<p>It is allowed to use the <code class="docutils literal notranslate"><span class="pre">classifier</span></code> and <code class="docutils literal notranslate"><span class="pre">match_number</span></code> functions in
aggregate function arguments. The following expression returns an array
containing all matched pattern variables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">array_agg</span><span class="p">(</span><span class="n">CLASSIFIER</span><span class="p">())</span>
</pre></div>
</div>
<p>This is particularly useful in combination with the option
<code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>. It allows to get all the components of the match while
keeping the output size reduced.</p>
</section>
<section id="row-pattern-count-aggregation">
<h4 id="row-pattern-count-aggregation">Row pattern count aggregation<a class="headerlink" href="match-recognize.html#row-pattern-count-aggregation" title="Link to this heading">#</a></h4>
<p>Like other aggregate functions in a row pattern recognition context, the
<code class="docutils literal notranslate"><span class="pre">count</span></code> function can be applied to all rows of the match, or to rows
associated with certain row pattern variables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">),</span><span class="w"> </span><span class="k">count</span><span class="p">()</span><span class="w"> </span><span class="cm">/* count all rows of the match */</span>

<span class="k">count</span><span class="p">(</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="cm">/* count non-null values of the totalprice column</span>
<span class="cm">                     in all rows of the match */</span>

<span class="k">count</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="cm">/* count non-null values of the totalprice column</span>
<span class="cm">                       in all rows matched to A */</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">count</span></code> function in a row pattern recognition context allows special syntax
to support the <code class="docutils literal notranslate"><span class="pre">count(*)</span></code> behavior over a limited set of rows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">count</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="o">*</span><span class="p">)</span><span class="w"> </span><span class="cm">/* count rows matched to A */</span>

<span class="k">count</span><span class="p">(</span><span class="n">U</span><span class="p">.</span><span class="o">*</span><span class="p">)</span><span class="w"> </span><span class="cm">/* count rows matched to pattern variables from subset U */</span>
</pre></div>
</div>
</section>
</section>
<section id="running-and-final-semantics">
<span id="running-and-final"></span><h3 id="running-and-final-semantics"><code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> and <code class="docutils literal notranslate"><span class="pre">FINAL</span></code> semantics<a class="headerlink" href="match-recognize.html#running-and-final-semantics" title="Link to this heading">#</a></h3>
<p>During pattern matching in a sequence of rows, one row after another is
examined to determine if it fits the pattern. At any step, a partial match is
known, but it is not yet known what rows will be added in the future or what
pattern variables they will be mapped to. So, when evaluating a boolean
condition in the <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clause for the current row, only the preceding part
of the match (plus the current row) is “visible”. This is the <code class="docutils literal notranslate"><span class="pre">running</span></code>
semantics.</p>
<p>When evaluating expressions in the <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause, the match is complete.
It is then possible to apply the <code class="docutils literal notranslate"><span class="pre">final</span></code> semantics. In the <code class="docutils literal notranslate"><span class="pre">final</span></code>
semantics, the whole match is “visible” as from the position of the final row.</p>
<p>In the <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause, the <code class="docutils literal notranslate"><span class="pre">running</span></code> semantics can also be applied. When
outputting information row by row (as in <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>), the
<code class="docutils literal notranslate"><span class="pre">running</span></code> semantics evaluate expressions from the positions of consecutive
rows.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">running</span></code> and <code class="docutils literal notranslate"><span class="pre">final</span></code> semantics are denoted by the keywords:
<code class="docutils literal notranslate"><span class="pre">RUNNING</span></code> and <code class="docutils literal notranslate"><span class="pre">FINAL</span></code>, preceding a logical navigation function <code class="docutils literal notranslate"><span class="pre">first</span></code> or
<code class="docutils literal notranslate"><span class="pre">last</span></code>, or an aggregate function:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">RUNNING</span><span class="w"> </span><span class="k">LAST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>

<span class="k">FINAL</span><span class="w"> </span><span class="k">LAST</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>

<span class="n">RUNNING</span><span class="w"> </span><span class="k">avg</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="n">totalprice</span><span class="p">)</span>

<span class="k">FINAL</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="n">A</span><span class="p">.</span><span class="o">*</span><span class="p">)</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">running</span></code> semantics is default in <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> and <code class="docutils literal notranslate"><span class="pre">DEFINE</span></code> clauses.
<code class="docutils literal notranslate"><span class="pre">FINAL</span></code> can only be specified in the <code class="docutils literal notranslate"><span class="pre">MEASURES</span></code> clause.</p>
<p>With the option <code class="docutils literal notranslate"><span class="pre">ONE</span> <span class="pre">ROW</span> <span class="pre">PER</span> <span class="pre">MATCH</span></code>, row pattern measures are evaluated from
the position of the final row in the match. Therefore, <code class="docutils literal notranslate"><span class="pre">running</span></code> and
<code class="docutils literal notranslate"><span class="pre">final</span></code> semantics are the same.</p>
</section>
</section>
<section id="evaluating-expressions-in-empty-matches-and-unmatched-rows">
<span id="empty-matches-and-unmatched-rows"></span><h2 id="evaluating-expressions-in-empty-matches-and-unmatched-rows">Evaluating expressions in empty matches and unmatched rows<a class="headerlink" href="match-recognize.html#evaluating-expressions-in-empty-matches-and-unmatched-rows" title="Link to this heading">#</a></h2>
<p>An empty match occurs when the row pattern is successfully matched, but no
pattern variables are assigned. The following pattern produces an empty match
for every row:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">PATTERN</span><span class="p">(())</span>
</pre></div>
</div>
<p>When evaluating row pattern measures for an empty match:</p>
<ul class="simple">
<li><p>all column references return <code class="docutils literal notranslate"><span class="pre">null</span></code></p></li>
<li><p>all navigation operations return <code class="docutils literal notranslate"><span class="pre">null</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">classifier</span></code> function returns <code class="docutils literal notranslate"><span class="pre">null</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">match_number</span></code> function returns the sequential number of the match</p></li>
<li><p>all aggregate functions are evaluated over an empty set of rows</p></li>
</ul>
<p>Like every match, an empty match has its starting row. All input values which
are to be output along with the measures (as explained in
<a class="reference internal" href="match-recognize.html#rows-per-match"><span class="std std-ref">Rows per match</span></a>), are the values from the starting row.</p>
<p>An unmatched row is a row that is neither part of any non-empty match nor the
starting row of an empty match. With the option <code class="docutils literal notranslate"><span class="pre">ALL</span> <span class="pre">ROWS</span> <span class="pre">PER</span> <span class="pre">MATCH</span> <span class="pre">WITH</span> <span class="pre">UNMATCHED</span> <span class="pre">ROWS</span></code>, a single output row is produced. In that row, all row pattern
measures are <code class="docutils literal notranslate"><span class="pre">null</span></code>. All input values which are to be output along with the
measures (as explained in <a class="reference internal" href="match-recognize.html#rows-per-match"><span class="std std-ref">Rows per match</span></a>), are the values from the
unmatched row. Using the <code class="docutils literal notranslate"><span class="pre">match_number</span></code> function as a measure can help
differentiate between an empty match and unmatched row.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="insert.html" title="INSERT"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> INSERT </span>
              </div>
            </a>
          
          
            <a href="merge.html" title="MERGE"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> MERGE </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>