<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>ALTER TABLE &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="alter-table.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="ALTER VIEW" href="alter-view.html" />
    <link rel="prev" title="ALTER SCHEMA" href="alter-schema.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="alter-table.html#sql/alter-table" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> ALTER TABLE </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="alter-materialized-view.html" class="md-nav__link">ALTER MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-schema.html" class="md-nav__link">ALTER SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> ALTER TABLE </label>
    
      <a href="alter-table.html#" class="md-nav__link md-nav__link--active">ALTER TABLE</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="alter-table.html#synopsis" class="md-nav__link">Synopsis</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#description" class="md-nav__link">Description</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="alter-table.html#set-properties" class="md-nav__link">SET PROPERTIES</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#execute" class="md-nav__link">EXECUTE</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#examples" class="md-nav__link">Examples</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#see-also" class="md-nav__link">See also</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="alter-view.html" class="md-nav__link">ALTER VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="analyze.html" class="md-nav__link">ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="call.html" class="md-nav__link">CALL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comment.html" class="md-nav__link">COMMENT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="commit.html" class="md-nav__link">COMMIT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-catalog.html" class="md-nav__link">CREATE CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-function.html" class="md-nav__link">CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-materialized-view.html" class="md-nav__link">CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-role.html" class="md-nav__link">CREATE ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-schema.html" class="md-nav__link">CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table.html" class="md-nav__link">CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-table-as.html" class="md-nav__link">CREATE TABLE AS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="create-view.html" class="md-nav__link">CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deallocate-prepare.html" class="md-nav__link">DEALLOCATE PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delete.html" class="md-nav__link">DELETE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="deny.html" class="md-nav__link">DENY</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe.html" class="md-nav__link">DESCRIBE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-input.html" class="md-nav__link">DESCRIBE INPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="describe-output.html" class="md-nav__link">DESCRIBE OUTPUT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-catalog.html" class="md-nav__link">DROP CATALOG</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-function.html" class="md-nav__link">DROP FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-materialized-view.html" class="md-nav__link">DROP MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-role.html" class="md-nav__link">DROP ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-schema.html" class="md-nav__link">DROP SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-table.html" class="md-nav__link">DROP TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="drop-view.html" class="md-nav__link">DROP VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute.html" class="md-nav__link">EXECUTE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="execute-immediate.html" class="md-nav__link">EXECUTE IMMEDIATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain.html" class="md-nav__link">EXPLAIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="explain-analyze.html" class="md-nav__link">EXPLAIN ANALYZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant.html" class="md-nav__link">GRANT privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="grant-roles.html" class="md-nav__link">GRANT role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">INSERT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="match-recognize.html" class="md-nav__link">MATCH_RECOGNIZE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="merge.html" class="md-nav__link">MERGE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prepare.html" class="md-nav__link">PREPARE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="refresh-materialized-view.html" class="md-nav__link">REFRESH MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session.html" class="md-nav__link">RESET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="reset-session-authorization.html" class="md-nav__link">RESET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke.html" class="md-nav__link">REVOKE privilege</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="revoke-roles.html" class="md-nav__link">REVOKE role</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="rollback.html" class="md-nav__link">ROLLBACK</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="select.html" class="md-nav__link">SELECT</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-path.html" class="md-nav__link">SET PATH</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-role.html" class="md-nav__link">SET ROLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session.html" class="md-nav__link">SET SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-session-authorization.html" class="md-nav__link">SET SESSION AUTHORIZATION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="set-time-zone.html" class="md-nav__link">SET TIME ZONE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-catalogs.html" class="md-nav__link">SHOW CATALOGS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-columns.html" class="md-nav__link">SHOW COLUMNS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-function.html" class="md-nav__link">SHOW CREATE FUNCTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-materialized-view.html" class="md-nav__link">SHOW CREATE MATERIALIZED VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-schema.html" class="md-nav__link">SHOW CREATE SCHEMA</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-table.html" class="md-nav__link">SHOW CREATE TABLE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-create-view.html" class="md-nav__link">SHOW CREATE VIEW</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-functions.html" class="md-nav__link">SHOW FUNCTIONS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-grants.html" class="md-nav__link">SHOW GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-role-grants.html" class="md-nav__link">SHOW ROLE GRANTS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-roles.html" class="md-nav__link">SHOW ROLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-schemas.html" class="md-nav__link">SHOW SCHEMAS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-session.html" class="md-nav__link">SHOW SESSION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-stats.html" class="md-nav__link">SHOW STATS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="show-tables.html" class="md-nav__link">SHOW TABLES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="start-transaction.html" class="md-nav__link">START TRANSACTION</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="truncate.html" class="md-nav__link">TRUNCATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="update.html" class="md-nav__link">UPDATE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="use.html" class="md-nav__link">USE</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="values.html" class="md-nav__link">VALUES</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pattern-recognition-in-window.html" class="md-nav__link">Row pattern recognition in window structures</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="alter-table.html#synopsis" class="md-nav__link">Synopsis</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#description" class="md-nav__link">Description</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="alter-table.html#set-properties" class="md-nav__link">SET PROPERTIES</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#execute" class="md-nav__link">EXECUTE</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#examples" class="md-nav__link">Examples</a>
        </li>
        <li class="md-nav__item"><a href="alter-table.html#see-also" class="md-nav__link">See also</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="alter-table">
<h1 id="sql-alter-table--page-root">ALTER TABLE<a class="headerlink" href="alter-table.html#sql-alter-table--page-root" title="Link to this heading">#</a></h1>
<section id="synopsis">
<h2 id="synopsis">Synopsis<a class="headerlink" href="alter-table.html#synopsis" title="Link to this heading">#</a></h2>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ALTER TABLE [ IF EXISTS ] name RENAME TO new_name
ALTER TABLE [ IF EXISTS ] name ADD COLUMN [ IF NOT EXISTS ] column_name data_type
  [ NOT NULL ] [ COMMENT comment ]
  [ WITH ( property_name = expression [, ...] ) ]
  [ FIRST | LAST | AFTER after_column_name ]
ALTER TABLE [ IF EXISTS ] name DROP COLUMN [ IF EXISTS ] column_name
ALTER TABLE [ IF EXISTS ] name RENAME COLUMN [ IF EXISTS ] old_name TO new_name
ALTER TABLE [ IF EXISTS ] name ALTER COLUMN column_name SET DATA TYPE new_type
ALTER TABLE [ IF EXISTS ] name ALTER COLUMN column_name DROP NOT NULL
ALTER TABLE name SET AUTHORIZATION ( user | USER user | ROLE role )
ALTER TABLE name SET PROPERTIES property_name = expression [, ...]
ALTER TABLE name EXECUTE command [ ( parameter =&gt; expression [, ... ] ) ]
    [ WHERE expression ]
</pre></div>
</div>
</section>
<section id="description">
<h2 id="description">Description<a class="headerlink" href="alter-table.html#description" title="Link to this heading">#</a></h2>
<p>Change the definition of an existing table.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">IF</span> <span class="pre">EXISTS</span></code> clause, when used before the table name, causes the
error to be suppressed if the table does not exist.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">IF</span> <span class="pre">EXISTS</span></code> clause, when used before the column name, causes the
error to be suppressed if the column does not exist.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">IF</span> <span class="pre">NOT</span> <span class="pre">EXISTS</span></code> clause causes the error to be suppressed if the
column already exists.</p>
<section id="set-properties">
<span id="alter-table-set-properties"></span><h3 id="set-properties">SET PROPERTIES<a class="headerlink" href="alter-table.html#set-properties" title="Link to this heading">#</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">SET</span> <span class="pre">PROPERTIES</span></code>  statement followed by a number of
<code class="docutils literal notranslate"><span class="pre">property_name</span></code> and <code class="docutils literal notranslate"><span class="pre">expression</span></code> pairs applies the specified properties and
values to a table. Omitting an already-set property from this statement leaves
that property unchanged in the table.</p>
<p>A property in a <code class="docutils literal notranslate"><span class="pre">SET</span> <span class="pre">PROPERTIES</span></code> statement can be set to <code class="docutils literal notranslate"><span class="pre">DEFAULT</span></code>, which
reverts its value back to the default in that table.</p>
<p>Support for <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">SET</span> <span class="pre">PROPERTIES</span></code> varies between
connectors, as not all connectors support modifying table properties.</p>
</section>
<section id="execute">
<span id="alter-table-execute"></span><h3 id="execute">EXECUTE<a class="headerlink" href="alter-table.html#execute" title="Link to this heading">#</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">EXECUTE</span></code> statement followed by a <code class="docutils literal notranslate"><span class="pre">command</span></code> and
<code class="docutils literal notranslate"><span class="pre">parameters</span></code> modifies the table according to the specified command and
parameters. <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">EXECUTE</span></code> supports different commands on a
per-connector basis.</p>
<p>You can use the <code class="docutils literal notranslate"><span class="pre">=&gt;</span></code> operator for passing named parameter values. The left side
is the name of the parameter, the right side is the value being passed.</p>
<p>Executable commands are contributed by connectors, such as the <code class="docutils literal notranslate"><span class="pre">optimize</span></code>
command provided by the <a class="reference internal" href="../connector/hive.html#hive-alter-table-execute"><span class="std std-ref">Hive</span></a>, <a class="reference internal" href="../connector/delta-lake.html#delta-lake-alter-table-execute"><span class="std std-ref">Delta
Lake</span></a>, and
<a class="reference internal" href="../connector/iceberg.html#iceberg-alter-table-execute"><span class="std std-ref">Iceberg</span></a> connectors. For example, a user observing
many small files in the storage of a table called <code class="docutils literal notranslate"><span class="pre">test_table</span></code> in the <code class="docutils literal notranslate"><span class="pre">test</span></code>
schema of the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog, can use the <code class="docutils literal notranslate"><span class="pre">optimize</span></code> command to merge all
files below the <code class="docutils literal notranslate"><span class="pre">file_size_threshold</span></code> value. The result is fewer, but larger
files, which typically results in higher query performance on the data in the
files:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">test</span><span class="p">.</span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span><span class="p">(</span><span class="n">file_size_threshold</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'16MB'</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="examples">
<h2 id="examples">Examples<a class="headerlink" href="alter-table.html#examples" title="Link to this heading">#</a></h2>
<p>Rename table <code class="docutils literal notranslate"><span class="pre">users</span></code> to <code class="docutils literal notranslate"><span class="pre">people</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="n">people</span><span class="p">;</span>
</pre></div>
</div>
<p>Rename table <code class="docutils literal notranslate"><span class="pre">users</span></code> to <code class="docutils literal notranslate"><span class="pre">people</span></code> if table <code class="docutils literal notranslate"><span class="pre">users</span></code> exists:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="n">people</span><span class="p">;</span>
</pre></div>
</div>
<p>Add column <code class="docutils literal notranslate"><span class="pre">zip</span></code> to the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ADD</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">zip</span><span class="w"> </span><span class="nb">varchar</span><span class="p">;</span>
</pre></div>
</div>
<p>Add column <code class="docutils literal notranslate"><span class="pre">zip</span></code> to the <code class="docutils literal notranslate"><span class="pre">users</span></code> table if table <code class="docutils literal notranslate"><span class="pre">users</span></code> exists and column <code class="docutils literal notranslate"><span class="pre">zip</span></code>
not already exists:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ADD</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">zip</span><span class="w"> </span><span class="nb">varchar</span><span class="p">;</span>
</pre></div>
</div>
<p>Add column <code class="docutils literal notranslate"><span class="pre">id</span></code> as the first column to the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ADD</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">id</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="k">FIRST</span><span class="p">;</span>
</pre></div>
</div>
<p>Add column <code class="docutils literal notranslate"><span class="pre">zip</span></code> after column <code class="docutils literal notranslate"><span class="pre">country</span></code> to the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ADD</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">zip</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="k">AFTER</span><span class="w"> </span><span class="n">country</span><span class="p">;</span>
</pre></div>
</div>
<p>Drop column <code class="docutils literal notranslate"><span class="pre">zip</span></code> from the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">DROP</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">zip</span><span class="p">;</span>
</pre></div>
</div>
<p>Drop column <code class="docutils literal notranslate"><span class="pre">zip</span></code> from the <code class="docutils literal notranslate"><span class="pre">users</span></code> table if table <code class="docutils literal notranslate"><span class="pre">users</span></code> and column <code class="docutils literal notranslate"><span class="pre">zip</span></code>
exists:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">DROP</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">zip</span><span class="p">;</span>
</pre></div>
</div>
<p>Rename column <code class="docutils literal notranslate"><span class="pre">id</span></code> to <code class="docutils literal notranslate"><span class="pre">user_id</span></code> in the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">id</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="n">user_id</span><span class="p">;</span>
</pre></div>
</div>
<p>Rename column <code class="docutils literal notranslate"><span class="pre">id</span></code> to <code class="docutils literal notranslate"><span class="pre">user_id</span></code> in the <code class="docutils literal notranslate"><span class="pre">users</span></code> table if table <code class="docutils literal notranslate"><span class="pre">users</span></code> and column
<code class="docutils literal notranslate"><span class="pre">id</span></code> exists:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">column</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">id</span><span class="w"> </span><span class="k">to</span><span class="w"> </span><span class="n">user_id</span><span class="p">;</span>
</pre></div>
</div>
<p>Change type of column <code class="docutils literal notranslate"><span class="pre">id</span></code> to <code class="docutils literal notranslate"><span class="pre">bigint</span></code> in the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ALTER</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">id</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">DATA</span><span class="w"> </span><span class="k">TYPE</span><span class="w"> </span><span class="nb">bigint</span><span class="p">;</span>
</pre></div>
</div>
<p>Drop a not null constraint on <code class="docutils literal notranslate"><span class="pre">id</span></code> column in the <code class="docutils literal notranslate"><span class="pre">users</span></code> table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="k">ALTER</span><span class="w"> </span><span class="k">COLUMN</span><span class="w"> </span><span class="n">id</span><span class="w"> </span><span class="k">DROP</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
</pre></div>
</div>
<p>Change owner of table <code class="docutils literal notranslate"><span class="pre">people</span></code> to user <code class="docutils literal notranslate"><span class="pre">alice</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">people</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">AUTHORIZATION</span><span class="w"> </span><span class="n">alice</span>
</pre></div>
</div>
<p>Allow everyone with role public to drop and alter table <code class="docutils literal notranslate"><span class="pre">people</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">people</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">AUTHORIZATION</span><span class="w"> </span><span class="k">ROLE</span><span class="w"> </span><span class="k">PUBLIC</span>
</pre></div>
</div>
<p>Set table properties (<code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">=</span> <span class="pre">y</span></code>) in table <code class="docutils literal notranslate"><span class="pre">people</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">people</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">PROPERTIES</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'y'</span><span class="p">;</span>
</pre></div>
</div>
<p>Set multiple table properties (<code class="docutils literal notranslate"><span class="pre">foo</span> <span class="pre">=</span> <span class="pre">123</span></code> and <code class="docutils literal notranslate"><span class="pre">foo</span> <span class="pre">bar</span> <span class="pre">=</span> <span class="pre">456</span></code>) in
table <code class="docutils literal notranslate"><span class="pre">people</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">people</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">PROPERTIES</span><span class="w"> </span><span class="n">foo</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span><span class="w"> </span><span class="ss">"foo bar"</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">456</span><span class="p">;</span>
</pre></div>
</div>
<p>Set table property <code class="docutils literal notranslate"><span class="pre">x</span></code> to its default value in table``people``:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">people</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">PROPERTIES</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">DEFAULT</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="see-also">
<h2 id="see-also">See also<a class="headerlink" href="alter-table.html#see-also" title="Link to this heading">#</a></h2>
<p><a class="reference internal" href="create-table.html"><span class="doc">CREATE TABLE</span></a></p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="alter-schema.html" title="ALTER SCHEMA"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> ALTER SCHEMA </span>
              </div>
            </a>
          
          
            <a href="alter-view.html" title="ALTER VIEW"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> ALTER VIEW </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>