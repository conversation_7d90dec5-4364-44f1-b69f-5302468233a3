<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Trino documentation &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="_static/trino.css@v=b5fc78e7.css" />
    <script src="_static/jquery.js@v=5d32c60e"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="_static/documentation_options.js@v=febf07ea"></script>
    <script src="_static/doctools.js@v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="index.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Overview" href="overview.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="index.html#index" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Trino documentation </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../versions.json",
        target_loc = "../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="index.html#"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <ul class="md-nav__list" data-md-scrollfix="">
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="trino-documentation">
<h1 id="index--page-root">Trino documentation<a class="headerlink" href="index.html#index--page-root" title="Link to this heading">#</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="overview.html">Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="overview/use-cases.html">Use cases</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/concepts.html">Trino concepts</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation/deployment.html">Deploying Trino</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/containers.html">Trino in a Docker container</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/kubernetes.html">Trino on Kubernetes with Helm</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/plugins.html">Plugins</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/query-resiliency.html">Improve query processing resilience</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="client.html">Clients</a><ul>
<li class="toctree-l2"><a class="reference internal" href="client/client-protocol.html">Client protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="client/cli.html">Command line interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="client/jdbc.html">JDBC driver</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security</a><ul>
<li class="toctree-l2"><a class="reference internal" href="security/overview.html">Security overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/tls.html">TLS and HTTPS</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/inspect-pem.html">PEM files</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/inspect-jks.html">JKS files</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/authentication-types.html">Authentication types</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/password-file.html">Password file authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/ldap.html">LDAP authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/salesforce.html">Salesforce authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/oauth2.html">OAuth 2.0 authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/kerberos.html">Kerberos authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/certificate.html">Certificate authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/jwt.html">JWT authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/user-mapping.html">User mapping</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/group-file.html">File group provider</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/built-in-system-access-control.html">System access control</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/file-system-access-control.html">File-based access control</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/opa-access-control.html">Open Policy Agent access control</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/ranger-access-control.html">Ranger access control</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/internal-communication.html">Secure internal communication</a></li>
<li class="toctree-l2"><a class="reference internal" href="security/secrets.html">Secrets</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="admin.html">Administration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="admin/web-interface.html">Web UI</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/preview-web-interface.html">Preview Web UI</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/logging.html">Logging</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/tuning.html">Tuning Trino</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/jmx.html">Monitoring with JMX</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/opentelemetry.html">Observability with OpenTelemetry</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/openmetrics.html">Trino metrics with OpenMetrics</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/properties.html">Properties reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-general.html">General</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-client-protocol.html">Client protocol</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-http-server.html">HTTP server</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-resource-management.html">Resource management</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-query-management.html">Query management</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-catalog.html">Catalog management</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-sql-environment.html">SQL environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-spilling.html">Spilling</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-exchange.html">Exchange</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-task.html">Task</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-write-partitioning.html">Write partitioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-writer-scaling.html">Writer scaling</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-node-scheduler.html">Node scheduler</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-optimizer.html">Optimizer</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-logging.html">Logging</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-web-interface.html">Web UI</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-regexp-function.html">Regular expression function</a></li>
<li class="toctree-l3"><a class="reference internal" href="admin/properties-http-client.html">HTTP client</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="admin/spill.html">Spill to disk</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/resource-groups.html">Resource groups</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/session-property-managers.html">Session property managers</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/dist-sort.html">Distributed sort</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/dynamic-filtering.html">Dynamic filtering</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/graceful-shutdown.html">Graceful shutdown</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/fault-tolerant-execution.html">Fault-tolerant execution</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/event-listeners-http.html">HTTP event listener</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/event-listeners-kafka.html">Kafka event listener</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/event-listeners-mysql.html">MySQL event listener</a></li>
<li class="toctree-l2"><a class="reference internal" href="admin/event-listeners-openlineage.html">OpenLineage event listener</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="optimizer.html">Query optimizer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="optimizer/statistics.html">Table statistics</a></li>
<li class="toctree-l2"><a class="reference internal" href="optimizer/cost-in-explain.html">Cost in EXPLAIN</a></li>
<li class="toctree-l2"><a class="reference internal" href="optimizer/cost-based-optimizations.html">Cost-based optimizations</a></li>
<li class="toctree-l2"><a class="reference internal" href="optimizer/pushdown.html">Pushdown</a></li>
<li class="toctree-l2"><a class="reference internal" href="optimizer/adaptive-plan-optimizations.html">Adaptive plan optimizations</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="connector.html">Connectors</a><ul>
<li class="toctree-l2"><a class="reference internal" href="connector/bigquery.html">BigQuery</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/blackhole.html">Black Hole</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/cassandra.html">Cassandra</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/clickhouse.html">ClickHouse</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/delta-lake.html">Delta Lake</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/druid.html">Druid</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/duckdb.html">DuckDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/elasticsearch.html">Elasticsearch</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/exasol.html">Exasol</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/faker.html">Faker</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/googlesheets.html">Google Sheets</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/hive.html">Hive</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/hudi.html">Hudi</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/iceberg.html">Iceberg</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/ignite.html">Ignite</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/jmx.html">JMX</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/kafka.html">Kafka</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/loki.html">Loki</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/mariadb.html">MariaDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/mongodb.html">MongoDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/mysql.html">MySQL</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/opensearch.html">OpenSearch</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/oracle.html">Oracle</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/pinot.html">Pinot</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/postgresql.html">PostgreSQL</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/prometheus.html">Prometheus</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/redis.html">Redis</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/redshift.html">Redshift</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/singlestore.html">SingleStore</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/snowflake.html">Snowflake</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/sqlserver.html">SQL Server</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/system.html">System</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/thrift.html">Thrift</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/tpcds.html">TPC-DS</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/tpch.html">TPC-H</a></li>
<li class="toctree-l2"><a class="reference internal" href="connector/vertica.html">Vertica</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="object-storage.html">Object storage</a></li>
<li class="toctree-l1"><a class="reference internal" href="functions.html">Functions and operators</a><ul>
<li class="toctree-l2"><a class="reference internal" href="functions/list.html">List of functions and operators</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/list-by-topic.html">List of functions by topic</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/aggregate.html">Aggregate</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/ai.html">AI</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/array.html">Array</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/binary.html">Binary</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/bitwise.html">Bitwise</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/color.html">Color</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/comparison.html">Comparison</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/conditional.html">Conditional</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/conversion.html">Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/datetime.html">Date and time</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/decimal.html">Decimal</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/geospatial.html">Geospatial</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/hyperloglog.html">HyperLogLog</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/ipaddress.html">IP Address</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/json.html">JSON</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/lambda.html">Lambda</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/logical.html">Logical</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/ml.html">Machine learning</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/map.html">Map</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/math.html">Math</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/qdigest.html">Quantile digest</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/regexp.html">Regular expression</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/session.html">Session</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/setdigest.html">Set Digest</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/string.html">String</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/system.html">System</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/table.html">Table</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/teradata.html">Teradata</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/tdigest.html">T-Digest</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/url.html">URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/uuid.html">UUID</a></li>
<li class="toctree-l2"><a class="reference internal" href="functions/window.html">Window</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="udf.html">User-defined functions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="udf/introduction.html">Introduction to UDFs</a></li>
<li class="toctree-l2"><a class="reference internal" href="udf/function.html">FUNCTION</a></li>
<li class="toctree-l2"><a class="reference internal" href="udf/sql.html">SQL user-defined functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="udf/python.html">Python user-defined functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="language.html">SQL language</a><ul>
<li class="toctree-l2"><a class="reference internal" href="language/sql-support.html">SQL statement support</a></li>
<li class="toctree-l2"><a class="reference internal" href="language/types.html">Data types</a></li>
<li class="toctree-l2"><a class="reference internal" href="language/reserved.html">Keywords and identifiers</a></li>
<li class="toctree-l2"><a class="reference internal" href="language/comments.html">Comments</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sql.html">SQL statement syntax</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sql/alter-materialized-view.html">ALTER MATERIALIZED VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/alter-schema.html">ALTER SCHEMA</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/alter-table.html">ALTER TABLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/alter-view.html">ALTER VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/analyze.html">ANALYZE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/call.html">CALL</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/comment.html">COMMENT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/commit.html">COMMIT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-catalog.html">CREATE CATALOG</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-function.html">CREATE FUNCTION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-materialized-view.html">CREATE MATERIALIZED VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-role.html">CREATE ROLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-schema.html">CREATE SCHEMA</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-table.html">CREATE TABLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-table-as.html">CREATE TABLE AS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/create-view.html">CREATE VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/deallocate-prepare.html">DEALLOCATE PREPARE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/delete.html">DELETE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/deny.html">DENY</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/describe.html">DESCRIBE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/describe-input.html">DESCRIBE INPUT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/describe-output.html">DESCRIBE OUTPUT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-catalog.html">DROP CATALOG</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-function.html">DROP FUNCTION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-materialized-view.html">DROP MATERIALIZED VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-role.html">DROP ROLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-schema.html">DROP SCHEMA</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-table.html">DROP TABLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/drop-view.html">DROP VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/execute.html">EXECUTE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/execute-immediate.html">EXECUTE IMMEDIATE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/explain.html">EXPLAIN</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/explain-analyze.html">EXPLAIN ANALYZE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/grant.html">GRANT privilege</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/grant-roles.html">GRANT role</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/insert.html">INSERT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/match-recognize.html">MATCH_RECOGNIZE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/merge.html">MERGE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/prepare.html">PREPARE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/refresh-materialized-view.html">REFRESH MATERIALIZED VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/reset-session.html">RESET SESSION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/reset-session-authorization.html">RESET SESSION AUTHORIZATION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/revoke.html">REVOKE privilege</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/revoke-roles.html">REVOKE role</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/rollback.html">ROLLBACK</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/select.html">SELECT</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/set-path.html">SET PATH</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/set-role.html">SET ROLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/set-session.html">SET SESSION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/set-session-authorization.html">SET SESSION AUTHORIZATION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/set-time-zone.html">SET TIME ZONE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-catalogs.html">SHOW CATALOGS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-columns.html">SHOW COLUMNS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-create-function.html">SHOW CREATE FUNCTION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-create-materialized-view.html">SHOW CREATE MATERIALIZED VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-create-schema.html">SHOW CREATE SCHEMA</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-create-table.html">SHOW CREATE TABLE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-create-view.html">SHOW CREATE VIEW</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-functions.html">SHOW FUNCTIONS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-grants.html">SHOW GRANTS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-role-grants.html">SHOW ROLE GRANTS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-roles.html">SHOW ROLES</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-schemas.html">SHOW SCHEMAS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-session.html">SHOW SESSION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-stats.html">SHOW STATS</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/show-tables.html">SHOW TABLES</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/start-transaction.html">START TRANSACTION</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/truncate.html">TRUNCATE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/update.html">UPDATE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/use.html">USE</a></li>
<li class="toctree-l2"><a class="reference internal" href="sql/values.html">VALUES</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="develop.html">Developer guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="develop/spi-overview.html">SPI overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/tests.html">Test writing guidelines</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/connectors.html">Connectors</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/example-http.html">Example HTTP connector</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/example-jdbc.html">Example JDBC connector</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/insert.html">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/supporting-merge.html">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/types.html">Types</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/functions.html">Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/table-functions.html">Table functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/system-access-control.html">System access control</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/password-authenticator.html">Password authenticator</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/certificate-authenticator.html">Certificate authenticator</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/header-authenticator.html">Header authenticator</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/group-provider.html">Group provider</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/event-listener.html">Event listener</a></li>
<li class="toctree-l2"><a class="reference internal" href="develop/client-protocol.html">Trino client REST API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
<li class="toctree-l1"><a class="reference internal" href="appendix.html">Appendix</a><ul>
<li class="toctree-l2"><a class="reference internal" href="appendix/from-hive.html">Migrating from Hive</a></li>
<li class="toctree-l2"><a class="reference internal" href="appendix/legal-notices.html">Legal notices</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="release.html">Release notes</a></li>
</ul>
</div>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
          
            <a href="overview.html" title="Overview"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Overview </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>