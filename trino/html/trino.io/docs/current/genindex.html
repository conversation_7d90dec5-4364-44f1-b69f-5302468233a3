<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Index &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="_static/trino.css@v=b5fc78e7.css" />
    <script src="_static/jquery.js@v=5d32c60e"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="_static/documentation_options.js@v=febf07ea"></script>
    <script src="_static/doctools.js@v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="genindex.html" />
    <link rel="index" title="Index" href="genindex.html#" />
    <link rel="search" title="Search" href="search.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="genindex.html#genindex" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Index </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../versions.json",
        target_loc = "../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <ul class="md-nav__list" data-md-scrollfix="">
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="genindex.html#A"><strong>A</strong></a>
 | <a href="genindex.html#B"><strong>B</strong></a>
 | <a href="genindex.html#C"><strong>C</strong></a>
 | <a href="genindex.html#D"><strong>D</strong></a>
 | <a href="genindex.html#E"><strong>E</strong></a>
 | <a href="genindex.html#F"><strong>F</strong></a>
 | <a href="genindex.html#G"><strong>G</strong></a>
 | <a href="genindex.html#H"><strong>H</strong></a>
 | <a href="genindex.html#I"><strong>I</strong></a>
 | <a href="genindex.html#J"><strong>J</strong></a>
 | <a href="genindex.html#K"><strong>K</strong></a>
 | <a href="genindex.html#L"><strong>L</strong></a>
 | <a href="genindex.html#M"><strong>M</strong></a>
 | <a href="genindex.html#N"><strong>N</strong></a>
 | <a href="genindex.html#O"><strong>O</strong></a>
 | <a href="genindex.html#P"><strong>P</strong></a>
 | <a href="genindex.html#Q"><strong>Q</strong></a>
 | <a href="genindex.html#R"><strong>R</strong></a>
 | <a href="genindex.html#S"><strong>S</strong></a>
 | <a href="genindex.html#T"><strong>T</strong></a>
 | <a href="genindex.html#U"><strong>U</strong></a>
 | <a href="genindex.html#V"><strong>V</strong></a>
 | <a href="genindex.html#W"><strong>W</strong></a>
 | <a href="genindex.html#X"><strong>X</strong></a>
 | <a href="genindex.html#Y"><strong>Y</strong></a>
 | <a href="genindex.html#Z"><strong>Z</strong></a>
 
</div>
<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    abs()

      <ul>
        <li><a href="functions/math.html#abs">built-in function</a>
</li>
      </ul></li>
      <li>
    acos()

      <ul>
        <li><a href="functions/math.html#acos">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_analyze_sentiment()

      <ul>
        <li><a href="functions/ai.html#ai_analyze_sentiment">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_classify()

      <ul>
        <li><a href="functions/ai.html#ai_classify">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_extract()

      <ul>
        <li><a href="functions/ai.html#ai_extract">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_fix_grammar()

      <ul>
        <li><a href="functions/ai.html#ai_fix_grammar">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_gen()

      <ul>
        <li><a href="functions/ai.html#ai_gen">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_mask()

      <ul>
        <li><a href="functions/ai.html#ai_mask">built-in function</a>
</li>
      </ul></li>
      <li>
    ai_translate()

      <ul>
        <li><a href="functions/ai.html#ai_translate">built-in function</a>
</li>
      </ul></li>
      <li>
    all_match()

      <ul>
        <li><a href="functions/array.html#all_match">built-in function</a>
</li>
      </ul></li>
      <li>
    any_match()

      <ul>
        <li><a href="functions/array.html#any_match">built-in function</a>
</li>
      </ul></li>
      <li>
    any_value()

      <ul>
        <li><a href="functions/aggregate.html#any_value">built-in function</a>
</li>
      </ul></li>
      <li>
    approx_distinct()

      <ul>
        <li><a href="functions/aggregate.html#approx_distinct">built-in function</a>
</li>
      </ul></li>
      <li>
    approx_most_frequent()

      <ul>
        <li><a href="functions/aggregate.html#approx_most_frequent">built-in function</a>
</li>
      </ul></li>
      <li>
    approx_percentile()

      <ul>
        <li><a href="functions/aggregate.html#approx_percentile">built-in function</a>
</li>
      </ul></li>
      <li>
    approx_set()

      <ul>
        <li><a href="functions/hyperloglog.html#approx_set">built-in function</a>
</li>
      </ul></li>
      <li>
    arbitrary()

      <ul>
        <li><a href="functions/aggregate.html#arbitrary">built-in function</a>
</li>
      </ul></li>
      <li>
    array_agg()

      <ul>
        <li><a href="functions/aggregate.html#array_agg">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    array_distinct()

      <ul>
        <li><a href="functions/array.html#array_distinct">built-in function</a>
</li>
      </ul></li>
      <li>
    array_except()

      <ul>
        <li><a href="functions/array.html#array_except">built-in function</a>
</li>
      </ul></li>
      <li>
    array_histogram()

      <ul>
        <li><a href="functions/array.html#array_histogram">built-in function</a>
</li>
      </ul></li>
      <li>
    array_intersect()

      <ul>
        <li><a href="functions/array.html#array_intersect">built-in function</a>
</li>
      </ul></li>
      <li>
    array_join()

      <ul>
        <li><a href="functions/array.html#array_join">built-in function</a>
</li>
      </ul></li>
      <li>
    array_max()

      <ul>
        <li><a href="functions/array.html#array_max">built-in function</a>
</li>
      </ul></li>
      <li>
    array_min()

      <ul>
        <li><a href="functions/array.html#array_min">built-in function</a>
</li>
      </ul></li>
      <li>
    array_position()

      <ul>
        <li><a href="functions/array.html#array_position">built-in function</a>
</li>
      </ul></li>
      <li>
    array_remove()

      <ul>
        <li><a href="functions/array.html#array_remove">built-in function</a>
</li>
      </ul></li>
      <li>
    array_sort()

      <ul>
        <li><a href="functions/array.html#array_sort">built-in function</a>
</li>
      </ul></li>
      <li>
    array_union()

      <ul>
        <li><a href="functions/array.html#array_union">built-in function</a>
</li>
      </ul></li>
      <li>
    arrays_overlap()

      <ul>
        <li><a href="functions/array.html#arrays_overlap">built-in function</a>
</li>
      </ul></li>
      <li>
    asin()

      <ul>
        <li><a href="functions/math.html#asin">built-in function</a>
</li>
      </ul></li>
      <li>
    at_timezone()

      <ul>
        <li><a href="functions/datetime.html#at_timezone">built-in function</a>
</li>
      </ul></li>
      <li>
    atan()

      <ul>
        <li><a href="functions/math.html#atan">built-in function</a>
</li>
      </ul></li>
      <li>
    atan2()

      <ul>
        <li><a href="functions/math.html#atan2">built-in function</a>
</li>
      </ul></li>
      <li>
    avg()

      <ul>
        <li><a href="functions/aggregate.html#avg">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    bar()

      <ul>
        <li><a href="functions/color.html#bar">built-in function</a>
</li>
      </ul></li>
      <li>
    beta_cdf()

      <ul>
        <li><a href="functions/math.html#beta_cdf">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile_at()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile_at">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile_coordinates()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile_coordinates">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile_polygon()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile_polygon">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile_quadkey()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile_quadkey">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tile_zoom_level()

      <ul>
        <li><a href="functions/geospatial.html#bing_tile_zoom_level">built-in function</a>
</li>
      </ul></li>
      <li>
    bing_tiles_around()

      <ul>
        <li><a href="functions/geospatial.html#bing_tiles_around">built-in function</a>
</li>
      </ul></li>
      <li>
    bit_count()

      <ul>
        <li><a href="functions/bitwise.html#bit_count">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_and()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_and">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_and_agg()

      <ul>
        <li><a href="functions/aggregate.html#bitwise_and_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_left_shift()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_left_shift">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_not()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_not">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_or()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_or">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_or_agg()

      <ul>
        <li><a href="functions/aggregate.html#bitwise_or_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_right_shift()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_right_shift">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_right_shift_arithmetic()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_right_shift_arithmetic">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_xor()

      <ul>
        <li><a href="functions/bitwise.html#bitwise_xor">built-in function</a>
</li>
      </ul></li>
      <li>
    bitwise_xor_agg()

      <ul>
        <li><a href="functions/aggregate.html#bitwise_xor_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    bool_and()

      <ul>
        <li><a href="functions/aggregate.html#bool_and">built-in function</a>
</li>
      </ul></li>
      <li>
    bool_or()

      <ul>
        <li><a href="functions/aggregate.html#bool_or">built-in function</a>
</li>
      </ul></li>
      <li>
    built-in function

      <ul>
        <li><a href="functions/math.html#abs">abs()</a>
</li>
        <li><a href="functions/math.html#acos">acos()</a>
</li>
        <li><a href="functions/ai.html#ai_analyze_sentiment">ai_analyze_sentiment()</a>
</li>
        <li><a href="functions/ai.html#ai_classify">ai_classify()</a>
</li>
        <li><a href="functions/ai.html#ai_extract">ai_extract()</a>
</li>
        <li><a href="functions/ai.html#ai_fix_grammar">ai_fix_grammar()</a>
</li>
        <li><a href="functions/ai.html#ai_gen">ai_gen()</a>
</li>
        <li><a href="functions/ai.html#ai_mask">ai_mask()</a>
</li>
        <li><a href="functions/ai.html#ai_translate">ai_translate()</a>
</li>
        <li><a href="functions/array.html#all_match">all_match()</a>
</li>
        <li><a href="functions/array.html#any_match">any_match()</a>
</li>
        <li><a href="functions/aggregate.html#any_value">any_value()</a>
</li>
        <li><a href="functions/aggregate.html#approx_distinct">approx_distinct()</a>
</li>
        <li><a href="functions/aggregate.html#approx_most_frequent">approx_most_frequent()</a>
</li>
        <li><a href="functions/aggregate.html#approx_percentile">approx_percentile()</a>
</li>
        <li><a href="functions/hyperloglog.html#approx_set">approx_set()</a>
</li>
        <li><a href="functions/aggregate.html#arbitrary">arbitrary()</a>
</li>
        <li><a href="functions/aggregate.html#array_agg">array_agg()</a>
</li>
        <li><a href="functions/array.html#array_distinct">array_distinct()</a>
</li>
        <li><a href="functions/array.html#array_except">array_except()</a>
</li>
        <li><a href="functions/array.html#array_histogram">array_histogram()</a>
</li>
        <li><a href="functions/array.html#array_intersect">array_intersect()</a>
</li>
        <li><a href="functions/array.html#array_join">array_join()</a>
</li>
        <li><a href="functions/array.html#array_max">array_max()</a>
</li>
        <li><a href="functions/array.html#array_min">array_min()</a>
</li>
        <li><a href="functions/array.html#array_position">array_position()</a>
</li>
        <li><a href="functions/array.html#array_remove">array_remove()</a>
</li>
        <li><a href="functions/array.html#array_sort">array_sort()</a>
</li>
        <li><a href="functions/array.html#array_union">array_union()</a>
</li>
        <li><a href="functions/array.html#arrays_overlap">arrays_overlap()</a>
</li>
        <li><a href="functions/math.html#asin">asin()</a>
</li>
        <li><a href="functions/datetime.html#at_timezone">at_timezone()</a>
</li>
        <li><a href="functions/math.html#atan">atan()</a>
</li>
        <li><a href="functions/math.html#atan2">atan2()</a>
</li>
        <li><a href="functions/aggregate.html#avg">avg()</a>
</li>
        <li><a href="functions/color.html#bar">bar()</a>
</li>
        <li><a href="functions/math.html#beta_cdf">beta_cdf()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile">bing_tile()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile_at">bing_tile_at()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile_coordinates">bing_tile_coordinates()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile_polygon">bing_tile_polygon()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile_quadkey">bing_tile_quadkey()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tile_zoom_level">bing_tile_zoom_level()</a>
</li>
        <li><a href="functions/geospatial.html#bing_tiles_around">bing_tiles_around()</a>
</li>
        <li><a href="functions/bitwise.html#bit_count">bit_count()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_and">bitwise_and()</a>
</li>
        <li><a href="functions/aggregate.html#bitwise_and_agg">bitwise_and_agg()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_left_shift">bitwise_left_shift()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_not">bitwise_not()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_or">bitwise_or()</a>
</li>
        <li><a href="functions/aggregate.html#bitwise_or_agg">bitwise_or_agg()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_right_shift">bitwise_right_shift()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_right_shift_arithmetic">bitwise_right_shift_arithmetic()</a>
</li>
        <li><a href="functions/bitwise.html#bitwise_xor">bitwise_xor()</a>
</li>
        <li><a href="functions/aggregate.html#bitwise_xor_agg">bitwise_xor_agg()</a>
</li>
        <li><a href="functions/aggregate.html#bool_and">bool_and()</a>
</li>
        <li><a href="functions/aggregate.html#bool_or">bool_or()</a>
</li>
        <li><a href="functions/array.html#cardinality">cardinality()</a>
</li>
        <li><a href="functions/conversion.html#cast">cast()</a>
</li>
        <li><a href="functions/math.html#cbrt">cbrt()</a>
</li>
        <li><a href="functions/math.html#ceil">ceil()</a>
</li>
        <li><a href="functions/math.html#ceiling">ceiling()</a>
</li>
        <li><a href="functions/teradata.html#char2hexint">char2hexint()</a>
</li>
        <li><a href="functions/aggregate.html#checksum">checksum()</a>
</li>
        <li><a href="functions/string.html#chr">chr()</a>
</li>
        <li><a href="functions/ml.html#classify">classify()</a>
</li>
        <li><a href="functions/conditional.html#id1">coalesce()</a>
</li>
        <li><a href="functions/string.html#codepoint">codepoint()</a>
</li>
        <li><a href="functions/color.html#color">color()</a>
</li>
        <li><a href="functions/array.html#combinations">combinations()</a>
</li>
        <li><a href="functions/string.html#concat">concat()</a>
</li>
        <li><a href="functions/string.html#concat_ws">concat_ws()</a>
</li>
        <li><a href="functions/array.html#contains">contains()</a>
</li>
        <li><a href="functions/array.html#contains_sequence">contains_sequence()</a>
</li>
        <li><a href="functions/geospatial.html#convex_hull_agg">convex_hull_agg()</a>
</li>
        <li><a href="functions/aggregate.html#corr">corr()</a>
</li>
        <li><a href="functions/math.html#cos">cos()</a>
</li>
        <li><a href="functions/math.html#cosh">cosh()</a>
</li>
        <li><a href="functions/math.html#cosine_distance">cosine_distance()</a>
</li>
        <li><a href="functions/math.html#cosine_similarity">cosine_similarity()</a>
</li>
        <li><a href="functions/aggregate.html#count">count()</a>
</li>
        <li><a href="functions/aggregate.html#count_if">count_if()</a>
</li>
        <li><a href="functions/aggregate.html#covar_pop">covar_pop()</a>
</li>
        <li><a href="functions/aggregate.html#covar_samp">covar_samp()</a>
</li>
        <li><a href="functions/binary.html#crc32">crc32()</a>
</li>
        <li><a href="functions/window.html#cume_dist">cume_dist()</a>
</li>
        <li><a href="functions/session.html#current_groups">current_groups()</a>
</li>
        <li><a href="functions/datetime.html#current_timezone">current_timezone()</a>
</li>
        <li><a href="functions/datetime.html#date">date()</a>
</li>
        <li><a href="functions/datetime.html#date_add">date_add()</a>
</li>
        <li><a href="functions/datetime.html#date_diff">date_diff()</a>
</li>
        <li><a href="functions/datetime.html#date_format">date_format()</a>
</li>
        <li><a href="functions/datetime.html#date_trunc">date_trunc()</a>
</li>
        <li><a href="functions/datetime.html#day">day()</a>
</li>
        <li><a href="functions/datetime.html#day_of_month">day_of_month()</a>
</li>
        <li><a href="functions/datetime.html#day_of_week">day_of_week()</a>
</li>
        <li><a href="functions/datetime.html#day_of_year">day_of_year()</a>
</li>
        <li><a href="functions/math.html#degrees">degrees()</a>
</li>
        <li><a href="functions/window.html#dense_rank">dense_rank()</a>
</li>
        <li><a href="functions/array.html#dot_product">dot_product()</a>
</li>
        <li><a href="functions/datetime.html#dow">dow()</a>
</li>
        <li><a href="functions/datetime.html#doy">doy()</a>
</li>
        <li><a href="functions/math.html#e">e()</a>
</li>
        <li><a href="functions/array.html#element_at">element_at()</a>
</li>
        <li><a href="functions/hyperloglog.html#empty_approx_set">empty_approx_set()</a>
</li>
        <li><a href="functions/array.html#euclidean_distance">euclidean_distance()</a>
</li>
        <li><a href="functions/aggregate.html#every">every()</a>
</li>
        <li><a href="functions/math.html#exp">exp()</a>
</li>
        <li><a href="functions/datetime.html#extract">extract()</a>
</li>
        <li><a href="functions/ml.html#features">features()</a>
</li>
        <li><a href="functions/array.html#filter">filter()</a>
</li>
        <li><a href="functions/window.html#first_value">first_value()</a>
</li>
        <li><a href="functions/array.html#flatten">flatten()</a>
</li>
        <li><a href="functions/math.html#floor">floor()</a>
</li>
        <li><a href="functions/conversion.html#format">format()</a>
</li>
        <li><a href="functions/datetime.html#format_datetime">format_datetime()</a>
</li>
        <li><a href="functions/conversion.html#format_number">format_number()</a>
</li>
        <li><a href="functions/math.html#from_base">from_base()</a>
</li>
        <li><a href="functions/binary.html#from_base32">from_base32()</a>
</li>
        <li><a href="functions/binary.html#from_base64">from_base64()</a>
</li>
        <li><a href="functions/binary.html#from_base64url">from_base64url()</a>
</li>
        <li><a href="functions/binary.html#from_big_endian_32">from_big_endian_32()</a>
</li>
        <li><a href="functions/binary.html#from_big_endian_64">from_big_endian_64()</a>
</li>
        <li><a href="functions/geospatial.html#from_encoded_polyline">from_encoded_polyline()</a>
</li>
        <li><a href="functions/geospatial.html#from_geojson_geometry">from_geojson_geometry()</a>
</li>
        <li><a href="functions/binary.html#from_hex">from_hex()</a>
</li>
        <li><a href="functions/binary.html#from_ieee754_32">from_ieee754_32()</a>
</li>
        <li><a href="functions/binary.html#from_ieee754_64">from_ieee754_64()</a>
</li>
        <li><a href="functions/datetime.html#from_iso8601_date">from_iso8601_date()</a>
</li>
        <li><a href="functions/datetime.html#from_iso8601_timestamp">from_iso8601_timestamp()</a>
</li>
        <li><a href="functions/datetime.html#from_iso8601_timestamp_nanos">from_iso8601_timestamp_nanos()</a>
</li>
        <li><a href="functions/datetime.html#from_unixtime">from_unixtime()</a>
</li>
        <li><a href="functions/datetime.html#from_unixtime_nanos">from_unixtime_nanos()</a>
</li>
        <li><a href="functions/string.html#from_utf8">from_utf8()</a>
</li>
        <li><a href="functions/aggregate.html#geometric_mean">geometric_mean()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_from_hadoop_shape">geometry_from_hadoop_shape()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_invalid_reason">geometry_invalid_reason()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_nearest_points">geometry_nearest_points()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_to_bing_tiles">geometry_to_bing_tiles()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_union">geometry_union()</a>
</li>
        <li><a href="functions/geospatial.html#geometry_union_agg">geometry_union_agg()</a>
</li>
        <li><a href="functions/geospatial.html#great_circle_distance">great_circle_distance()</a>
</li>
        <li><a href="functions/comparison.html#greatest">greatest()</a>
</li>
        <li><a href="functions/string.html#hamming_distance">hamming_distance()</a>
</li>
        <li><a href="functions/setdigest.html#hash_counts">hash_counts()</a>
</li>
        <li><a href="functions/aggregate.html#histogram">histogram()</a>
</li>
        <li><a href="functions/binary.html#hmac_md5">hmac_md5()</a>
</li>
        <li><a href="functions/binary.html#hmac_sha1">hmac_sha1()</a>
</li>
        <li><a href="functions/binary.html#hmac_sha256">hmac_sha256()</a>
</li>
        <li><a href="functions/binary.html#hmac_sha512">hmac_sha512()</a>
</li>
        <li><a href="functions/datetime.html#hour">hour()</a>
</li>
        <li><a href="functions/datetime.html#human_readable_seconds">human_readable_seconds()</a>
</li>
        <li><a href="functions/conditional.html#id0">if()</a>
</li>
        <li><a href="functions/teradata.html#index">index()</a>
</li>
        <li><a href="functions/math.html#infinity">infinity()</a>
</li>
        <li><a href="functions/setdigest.html#intersection_cardinality">intersection_cardinality()</a>
</li>
        <li><a href="functions/math.html#inverse_beta_cdf">inverse_beta_cdf()</a>
</li>
        <li><a href="functions/math.html#inverse_normal_cdf">inverse_normal_cdf()</a>
</li>
        <li><a href="functions/math.html#is_finite">is_finite()</a>
</li>
        <li><a href="functions/math.html#is_infinite">is_infinite()</a>
</li>
        <li><a href="functions/json.html#is_json_scalar">is_json_scalar()</a>
</li>
        <li><a href="functions/math.html#is_nan">is_nan()</a>
</li>
        <li><a href="functions/setdigest.html#jaccard_index">jaccard_index()</a>
</li>
        <li><a href="functions/json.html#json_array_contains">json_array_contains()</a>
</li>
        <li><a href="functions/json.html#json_array_get">json_array_get()</a>
</li>
        <li><a href="functions/json.html#json_array_length">json_array_length()</a>
</li>
        <li><a href="functions/json.html#json_extract">json_extract()</a>
</li>
        <li><a href="functions/json.html#json_extract_scalar">json_extract_scalar()</a>
</li>
        <li><a href="functions/json.html#json_format">json_format()</a>
</li>
        <li><a href="functions/json.html#json_parse">json_parse()</a>
</li>
        <li><a href="functions/json.html#json_size">json_size()</a>
</li>
        <li><a href="functions/aggregate.html#kurtosis">kurtosis()</a>
</li>
        <li><a href="functions/window.html#lag">lag()</a>
</li>
        <li><a href="functions/datetime.html#last_day_of_month">last_day_of_month()</a>
</li>
        <li><a href="functions/window.html#last_value">last_value()</a>
</li>
        <li><a href="functions/window.html#lead">lead()</a>
</li>
        <li><a href="functions/ml.html#learn_classifier">learn_classifier()</a>
</li>
        <li><a href="functions/ml.html#learn_libsvm_classifier">learn_libsvm_classifier()</a>
</li>
        <li><a href="functions/ml.html#learn_libsvm_regressor">learn_libsvm_regressor()</a>
</li>
        <li><a href="functions/ml.html#learn_regressor">learn_regressor()</a>
</li>
        <li><a href="functions/comparison.html#least">least()</a>
</li>
        <li><a href="functions/string.html#length">length()</a>
</li>
        <li><a href="functions/string.html#levenshtein_distance">levenshtein_distance()</a>
</li>
        <li><a href="functions/geospatial.html#line_interpolate_point">line_interpolate_point()</a>
</li>
        <li><a href="functions/geospatial.html#line_interpolate_points">line_interpolate_points()</a>
</li>
        <li><a href="functions/geospatial.html#line_locate_point">line_locate_point()</a>
</li>
        <li><a href="functions/aggregate.html#listagg">listagg()</a>
</li>
        <li><a href="functions/math.html#ln">ln()</a>
</li>
        <li><a href="functions/math.html#log">log()</a>
</li>
        <li><a href="functions/math.html#log10">log10()</a>
</li>
        <li><a href="functions/math.html#log2">log2()</a>
</li>
        <li><a href="functions/string.html#lower">lower()</a>
</li>
        <li><a href="functions/string.html#lpad">lpad()</a>
</li>
        <li><a href="functions/string.html#ltrim">ltrim()</a>
</li>
        <li><a href="functions/string.html#luhn_check">luhn_check()</a>
</li>
        <li><a href="functions/setdigest.html#make_set_digest">make_set_digest()</a>
</li>
        <li><a href="functions/map.html#map">map()</a>
</li>
        <li><a href="functions/aggregate.html#map_agg">map_agg()</a>
</li>
        <li><a href="functions/map.html#map_concat">map_concat()</a>
</li>
        <li><a href="functions/map.html#map_entries">map_entries()</a>
</li>
        <li><a href="functions/map.html#map_filter">map_filter()</a>
</li>
        <li><a href="functions/map.html#map_from_entries">map_from_entries()</a>
</li>
        <li><a href="functions/map.html#map_keys">map_keys()</a>
</li>
        <li><a href="functions/aggregate.html#map_union">map_union()</a>
</li>
        <li><a href="functions/map.html#map_values">map_values()</a>
</li>
        <li><a href="functions/map.html#map_zip_with">map_zip_with()</a>
</li>
        <li><a href="functions/aggregate.html#max">max()</a>
</li>
        <li><a href="functions/aggregate.html#max_by">max_by()</a>
</li>
        <li><a href="functions/binary.html#md5">md5()</a>
</li>
        <li><a href="functions/hyperloglog.html#merge">merge()</a>
</li>
        <li><a href="functions/setdigest.html#merge_set_digest">merge_set_digest()</a>
</li>
        <li><a href="functions/datetime.html#millisecond">millisecond()</a>
</li>
        <li><a href="functions/aggregate.html#min">min()</a>
</li>
        <li><a href="functions/aggregate.html#min_by">min_by()</a>
</li>
        <li><a href="functions/datetime.html#minute">minute()</a>
</li>
        <li><a href="functions/math.html#mod">mod()</a>
</li>
        <li><a href="functions/datetime.html#month">month()</a>
</li>
        <li><a href="functions/aggregate.html#multimap_agg">multimap_agg()</a>
</li>
        <li><a href="functions/map.html#multimap_from_entries">multimap_from_entries()</a>
</li>
        <li><a href="functions/binary.html#murmur3">murmur3()</a>
</li>
        <li><a href="functions/math.html#nan">nan()</a>
</li>
        <li><a href="functions/array.html#ngrams">ngrams()</a>
</li>
        <li><a href="functions/array.html#none_match">none_match()</a>
</li>
        <li><a href="functions/math.html#normal_cdf">normal_cdf()</a>
</li>
        <li><a href="functions/string.html#normalize">normalize()</a>
</li>
        <li><a href="functions/datetime.html#now">now()</a>
</li>
        <li><a href="functions/window.html#nth_value">nth_value()</a>
</li>
        <li><a href="functions/window.html#ntile">ntile()</a>
</li>
        <li><a href="functions/conditional.html#id2">nullif()</a>
</li>
        <li><a href="functions/aggregate.html#numeric_histogram">numeric_histogram()</a>
</li>
        <li><a href="connector/mongodb.html#objectid_timestamp">objectid_timestamp()</a>
</li>
        <li><a href="functions/conversion.html#parse_data_size">parse_data_size()</a>
</li>
        <li><a href="functions/datetime.html#parse_datetime">parse_datetime()</a>
</li>
        <li><a href="functions/datetime.html#parse_duration">parse_duration()</a>
</li>
        <li><a href="functions/window.html#percent_rank">percent_rank()</a>
</li>
        <li><a href="functions/math.html#pi">pi()</a>
</li>
        <li><a href="functions/string.html#position">position()</a>
</li>
        <li><a href="functions/math.html#pow">pow()</a>
</li>
        <li><a href="functions/math.html#power">power()</a>
</li>
        <li><a href="functions/qdigest.html#qdigest_agg">qdigest_agg()</a>
</li>
        <li><a href="functions/qdigest.html#quantile_at_value">quantile_at_value()</a>
</li>
        <li><a href="functions/datetime.html#quarter">quarter()</a>
</li>
        <li><a href="functions/math.html#radians">radians()</a>
</li>
        <li><a href="functions/math.html#rand">rand()</a>
</li>
        <li><a href="functions/math.html#random">random()</a>
</li>
        <li><a href="connector/faker.html#random_string">random_string()</a>
</li>
        <li><a href="functions/window.html#rank">rank()</a>
</li>
        <li><a href="functions/array.html#reduce">reduce()</a>
</li>
        <li><a href="functions/aggregate.html#reduce_agg">reduce_agg()</a>
</li>
        <li><a href="functions/regexp.html#regexp_count">regexp_count()</a>
</li>
        <li><a href="functions/regexp.html#regexp_extract">regexp_extract()</a>
</li>
        <li><a href="functions/regexp.html#regexp_extract_all">regexp_extract_all()</a>
</li>
        <li><a href="functions/regexp.html#regexp_like">regexp_like()</a>
</li>
        <li><a href="functions/regexp.html#regexp_position">regexp_position()</a>
</li>
        <li><a href="functions/regexp.html#regexp_replace">regexp_replace()</a>
</li>
        <li><a href="functions/regexp.html#regexp_split">regexp_split()</a>
</li>
        <li><a href="functions/aggregate.html#regr_intercept">regr_intercept()</a>
</li>
        <li><a href="functions/aggregate.html#regr_slope">regr_slope()</a>
</li>
        <li><a href="functions/ml.html#regress">regress()</a>
</li>
        <li><a href="functions/color.html#render">render()</a>
</li>
        <li><a href="functions/array.html#repeat">repeat()</a>
</li>
        <li><a href="functions/string.html#replace">replace()</a>
</li>
        <li><a href="functions/string.html#reverse">reverse()</a>
</li>
        <li><a href="functions/color.html#rgb">rgb()</a>
</li>
        <li><a href="functions/math.html#round">round()</a>
</li>
        <li><a href="functions/window.html#row_number">row_number()</a>
</li>
        <li><a href="functions/string.html#rpad">rpad()</a>
</li>
        <li><a href="functions/string.html#rtrim">rtrim()</a>
</li>
        <li><a href="connector/system.html#runtime.kill_query">runtime.kill_query()</a>
</li>
        <li><a href="functions/datetime.html#second">second()</a>
</li>
        <li><a href="functions/array.html#sequence">sequence()</a>
</li>
        <li><a href="functions/binary.html#sha1">sha1()</a>
</li>
        <li><a href="functions/binary.html#sha256">sha256()</a>
</li>
        <li><a href="functions/binary.html#sha512">sha512()</a>
</li>
        <li><a href="functions/array.html#shuffle">shuffle()</a>
</li>
        <li><a href="functions/math.html#sign">sign()</a>
</li>
        <li><a href="functions/geospatial.html#simplify_geometry">simplify_geometry()</a>
</li>
        <li><a href="functions/math.html#sin">sin()</a>
</li>
        <li><a href="functions/math.html#sinh">sinh()</a>
</li>
        <li><a href="functions/aggregate.html#skewness">skewness()</a>
</li>
        <li><a href="functions/array.html#slice">slice()</a>
</li>
        <li><a href="functions/string.html#soundex">soundex()</a>
</li>
        <li><a href="functions/string.html#split">split()</a>
</li>
        <li><a href="functions/string.html#split_part">split_part()</a>
</li>
        <li><a href="functions/string.html#split_to_map">split_to_map()</a>
</li>
        <li><a href="functions/string.html#split_to_multimap">split_to_multimap()</a>
</li>
        <li><a href="functions/binary.html#spooky_hash_v2_32">spooky_hash_v2_32()</a>
</li>
        <li><a href="functions/binary.html#spooky_hash_v2_64">spooky_hash_v2_64()</a>
</li>
        <li><a href="functions/math.html#sqrt">sqrt()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Area">ST_Area()</a>
</li>
        <li><a href="functions/geospatial.html#ST_AsBinary">ST_AsBinary()</a>
</li>
        <li><a href="functions/geospatial.html#ST_AsText">ST_AsText()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Boundary">ST_Boundary()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Buffer">ST_Buffer()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Centroid">ST_Centroid()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Contains">ST_Contains()</a>
</li>
        <li><a href="functions/geospatial.html#ST_ConvexHull">ST_ConvexHull()</a>
</li>
        <li><a href="functions/geospatial.html#ST_CoordDim">ST_CoordDim()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Crosses">ST_Crosses()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Difference">ST_Difference()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Dimension">ST_Dimension()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Disjoint">ST_Disjoint()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Distance">ST_Distance()</a>
</li>
        <li><a href="functions/geospatial.html#ST_EndPoint">ST_EndPoint()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Envelope">ST_Envelope()</a>
</li>
        <li><a href="functions/geospatial.html#ST_EnvelopeAsPts">ST_EnvelopeAsPts()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Equals">ST_Equals()</a>
</li>
        <li><a href="functions/geospatial.html#ST_ExteriorRing">ST_ExteriorRing()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Geometries">ST_Geometries()</a>
</li>
        <li><a href="functions/geospatial.html#ST_GeometryFromText">ST_GeometryFromText()</a>
</li>
        <li><a href="functions/geospatial.html#ST_GeometryN">ST_GeometryN()</a>
</li>
        <li><a href="functions/geospatial.html#ST_GeometryType">ST_GeometryType()</a>
</li>
        <li><a href="functions/geospatial.html#ST_GeomFromBinary">ST_GeomFromBinary()</a>
</li>
        <li><a href="functions/geospatial.html#ST_GeomFromKML">ST_GeomFromKML()</a>
</li>
        <li><a href="functions/geospatial.html#ST_InteriorRingN">ST_InteriorRingN()</a>
</li>
        <li><a href="functions/geospatial.html#ST_InteriorRings">ST_InteriorRings()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Intersection">ST_Intersection()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Intersects">ST_Intersects()</a>
</li>
        <li><a href="functions/geospatial.html#ST_IsClosed">ST_IsClosed()</a>
</li>
        <li><a href="functions/geospatial.html#ST_IsEmpty">ST_IsEmpty()</a>
</li>
        <li><a href="functions/geospatial.html#ST_IsRing">ST_IsRing()</a>
</li>
        <li><a href="functions/geospatial.html#ST_IsSimple">ST_IsSimple()</a>
</li>
        <li><a href="functions/geospatial.html#ST_IsValid">ST_IsValid()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Length">ST_Length()</a>
</li>
        <li><a href="functions/geospatial.html#ST_LineFromText">ST_LineFromText()</a>
</li>
        <li><a href="functions/geospatial.html#ST_LineString">ST_LineString()</a>
</li>
        <li><a href="functions/geospatial.html#ST_MultiPoint">ST_MultiPoint()</a>
</li>
        <li><a href="functions/geospatial.html#ST_NumGeometries">ST_NumGeometries()</a>
</li>
        <li><a href="functions/geospatial.html#ST_NumInteriorRing">ST_NumInteriorRing()</a>
</li>
        <li><a href="functions/geospatial.html#ST_NumPoints">ST_NumPoints()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Overlaps">ST_Overlaps()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Point">ST_Point()</a>
</li>
        <li><a href="functions/geospatial.html#ST_PointN">ST_PointN()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Points">ST_Points()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Polygon">ST_Polygon()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Relate">ST_Relate()</a>
</li>
        <li><a href="functions/geospatial.html#ST_StartPoint">ST_StartPoint()</a>
</li>
        <li><a href="functions/geospatial.html#ST_SymDifference">ST_SymDifference()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Touches">ST_Touches()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Union">ST_Union()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Within">ST_Within()</a>
</li>
        <li><a href="functions/geospatial.html#ST_X">ST_X()</a>
</li>
        <li><a href="functions/geospatial.html#ST_XMax">ST_XMax()</a>
</li>
        <li><a href="functions/geospatial.html#ST_XMin">ST_XMin()</a>
</li>
        <li><a href="functions/geospatial.html#ST_Y">ST_Y()</a>
</li>
        <li><a href="functions/geospatial.html#ST_YMax">ST_YMax()</a>
</li>
        <li><a href="functions/geospatial.html#ST_YMin">ST_YMin()</a>
</li>
        <li><a href="functions/string.html#starts_with">starts_with()</a>
</li>
        <li><a href="functions/aggregate.html#stddev">stddev()</a>
</li>
        <li><a href="functions/aggregate.html#stddev_pop">stddev_pop()</a>
</li>
        <li><a href="functions/aggregate.html#stddev_samp">stddev_samp()</a>
</li>
        <li><a href="functions/string.html#strpos">strpos()</a>
</li>
        <li><a href="functions/string.html#substr">substr()</a>
</li>
        <li><a href="functions/string.html#substring">substring()</a>
</li>
        <li><a href="functions/aggregate.html#sum">sum()</a>
</li>
        <li><a href="functions/math.html#t_cdf">t_cdf()</a>
</li>
        <li><a href="functions/math.html#t_pdf">t_pdf()</a>
</li>
        <li><a href="functions/math.html#tan">tan()</a>
</li>
        <li><a href="functions/math.html#tanh">tanh()</a>
</li>
        <li><a href="functions/tdigest.html#tdigest_agg">tdigest_agg()</a>
</li>
        <li><a href="connector/mongodb.html#timestamp_objectid">timestamp_objectid()</a>
</li>
        <li><a href="functions/datetime.html#timezone">timezone()</a>
</li>
        <li><a href="functions/datetime.html#timezone_hour">timezone_hour()</a>
</li>
        <li><a href="functions/datetime.html#timezone_minute">timezone_minute()</a>
</li>
        <li><a href="functions/math.html#to_base">to_base()</a>
</li>
        <li><a href="functions/binary.html#to_base32">to_base32()</a>
</li>
        <li><a href="functions/binary.html#to_base64">to_base64()</a>
</li>
        <li><a href="functions/binary.html#to_base64url">to_base64url()</a>
</li>
        <li><a href="functions/binary.html#to_big_endian_32">to_big_endian_32()</a>
</li>
        <li><a href="functions/binary.html#to_big_endian_64">to_big_endian_64()</a>
</li>
        <li><a href="functions/teradata.html#to_char">to_char()</a>
</li>
        <li><a href="functions/teradata.html#to_date">to_date()</a>
</li>
        <li><a href="functions/geospatial.html#to_encoded_polyline">to_encoded_polyline()</a>
</li>
        <li><a href="functions/geospatial.html#to_geojson_geometry">to_geojson_geometry()</a>
</li>
        <li><a href="functions/geospatial.html#to_geometry">to_geometry()</a>
</li>
        <li><a href="functions/binary.html#to_hex">to_hex()</a>
</li>
        <li><a href="functions/binary.html#to_ieee754_32">to_ieee754_32()</a>
</li>
        <li><a href="functions/binary.html#to_ieee754_64">to_ieee754_64()</a>
</li>
        <li><a href="functions/datetime.html#to_iso8601">to_iso8601()</a>
</li>
        <li><a href="functions/datetime.html#to_milliseconds">to_milliseconds()</a>
</li>
        <li><a href="functions/geospatial.html#to_spherical_geography">to_spherical_geography()</a>
</li>
        <li><a href="functions/teradata.html#to_timestamp">to_timestamp()</a>
</li>
        <li><a href="functions/datetime.html#to_unixtime">to_unixtime()</a>
</li>
        <li><a href="functions/string.html#to_utf8">to_utf8()</a>
</li>
        <li><a href="functions/array.html#transform">transform()</a>
</li>
        <li><a href="functions/map.html#transform_keys">transform_keys()</a>
</li>
        <li><a href="functions/map.html#transform_values">transform_values()</a>
</li>
        <li><a href="functions/string.html#translate">translate()</a>
</li>
        <li><a href="functions/string.html#trim">trim()</a>
</li>
        <li><a href="functions/array.html#trim_array">trim_array()</a>
</li>
        <li><a href="functions/math.html#truncate">truncate()</a>
</li>
        <li><a href="functions/conditional.html#id3">try()</a>
</li>
        <li><a href="functions/conversion.html#try_cast">try_cast()</a>
</li>
        <li><a href="functions/conversion.html#typeof">typeof()</a>
</li>
        <li><a href="functions/string.html#upper">upper()</a>
</li>
        <li><a href="functions/url.html#url_decode">url_decode()</a>
</li>
        <li><a href="functions/url.html#url_encode">url_encode()</a>
</li>
        <li><a href="functions/url.html#url_extract_fragment">url_extract_fragment()</a>
</li>
        <li><a href="functions/url.html#url_extract_host">url_extract_host()</a>
</li>
        <li><a href="functions/url.html#url_extract_parameter">url_extract_parameter()</a>
</li>
        <li><a href="functions/url.html#url_extract_path">url_extract_path()</a>
</li>
        <li><a href="functions/url.html#url_extract_port">url_extract_port()</a>
</li>
        <li><a href="functions/url.html#url_extract_protocol">url_extract_protocol()</a>
</li>
        <li><a href="functions/url.html#url_extract_query">url_extract_query()</a>
</li>
        <li><a href="functions/uuid.html#uuid">uuid()</a>
</li>
        <li><a href="functions/qdigest.html#value_at_quantile">value_at_quantile()</a>
</li>
        <li><a href="functions/qdigest.html#values_at_quantiles">values_at_quantiles()</a>
</li>
        <li><a href="functions/aggregate.html#var_pop">var_pop()</a>
</li>
        <li><a href="functions/aggregate.html#var_samp">var_samp()</a>
</li>
        <li><a href="functions/aggregate.html#variance">variance()</a>
</li>
        <li><a href="functions/system.html#version">version()</a>
</li>
        <li><a href="functions/datetime.html#week">week()</a>
</li>
        <li><a href="functions/datetime.html#week_of_year">week_of_year()</a>
</li>
        <li><a href="functions/math.html#width_bucket">width_bucket()</a>
</li>
        <li><a href="functions/math.html#wilson_interval_lower">wilson_interval_lower()</a>
</li>
        <li><a href="functions/math.html#wilson_interval_upper">wilson_interval_upper()</a>
</li>
        <li><a href="functions/datetime.html#with_timezone">with_timezone()</a>
</li>
        <li><a href="functions/string.html#word_stem">word_stem()</a>
</li>
        <li><a href="functions/binary.html#xxhash64">xxhash64()</a>
</li>
        <li><a href="functions/datetime.html#year">year()</a>
</li>
        <li><a href="functions/datetime.html#year_of_week">year_of_week()</a>
</li>
        <li><a href="functions/datetime.html#yow">yow()</a>
</li>
        <li><a href="functions/array.html#zip">zip()</a>
</li>
        <li><a href="functions/array.html#zip_with">zip_with()</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    cardinality()

      <ul>
        <li><a href="functions/array.html#cardinality">built-in function</a>
</li>
      </ul></li>
      <li>
    cast()

      <ul>
        <li><a href="functions/conversion.html#cast">built-in function</a>
</li>
      </ul></li>
      <li>
    cbrt()

      <ul>
        <li><a href="functions/math.html#cbrt">built-in function</a>
</li>
      </ul></li>
      <li>
    ceil()

      <ul>
        <li><a href="functions/math.html#ceil">built-in function</a>
</li>
      </ul></li>
      <li>
    ceiling()

      <ul>
        <li><a href="functions/math.html#ceiling">built-in function</a>
</li>
      </ul></li>
      <li>
    char2hexint()

      <ul>
        <li><a href="functions/teradata.html#char2hexint">built-in function</a>
</li>
      </ul></li>
      <li>
    checksum()

      <ul>
        <li><a href="functions/aggregate.html#checksum">built-in function</a>
</li>
      </ul></li>
      <li>
    chr()

      <ul>
        <li><a href="functions/string.html#chr">built-in function</a>
</li>
      </ul></li>
      <li>
    classify()

      <ul>
        <li><a href="functions/ml.html#classify">built-in function</a>
</li>
      </ul></li>
      <li>
    coalesce()

      <ul>
        <li><a href="functions/conditional.html#id1">built-in function</a>
</li>
      </ul></li>
      <li>
    codepoint()

      <ul>
        <li><a href="functions/string.html#codepoint">built-in function</a>
</li>
      </ul></li>
      <li>
    color()

      <ul>
        <li><a href="functions/color.html#color">built-in function</a>
</li>
      </ul></li>
      <li>
    combinations()

      <ul>
        <li><a href="functions/array.html#combinations">built-in function</a>
</li>
      </ul></li>
      <li>
    concat()

      <ul>
        <li><a href="functions/string.html#concat">built-in function</a>
</li>
      </ul></li>
      <li>
    concat_ws()

      <ul>
        <li><a href="functions/string.html#concat_ws">built-in function</a>
</li>
      </ul></li>
      <li>
    contains()

      <ul>
        <li><a href="functions/array.html#contains">built-in function</a>
</li>
      </ul></li>
      <li>
    contains_sequence()

      <ul>
        <li><a href="functions/array.html#contains_sequence">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    convex_hull_agg()

      <ul>
        <li><a href="functions/geospatial.html#convex_hull_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    corr()

      <ul>
        <li><a href="functions/aggregate.html#corr">built-in function</a>
</li>
      </ul></li>
      <li>
    cos()

      <ul>
        <li><a href="functions/math.html#cos">built-in function</a>
</li>
      </ul></li>
      <li>
    cosh()

      <ul>
        <li><a href="functions/math.html#cosh">built-in function</a>
</li>
      </ul></li>
      <li>
    cosine_distance()

      <ul>
        <li><a href="functions/math.html#cosine_distance">built-in function</a>
</li>
      </ul></li>
      <li>
    cosine_similarity()

      <ul>
        <li><a href="functions/math.html#cosine_similarity">built-in function</a>
</li>
      </ul></li>
      <li>
    count()

      <ul>
        <li><a href="functions/aggregate.html#count">built-in function</a>
</li>
      </ul></li>
      <li>
    count_if()

      <ul>
        <li><a href="functions/aggregate.html#count_if">built-in function</a>
</li>
      </ul></li>
      <li>
    covar_pop()

      <ul>
        <li><a href="functions/aggregate.html#covar_pop">built-in function</a>
</li>
      </ul></li>
      <li>
    covar_samp()

      <ul>
        <li><a href="functions/aggregate.html#covar_samp">built-in function</a>
</li>
      </ul></li>
      <li>
    crc32()

      <ul>
        <li><a href="functions/binary.html#crc32">built-in function</a>
</li>
      </ul></li>
      <li>
    cume_dist()

      <ul>
        <li><a href="functions/window.html#cume_dist">built-in function</a>
</li>
      </ul></li>
      <li><a href="functions/session.html#current_catalog">current_catalog (built-in variable)</a>
</li>
      <li><a href="functions/datetime.html#current_date">current_date (built-in variable)</a>
</li>
      <li>
    current_groups()

      <ul>
        <li><a href="functions/session.html#current_groups">built-in function</a>
</li>
      </ul></li>
      <li><a href="functions/session.html#current_schema">current_schema (built-in variable)</a>
</li>
      <li><a href="functions/datetime.html#current_time">current_time (built-in variable)</a>
</li>
      <li><a href="functions/datetime.html#current_timestamp">current_timestamp (built-in variable)</a>
</li>
      <li>
    current_timezone()

      <ul>
        <li><a href="functions/datetime.html#current_timezone">built-in function</a>
</li>
      </ul></li>
      <li><a href="functions/session.html#current_user">current_user (built-in variable)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    date()

      <ul>
        <li><a href="functions/datetime.html#date">built-in function</a>
</li>
      </ul></li>
      <li>
    date_add()

      <ul>
        <li><a href="functions/datetime.html#date_add">built-in function</a>
</li>
      </ul></li>
      <li>
    date_diff()

      <ul>
        <li><a href="functions/datetime.html#date_diff">built-in function</a>
</li>
      </ul></li>
      <li>
    date_format()

      <ul>
        <li><a href="functions/datetime.html#date_format">built-in function</a>
</li>
      </ul></li>
      <li><a href="functions/datetime.html#date_parse">date_parse() (built-in function)</a>
</li>
      <li>
    date_trunc()

      <ul>
        <li><a href="functions/datetime.html#date_trunc">built-in function</a>
</li>
      </ul></li>
      <li>
    day()

      <ul>
        <li><a href="functions/datetime.html#day">built-in function</a>
</li>
      </ul></li>
      <li>
    day_of_month()

      <ul>
        <li><a href="functions/datetime.html#day_of_month">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    day_of_week()

      <ul>
        <li><a href="functions/datetime.html#day_of_week">built-in function</a>
</li>
      </ul></li>
      <li>
    day_of_year()

      <ul>
        <li><a href="functions/datetime.html#day_of_year">built-in function</a>
</li>
      </ul></li>
      <li>
    degrees()

      <ul>
        <li><a href="functions/math.html#degrees">built-in function</a>
</li>
      </ul></li>
      <li>
    dense_rank()

      <ul>
        <li><a href="functions/window.html#dense_rank">built-in function</a>
</li>
      </ul></li>
      <li>
    dot_product()

      <ul>
        <li><a href="functions/array.html#dot_product">built-in function</a>
</li>
      </ul></li>
      <li>
    dow()

      <ul>
        <li><a href="functions/datetime.html#dow">built-in function</a>
</li>
      </ul></li>
      <li>
    doy()

      <ul>
        <li><a href="functions/datetime.html#doy">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    e()

      <ul>
        <li><a href="functions/math.html#e">built-in function</a>
</li>
      </ul></li>
      <li>
    element_at()

      <ul>
        <li><a href="functions/array.html#element_at">built-in function</a>
</li>
      </ul></li>
      <li>
    empty_approx_set()

      <ul>
        <li><a href="functions/hyperloglog.html#empty_approx_set">built-in function</a>
</li>
      </ul></li>
      <li>
    euclidean_distance()

      <ul>
        <li><a href="functions/array.html#euclidean_distance">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    every()

      <ul>
        <li><a href="functions/aggregate.html#every">built-in function</a>
</li>
      </ul></li>
      <li>
    exp()

      <ul>
        <li><a href="functions/math.html#exp">built-in function</a>
</li>
      </ul></li>
      <li>
    extract()

      <ul>
        <li><a href="functions/datetime.html#extract">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    features()

      <ul>
        <li><a href="functions/ml.html#features">built-in function</a>
</li>
      </ul></li>
      <li>
    filter()

      <ul>
        <li><a href="functions/array.html#filter">built-in function</a>
</li>
      </ul></li>
      <li>
    first_value()

      <ul>
        <li><a href="functions/window.html#first_value">built-in function</a>
</li>
      </ul></li>
      <li>
    flatten()

      <ul>
        <li><a href="functions/array.html#flatten">built-in function</a>
</li>
      </ul></li>
      <li>
    floor()

      <ul>
        <li><a href="functions/math.html#floor">built-in function</a>
</li>
      </ul></li>
      <li>
    format()

      <ul>
        <li><a href="functions/conversion.html#format">built-in function</a>
</li>
      </ul></li>
      <li>
    format_datetime()

      <ul>
        <li><a href="functions/datetime.html#format_datetime">built-in function</a>
</li>
      </ul></li>
      <li>
    format_number()

      <ul>
        <li><a href="functions/conversion.html#format_number">built-in function</a>
</li>
      </ul></li>
      <li>
    from_base()

      <ul>
        <li><a href="functions/math.html#from_base">built-in function</a>
</li>
      </ul></li>
      <li>
    from_base32()

      <ul>
        <li><a href="functions/binary.html#from_base32">built-in function</a>
</li>
      </ul></li>
      <li>
    from_base64()

      <ul>
        <li><a href="functions/binary.html#from_base64">built-in function</a>
</li>
      </ul></li>
      <li>
    from_base64url()

      <ul>
        <li><a href="functions/binary.html#from_base64url">built-in function</a>
</li>
      </ul></li>
      <li>
    from_big_endian_32()

      <ul>
        <li><a href="functions/binary.html#from_big_endian_32">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    from_big_endian_64()

      <ul>
        <li><a href="functions/binary.html#from_big_endian_64">built-in function</a>
</li>
      </ul></li>
      <li>
    from_encoded_polyline()

      <ul>
        <li><a href="functions/geospatial.html#from_encoded_polyline">built-in function</a>
</li>
      </ul></li>
      <li>
    from_geojson_geometry()

      <ul>
        <li><a href="functions/geospatial.html#from_geojson_geometry">built-in function</a>
</li>
      </ul></li>
      <li>
    from_hex()

      <ul>
        <li><a href="functions/binary.html#from_hex">built-in function</a>
</li>
      </ul></li>
      <li>
    from_ieee754_32()

      <ul>
        <li><a href="functions/binary.html#from_ieee754_32">built-in function</a>
</li>
      </ul></li>
      <li>
    from_ieee754_64()

      <ul>
        <li><a href="functions/binary.html#from_ieee754_64">built-in function</a>
</li>
      </ul></li>
      <li>
    from_iso8601_date()

      <ul>
        <li><a href="functions/datetime.html#from_iso8601_date">built-in function</a>
</li>
      </ul></li>
      <li>
    from_iso8601_timestamp()

      <ul>
        <li><a href="functions/datetime.html#from_iso8601_timestamp">built-in function</a>
</li>
      </ul></li>
      <li>
    from_iso8601_timestamp_nanos()

      <ul>
        <li><a href="functions/datetime.html#from_iso8601_timestamp_nanos">built-in function</a>
</li>
      </ul></li>
      <li>
    from_unixtime()

      <ul>
        <li><a href="functions/datetime.html#from_unixtime">built-in function</a>
</li>
      </ul></li>
      <li>
    from_unixtime_nanos()

      <ul>
        <li><a href="functions/datetime.html#from_unixtime_nanos">built-in function</a>
</li>
      </ul></li>
      <li>
    from_utf8()

      <ul>
        <li><a href="functions/string.html#from_utf8">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    geometric_mean()

      <ul>
        <li><a href="functions/aggregate.html#geometric_mean">built-in function</a>
</li>
      </ul></li>
      <li>
    geometry_from_hadoop_shape()

      <ul>
        <li><a href="functions/geospatial.html#geometry_from_hadoop_shape">built-in function</a>
</li>
      </ul></li>
      <li>
    geometry_invalid_reason()

      <ul>
        <li><a href="functions/geospatial.html#geometry_invalid_reason">built-in function</a>
</li>
      </ul></li>
      <li>
    geometry_nearest_points()

      <ul>
        <li><a href="functions/geospatial.html#geometry_nearest_points">built-in function</a>
</li>
      </ul></li>
      <li>
    geometry_to_bing_tiles()

      <ul>
        <li><a href="functions/geospatial.html#geometry_to_bing_tiles">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    geometry_union()

      <ul>
        <li><a href="functions/geospatial.html#geometry_union">built-in function</a>
</li>
      </ul></li>
      <li>
    geometry_union_agg()

      <ul>
        <li><a href="functions/geospatial.html#geometry_union_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    great_circle_distance()

      <ul>
        <li><a href="functions/geospatial.html#great_circle_distance">built-in function</a>
</li>
      </ul></li>
      <li>
    greatest()

      <ul>
        <li><a href="functions/comparison.html#greatest">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    hamming_distance()

      <ul>
        <li><a href="functions/string.html#hamming_distance">built-in function</a>
</li>
      </ul></li>
      <li>
    hash_counts()

      <ul>
        <li><a href="functions/setdigest.html#hash_counts">built-in function</a>
</li>
      </ul></li>
      <li>
    histogram()

      <ul>
        <li><a href="functions/aggregate.html#histogram">built-in function</a>
</li>
      </ul></li>
      <li>
    hmac_md5()

      <ul>
        <li><a href="functions/binary.html#hmac_md5">built-in function</a>
</li>
      </ul></li>
      <li>
    hmac_sha1()

      <ul>
        <li><a href="functions/binary.html#hmac_sha1">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    hmac_sha256()

      <ul>
        <li><a href="functions/binary.html#hmac_sha256">built-in function</a>
</li>
      </ul></li>
      <li>
    hmac_sha512()

      <ul>
        <li><a href="functions/binary.html#hmac_sha512">built-in function</a>
</li>
      </ul></li>
      <li>
    hour()

      <ul>
        <li><a href="functions/datetime.html#hour">built-in function</a>
</li>
      </ul></li>
      <li>
    human_readable_seconds()

      <ul>
        <li><a href="functions/datetime.html#human_readable_seconds">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    if()

      <ul>
        <li><a href="functions/conditional.html#id0">built-in function</a>
</li>
      </ul></li>
      <li>
    index()

      <ul>
        <li><a href="functions/teradata.html#index">built-in function</a>
</li>
      </ul></li>
      <li>
    infinity()

      <ul>
        <li><a href="functions/math.html#infinity">built-in function</a>
</li>
      </ul></li>
      <li>
    intersection_cardinality()

      <ul>
        <li><a href="functions/setdigest.html#intersection_cardinality">built-in function</a>
</li>
      </ul></li>
      <li>
    inverse_beta_cdf()

      <ul>
        <li><a href="functions/math.html#inverse_beta_cdf">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    inverse_normal_cdf()

      <ul>
        <li><a href="functions/math.html#inverse_normal_cdf">built-in function</a>
</li>
      </ul></li>
      <li>
    is_finite()

      <ul>
        <li><a href="functions/math.html#is_finite">built-in function</a>
</li>
      </ul></li>
      <li>
    is_infinite()

      <ul>
        <li><a href="functions/math.html#is_infinite">built-in function</a>
</li>
      </ul></li>
      <li>
    is_json_scalar()

      <ul>
        <li><a href="functions/json.html#is_json_scalar">built-in function</a>
</li>
      </ul></li>
      <li>
    is_nan()

      <ul>
        <li><a href="functions/math.html#is_nan">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    jaccard_index()

      <ul>
        <li><a href="functions/setdigest.html#jaccard_index">built-in function</a>
</li>
      </ul></li>
      <li>
    json_array_contains()

      <ul>
        <li><a href="functions/json.html#json_array_contains">built-in function</a>
</li>
      </ul></li>
      <li>
    json_array_get()

      <ul>
        <li><a href="functions/json.html#json_array_get">built-in function</a>
</li>
      </ul></li>
      <li>
    json_array_length()

      <ul>
        <li><a href="functions/json.html#json_array_length">built-in function</a>
</li>
      </ul></li>
      <li>
    json_extract()

      <ul>
        <li><a href="functions/json.html#json_extract">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    json_extract_scalar()

      <ul>
        <li><a href="functions/json.html#json_extract_scalar">built-in function</a>
</li>
      </ul></li>
      <li>
    json_format()

      <ul>
        <li><a href="functions/json.html#json_format">built-in function</a>
</li>
      </ul></li>
      <li>
    json_parse()

      <ul>
        <li><a href="functions/json.html#json_parse">built-in function</a>
</li>
      </ul></li>
      <li>
    json_size()

      <ul>
        <li><a href="functions/json.html#json_size">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    kurtosis()

      <ul>
        <li><a href="functions/aggregate.html#kurtosis">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    lag()

      <ul>
        <li><a href="functions/window.html#lag">built-in function</a>
</li>
      </ul></li>
      <li>
    last_day_of_month()

      <ul>
        <li><a href="functions/datetime.html#last_day_of_month">built-in function</a>
</li>
      </ul></li>
      <li>
    last_value()

      <ul>
        <li><a href="functions/window.html#last_value">built-in function</a>
</li>
      </ul></li>
      <li>
    lead()

      <ul>
        <li><a href="functions/window.html#lead">built-in function</a>
</li>
      </ul></li>
      <li>
    learn_classifier()

      <ul>
        <li><a href="functions/ml.html#learn_classifier">built-in function</a>
</li>
      </ul></li>
      <li>
    learn_libsvm_classifier()

      <ul>
        <li><a href="functions/ml.html#learn_libsvm_classifier">built-in function</a>
</li>
      </ul></li>
      <li>
    learn_libsvm_regressor()

      <ul>
        <li><a href="functions/ml.html#learn_libsvm_regressor">built-in function</a>
</li>
      </ul></li>
      <li>
    learn_regressor()

      <ul>
        <li><a href="functions/ml.html#learn_regressor">built-in function</a>
</li>
      </ul></li>
      <li>
    least()

      <ul>
        <li><a href="functions/comparison.html#least">built-in function</a>
</li>
      </ul></li>
      <li>
    length()

      <ul>
        <li><a href="functions/string.html#length">built-in function</a>
</li>
      </ul></li>
      <li>
    levenshtein_distance()

      <ul>
        <li><a href="functions/string.html#levenshtein_distance">built-in function</a>
</li>
      </ul></li>
      <li>
    line_interpolate_point()

      <ul>
        <li><a href="functions/geospatial.html#line_interpolate_point">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    line_interpolate_points()

      <ul>
        <li><a href="functions/geospatial.html#line_interpolate_points">built-in function</a>
</li>
      </ul></li>
      <li>
    line_locate_point()

      <ul>
        <li><a href="functions/geospatial.html#line_locate_point">built-in function</a>
</li>
      </ul></li>
      <li>
    listagg()

      <ul>
        <li><a href="functions/aggregate.html#listagg">built-in function</a>
</li>
      </ul></li>
      <li>
    ln()

      <ul>
        <li><a href="functions/math.html#ln">built-in function</a>
</li>
      </ul></li>
      <li><a href="functions/datetime.html#localtime">localtime (built-in variable)</a>
</li>
      <li><a href="functions/datetime.html#localtimestamp">localtimestamp (built-in variable)</a>
</li>
      <li>
    log()

      <ul>
        <li><a href="functions/math.html#log">built-in function</a>
</li>
      </ul></li>
      <li>
    log10()

      <ul>
        <li><a href="functions/math.html#log10">built-in function</a>
</li>
      </ul></li>
      <li>
    log2()

      <ul>
        <li><a href="functions/math.html#log2">built-in function</a>
</li>
      </ul></li>
      <li>
    lower()

      <ul>
        <li><a href="functions/string.html#lower">built-in function</a>
</li>
      </ul></li>
      <li>
    lpad()

      <ul>
        <li><a href="functions/string.html#lpad">built-in function</a>
</li>
      </ul></li>
      <li>
    ltrim()

      <ul>
        <li><a href="functions/string.html#ltrim">built-in function</a>
</li>
      </ul></li>
      <li>
    luhn_check()

      <ul>
        <li><a href="functions/string.html#luhn_check">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    make_set_digest()

      <ul>
        <li><a href="functions/setdigest.html#make_set_digest">built-in function</a>
</li>
      </ul></li>
      <li>
    map()

      <ul>
        <li><a href="functions/map.html#map">built-in function</a>
</li>
      </ul></li>
      <li>
    map_agg()

      <ul>
        <li><a href="functions/aggregate.html#map_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    map_concat()

      <ul>
        <li><a href="functions/map.html#map_concat">built-in function</a>
</li>
      </ul></li>
      <li>
    map_entries()

      <ul>
        <li><a href="functions/map.html#map_entries">built-in function</a>
</li>
      </ul></li>
      <li>
    map_filter()

      <ul>
        <li><a href="functions/map.html#map_filter">built-in function</a>
</li>
      </ul></li>
      <li>
    map_from_entries()

      <ul>
        <li><a href="functions/map.html#map_from_entries">built-in function</a>
</li>
      </ul></li>
      <li>
    map_keys()

      <ul>
        <li><a href="functions/map.html#map_keys">built-in function</a>
</li>
      </ul></li>
      <li>
    map_union()

      <ul>
        <li><a href="functions/aggregate.html#map_union">built-in function</a>
</li>
      </ul></li>
      <li>
    map_values()

      <ul>
        <li><a href="functions/map.html#map_values">built-in function</a>
</li>
      </ul></li>
      <li>
    map_zip_with()

      <ul>
        <li><a href="functions/map.html#map_zip_with">built-in function</a>
</li>
      </ul></li>
      <li>
    max()

      <ul>
        <li><a href="functions/aggregate.html#max">built-in function</a>
</li>
      </ul></li>
      <li>
    max_by()

      <ul>
        <li><a href="functions/aggregate.html#max_by">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    md5()

      <ul>
        <li><a href="functions/binary.html#md5">built-in function</a>
</li>
      </ul></li>
      <li>
    merge()

      <ul>
        <li><a href="functions/hyperloglog.html#merge">built-in function</a>
</li>
      </ul></li>
      <li>
    merge_set_digest()

      <ul>
        <li><a href="functions/setdigest.html#merge_set_digest">built-in function</a>
</li>
      </ul></li>
      <li>
    millisecond()

      <ul>
        <li><a href="functions/datetime.html#millisecond">built-in function</a>
</li>
      </ul></li>
      <li>
    min()

      <ul>
        <li><a href="functions/aggregate.html#min">built-in function</a>
</li>
      </ul></li>
      <li>
    min_by()

      <ul>
        <li><a href="functions/aggregate.html#min_by">built-in function</a>
</li>
      </ul></li>
      <li>
    minute()

      <ul>
        <li><a href="functions/datetime.html#minute">built-in function</a>
</li>
      </ul></li>
      <li>
    mod()

      <ul>
        <li><a href="functions/math.html#mod">built-in function</a>
</li>
      </ul></li>
      <li>
    month()

      <ul>
        <li><a href="functions/datetime.html#month">built-in function</a>
</li>
      </ul></li>
      <li>
    multimap_agg()

      <ul>
        <li><a href="functions/aggregate.html#multimap_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    multimap_from_entries()

      <ul>
        <li><a href="functions/map.html#multimap_from_entries">built-in function</a>
</li>
      </ul></li>
      <li>
    murmur3()

      <ul>
        <li><a href="functions/binary.html#murmur3">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    nan()

      <ul>
        <li><a href="functions/math.html#nan">built-in function</a>
</li>
      </ul></li>
      <li>
    ngrams()

      <ul>
        <li><a href="functions/array.html#ngrams">built-in function</a>
</li>
      </ul></li>
      <li>
    none_match()

      <ul>
        <li><a href="functions/array.html#none_match">built-in function</a>
</li>
      </ul></li>
      <li>
    normal_cdf()

      <ul>
        <li><a href="functions/math.html#normal_cdf">built-in function</a>
</li>
      </ul></li>
      <li>
    normalize()

      <ul>
        <li><a href="functions/string.html#normalize">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    now()

      <ul>
        <li><a href="functions/datetime.html#now">built-in function</a>
</li>
      </ul></li>
      <li>
    nth_value()

      <ul>
        <li><a href="functions/window.html#nth_value">built-in function</a>
</li>
      </ul></li>
      <li>
    ntile()

      <ul>
        <li><a href="functions/window.html#ntile">built-in function</a>
</li>
      </ul></li>
      <li>
    nullif()

      <ul>
        <li><a href="functions/conditional.html#id2">built-in function</a>
</li>
      </ul></li>
      <li>
    numeric_histogram()

      <ul>
        <li><a href="functions/aggregate.html#numeric_histogram">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    objectid_timestamp()

      <ul>
        <li><a href="connector/mongodb.html#objectid_timestamp">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    parse_data_size()

      <ul>
        <li><a href="functions/conversion.html#parse_data_size">built-in function</a>
</li>
      </ul></li>
      <li>
    parse_datetime()

      <ul>
        <li><a href="functions/datetime.html#parse_datetime">built-in function</a>
</li>
      </ul></li>
      <li>
    parse_duration()

      <ul>
        <li><a href="functions/datetime.html#parse_duration">built-in function</a>
</li>
      </ul></li>
      <li>
    percent_rank()

      <ul>
        <li><a href="functions/window.html#percent_rank">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    pi()

      <ul>
        <li><a href="functions/math.html#pi">built-in function</a>
</li>
      </ul></li>
      <li>
    position()

      <ul>
        <li><a href="functions/string.html#position">built-in function</a>
</li>
      </ul></li>
      <li>
    pow()

      <ul>
        <li><a href="functions/math.html#pow">built-in function</a>
</li>
      </ul></li>
      <li>
    power()

      <ul>
        <li><a href="functions/math.html#power">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    qdigest_agg()

      <ul>
        <li><a href="functions/qdigest.html#qdigest_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    quantile_at_value()

      <ul>
        <li><a href="functions/qdigest.html#quantile_at_value">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    quarter()

      <ul>
        <li><a href="functions/datetime.html#quarter">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    radians()

      <ul>
        <li><a href="functions/math.html#radians">built-in function</a>
</li>
      </ul></li>
      <li>
    rand()

      <ul>
        <li><a href="functions/math.html#rand">built-in function</a>
</li>
      </ul></li>
      <li>
    random()

      <ul>
        <li><a href="functions/math.html#random">built-in function</a>
</li>
      </ul></li>
      <li>
    random_string()

      <ul>
        <li><a href="connector/faker.html#random_string">built-in function</a>
</li>
      </ul></li>
      <li>
    rank()

      <ul>
        <li><a href="functions/window.html#rank">built-in function</a>
</li>
      </ul></li>
      <li>
    reduce()

      <ul>
        <li><a href="functions/array.html#reduce">built-in function</a>
</li>
      </ul></li>
      <li>
    reduce_agg()

      <ul>
        <li><a href="functions/aggregate.html#reduce_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_count()

      <ul>
        <li><a href="functions/regexp.html#regexp_count">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_extract()

      <ul>
        <li><a href="functions/regexp.html#regexp_extract">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_extract_all()

      <ul>
        <li><a href="functions/regexp.html#regexp_extract_all">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_like()

      <ul>
        <li><a href="functions/regexp.html#regexp_like">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_position()

      <ul>
        <li><a href="functions/regexp.html#regexp_position">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_replace()

      <ul>
        <li><a href="functions/regexp.html#regexp_replace">built-in function</a>
</li>
      </ul></li>
      <li>
    regexp_split()

      <ul>
        <li><a href="functions/regexp.html#regexp_split">built-in function</a>
</li>
      </ul></li>
      <li>
    regr_intercept()

      <ul>
        <li><a href="functions/aggregate.html#regr_intercept">built-in function</a>
</li>
      </ul></li>
      <li>
    regr_slope()

      <ul>
        <li><a href="functions/aggregate.html#regr_slope">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    regress()

      <ul>
        <li><a href="functions/ml.html#regress">built-in function</a>
</li>
      </ul></li>
      <li>
    render()

      <ul>
        <li><a href="functions/color.html#render">built-in function</a>
</li>
      </ul></li>
      <li>
    repeat()

      <ul>
        <li><a href="functions/array.html#repeat">built-in function</a>
</li>
      </ul></li>
      <li>
    replace()

      <ul>
        <li><a href="functions/string.html#replace">built-in function</a>
</li>
      </ul></li>
      <li>
    reverse()

      <ul>
        <li><a href="functions/string.html#reverse">built-in function</a>
</li>
      </ul></li>
      <li>
    RFC

      <ul>
        <li><a href="functions/url.html#index-1">RFC 1866#section-8.2.1</a>
</li>
        <li><a href="functions/url.html#index-0">RFC 2396</a>
</li>
        <li><a href="connector/kafka.html#index-2">RFC 2822</a>, <a href="connector/redis.html#index-1">[1]</a>
</li>
        <li><a href="language/types.html#index-2">RFC 4122</a>
</li>
        <li><a href="language/types.html#index-0">RFC 4291#section-2.5.5.2</a>
</li>
        <li><a href="connector/kafka.html#index-0">RFC 4627</a>, <a href="connector/kafka.html#index-1">[1]</a>, <a href="connector/redis.html#index-0">[2]</a>
</li>
        <li><a href="functions/binary.html#index-0">RFC 4648</a>
</li>
        <li><a href="language/types.html#index-1">RFC 5952</a>
</li>
        <li><a href="functions/json.html#index-0">RFC 7159</a>, <a href="functions/json.html#index-1">[1]</a>
</li>
        <li><a href="release/release-334.html#index-0">RFC 7239</a>
</li>
      </ul></li>
      <li>
    rgb()

      <ul>
        <li><a href="functions/color.html#rgb">built-in function</a>
</li>
      </ul></li>
      <li>
    round()

      <ul>
        <li><a href="functions/math.html#round">built-in function</a>
</li>
      </ul></li>
      <li>
    row_number()

      <ul>
        <li><a href="functions/window.html#row_number">built-in function</a>
</li>
      </ul></li>
      <li>
    rpad()

      <ul>
        <li><a href="functions/string.html#rpad">built-in function</a>
</li>
      </ul></li>
      <li>
    rtrim()

      <ul>
        <li><a href="functions/string.html#rtrim">built-in function</a>
</li>
      </ul></li>
      <li>
    runtime.kill_query()

      <ul>
        <li><a href="connector/system.html#runtime.kill_query">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    second()

      <ul>
        <li><a href="functions/datetime.html#second">built-in function</a>
</li>
      </ul></li>
      <li>
    sequence()

      <ul>
        <li><a href="functions/array.html#sequence">built-in function</a>
</li>
      </ul></li>
      <li>
    sha1()

      <ul>
        <li><a href="functions/binary.html#sha1">built-in function</a>
</li>
      </ul></li>
      <li>
    sha256()

      <ul>
        <li><a href="functions/binary.html#sha256">built-in function</a>
</li>
      </ul></li>
      <li>
    sha512()

      <ul>
        <li><a href="functions/binary.html#sha512">built-in function</a>
</li>
      </ul></li>
      <li>
    shuffle()

      <ul>
        <li><a href="functions/array.html#shuffle">built-in function</a>
</li>
      </ul></li>
      <li>
    sign()

      <ul>
        <li><a href="functions/math.html#sign">built-in function</a>
</li>
      </ul></li>
      <li>
    simplify_geometry()

      <ul>
        <li><a href="functions/geospatial.html#simplify_geometry">built-in function</a>
</li>
      </ul></li>
      <li>
    sin()

      <ul>
        <li><a href="functions/math.html#sin">built-in function</a>
</li>
      </ul></li>
      <li>
    sinh()

      <ul>
        <li><a href="functions/math.html#sinh">built-in function</a>
</li>
      </ul></li>
      <li>
    skewness()

      <ul>
        <li><a href="functions/aggregate.html#skewness">built-in function</a>
</li>
      </ul></li>
      <li>
    slice()

      <ul>
        <li><a href="functions/array.html#slice">built-in function</a>
</li>
      </ul></li>
      <li>
    soundex()

      <ul>
        <li><a href="functions/string.html#soundex">built-in function</a>
</li>
      </ul></li>
      <li>
    split()

      <ul>
        <li><a href="functions/string.html#split">built-in function</a>
</li>
      </ul></li>
      <li>
    split_part()

      <ul>
        <li><a href="functions/string.html#split_part">built-in function</a>
</li>
      </ul></li>
      <li>
    split_to_map()

      <ul>
        <li><a href="functions/string.html#split_to_map">built-in function</a>
</li>
      </ul></li>
      <li>
    split_to_multimap()

      <ul>
        <li><a href="functions/string.html#split_to_multimap">built-in function</a>
</li>
      </ul></li>
      <li>
    spooky_hash_v2_32()

      <ul>
        <li><a href="functions/binary.html#spooky_hash_v2_32">built-in function</a>
</li>
      </ul></li>
      <li>
    spooky_hash_v2_64()

      <ul>
        <li><a href="functions/binary.html#spooky_hash_v2_64">built-in function</a>
</li>
      </ul></li>
      <li>
    sqrt()

      <ul>
        <li><a href="functions/math.html#sqrt">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Area()

      <ul>
        <li><a href="functions/geospatial.html#ST_Area">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_AsBinary()

      <ul>
        <li><a href="functions/geospatial.html#ST_AsBinary">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_AsText()

      <ul>
        <li><a href="functions/geospatial.html#ST_AsText">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Boundary()

      <ul>
        <li><a href="functions/geospatial.html#ST_Boundary">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Buffer()

      <ul>
        <li><a href="functions/geospatial.html#ST_Buffer">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Centroid()

      <ul>
        <li><a href="functions/geospatial.html#ST_Centroid">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Contains()

      <ul>
        <li><a href="functions/geospatial.html#ST_Contains">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_ConvexHull()

      <ul>
        <li><a href="functions/geospatial.html#ST_ConvexHull">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_CoordDim()

      <ul>
        <li><a href="functions/geospatial.html#ST_CoordDim">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Crosses()

      <ul>
        <li><a href="functions/geospatial.html#ST_Crosses">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Difference()

      <ul>
        <li><a href="functions/geospatial.html#ST_Difference">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Dimension()

      <ul>
        <li><a href="functions/geospatial.html#ST_Dimension">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Disjoint()

      <ul>
        <li><a href="functions/geospatial.html#ST_Disjoint">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Distance()

      <ul>
        <li><a href="functions/geospatial.html#ST_Distance">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_EndPoint()

      <ul>
        <li><a href="functions/geospatial.html#ST_EndPoint">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Envelope()

      <ul>
        <li><a href="functions/geospatial.html#ST_Envelope">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_EnvelopeAsPts()

      <ul>
        <li><a href="functions/geospatial.html#ST_EnvelopeAsPts">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Equals()

      <ul>
        <li><a href="functions/geospatial.html#ST_Equals">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_ExteriorRing()

      <ul>
        <li><a href="functions/geospatial.html#ST_ExteriorRing">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Geometries()

      <ul>
        <li><a href="functions/geospatial.html#ST_Geometries">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_GeometryFromText()

      <ul>
        <li><a href="functions/geospatial.html#ST_GeometryFromText">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_GeometryN()

      <ul>
        <li><a href="functions/geospatial.html#ST_GeometryN">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_GeometryType()

      <ul>
        <li><a href="functions/geospatial.html#ST_GeometryType">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    ST_GeomFromBinary()

      <ul>
        <li><a href="functions/geospatial.html#ST_GeomFromBinary">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_GeomFromKML()

      <ul>
        <li><a href="functions/geospatial.html#ST_GeomFromKML">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_InteriorRingN()

      <ul>
        <li><a href="functions/geospatial.html#ST_InteriorRingN">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_InteriorRings()

      <ul>
        <li><a href="functions/geospatial.html#ST_InteriorRings">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Intersection()

      <ul>
        <li><a href="functions/geospatial.html#ST_Intersection">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Intersects()

      <ul>
        <li><a href="functions/geospatial.html#ST_Intersects">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_IsClosed()

      <ul>
        <li><a href="functions/geospatial.html#ST_IsClosed">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_IsEmpty()

      <ul>
        <li><a href="functions/geospatial.html#ST_IsEmpty">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_IsRing()

      <ul>
        <li><a href="functions/geospatial.html#ST_IsRing">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_IsSimple()

      <ul>
        <li><a href="functions/geospatial.html#ST_IsSimple">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_IsValid()

      <ul>
        <li><a href="functions/geospatial.html#ST_IsValid">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Length()

      <ul>
        <li><a href="functions/geospatial.html#ST_Length">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_LineFromText()

      <ul>
        <li><a href="functions/geospatial.html#ST_LineFromText">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_LineString()

      <ul>
        <li><a href="functions/geospatial.html#ST_LineString">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_MultiPoint()

      <ul>
        <li><a href="functions/geospatial.html#ST_MultiPoint">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_NumGeometries()

      <ul>
        <li><a href="functions/geospatial.html#ST_NumGeometries">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_NumInteriorRing()

      <ul>
        <li><a href="functions/geospatial.html#ST_NumInteriorRing">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_NumPoints()

      <ul>
        <li><a href="functions/geospatial.html#ST_NumPoints">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Overlaps()

      <ul>
        <li><a href="functions/geospatial.html#ST_Overlaps">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Point()

      <ul>
        <li><a href="functions/geospatial.html#ST_Point">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_PointN()

      <ul>
        <li><a href="functions/geospatial.html#ST_PointN">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Points()

      <ul>
        <li><a href="functions/geospatial.html#ST_Points">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Polygon()

      <ul>
        <li><a href="functions/geospatial.html#ST_Polygon">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Relate()

      <ul>
        <li><a href="functions/geospatial.html#ST_Relate">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_StartPoint()

      <ul>
        <li><a href="functions/geospatial.html#ST_StartPoint">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_SymDifference()

      <ul>
        <li><a href="functions/geospatial.html#ST_SymDifference">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Touches()

      <ul>
        <li><a href="functions/geospatial.html#ST_Touches">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Union()

      <ul>
        <li><a href="functions/geospatial.html#ST_Union">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Within()

      <ul>
        <li><a href="functions/geospatial.html#ST_Within">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_X()

      <ul>
        <li><a href="functions/geospatial.html#ST_X">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_XMax()

      <ul>
        <li><a href="functions/geospatial.html#ST_XMax">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_XMin()

      <ul>
        <li><a href="functions/geospatial.html#ST_XMin">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_Y()

      <ul>
        <li><a href="functions/geospatial.html#ST_Y">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_YMax()

      <ul>
        <li><a href="functions/geospatial.html#ST_YMax">built-in function</a>
</li>
      </ul></li>
      <li>
    ST_YMin()

      <ul>
        <li><a href="functions/geospatial.html#ST_YMin">built-in function</a>
</li>
      </ul></li>
      <li>
    starts_with()

      <ul>
        <li><a href="functions/string.html#starts_with">built-in function</a>
</li>
      </ul></li>
      <li>
    stddev()

      <ul>
        <li><a href="functions/aggregate.html#stddev">built-in function</a>
</li>
      </ul></li>
      <li>
    stddev_pop()

      <ul>
        <li><a href="functions/aggregate.html#stddev_pop">built-in function</a>
</li>
      </ul></li>
      <li>
    stddev_samp()

      <ul>
        <li><a href="functions/aggregate.html#stddev_samp">built-in function</a>
</li>
      </ul></li>
      <li>
    strpos()

      <ul>
        <li><a href="functions/string.html#strpos">built-in function</a>
</li>
      </ul></li>
      <li>
    substr()

      <ul>
        <li><a href="functions/string.html#substr">built-in function</a>
</li>
      </ul></li>
      <li>
    substring()

      <ul>
        <li><a href="functions/string.html#substring">built-in function</a>
</li>
      </ul></li>
      <li>
    sum()

      <ul>
        <li><a href="functions/aggregate.html#sum">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    t_cdf()

      <ul>
        <li><a href="functions/math.html#t_cdf">built-in function</a>
</li>
      </ul></li>
      <li>
    t_pdf()

      <ul>
        <li><a href="functions/math.html#t_pdf">built-in function</a>
</li>
      </ul></li>
      <li>
    tan()

      <ul>
        <li><a href="functions/math.html#tan">built-in function</a>
</li>
      </ul></li>
      <li>
    tanh()

      <ul>
        <li><a href="functions/math.html#tanh">built-in function</a>
</li>
      </ul></li>
      <li>
    tdigest_agg()

      <ul>
        <li><a href="functions/tdigest.html#tdigest_agg">built-in function</a>
</li>
      </ul></li>
      <li>
    timestamp_objectid()

      <ul>
        <li><a href="connector/mongodb.html#timestamp_objectid">built-in function</a>
</li>
      </ul></li>
      <li>
    timezone()

      <ul>
        <li><a href="functions/datetime.html#timezone">built-in function</a>
</li>
      </ul></li>
      <li>
    timezone_hour()

      <ul>
        <li><a href="functions/datetime.html#timezone_hour">built-in function</a>
</li>
      </ul></li>
      <li>
    timezone_minute()

      <ul>
        <li><a href="functions/datetime.html#timezone_minute">built-in function</a>
</li>
      </ul></li>
      <li>
    to_base()

      <ul>
        <li><a href="functions/math.html#to_base">built-in function</a>
</li>
      </ul></li>
      <li>
    to_base32()

      <ul>
        <li><a href="functions/binary.html#to_base32">built-in function</a>
</li>
      </ul></li>
      <li>
    to_base64()

      <ul>
        <li><a href="functions/binary.html#to_base64">built-in function</a>
</li>
      </ul></li>
      <li>
    to_base64url()

      <ul>
        <li><a href="functions/binary.html#to_base64url">built-in function</a>
</li>
      </ul></li>
      <li>
    to_big_endian_32()

      <ul>
        <li><a href="functions/binary.html#to_big_endian_32">built-in function</a>
</li>
      </ul></li>
      <li>
    to_big_endian_64()

      <ul>
        <li><a href="functions/binary.html#to_big_endian_64">built-in function</a>
</li>
      </ul></li>
      <li>
    to_char()

      <ul>
        <li><a href="functions/teradata.html#to_char">built-in function</a>
</li>
      </ul></li>
      <li>
    to_date()

      <ul>
        <li><a href="functions/teradata.html#to_date">built-in function</a>
</li>
      </ul></li>
      <li>
    to_encoded_polyline()

      <ul>
        <li><a href="functions/geospatial.html#to_encoded_polyline">built-in function</a>
</li>
      </ul></li>
      <li>
    to_geojson_geometry()

      <ul>
        <li><a href="functions/geospatial.html#to_geojson_geometry">built-in function</a>
</li>
      </ul></li>
      <li>
    to_geometry()

      <ul>
        <li><a href="functions/geospatial.html#to_geometry">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    to_hex()

      <ul>
        <li><a href="functions/binary.html#to_hex">built-in function</a>
</li>
      </ul></li>
      <li>
    to_ieee754_32()

      <ul>
        <li><a href="functions/binary.html#to_ieee754_32">built-in function</a>
</li>
      </ul></li>
      <li>
    to_ieee754_64()

      <ul>
        <li><a href="functions/binary.html#to_ieee754_64">built-in function</a>
</li>
      </ul></li>
      <li>
    to_iso8601()

      <ul>
        <li><a href="functions/datetime.html#to_iso8601">built-in function</a>
</li>
      </ul></li>
      <li>
    to_milliseconds()

      <ul>
        <li><a href="functions/datetime.html#to_milliseconds">built-in function</a>
</li>
      </ul></li>
      <li>
    to_spherical_geography()

      <ul>
        <li><a href="functions/geospatial.html#to_spherical_geography">built-in function</a>
</li>
      </ul></li>
      <li>
    to_timestamp()

      <ul>
        <li><a href="functions/teradata.html#to_timestamp">built-in function</a>
</li>
      </ul></li>
      <li>
    to_unixtime()

      <ul>
        <li><a href="functions/datetime.html#to_unixtime">built-in function</a>
</li>
      </ul></li>
      <li>
    to_utf8()

      <ul>
        <li><a href="functions/string.html#to_utf8">built-in function</a>
</li>
      </ul></li>
      <li>
    transform()

      <ul>
        <li><a href="functions/array.html#transform">built-in function</a>
</li>
      </ul></li>
      <li>
    transform_keys()

      <ul>
        <li><a href="functions/map.html#transform_keys">built-in function</a>
</li>
      </ul></li>
      <li>
    transform_values()

      <ul>
        <li><a href="functions/map.html#transform_values">built-in function</a>
</li>
      </ul></li>
      <li>
    translate()

      <ul>
        <li><a href="functions/string.html#translate">built-in function</a>
</li>
      </ul></li>
      <li>
    trim()

      <ul>
        <li><a href="functions/string.html#trim">built-in function</a>
</li>
      </ul></li>
      <li>
    trim_array()

      <ul>
        <li><a href="functions/array.html#trim_array">built-in function</a>
</li>
      </ul></li>
      <li>
    truncate()

      <ul>
        <li><a href="functions/math.html#truncate">built-in function</a>
</li>
      </ul></li>
      <li>
    try()

      <ul>
        <li><a href="functions/conditional.html#id3">built-in function</a>
</li>
      </ul></li>
      <li>
    try_cast()

      <ul>
        <li><a href="functions/conversion.html#try_cast">built-in function</a>
</li>
      </ul></li>
      <li>
    typeof()

      <ul>
        <li><a href="functions/conversion.html#typeof">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    upper()

      <ul>
        <li><a href="functions/string.html#upper">built-in function</a>
</li>
      </ul></li>
      <li>
    url_decode()

      <ul>
        <li><a href="functions/url.html#url_decode">built-in function</a>
</li>
      </ul></li>
      <li>
    url_encode()

      <ul>
        <li><a href="functions/url.html#url_encode">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_fragment()

      <ul>
        <li><a href="functions/url.html#url_extract_fragment">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_host()

      <ul>
        <li><a href="functions/url.html#url_extract_host">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_parameter()

      <ul>
        <li><a href="functions/url.html#url_extract_parameter">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    url_extract_path()

      <ul>
        <li><a href="functions/url.html#url_extract_path">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_port()

      <ul>
        <li><a href="functions/url.html#url_extract_port">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_protocol()

      <ul>
        <li><a href="functions/url.html#url_extract_protocol">built-in function</a>
</li>
      </ul></li>
      <li>
    url_extract_query()

      <ul>
        <li><a href="functions/url.html#url_extract_query">built-in function</a>
</li>
      </ul></li>
      <li>
    uuid()

      <ul>
        <li><a href="functions/uuid.html#uuid">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    value_at_quantile()

      <ul>
        <li><a href="functions/qdigest.html#value_at_quantile">built-in function</a>
</li>
      </ul></li>
      <li>
    values_at_quantiles()

      <ul>
        <li><a href="functions/qdigest.html#values_at_quantiles">built-in function</a>
</li>
      </ul></li>
      <li>
    var_pop()

      <ul>
        <li><a href="functions/aggregate.html#var_pop">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    var_samp()

      <ul>
        <li><a href="functions/aggregate.html#var_samp">built-in function</a>
</li>
      </ul></li>
      <li>
    variance()

      <ul>
        <li><a href="functions/aggregate.html#variance">built-in function</a>
</li>
      </ul></li>
      <li>
    version()

      <ul>
        <li><a href="functions/system.html#version">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    week()

      <ul>
        <li><a href="functions/datetime.html#week">built-in function</a>
</li>
      </ul></li>
      <li>
    week_of_year()

      <ul>
        <li><a href="functions/datetime.html#week_of_year">built-in function</a>
</li>
      </ul></li>
      <li>
    width_bucket()

      <ul>
        <li><a href="functions/math.html#width_bucket">built-in function</a>
</li>
      </ul></li>
      <li>
    wilson_interval_lower()

      <ul>
        <li><a href="functions/math.html#wilson_interval_lower">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    wilson_interval_upper()

      <ul>
        <li><a href="functions/math.html#wilson_interval_upper">built-in function</a>
</li>
      </ul></li>
      <li>
    with_timezone()

      <ul>
        <li><a href="functions/datetime.html#with_timezone">built-in function</a>
</li>
      </ul></li>
      <li>
    word_stem()

      <ul>
        <li><a href="functions/string.html#word_stem">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="X">X</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    xxhash64()

      <ul>
        <li><a href="functions/binary.html#xxhash64">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Y">Y</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    year()

      <ul>
        <li><a href="functions/datetime.html#year">built-in function</a>
</li>
      </ul></li>
      <li>
    year_of_week()

      <ul>
        <li><a href="functions/datetime.html#year_of_week">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    yow()

      <ul>
        <li><a href="functions/datetime.html#yow">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Z">Z</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    zip()

      <ul>
        <li><a href="functions/array.html#zip">built-in function</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    zip_with()

      <ul>
        <li><a href="functions/array.html#zip_with">built-in function</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>



          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>