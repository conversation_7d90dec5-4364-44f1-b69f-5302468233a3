<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Set Digest functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="setdigest.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="String functions and operators" href="string.html" />
    <link rel="prev" title="Session information" href="session.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="setdigest.html#functions/setdigest" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Set Digest functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Set Digest </label>
    
      <a href="setdigest.html#" class="md-nav__link md-nav__link--active">Set Digest</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="setdigest.html#data-structures" class="md-nav__link">Data structures</a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#serialization" class="md-nav__link">Serialization</a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#functions" class="md-nav__link">Functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="setdigest.html#make_set_digest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">make_set_digest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#merge_set_digest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">merge_set_digest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#intersection_cardinality" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">intersection_cardinality()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#jaccard_index" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">jaccard_index()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#hash_counts" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hash_counts()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="setdigest.html#data-structures" class="md-nav__link">Data structures</a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#serialization" class="md-nav__link">Serialization</a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#functions" class="md-nav__link">Functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="setdigest.html#make_set_digest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">make_set_digest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#merge_set_digest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">merge_set_digest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#intersection_cardinality" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">intersection_cardinality()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#jaccard_index" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">jaccard_index()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="setdigest.html#hash_counts" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hash_counts()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="set-digest-functions">
<h1 id="functions-setdigest--page-root">Set Digest functions<a class="headerlink" href="setdigest.html#functions-setdigest--page-root" title="Link to this heading">#</a></h1>
<p>Trino offers several functions that deal with the
<a class="reference external" href="https://wikipedia.org/wiki/MinHash">MinHash</a> technique.</p>
<p>MinHash is used to quickly estimate the
<a class="reference external" href="https://wikipedia.org/wiki/Jaccard_index">Jaccard similarity coefficient</a>
between two sets.</p>
<p>It is commonly used in data mining to detect near-duplicate web pages at scale.
By using this information, the search engines efficiently avoid showing
within the search results two pages that are nearly identical.</p>
<p>The following example showcases how the Set Digest functions can be
used to naively estimate the similarity between texts. The input texts
are split by using the function <a class="reference internal" href="array.html#ngrams" title="ngrams"><code class="xref py py-func docutils literal notranslate"><span class="pre">ngrams()</span></code></a> to
<a class="reference external" href="https://wikipedia.org/wiki/W-shingling">4-shingles</a> which are
used as input for creating a set digest of each initial text.
The set digests are compared to each other to get an
approximation of the similarity of their corresponding
initial texts:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span><span class="w"> </span><span class="n">text_input</span><span class="p">(</span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="nb">text</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">         </span><span class="k">VALUES</span>
<span class="w">             </span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'The quick brown fox jumps over the lazy dog'</span><span class="p">),</span>
<span class="w">             </span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="s1">'The quick and the lazy'</span><span class="p">),</span>
<span class="w">             </span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="s1">'The quick brown fox jumps over the dog'</span><span class="p">)</span>
<span class="w">     </span><span class="p">),</span>
<span class="w">     </span><span class="n">text_ngrams</span><span class="p">(</span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">ngrams</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">         </span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span>
<span class="w">                </span><span class="k">transform</span><span class="p">(</span>
<span class="w">                  </span><span class="n">ngrams</span><span class="p">(</span>
<span class="w">                    </span><span class="n">split</span><span class="p">(</span><span class="nb">text</span><span class="p">,</span><span class="w"> </span><span class="s1">' '</span><span class="p">),</span>
<span class="w">                    </span><span class="mi">4</span>
<span class="w">                  </span><span class="p">),</span>
<span class="w">                  </span><span class="n">token</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">array_join</span><span class="p">(</span><span class="n">token</span><span class="p">,</span><span class="w"> </span><span class="s1">' '</span><span class="p">)</span>
<span class="w">                </span><span class="p">)</span>
<span class="w">         </span><span class="k">FROM</span><span class="w"> </span><span class="n">text_input</span>
<span class="w">     </span><span class="p">),</span>
<span class="w">     </span><span class="n">minhash_digest</span><span class="p">(</span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">digest</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">         </span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span>
<span class="w">                </span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">v</span><span class="p">)</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">unnest</span><span class="p">(</span><span class="n">ngrams</span><span class="p">)</span><span class="w"> </span><span class="n">u</span><span class="p">(</span><span class="n">v</span><span class="p">))</span>
<span class="w">         </span><span class="k">FROM</span><span class="w"> </span><span class="n">text_ngrams</span>
<span class="w">     </span><span class="p">),</span>
<span class="w">     </span><span class="n">setdigest_side_by_side</span><span class="p">(</span><span class="n">id1</span><span class="p">,</span><span class="w"> </span><span class="n">digest1</span><span class="p">,</span><span class="w"> </span><span class="n">id2</span><span class="p">,</span><span class="w"> </span><span class="n">digest2</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">         </span><span class="k">SELECT</span><span class="w"> </span><span class="n">m1</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">id1</span><span class="p">,</span>
<span class="w">                </span><span class="n">m1</span><span class="p">.</span><span class="n">digest</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">digest1</span><span class="p">,</span>
<span class="w">                </span><span class="n">m2</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">id2</span><span class="p">,</span>
<span class="w">                </span><span class="n">m2</span><span class="p">.</span><span class="n">digest</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">digest2</span>
<span class="w">         </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">digest</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">minhash_digest</span><span class="p">)</span><span class="w"> </span><span class="n">m1</span>
<span class="w">         </span><span class="k">JOIN</span><span class="w"> </span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">digest</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">minhash_digest</span><span class="p">)</span><span class="w"> </span><span class="n">m2</span>
<span class="w">           </span><span class="k">ON</span><span class="w"> </span><span class="n">m1</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">m2</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">m1</span><span class="p">.</span><span class="n">id</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">m2</span><span class="p">.</span><span class="n">id</span>
<span class="w">     </span><span class="p">)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">id1</span><span class="p">,</span>
<span class="w">       </span><span class="n">id2</span><span class="p">,</span>
<span class="w">       </span><span class="n">intersection_cardinality</span><span class="p">(</span><span class="n">digest1</span><span class="p">,</span><span class="w"> </span><span class="n">digest2</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">intersection_cardinality</span><span class="p">,</span>
<span class="w">       </span><span class="n">jaccard_index</span><span class="p">(</span><span class="n">digest1</span><span class="p">,</span><span class="w"> </span><span class="n">digest2</span><span class="p">)</span><span class="w">            </span><span class="k">AS</span><span class="w"> </span><span class="n">jaccard_index</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">setdigest_side_by_side</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">id1</span><span class="p">,</span><span class="w"> </span><span class="n">id2</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> id1 | id2 | intersection_cardinality | jaccard_index
-----+-----+--------------------------+---------------
   1 |   2 |                        0 |           0.0
   1 |   3 |                        4 |           0.6
   2 |   3 |                        0 |           0.0
</pre></div>
</div>
<p>The above result listing points out, as expected, that the texts
with the id <code class="docutils literal notranslate"><span class="pre">1</span></code> and <code class="docutils literal notranslate"><span class="pre">3</span></code> are quite similar.</p>
<p>One may argue that the text with the id <code class="docutils literal notranslate"><span class="pre">2</span></code> is somewhat similar to
the texts with the id <code class="docutils literal notranslate"><span class="pre">1</span></code> and <code class="docutils literal notranslate"><span class="pre">3</span></code>. Due to the fact in the example above
<em>4-shingles</em> are taken into account for measuring the similarity of the texts,
there are no intersections found for the text pairs <code class="docutils literal notranslate"><span class="pre">1</span></code> and <code class="docutils literal notranslate"><span class="pre">2</span></code>, respectively
<code class="docutils literal notranslate"><span class="pre">3</span></code> and <code class="docutils literal notranslate"><span class="pre">2</span></code> and therefore there the similarity index for these text pairs
is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<section id="data-structures">
<h2 id="data-structures">Data structures<a class="headerlink" href="setdigest.html#data-structures" title="Link to this heading">#</a></h2>
<p>Trino implements Set Digest data sketches by encapsulating the following components:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://wikipedia.org/wiki/HyperLogLog">HyperLogLog</a></p></li>
<li><p><a class="reference external" href="https://wikipedia.org/wiki/MinHash#Variant_with_a_single_hash_function">MinHash with a single hash function</a></p></li>
</ul>
<p>The HyperLogLog structure is used for the approximation of the distinct elements
in the original set.</p>
<p>The MinHash structure is used to store a low memory footprint signature of the original set.
The similarity of any two sets is estimated by comparing their signatures.</p>
<p>The Trino type for this data structure is called <code class="docutils literal notranslate"><span class="pre">setdigest</span></code>.
Trino offers the ability to merge multiple Set Digest data sketches.</p>
</section>
<section id="serialization">
<h2 id="serialization">Serialization<a class="headerlink" href="setdigest.html#serialization" title="Link to this heading">#</a></h2>
<p>Data sketches can be serialized to and deserialized from <code class="docutils literal notranslate"><span class="pre">varbinary</span></code>. This
allows them to be stored for later use.</p>
</section>
<section id="functions">
<h2 id="functions">Functions<a class="headerlink" href="setdigest.html#functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="make_set_digest">
<span class="sig-name descname"><span class="pre">make_set_digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">setdigest</span></span></span><a class="headerlink" href="setdigest.html#make_set_digest" title="Link to this definition">#</a></dt>
<dd><p>Composes all input values of <code class="docutils literal notranslate"><span class="pre">x</span></code> into a <code class="docutils literal notranslate"><span class="pre">setdigest</span></code>.</p>
<p>Create a <code class="docutils literal notranslate"><span class="pre">setdigest</span></code> corresponding to a <code class="docutils literal notranslate"><span class="pre">bigint</span></code> array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
</pre></div>
</div>
<p>Create a <code class="docutils literal notranslate"><span class="pre">setdigest</span></code> corresponding to a <code class="docutils literal notranslate"><span class="pre">varchar</span></code> array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="s1">'Trino'</span><span class="p">,</span><span class="w"> </span><span class="s1">'SQL'</span><span class="p">,</span><span class="w"> </span><span class="s1">'on'</span><span class="p">,</span><span class="w"> </span><span class="s1">'everything'</span><span class="p">)</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="merge_set_digest">
<span class="sig-name descname"><span class="pre">merge_set_digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">setdigest</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">setdigest</span></span></span><a class="headerlink" href="setdigest.html#merge_set_digest" title="Link to this definition">#</a></dt>
<dd><p>Returns the <code class="docutils literal notranslate"><span class="pre">setdigest</span></code> of the aggregate union of the individual <code class="docutils literal notranslate"><span class="pre">setdigest</span></code>
Set Digest structures.</p>
</dd></dl>
<dl class="py function" id="setdigest-cardinality">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">cardinality</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">setdigest</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">long</span></span></span></dt>
<dd><p>Returns the cardinality of the set digest from its internal
<code class="docutils literal notranslate"><span class="pre">HyperLogLog</span></code> component.</p>
<p>Examples:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">cardinality</span><span class="p">(</span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">value</span><span class="p">))</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="c1">-- 5</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="intersection_cardinality">
<span class="sig-name descname"><span class="pre">intersection_cardinality</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">long</span></span></span><a class="headerlink" href="setdigest.html#intersection_cardinality" title="Link to this definition">#</a></dt>
<dd><p>Returns the estimation for the cardinality of the intersection of the two set digests.</p>
<p><code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> must be of type  <code class="docutils literal notranslate"><span class="pre">setdigest</span></code></p>
<p>Examples:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">intersection_cardinality</span><span class="p">(</span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">v1</span><span class="p">),</span><span class="w"> </span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">v2</span><span class="p">))</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="k">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">))</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">v1</span><span class="p">,</span><span class="w"> </span><span class="n">v2</span><span class="p">);</span>
<span class="c1">-- 3</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="jaccard_index">
<span class="sig-name descname"><span class="pre">jaccard_index</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="setdigest.html#jaccard_index" title="Link to this definition">#</a></dt>
<dd><p>Returns the estimation of <a class="reference external" href="https://wikipedia.org/wiki/Jaccard_index">Jaccard index</a> for
the two set digests.</p>
<p><code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> must be of type  <code class="docutils literal notranslate"><span class="pre">setdigest</span></code>.</p>
<p>Examples:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">jaccard_index</span><span class="p">(</span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">v1</span><span class="p">),</span><span class="w"> </span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">v2</span><span class="p">))</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="k">NULL</span><span class="p">,</span><span class="mi">2</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">),</span><span class="w"> </span><span class="p">(</span><span class="k">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">))</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">v1</span><span class="p">,</span><span class="w"> </span><span class="n">v2</span><span class="p">);</span>
<span class="c1">-- 0.5</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hash_counts">
<span class="sig-name descname"><span class="pre">hash_counts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="setdigest.html#hash_counts" title="Link to this definition">#</a></dt>
<dd><p>Returns a map containing the <a class="reference external" href="https://wikipedia.org/wiki/MurmurHash#MurmurHash3">Murmur3Hash128</a>
hashed values and the count of their occurences within
the internal <code class="docutils literal notranslate"><span class="pre">MinHash</span></code> structure belonging to <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">x</span></code> must be of type  <code class="docutils literal notranslate"><span class="pre">setdigest</span></code>.</p>
<p>Examples:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">hash_counts</span><span class="p">(</span><span class="n">make_set_digest</span><span class="p">(</span><span class="n">value</span><span class="p">))</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="n">T</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="c1">-- {19144387141682250=3, -2447670524089286488=2}</span>
</pre></div>
</div>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="session.html" title="Session information"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Session information </span>
              </div>
            </a>
          
          
            <a href="string.html" title="String functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> String functions and operators </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>