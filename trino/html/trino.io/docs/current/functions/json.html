<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>JSON functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="json.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Lambda expressions" href="lambda.html" />
    <link rel="prev" title="IP Address Functions" href="ipaddress.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="json.html#functions/json" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> JSON functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> JSON </label>
    
      <a href="json.html#" class="md-nav__link md-nav__link--active">JSON</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="json.html#json-path-language" class="md-nav__link">JSON path language</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#json-path-syntax-and-semantics" class="md-nav__link">JSON path syntax and semantics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#literals" class="md-nav__link">literals</a>
        </li>
        <li class="md-nav__item"><a href="json.html#variables" class="md-nav__link">variables</a>
        </li>
        <li class="md-nav__item"><a href="json.html#arithmetic-binary-expressions" class="md-nav__link">arithmetic binary expressions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#arithmetic-unary-expressions" class="md-nav__link">arithmetic unary expressions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#member-accessor" class="md-nav__link">member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#wildcard-member-accessor" class="md-nav__link">wildcard member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#descendant-member-accessor" class="md-nav__link">descendant member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#array-accessor" class="md-nav__link">array accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#wildcard-array-accessor" class="md-nav__link">wildcard array accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#filter" class="md-nav__link">filter</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#comparison-rules" class="md-nav__link">Comparison rules</a>
        </li>
        <li class="md-nav__item"><a href="json.html#examples-of-filter" class="md-nav__link">Examples of filter</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#double" class="md-nav__link">double()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#ceiling-floor-and-abs" class="md-nav__link">ceiling(), floor(), and abs()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#keyvalue" class="md-nav__link">keyvalue()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#type" class="md-nav__link">type()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#size" class="md-nav__link">size()</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#limitations" class="md-nav__link">Limitations</a>
        </li>
        <li class="md-nav__item"><a href="json.html#json-path-modes" class="md-nav__link">JSON path modes</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#examples-of-the-lax-mode-behavior" class="md-nav__link">Examples of the lax mode behavior</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-exists" class="md-nav__link">json_exists</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-query" class="md-nav__link">json_query</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id6" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-value" class="md-nav__link">json_value</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id8" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-table" class="md-nav__link">json_table</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id10" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-array" class="md-nav__link">json_array</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#argument-types" class="md-nav__link">Argument types</a>
        </li>
        <li class="md-nav__item"><a href="json.html#null-handling" class="md-nav__link">Null handling</a>
        </li>
        <li class="md-nav__item"><a href="json.html#returned-type" class="md-nav__link">Returned type</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-object" class="md-nav__link">json_object</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#argument-passing-conventions" class="md-nav__link">Argument passing conventions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id13" class="md-nav__link">Argument types</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id14" class="md-nav__link">Null handling</a>
        </li>
        <li class="md-nav__item"><a href="json.html#key-uniqueness" class="md-nav__link">Key uniqueness</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id15" class="md-nav__link">Returned type</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#cast-to-json" class="md-nav__link">Cast to JSON</a>
        </li>
        <li class="md-nav__item"><a href="json.html#cast-from-json" class="md-nav__link">Cast from JSON</a>
        </li>
        <li class="md-nav__item"><a href="json.html#other-json-functions" class="md-nav__link">Other JSON functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#is_json_scalar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_json_scalar()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_contains" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_contains()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_get" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_get()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_extract()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_extract_scalar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_extract_scalar()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_format" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_format()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_parse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_parse()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_size()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="json.html#json-path-language" class="md-nav__link">JSON path language</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#json-path-syntax-and-semantics" class="md-nav__link">JSON path syntax and semantics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#literals" class="md-nav__link">literals</a>
        </li>
        <li class="md-nav__item"><a href="json.html#variables" class="md-nav__link">variables</a>
        </li>
        <li class="md-nav__item"><a href="json.html#arithmetic-binary-expressions" class="md-nav__link">arithmetic binary expressions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#arithmetic-unary-expressions" class="md-nav__link">arithmetic unary expressions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#member-accessor" class="md-nav__link">member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#wildcard-member-accessor" class="md-nav__link">wildcard member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#descendant-member-accessor" class="md-nav__link">descendant member accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#array-accessor" class="md-nav__link">array accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#wildcard-array-accessor" class="md-nav__link">wildcard array accessor</a>
        </li>
        <li class="md-nav__item"><a href="json.html#filter" class="md-nav__link">filter</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#comparison-rules" class="md-nav__link">Comparison rules</a>
        </li>
        <li class="md-nav__item"><a href="json.html#examples-of-filter" class="md-nav__link">Examples of filter</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#double" class="md-nav__link">double()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#ceiling-floor-and-abs" class="md-nav__link">ceiling(), floor(), and abs()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#keyvalue" class="md-nav__link">keyvalue()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#type" class="md-nav__link">type()</a>
        </li>
        <li class="md-nav__item"><a href="json.html#size" class="md-nav__link">size()</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#limitations" class="md-nav__link">Limitations</a>
        </li>
        <li class="md-nav__item"><a href="json.html#json-path-modes" class="md-nav__link">JSON path modes</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#examples-of-the-lax-mode-behavior" class="md-nav__link">Examples of the lax mode behavior</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-exists" class="md-nav__link">json_exists</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-query" class="md-nav__link">json_query</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id6" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-value" class="md-nav__link">json_value</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id8" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-table" class="md-nav__link">json_table</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#id10" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-array" class="md-nav__link">json_array</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#argument-types" class="md-nav__link">Argument types</a>
        </li>
        <li class="md-nav__item"><a href="json.html#null-handling" class="md-nav__link">Null handling</a>
        </li>
        <li class="md-nav__item"><a href="json.html#returned-type" class="md-nav__link">Returned type</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#json-object" class="md-nav__link">json_object</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#argument-passing-conventions" class="md-nav__link">Argument passing conventions</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id13" class="md-nav__link">Argument types</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id14" class="md-nav__link">Null handling</a>
        </li>
        <li class="md-nav__item"><a href="json.html#key-uniqueness" class="md-nav__link">Key uniqueness</a>
        </li>
        <li class="md-nav__item"><a href="json.html#id15" class="md-nav__link">Returned type</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="json.html#cast-to-json" class="md-nav__link">Cast to JSON</a>
        </li>
        <li class="md-nav__item"><a href="json.html#cast-from-json" class="md-nav__link">Cast from JSON</a>
        </li>
        <li class="md-nav__item"><a href="json.html#other-json-functions" class="md-nav__link">Other JSON functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="json.html#is_json_scalar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_json_scalar()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_contains" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_contains()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_get" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_get()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_array_length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_array_length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_extract()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_extract_scalar" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_extract_scalar()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_format" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_format()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_parse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_parse()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="json.html#json_size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">json_size()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="json-functions-and-operators">
<h1 id="functions-json--page-root">JSON functions and operators<a class="headerlink" href="json.html#functions-json--page-root" title="Link to this heading">#</a></h1>
<p>The SQL standard describes functions and operators to process JSON data. They
allow you to access JSON data according to its structure, generate JSON data,
and store it persistently in SQL tables.</p>
<p>Importantly, the SQL standard imposes that there is no dedicated data type to
represent JSON data in SQL. Instead, JSON data is represented as character or
binary strings. Although Trino supports <code class="docutils literal notranslate"><span class="pre">JSON</span></code> type, it is not used or
produced by the following functions.</p>
<p>Trino supports three functions for querying JSON data:
<a class="reference internal" href="json.html#json-exists"><span class="std std-ref">json_exists</span></a>,
<a class="reference internal" href="json.html#json-query"><span class="std std-ref">json_query</span></a>, and <a class="reference internal" href="json.html#json-value"><span class="std std-ref">json_value</span></a>. Each of them
is based on the same mechanism of exploring and processing JSON input using
JSON path.</p>
<p>Trino also supports two functions for generating JSON data –
<a class="reference internal" href="json.html#json-array"><span class="std std-ref">json_array</span></a>, and <a class="reference internal" href="json.html#json-object"><span class="std std-ref">json_object</span></a>.</p>
<section id="json-path-language">
<span id="id1"></span><h2 id="json-path-language">JSON path language<a class="headerlink" href="json.html#json-path-language" title="Link to this heading">#</a></h2>
<p>The JSON path language is a special language, used exclusively by certain SQL
operators to specify the query to perform on the JSON input. Although JSON path
expressions are embedded in SQL queries, their syntax significantly differs
from SQL. The semantics of predicates, operators, etc. in JSON path expressions
generally follow the semantics of SQL. The JSON path language is case-sensitive
for keywords and identifiers.</p>
<section id="json-path-syntax-and-semantics">
<span id="id2"></span><h3 id="json-path-syntax-and-semantics">JSON path syntax and semantics<a class="headerlink" href="json.html#json-path-syntax-and-semantics" title="Link to this heading">#</a></h3>
<p>JSON path expressions are recursive structures. Although the name “path”
suggests a linear sequence of operations going step by step deeper into the JSON
structure, a JSON path expression is in fact a tree. It can access the input
JSON item multiple times, in multiple ways, and combine the results. Moreover,
the result of a JSON path expression is not a single item, but an ordered
sequence of items. Each of the sub-expressions takes one or more input
sequences, and returns a sequence as the result.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In the lax mode, most path operations first unnest all JSON arrays in the
input sequence. Any divergence from this rule is mentioned in the following
listing. Path modes are explained in <a class="reference internal" href="json.html#json-path-modes"><span class="std std-ref">JSON path modes</span></a>.</p>
</div>
<p>The JSON path language features are divided into: literals, variables,
arithmetic binary expressions, arithmetic unary expressions, and a group of
operators collectively known as accessors.</p>
<section id="literals">
<h4 id="literals">literals<a class="headerlink" href="json.html#literals" title="Link to this heading">#</a></h4>
<ul>
<li><p>numeric literals</p>
<p>They include exact and approximate numbers, and are interpreted as if they
were SQL values.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-1, 1.2e3, NaN
</pre></div>
</div>
<ul>
<li><p>string literals</p>
<p>They are enclosed in double quotes.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>"Some text"
</pre></div>
</div>
<ul class="simple">
<li><p>boolean literals</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>true, false
</pre></div>
</div>
<ul>
<li><p>null literal</p>
<p>It has the semantics of the JSON null, not of SQL null. See <a class="reference internal" href="json.html#json-comparison-rules"><span class="std std-ref">Comparison rules</span></a>.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>null
</pre></div>
</div>
</section>
<section id="variables">
<h4 id="variables">variables<a class="headerlink" href="json.html#variables" title="Link to this heading">#</a></h4>
<ul>
<li><p>context variable</p>
<p>It refers to the currently processed input of the JSON
function.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$
</pre></div>
</div>
<ul>
<li><p>named variable</p>
<p>It refers to a named parameter by its name.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$param
</pre></div>
</div>
<ul>
<li><p>current item variable</p>
<p>It is used inside the filter expression to refer to the currently processed
item from the input sequence.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@
</pre></div>
</div>
<ul>
<li><p>last subscript variable</p>
<p>It refers to the last index of the innermost enclosing array. Array indexes
in JSON path expressions are zero-based.</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>last
</pre></div>
</div>
</section>
<section id="arithmetic-binary-expressions">
<h4 id="arithmetic-binary-expressions">arithmetic binary expressions<a class="headerlink" href="json.html#arithmetic-binary-expressions" title="Link to this heading">#</a></h4>
<p>The JSON path language supports five arithmetic binary operators:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path1&gt; + &lt;path2&gt;
&lt;path1&gt; - &lt;path2&gt;
&lt;path1&gt; * &lt;path2&gt;
&lt;path1&gt; / &lt;path2&gt;
&lt;path1&gt; % &lt;path2&gt;
</pre></div>
</div>
<p>Both operands, <code class="docutils literal notranslate"><span class="pre">&lt;path1&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;path2&gt;</span></code>, are evaluated to sequences of
items. For arithmetic binary operators, each input sequence must contain a
single numeric item. The arithmetic operation is performed according to SQL
semantics, and it returns a sequence containing a single element with the
result.</p>
<p>The operators follow the same precedence rules as in SQL arithmetic operations,
and parentheses can be used for grouping.</p>
</section>
<section id="arithmetic-unary-expressions">
<h4 id="arithmetic-unary-expressions">arithmetic unary expressions<a class="headerlink" href="json.html#arithmetic-unary-expressions" title="Link to this heading">#</a></h4>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>+ &lt;path&gt;
- &lt;path&gt;
</pre></div>
</div>
<p>The operand <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> is evaluated to a sequence of items. Every item must be
a numeric value. The unary plus or minus is applied to every item in the
sequence, following SQL semantics, and the results form the returned sequence.</p>
</section>
<section id="member-accessor">
<h4 id="member-accessor">member accessor<a class="headerlink" href="json.html#member-accessor" title="Link to this heading">#</a></h4>
<p>The member accessor returns the value of the member with the specified key for
each JSON object in the input sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.key
&lt;path&gt;."key"
</pre></div>
</div>
<p>The condition when a JSON object does not have such a member is called a
structural error. In the lax mode, it is suppressed, and the faulty object is
excluded from the result.</p>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON objects:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{"customer" : 100, "region" : "AFRICA"},
{"region" : "ASIA"},
{"customer" : 300, "region" : "AFRICA", "comment" : null}
</pre></div>
</div>
<p>the expression <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;.customer</span></code> succeeds in the first and the third object,
but the second object lacks the required member. In strict mode, path
evaluation fails. In lax mode, the second object is silently skipped, and the
resulting sequence is <code class="docutils literal notranslate"><span class="pre">100,</span> <span class="pre">300</span></code>.</p>
<p>All items in the input sequence must be JSON objects.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Trino does not support JSON objects with duplicate keys.</p>
</div>
</section>
<section id="wildcard-member-accessor">
<h4 id="wildcard-member-accessor">wildcard member accessor<a class="headerlink" href="json.html#wildcard-member-accessor" title="Link to this heading">#</a></h4>
<p>Returns values from all key-value pairs for each JSON object in the input
sequence. All the partial results are concatenated into the returned sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.*
</pre></div>
</div>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON objects:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{"customer" : 100, "region" : "AFRICA"},
{"region" : "ASIA"},
{"customer" : 300, "region" : "AFRICA", "comment" : null}
</pre></div>
</div>
<p>The results is:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>100, "AFRICA", "ASIA", 300, "AFRICA", null
</pre></div>
</div>
<p>All items in the input sequence must be JSON objects.</p>
<p>The order of values returned from a single JSON object is arbitrary. The
sub-sequences from all JSON objects are concatenated in the same order in which
the JSON objects appear in the input sequence.</p>
</section>
<section id="descendant-member-accessor">
<span id="json-descendant-member-accessor"></span><h4 id="descendant-member-accessor">descendant member accessor<a class="headerlink" href="json.html#descendant-member-accessor" title="Link to this heading">#</a></h4>
<p>Returns the values associated with the specified key in all JSON objects on all
levels of nesting in the input sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;..key
&lt;path&gt;.."key"
</pre></div>
</div>
<p>The order of returned values is that of preorder depth first search. First, the
enclosing object is visited, and then all child nodes are visited.</p>
<p>This method does not perform array unwrapping in the lax mode. The results
are the same in the lax and strict modes. The method traverses into JSON
arrays and JSON objects. Non-structural JSON items are skipped.</p>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> be a sequence containing a JSON object:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "id" : 1,
    "notes" : [{"type" : 1, "comment" : "foo"}, {"type" : 2, "comment" : null}],
    "comment" : ["bar", "baz"]
}
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;..comment --&gt; ["bar", "baz"], "foo", null
</pre></div>
</div>
</section>
<section id="array-accessor">
<h4 id="array-accessor">array accessor<a class="headerlink" href="json.html#array-accessor" title="Link to this heading">#</a></h4>
<p>Returns the elements at the specified indexes for each JSON array in the input
sequence. Indexes are zero-based.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[ &lt;subscripts&gt; ]
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;subscripts&gt;</span></code> list contains one or more subscripts. Each subscript
specifies a single index or a range (ends inclusive):</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[&lt;path1&gt;, &lt;path2&gt; to &lt;path3&gt;, &lt;path4&gt;,...]
</pre></div>
</div>
<p>In lax mode, any non-array items resulting from the evaluation of the input
sequence are wrapped into single-element arrays. Note that this is an exception
to the rule of automatic array wrapping.</p>
<p>Each array in the input sequence is processed in the following way:</p>
<ul class="simple">
<li><p>The variable <code class="docutils literal notranslate"><span class="pre">last</span></code> is set to the last index of the array.</p></li>
<li><p>All subscript indexes are computed in order of declaration. For a
singleton subscript <code class="docutils literal notranslate"><span class="pre">&lt;path1&gt;</span></code>, the result must be a singleton numeric item.
For a range subscript <code class="docutils literal notranslate"><span class="pre">&lt;path2&gt;</span> <span class="pre">to</span> <span class="pre">&lt;path3&gt;</span></code>, two numeric items are expected.</p></li>
<li><p>The specified array elements are added in order to the output sequence.</p></li>
</ul>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON arrays:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[0, 1, 2], ["a", "b", "c", "d"], [null, null]
</pre></div>
</div>
<p>The following expression returns a sequence containing the last element from
every array:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[last] --&gt; 2, "d", null
</pre></div>
</div>
<p>The following expression returns the third and fourth element from every array:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[2 to 3] --&gt; 2, "c", "d"
</pre></div>
</div>
<p>Note that the first array does not have the fourth element, and the last array
does not have the third or fourth element. Accessing non-existent elements is a
structural error. In strict mode, it causes the path expression to fail. In lax
mode, such errors are suppressed, and only the existing elements are returned.</p>
<p>Another example of a structural error is an improper range specification such
as <code class="docutils literal notranslate"><span class="pre">5</span> <span class="pre">to</span> <span class="pre">3</span></code>.</p>
<p>Note that the subscripts may overlap, and they do not need to follow the
element order. The order in the returned sequence follows the subscripts:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[1, 0, 0] --&gt; 1, 0, 0, "b", "a", "a", null, null, null
</pre></div>
</div>
</section>
<section id="wildcard-array-accessor">
<h4 id="wildcard-array-accessor">wildcard array accessor<a class="headerlink" href="json.html#wildcard-array-accessor" title="Link to this heading">#</a></h4>
<p>Returns all elements of each JSON array in the input sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[*]
</pre></div>
</div>
<p>In lax mode, any non-array items resulting from the evaluation of the input
sequence are wrapped into single-element arrays. Note that this is an exception
to the rule of automatic array wrapping.</p>
<p>The output order follows the order of the original JSON arrays. Also, the order
of elements within the arrays is preserved.</p>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON arrays:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[0, 1, 2], ["a", "b", "c", "d"], [null, null]
&lt;path&gt;[*] --&gt; 0, 1, 2, "a", "b", "c", "d", null, null
</pre></div>
</div>
</section>
<section id="filter">
<h4 id="filter">filter<a class="headerlink" href="json.html#filter" title="Link to this heading">#</a></h4>
<p>Retrieves the items from the input sequence which satisfy the predicate.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;?( &lt;predicate&gt; )
</pre></div>
</div>
<p>JSON path predicates are syntactically similar to boolean expressions in SQL.
However, the semantics are different in many aspects:</p>
<ul class="simple">
<li><p>They operate on sequences of items.</p></li>
<li><p>They have their own error handling (they never fail).</p></li>
<li><p>They behave different depending on the lax or strict mode.</p></li>
</ul>
<p>The predicate evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code>, or <code class="docutils literal notranslate"><span class="pre">unknown</span></code>. Note that some
predicate expressions involve nested JSON path expression. When evaluating the
nested path, the variable <code class="docutils literal notranslate"><span class="pre">@</span></code> refers to the currently examined item from the
input sequence.</p>
<p>The following predicate expressions are supported:</p>
<ul class="simple">
<li><p>Conjunction</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;predicate1&gt; &amp;&amp; &lt;predicate2&gt;
</pre></div>
</div>
<ul class="simple">
<li><p>Disjunction</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;predicate1&gt; || &lt;predicate2&gt;
</pre></div>
</div>
<ul class="simple">
<li><p>Negation</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>! &lt;predicate&gt;
</pre></div>
</div>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">exists</span></code> predicate</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>exists( &lt;path&gt; )
</pre></div>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the nested path evaluates to a non-empty sequence, and
<code class="docutils literal notranslate"><span class="pre">false</span></code> when the nested path evaluates to an empty sequence. If the path
evaluation throws an error, returns <code class="docutils literal notranslate"><span class="pre">unknown</span></code>.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">starts</span> <span class="pre">with</span></code> predicate</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt; starts with "Some text"
&lt;path&gt; starts with $variable
</pre></div>
</div>
<p>The nested <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> must evaluate to a sequence of textual items, and the
other operand must evaluate to a single textual item. If evaluating of either
operand throws an error, the result is <code class="docutils literal notranslate"><span class="pre">unknown</span></code>. All items from the sequence
are checked for starting with the right operand. The result is <code class="docutils literal notranslate"><span class="pre">true</span></code> if a
match is found, otherwise <code class="docutils literal notranslate"><span class="pre">false</span></code>. However, if any of the comparisons throws
an error, the result in the strict mode is <code class="docutils literal notranslate"><span class="pre">unknown</span></code>. The result in the lax
mode depends on whether the match or the error was found first.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">is</span> <span class="pre">unknown</span></code> predicate</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>( &lt;predicate&gt; ) is unknown
</pre></div>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the nested predicate evaluates to <code class="docutils literal notranslate"><span class="pre">unknown</span></code>, and
<code class="docutils literal notranslate"><span class="pre">false</span></code> otherwise.</p>
<ul class="simple">
<li><p>Comparisons</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path1&gt; == &lt;path2&gt;
&lt;path1&gt; &lt;&gt; &lt;path2&gt;
&lt;path1&gt; != &lt;path2&gt;
&lt;path1&gt; &lt; &lt;path2&gt;
&lt;path1&gt; &gt; &lt;path2&gt;
&lt;path1&gt; &lt;= &lt;path2&gt;
&lt;path1&gt; &gt;= &lt;path2&gt;
</pre></div>
</div>
<p>Both operands of a comparison evaluate to sequences of items. If either
evaluation throws an error, the result is <code class="docutils literal notranslate"><span class="pre">unknown</span></code>. Items from the left and
right sequence are then compared pairwise. Similarly to the <code class="docutils literal notranslate"><span class="pre">starts</span> <span class="pre">with</span></code>
predicate, the result is <code class="docutils literal notranslate"><span class="pre">true</span></code> if any of the comparisons returns <code class="docutils literal notranslate"><span class="pre">true</span></code>,
otherwise <code class="docutils literal notranslate"><span class="pre">false</span></code>. However, if any of the comparisons throws an error, for
example because the compared types are not compatible, the result in the strict
mode is <code class="docutils literal notranslate"><span class="pre">unknown</span></code>. The result in the lax mode depends on whether the <code class="docutils literal notranslate"><span class="pre">true</span></code>
comparison or the error was found first.</p>
<section id="comparison-rules">
<span id="json-comparison-rules"></span><h5 id="comparison-rules">Comparison rules<a class="headerlink" href="json.html#comparison-rules" title="Link to this heading">#</a></h5>
<p>Null values in the context of comparison behave different than SQL null:</p>
<ul class="simple">
<li><p>null == null –&gt; <code class="docutils literal notranslate"><span class="pre">true</span></code></p></li>
<li><p>null != null, null &lt; null, … –&gt; <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p>null compared to a scalar value –&gt; <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
<li><p>null compared to a JSON array or a JSON object –&gt; <code class="docutils literal notranslate"><span class="pre">false</span></code></p></li>
</ul>
<p>When comparing two scalar values, <code class="docutils literal notranslate"><span class="pre">true</span></code> or <code class="docutils literal notranslate"><span class="pre">false</span></code> is returned if the
comparison is successfully performed. The semantics of the comparison is the
same as in SQL. In case of an error, e.g. comparing text and number,
<code class="docutils literal notranslate"><span class="pre">unknown</span></code> is returned.</p>
<p>Comparing a scalar value with a JSON array or a JSON object, and comparing JSON
arrays/objects is an error, so <code class="docutils literal notranslate"><span class="pre">unknown</span></code> is returned.</p>
</section>
<section id="examples-of-filter">
<h5 id="examples-of-filter">Examples of filter<a class="headerlink" href="json.html#examples-of-filter" title="Link to this heading">#</a></h5>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON objects:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{"customer" : 100, "region" : "AFRICA"},
{"region" : "ASIA"},
{"customer" : 300, "region" : "AFRICA", "comment" : null}
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;?(@.region != "ASIA") --&gt; {"customer" : 100, "region" : "AFRICA"},
                                {"customer" : 300, "region" : "AFRICA", "comment" : null}
&lt;path&gt;?(!exists(@.customer)) --&gt; {"region" : "ASIA"}
</pre></div>
</div>
<p>The following accessors are collectively referred to as <strong>item methods</strong>.</p>
</section>
</section>
<section id="double">
<h4 id="double">double()<a class="headerlink" href="json.html#double" title="Link to this heading">#</a></h4>
<p>Converts numeric or text values into double values.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.double()
</pre></div>
</div>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence <code class="docutils literal notranslate"><span class="pre">-1,</span> <span class="pre">23e4,</span> <span class="pre">"5.6"</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.double() --&gt; -1e0, 23e4, 5.6e0
</pre></div>
</div>
</section>
<section id="ceiling-floor-and-abs">
<h4 id="ceiling-floor-and-abs">ceiling(), floor(), and abs()<a class="headerlink" href="json.html#ceiling-floor-and-abs" title="Link to this heading">#</a></h4>
<p>Gets the ceiling, the floor or the absolute value for every numeric item in the
sequence. The semantics of the operations is the same as in SQL.</p>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence <code class="docutils literal notranslate"><span class="pre">-1.5,</span> <span class="pre">-1,</span> <span class="pre">1.3</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.ceiling() --&gt; -1.0, -1, 2.0
&lt;path&gt;.floor() --&gt; -2.0, -1, 1.0
&lt;path&gt;.abs() --&gt; 1.5, 1, 1.3
</pre></div>
</div>
</section>
<section id="keyvalue">
<h4 id="keyvalue">keyvalue()<a class="headerlink" href="json.html#keyvalue" title="Link to this heading">#</a></h4>
<p>Returns a collection of JSON objects including one object per every member of
the original object for every JSON object in the sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.keyvalue()
</pre></div>
</div>
<p>The returned objects have three members:</p>
<ul class="simple">
<li><p>“name”, which is the original key,</p></li>
<li><p>“value”, which is the original bound value,</p></li>
<li><p>“id”, which is the unique number, specific to an input object.</p></li>
</ul>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> be a sequence of three JSON objects:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{"customer" : 100, "region" : "AFRICA"},
{"region" : "ASIA"},
{"customer" : 300, "region" : "AFRICA", "comment" : null}
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.keyvalue() --&gt; {"name" : "customer", "value" : 100, "id" : 0},
                      {"name" : "region", "value" : "AFRICA", "id" : 0},
                      {"name" : "region", "value" : "ASIA", "id" : 1},
                      {"name" : "customer", "value" : 300, "id" : 2},
                      {"name" : "region", "value" : "AFRICA", "id" : 2},
                      {"name" : "comment", "value" : null, "id" : 2}
</pre></div>
</div>
<p>It is required that all items in the input sequence are JSON objects.</p>
<p>The order of the returned values follows the order of the original JSON
objects. However, within objects, the order of returned entries is arbitrary.</p>
</section>
<section id="type">
<h4 id="type">type()<a class="headerlink" href="json.html#type" title="Link to this heading">#</a></h4>
<p>Returns a textual value containing the type name for every item in the
sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.type()
</pre></div>
</div>
<p>This method does not perform array unwrapping in the lax mode.</p>
<p>The returned values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">"null"</span></code> for JSON null,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"number"</span></code> for a numeric item,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"string"</span></code> for a textual item,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"boolean"</span></code> for a boolean item,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"date"</span></code> for an item of type date,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"time</span> <span class="pre">without</span> <span class="pre">time</span> <span class="pre">zone"</span></code> for an item of type time,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"time</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone"</span></code> for an item of type time with time zone,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"timestamp</span> <span class="pre">without</span> <span class="pre">time</span> <span class="pre">zone"</span></code> for an item of type timestamp,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"timestamp</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone"</span></code> for an item of type timestamp with time zone,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"array"</span></code> for JSON array,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">"object"</span></code> for JSON object,</p></li>
</ul>
</section>
<section id="size">
<h4 id="size">size()<a class="headerlink" href="json.html#size" title="Link to this heading">#</a></h4>
<p>Returns a numeric value containing the size for every JSON array in the
sequence.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.size()
</pre></div>
</div>
<p>This method does not perform array unwrapping in the lax mode. Instead, all
non-array items are wrapped in singleton JSON arrays, so their size is <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>It is required that all items in the input sequence are JSON arrays.</p>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three JSON arrays:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[0, 1, 2], ["a", "b", "c", "d"], [null, null]
&lt;path&gt;.size() --&gt; 3, 4, 2
</pre></div>
</div>
</section>
</section>
<section id="limitations">
<h3 id="limitations">Limitations<a class="headerlink" href="json.html#limitations" title="Link to this heading">#</a></h3>
<p>The SQL standard describes the <code class="docutils literal notranslate"><span class="pre">datetime()</span></code> JSON path item method and the
<code class="docutils literal notranslate"><span class="pre">like_regex()</span></code> JSON path predicate. Trino does not support them.</p>
</section>
<section id="json-path-modes">
<span id="id3"></span><h3 id="json-path-modes">JSON path modes<a class="headerlink" href="json.html#json-path-modes" title="Link to this heading">#</a></h3>
<p>The JSON path expression can be evaluated in two modes: strict and lax. In the
strict mode, it is required that the input JSON data strictly fits the schema
required by the path expression. In the lax mode, the input JSON data can
diverge from the expected schema.</p>
<p>The following table shows the differences between the two modes.</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 20%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Condition</p></th>
<th class="head"><p>strict mode</p></th>
<th class="head"><p>lax mode</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Performing an operation which requires a non-array on an array, e.g.:</p>
<p><code class="docutils literal notranslate"><span class="pre">$.key</span></code> requires a JSON object</p>
<p><code class="docutils literal notranslate"><span class="pre">$.floor()</span></code> requires a numeric value</p>
</td>
<td><p>ERROR</p></td>
<td><p>The array is automatically unnested, and the operation is performed on
each array element.</p></td>
</tr>
<tr class="row-odd"><td><p>Performing an operation which requires an array on a non-array, e.g.:</p>
<p><code class="docutils literal notranslate"><span class="pre">$[0]</span></code>, <code class="docutils literal notranslate"><span class="pre">$[*]</span></code>, <code class="docutils literal notranslate"><span class="pre">$.size()</span></code></p>
</td>
<td><p>ERROR</p></td>
<td><p>The non-array item is automatically wrapped in a singleton array, and
the operation is performed on the array.</p></td>
</tr>
<tr class="row-even"><td><p>A structural error: accessing a non-existent element of an array or a
non-existent member of a JSON object, e.g.:</p>
<p><code class="docutils literal notranslate"><span class="pre">$[-1]</span></code> (array index out of bounds)</p>
<p><code class="docutils literal notranslate"><span class="pre">$.key</span></code>, where the input JSON object does not have a member <code class="docutils literal notranslate"><span class="pre">key</span></code></p>
</td>
<td><p>ERROR</p></td>
<td><p>The error is suppressed, and the operation results in an empty sequence.</p></td>
</tr>
</tbody>
</table>
<section id="examples-of-the-lax-mode-behavior">
<h4 id="examples-of-the-lax-mode-behavior">Examples of the lax mode behavior<a class="headerlink" href="json.html#examples-of-the-lax-mode-behavior" title="Link to this heading">#</a></h4>
<p>Let <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> return a sequence of three items, a JSON array, a JSON object,
and a scalar numeric value:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[1, "a", null], {"key1" : 1.0, "key2" : true}, -2e3
</pre></div>
</div>
<p>The following example shows the wildcard array accessor in the lax mode. The
JSON array returns all its elements, while the JSON object and the number are
wrapped in singleton arrays and then unnested, so effectively they appear
unchanged in the output sequence:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;[*] --&gt; 1, "a", null, {"key1" : 1.0, "key2" : true}, -2e3
</pre></div>
</div>
<p>When calling the <code class="docutils literal notranslate"><span class="pre">size()</span></code> method, the JSON object and the number are also
wrapped in singleton arrays:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.size() --&gt; 3, 1, 1
</pre></div>
</div>
<p>In some cases, the lax mode cannot prevent failure. In the following example,
even though the JSON array is unwrapped prior to calling the <code class="docutils literal notranslate"><span class="pre">floor()</span></code>
method, the item <code class="docutils literal notranslate"><span class="pre">"a"</span></code> causes type mismatch.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>&lt;path&gt;.floor() --&gt; ERROR
</pre></div>
</div>
</section>
</section>
</section>
<section id="json-exists">
<span id="id4"></span><h2 id="json-exists">json_exists<a class="headerlink" href="json.html#json-exists" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_exists</span></code> function determines whether a JSON value satisfies a JSON
path specification.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_EXISTS(
    json_input [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ],
    json_path
    [ PASSING json_argument [, ...] ]
    [ { TRUE | FALSE | UNKNOWN | ERROR } ON ERROR ]
    )
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">json_path</span></code> is evaluated using the <code class="docutils literal notranslate"><span class="pre">json_input</span></code> as the context variable
(<code class="docutils literal notranslate"><span class="pre">$</span></code>), and the passed arguments as the named variables (<code class="docutils literal notranslate"><span class="pre">$variable_name</span></code>).
The returned value is <code class="docutils literal notranslate"><span class="pre">true</span></code> if the path returns a non-empty sequence, and
<code class="docutils literal notranslate"><span class="pre">false</span></code> if the path returns an empty sequence. If an error occurs, the
returned value depends on the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause. The default value returned
<code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> is <code class="docutils literal notranslate"><span class="pre">FALSE</span></code>. The <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause is applied for the following
kinds of errors:</p>
<ul class="simple">
<li><p>Input conversion errors, such as malformed JSON</p></li>
<li><p>JSON path evaluation errors, e.g. division by zero</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">json_input</span></code> is a character string or a binary string. It should contain
a single JSON item. For a binary string, you can specify encoding.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_path</span></code> is a string literal, containing the path mode specification, and
the path expression, following the syntax rules described in
<a class="reference internal" href="json.html#json-path-syntax-and-semantics"><span class="std std-ref">JSON path syntax and semantics</span></a>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict ($.price + $.tax)?(@ &gt; 99.9)'
'lax $[0 to 1].floor()?(@ &gt; 10)'
</pre></div>
</div>
<p>In the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause you can pass arbitrary expressions to be used by the
path expression.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.totalprice AS O_PRICE,
        orders.tax % 10 AS O_TAX
</pre></div>
</div>
<p>The passed parameters can be referenced in the path expression by named
variables, prefixed with <code class="docutils literal notranslate"><span class="pre">$</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $?(@.price &gt; $O_PRICE || @.tax &gt; $O_TAX)'
</pre></div>
</div>
<p>Additionally to SQL values, you can pass JSON values, specifying the format and
optional encoding:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.json_desc FORMAT JSON AS o_desc,
        orders.binary_record FORMAT JSON ENCODING UTF16 AS o_rec
</pre></div>
</div>
<p>Note that the JSON path language is case-sensitive, while the unquoted SQL
identifiers are upper-cased. Therefore, it is recommended to use quoted
identifiers in the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS KeyName --&gt; ERROR; no passed value found
'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS "KeyName" --&gt; correct
</pre></div>
</div>
<section id="examples">
<h3 id="examples">Examples<a class="headerlink" href="json.html#examples" title="Link to this heading">#</a></h3>
<p>Let <code class="docutils literal notranslate"><span class="pre">customers</span></code> be a table containing two columns: <code class="docutils literal notranslate"><span class="pre">id:bigint</span></code>,
<code class="docutils literal notranslate"><span class="pre">description:varchar</span></code>.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘{“comment” : “nice”, “children” : [10, 13, 16]}’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘{“comment” : “problematic”, “children” : [8, 11]}’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘{“comment” : “knows best”, “children” : [2]}’</p></td>
</tr>
</tbody>
</table>
<p>The following query checks which customers have children above the age of 10:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_exists(
                  description,
                  'lax $.children[*]?(@ &gt; 10)'
                 ) AS children_above_ten
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>children_above_ten</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>true</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>true</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>false</p></td>
</tr>
</tbody>
</table>
<p>In the following query, the path mode is strict. We check the third child for
each customer. This should cause a structural error for the customers who do
not have three or more children. This error is handled according to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_exists(
                  description,
                  'strict $.children[2]?(@ &gt; 10)'
                  UNKNOWN ON ERROR
                 ) AS child_3_above_ten
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>child_3_above_ten</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>true</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>NULL</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="json-query">
<span id="id5"></span><h2 id="json-query">json_query<a class="headerlink" href="json.html#json-query" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_query</span></code> function extracts a JSON value from a JSON value.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_QUERY(
    json_input [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ],
    json_path
    [ PASSING json_argument [, ...] ]
    [ RETURNING type [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ] ]
    [ WITHOUT [ ARRAY ] WRAPPER |
      WITH [ { CONDITIONAL | UNCONDITIONAL } ] [ ARRAY ] WRAPPER ]
    [ { KEEP | OMIT } QUOTES [ ON SCALAR STRING ] ]
    [ { ERROR | NULL | EMPTY ARRAY | EMPTY OBJECT } ON EMPTY ]
    [ { ERROR | NULL | EMPTY ARRAY | EMPTY OBJECT } ON ERROR ]
    )
</pre></div>
</div>
<p>The constant string <code class="docutils literal notranslate"><span class="pre">json_path</span></code> is evaluated using the <code class="docutils literal notranslate"><span class="pre">json_input</span></code> as the
context variable (<code class="docutils literal notranslate"><span class="pre">$</span></code>), and the passed arguments as the named variables
(<code class="docutils literal notranslate"><span class="pre">$variable_name</span></code>).</p>
<p>The returned value is a JSON item returned by the path. By default, it is
represented as a character string (<code class="docutils literal notranslate"><span class="pre">varchar</span></code>). In the <code class="docutils literal notranslate"><span class="pre">RETURNING</span></code> clause,
you can specify other character string type or <code class="docutils literal notranslate"><span class="pre">varbinary</span></code>. With
<code class="docutils literal notranslate"><span class="pre">varbinary</span></code>, you can also specify the desired encoding.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_input</span></code> is a character string or a binary string. It should contain
a single JSON item. For a binary string, you can specify encoding.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_path</span></code> is a string literal, containing the path mode specification, and
the path expression, following the syntax rules described in
<a class="reference internal" href="json.html#json-path-syntax-and-semantics"><span class="std std-ref">JSON path syntax and semantics</span></a>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict $.keyvalue()?(@.name == $cust_id)'
'lax $[5 to last]'
</pre></div>
</div>
<p>In the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause you can pass arbitrary expressions to be used by the
path expression.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.custkey AS CUST_ID
</pre></div>
</div>
<p>The passed parameters can be referenced in the path expression by named
variables, prefixed with <code class="docutils literal notranslate"><span class="pre">$</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict $.keyvalue()?(@.value == $CUST_ID)'
</pre></div>
</div>
<p>Additionally to SQL values, you can pass JSON values, specifying the format and
optional encoding:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.json_desc FORMAT JSON AS o_desc,
        orders.binary_record FORMAT JSON ENCODING UTF16 AS o_rec
</pre></div>
</div>
<p>Note that the JSON path language is case-sensitive, while the unquoted SQL
identifiers are upper-cased. Therefore, it is recommended to use quoted
identifiers in the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS KeyName --&gt; ERROR; no passed value found
'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS "KeyName" --&gt; correct
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">ARRAY</span> <span class="pre">WRAPPER</span></code> clause lets you modify the output by wrapping the results
in a JSON array. <code class="docutils literal notranslate"><span class="pre">WITHOUT</span> <span class="pre">ARRAY</span> <span class="pre">WRAPPER</span></code> is the default option. <code class="docutils literal notranslate"><span class="pre">WITH</span> <span class="pre">CONDITIONAL</span> <span class="pre">ARRAY</span> <span class="pre">WRAPPER</span></code> wraps every result which is not a singleton JSON
array or JSON object. <code class="docutils literal notranslate"><span class="pre">WITH</span> <span class="pre">UNCONDITIONAL</span> <span class="pre">ARRAY</span> <span class="pre">WRAPPER</span></code> wraps every result.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">QUOTES</span></code> clause lets you modify the result for a scalar string by
removing the double quotes being part of the JSON string representation.</p>
<section id="id6">
<h3 id="id6">Examples<a class="headerlink" href="json.html#id6" title="Link to this heading">#</a></h3>
<p>Let <code class="docutils literal notranslate"><span class="pre">customers</span></code> be a table containing two columns: <code class="docutils literal notranslate"><span class="pre">id:bigint</span></code>,
<code class="docutils literal notranslate"><span class="pre">description:varchar</span></code>.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘{“comment” : “nice”, “children” : [10, 13, 16]}’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘{“comment” : “problematic”, “children” : [8, 11]}’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘{“comment” : “knows best”, “children” : [2]}’</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the <code class="docutils literal notranslate"><span class="pre">children</span></code> array for each customer:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_query(
                 description,
                 'lax $.children'
                ) AS children
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>children</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘[10,13,16]’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘[8,11]’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘[2]’</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the collection of children for each customer.
Note that the <code class="docutils literal notranslate"><span class="pre">json_query</span></code> function can only output a single JSON item. If
you don’t use array wrapper, you get an error for every customer with multiple
children. The error is handled according to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_query(
                 description,
                 'lax $.children[*]'
                 WITHOUT ARRAY WRAPPER
                 NULL ON ERROR
                ) AS children
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>children</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘2’</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the last child for each customer, wrapped in a JSON
array:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_query(
                 description,
                 'lax $.children[last]'
                 WITH ARRAY WRAPPER
                ) AS last_child
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>last_child</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘[16]’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘[11]’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘[2]’</p></td>
</tr>
</tbody>
</table>
<p>The following query gets all children above the age of 12 for each customer,
wrapped in a JSON array. The second and the third customer don’t have children
of this age. Such case is handled according to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">EMPTY</span></code> clause. The
default value returned <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">EMPTY</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. In the following example,
<code class="docutils literal notranslate"><span class="pre">EMPTY</span> <span class="pre">ARRAY</span> <span class="pre">ON</span> <span class="pre">EMPTY</span></code> is specified.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_query(
                 description,
                 'strict $.children[*]?(@ &gt; 12)'
                 WITH ARRAY WRAPPER
                 EMPTY ARRAY ON EMPTY
                ) AS children
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>children</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘[13,16]’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘[]’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘[]’</p></td>
</tr>
</tbody>
</table>
<p>The following query shows the result of the <code class="docutils literal notranslate"><span class="pre">QUOTES</span></code> clause. Note that <code class="docutils literal notranslate"><span class="pre">KEEP</span> <span class="pre">QUOTES</span></code> is the default.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT
      id,
      json_query(description, 'strict $.comment' KEEP QUOTES) AS quoted_comment,
      json_query(description, 'strict $.comment' OMIT QUOTES) AS unquoted_comment
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>quoted_comment</p></th>
<th class="head"><p>unquoted_comment</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘“nice”’</p></td>
<td><p>‘nice’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘“problematic”’</p></td>
<td><p>‘problematic’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘“knows best”’</p></td>
<td><p>‘knows best’</p></td>
</tr>
</tbody>
</table>
<p>If an error occurs, the returned value depends on the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause. The
default value returned <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. One example of error is
multiple items returned by the path. Other errors caught and handled according
to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause are:</p>
<ul class="simple">
<li><p>Input conversion errors, such as malformed JSON</p></li>
<li><p>JSON path evaluation errors, e.g. division by zero</p></li>
<li><p>Output conversion errors</p></li>
</ul>
</section>
</section>
<section id="json-value">
<span id="id7"></span><h2 id="json-value">json_value<a class="headerlink" href="json.html#json-value" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_value</span></code> function extracts a scalar SQL value from a JSON value.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_VALUE(
    json_input [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ],
    json_path
    [ PASSING json_argument [, ...] ]
    [ RETURNING type ]
    [ { ERROR | NULL | DEFAULT expression } ON EMPTY ]
    [ { ERROR | NULL | DEFAULT expression } ON ERROR ]
    )
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">json_path</span></code> is evaluated using the <code class="docutils literal notranslate"><span class="pre">json_input</span></code> as the context variable
(<code class="docutils literal notranslate"><span class="pre">$</span></code>), and the passed arguments as the named variables (<code class="docutils literal notranslate"><span class="pre">$variable_name</span></code>).</p>
<p>The returned value is the SQL scalar returned by the path. By default, it is
converted to string (<code class="docutils literal notranslate"><span class="pre">varchar</span></code>). In the <code class="docutils literal notranslate"><span class="pre">RETURNING</span></code> clause, you can specify
other desired type: a character string type, numeric, boolean or datetime type.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_input</span></code> is a character string or a binary string. It should contain
a single JSON item. For a binary string, you can specify encoding.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_path</span></code> is a string literal, containing the path mode specification, and
the path expression, following the syntax rules described in
<a class="reference internal" href="json.html#json-path-syntax-and-semantics"><span class="std std-ref">JSON path syntax and semantics</span></a>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict $.price + $tax'
'lax $[last].abs().floor()'
</pre></div>
</div>
<p>In the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause you can pass arbitrary expressions to be used by the
path expression.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.tax AS O_TAX
</pre></div>
</div>
<p>The passed parameters can be referenced in the path expression by named
variables, prefixed with <code class="docutils literal notranslate"><span class="pre">$</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict $[last].price + $O_TAX'
</pre></div>
</div>
<p>Additionally to SQL values, you can pass JSON values, specifying the format and
optional encoding:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.json_desc FORMAT JSON AS o_desc,
        orders.binary_record FORMAT JSON ENCODING UTF16 AS o_rec
</pre></div>
</div>
<p>Note that the JSON path language is case-sensitive, while the unquoted SQL
identifiers are upper-cased. Therefore, it is recommended to use quoted
identifiers in the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS KeyName --&gt; ERROR; no passed value found
'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS "KeyName" --&gt; correct
</pre></div>
</div>
<p>If the path returns an empty sequence, the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">EMPTY</span></code> clause is applied. The
default value returned <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">EMPTY</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. You can also specify the
default value:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>DEFAULT -1 ON EMPTY
</pre></div>
</div>
<p>If an error occurs, the returned value depends on the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause. The
default value returned <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. One example of error is
multiple items returned by the path. Other errors caught and handled according
to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause are:</p>
<ul class="simple">
<li><p>Input conversion errors, such as malformed JSON</p></li>
<li><p>JSON path evaluation errors, e.g. division by zero</p></li>
<li><p>Returned scalar not convertible to the desired type</p></li>
</ul>
<section id="id8">
<h3 id="id8">Examples<a class="headerlink" href="json.html#id8" title="Link to this heading">#</a></h3>
<p>Let <code class="docutils literal notranslate"><span class="pre">customers</span></code> be a table containing two columns: <code class="docutils literal notranslate"><span class="pre">id:bigint</span></code>,
<code class="docutils literal notranslate"><span class="pre">description:varchar</span></code>.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘{“comment” : “nice”, “children” : [10, 13, 16]}’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘{“comment” : “problematic”, “children” : [8, 11]}’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘{“comment” : “knows best”, “children” : [2]}’</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the <code class="docutils literal notranslate"><span class="pre">comment</span></code> for each customer as <code class="docutils literal notranslate"><span class="pre">char(12)</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT id, json_value(
                      description,
                      'lax $.comment'
                      RETURNING char(12)
                     ) AS comment
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>comment</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘nice        ‘</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘problematic ‘</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘knows best  ‘</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the first child’s age for each customer as
<code class="docutils literal notranslate"><span class="pre">tinyint</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT id, json_value(
                      description,
                      'lax $.children[0]'
                      RETURNING tinyint
                     ) AS child
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>child</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>10</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>2</p></td>
</tr>
</tbody>
</table>
<p>The following query gets the third child’s age for each customer. In the strict
mode, this should cause a structural error for the customers who do not have
the third child. This error is handled according to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> clause.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT id, json_value(
                      description,
                      'strict $.children[2]'
                      DEFAULT 'err' ON ERROR
                     ) AS child
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>child</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘16’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘err’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘err’</p></td>
</tr>
</tbody>
</table>
<p>After changing the mode to lax, the structural error is suppressed, and the
customers without a third child produce empty sequence. This case is handled
according to the <code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">EMPTY</span></code> clause.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT id, json_value(
                      description,
                      'lax $.children[2]'
                      DEFAULT 'missing' ON EMPTY
                     ) AS child
FROM customers
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>child</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>101</p></td>
<td><p>‘16’</p></td>
</tr>
<tr class="row-odd"><td><p>102</p></td>
<td><p>‘missing’</p></td>
</tr>
<tr class="row-even"><td><p>103</p></td>
<td><p>‘missing’</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="json-table">
<span id="id9"></span><h2 id="json-table">json_table<a class="headerlink" href="json.html#json-table" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_table</span></code> clause extracts a table from a JSON value. Use this clause to
transform JSON data into a relational format, making it easier to query and
analyze. Use <code class="docutils literal notranslate"><span class="pre">json_table</span></code> in the <code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause of a
<a class="reference internal" href="../sql/select.html#select-json-table"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">SELECT</span></code></span></a> statement to create a table from JSON data.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_TABLE(
    json_input,
    json_path [ AS path_name ]
    [ PASSING value AS parameter_name [, ...] ]
    COLUMNS (
        column_definition [, ...] )
    [ PLAN ( json_table_specific_plan )
      | PLAN DEFAULT ( json_table_default_plan ) ]
    [ { ERROR | EMPTY } ON ERROR ]
)
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">COLUMNS</span></code> clause supports the following <code class="docutils literal notranslate"><span class="pre">column_definition</span></code> arguments:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>column_name FOR ORDINALITY
| column_name type
    [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ]
    [ PATH json_path ]
    [ { WITHOUT | WITH { CONDITIONAL | UNCONDITIONAL } } [ ARRAY ] WRAPPER ]
    [ { KEEP | OMIT } QUOTES [ ON SCALAR STRING ] ]
    [ { ERROR | NULL | EMPTY { [ARRAY] | OBJECT } | DEFAULT expression } ON EMPTY ]
    [ { ERROR | NULL | DEFAULT expression } ON ERROR ]
| NESTED [ PATH ] json_path [ AS path_name ] COLUMNS ( column_definition [, ...] )
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">json_input</span></code> is a character string or a binary string. It must contain a single
JSON item.</p>
<p><code class="docutils literal notranslate"><span class="pre">json_path</span></code> is a string literal containing the path mode specification and the
path expression. It follows the syntax rules described in
<a class="reference internal" href="json.html#json-path-syntax-and-semantics"><span class="std std-ref">JSON path syntax and semantics</span></a>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'strict ($.price + $.tax)?(@ &gt; 99.9)'
'lax $[0 to 1].floor()?(@ &gt; 10)'
</pre></div>
</div>
<p>In the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause, pass values as named parameters that the <code class="docutils literal notranslate"><span class="pre">json_path</span></code>
expression can reference.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.totalprice AS o_price,
        orders.tax % 10 AS o_tax
</pre></div>
</div>
<p>Use named parameters to reference the values in the path expression. Prefix
named parameters with <code class="docutils literal notranslate"><span class="pre">$</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $?(@.price &gt; $o_price || @.tax &gt; $o_tax)'
</pre></div>
</div>
<p>You can also pass JSON values in the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause. Use <code class="docutils literal notranslate"><span class="pre">FORMAT</span> <span class="pre">JSON</span></code> to
specify the format and <code class="docutils literal notranslate"><span class="pre">ENCODING</span></code> to specify the encoding:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>PASSING orders.json_desc FORMAT JSON AS o_desc,
        orders.binary_record FORMAT JSON ENCODING UTF16 AS o_rec
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">json_path</span></code> value is case-sensitive. The SQL identifiers are uppercase. Use
quoted identifiers in the <code class="docutils literal notranslate"><span class="pre">PASSING</span></code> clause:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS KeyName --&gt; ERROR; no passed value found
'lax $.keyvalue()?(@.name == $KeyName).value' PASSING nation.name AS "KeyName" --&gt; correct
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">PLAN</span></code> clause specifies how to join columns from different paths. Use
<code class="docutils literal notranslate"><span class="pre">OUTER</span></code> or <code class="docutils literal notranslate"><span class="pre">INNER</span></code> to define how to join parent paths with their child paths.
Use <code class="docutils literal notranslate"><span class="pre">CROSS</span></code> or <code class="docutils literal notranslate"><span class="pre">UNION</span></code> to join siblings.</p>
<p><code class="docutils literal notranslate"><span class="pre">COLUMNS</span></code> defines the schema of your table. Each <code class="docutils literal notranslate"><span class="pre">column_definition</span></code> specifies
how to extract and format your <code class="docutils literal notranslate"><span class="pre">json_input</span></code> value into a relational column.</p>
<p><code class="docutils literal notranslate"><span class="pre">PLAN</span></code> is an optional clause to control how to process and join nested JSON
data.</p>
<p><code class="docutils literal notranslate"><span class="pre">ON</span> <span class="pre">ERROR</span></code> specifies how to handle processing errors. <code class="docutils literal notranslate"><span class="pre">ERROR</span> <span class="pre">ON</span> <span class="pre">ERROR</span></code> throws an
error. <code class="docutils literal notranslate"><span class="pre">EMPTY</span> <span class="pre">ON</span> <span class="pre">ERROR</span></code> returns an empty result set.</p>
<p><code class="docutils literal notranslate"><span class="pre">column_name</span></code> specifies a column name.</p>
<p><code class="docutils literal notranslate"><span class="pre">FOR</span> <span class="pre">ORDINALITY</span></code> adds a row number column to the output table, starting at <code class="docutils literal notranslate"><span class="pre">1</span></code>.
Specify the column name in the column definition:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>row_num FOR ORDINALITY
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">NESTED</span> <span class="pre">PATH</span></code> extracts data from nested levels of a <code class="docutils literal notranslate"><span class="pre">json_input</span></code> value. Each
<code class="docutils literal notranslate"><span class="pre">NESTED</span> <span class="pre">PATH</span></code> clause can contain <code class="docutils literal notranslate"><span class="pre">column_definition</span></code> values.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">json_table</span></code> function returns a result set that you can use like any other
table in your queries. You can join the result set with other tables or
combine multiple arrays from your JSON data.</p>
<p>You can also process nested JSON objects without parsing the data multiple
times.</p>
<p>Use <code class="docutils literal notranslate"><span class="pre">json_table</span></code> as a lateral join to process JSON data from another table.</p>
<section id="id10">
<h3 id="id10">Examples<a class="headerlink" href="json.html#id10" title="Link to this heading">#</a></h3>
<p>The following query uses <code class="docutils literal notranslate"><span class="pre">json_table</span></code> to extract values from a JSON array and
return them as rows in a table with three columns:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">      </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">      </span><span class="n">json_table</span><span class="p">(</span>
<span class="w">                </span><span class="s1">'[</span>
<span class="s1">                  {"id":1,"name":"Africa","wikiDataId":"Q15"},</span>
<span class="s1">                  {"id":2,"name":"Americas","wikiDataId":"Q828"},</span>
<span class="s1">                  {"id":3,"name":"Asia","wikiDataId":"Q48"},</span>
<span class="s1">                  {"id":4,"name":"Europe","wikiDataId":"Q51"}</span>
<span class="s1">                ]'</span><span class="p">,</span>
<span class="w">                </span><span class="s1">'strict $'</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                  </span><span class="n">NESTED</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'strict $[*]'</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                    </span><span class="n">id</span><span class="w"> </span><span class="nb">integer</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'strict $.id'</span><span class="p">,</span>
<span class="w">                    </span><span class="n">name</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'strict $.name'</span><span class="p">,</span>
<span class="w">                    </span><span class="n">wiki_data_id</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'strict $."wikiDataId"'</span>
<span class="w">                  </span><span class="p">)</span>
<span class="w">                </span><span class="p">)</span>
<span class="w">              </span><span class="p">);</span>
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>id</p></th>
<th class="head"><p>child</p></th>
<th class="head"><p>wiki_data_id</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>1</p></td>
<td><p>Africa</p></td>
<td><p>Q1</p></td>
</tr>
<tr class="row-odd"><td><p>2</p></td>
<td><p>Americas</p></td>
<td><p>Q828</p></td>
</tr>
<tr class="row-even"><td><p>3</p></td>
<td><p>Asia</p></td>
<td><p>Q48</p></td>
</tr>
<tr class="row-odd"><td><p>4</p></td>
<td><p>Europe</p></td>
<td><p>Q51</p></td>
</tr>
</tbody>
</table>
<p>The following query uses <code class="docutils literal notranslate"><span class="pre">json_table</span></code> to extract values from an array of nested
JSON objects. It flattens the nested JSON data into a single table. The example
query processes an array of continent names, where each continent contains an
array of countries and their populations.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">NESTED</span> <span class="pre">PATH</span> <span class="pre">'lax</span> <span class="pre">$[*]'</span></code> clause iterates through the continent objects,
while the <code class="docutils literal notranslate"><span class="pre">NESTED</span> <span class="pre">PATH</span> <span class="pre">'lax</span> <span class="pre">$.countries[*]'</span></code> iterates through each country
within each continent. This creates a flat table structure with four rows
combining each continent with each of its countries. Continent values repeat for
each of their countries.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">      </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">      </span><span class="n">json_table</span><span class="p">(</span>
<span class="w">                </span><span class="s1">'[</span>
<span class="s1">                    {"continent": "Asia", "countries": [</span>
<span class="s1">                        {"name": "Japan", "population": 125.7},</span>
<span class="s1">                        {"name": "Thailand", "population": 71.6}</span>
<span class="s1">                    ]},</span>
<span class="s1">                    {"continent": "Europe", "countries": [</span>
<span class="s1">                        {"name": "France", "population": 67.4},</span>
<span class="s1">                        {"name": "Germany", "population": 83.2}</span>
<span class="s1">                    ]}</span>
<span class="s1">                ]'</span><span class="p">,</span>
<span class="w">                </span><span class="s1">'lax $'</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                    </span><span class="n">NESTED</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $[*]'</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                        </span><span class="n">continent</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $.continent'</span><span class="p">,</span>
<span class="w">                        </span><span class="n">NESTED</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $.countries[*]'</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span>
<span class="w">                            </span><span class="n">country</span><span class="w"> </span><span class="nb">varchar</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $.name'</span><span class="p">,</span>
<span class="w">                            </span><span class="n">population</span><span class="w"> </span><span class="n">double</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $.population'</span>
<span class="w">                        </span><span class="p">)</span>
<span class="w">                    </span><span class="p">)</span>
<span class="w">                </span><span class="p">));</span>
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>continent</p></th>
<th class="head"><p>country</p></th>
<th class="head"><p>population</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Asia</p></td>
<td><p>Japan</p></td>
<td><p>125.7</p></td>
</tr>
<tr class="row-odd"><td><p>Asia</p></td>
<td><p>Thailand</p></td>
<td><p>71.6</p></td>
</tr>
<tr class="row-even"><td><p>Europe</p></td>
<td><p>France</p></td>
<td><p>67.4</p></td>
</tr>
<tr class="row-odd"><td><p>Europe</p></td>
<td><p>Germany</p></td>
<td><p>83.2</p></td>
</tr>
</tbody>
</table>
<p>The following query uses <code class="docutils literal notranslate"><span class="pre">PLAN</span></code> to specify an <code class="docutils literal notranslate"><span class="pre">OUTER</span></code> join between a parent path
and a child path:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">      </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">      </span><span class="n">JSON_TABLE</span><span class="p">(</span>
<span class="w">                </span><span class="s1">'[]'</span><span class="p">,</span>
<span class="w">                </span><span class="s1">'lax $'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="ss">"root_path"</span>
<span class="w">                </span><span class="n">COLUMNS</span><span class="p">(</span>
<span class="w">                    </span><span class="n">a</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax "A"'</span><span class="p">,</span>
<span class="w">                    </span><span class="n">NESTED</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $[*]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="ss">"nested_path"</span>
<span class="w">                            </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span><span class="n">b</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax "B"'</span><span class="p">))</span>
<span class="w">                </span><span class="n">PLAN</span><span class="w"> </span><span class="p">(</span><span class="ss">"root_path"</span><span class="w"> </span><span class="k">OUTER</span><span class="w"> </span><span class="ss">"nested_path"</span><span class="p">)</span>
<span class="w">                </span><span class="p">);</span>
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>a</p></th>
<th class="head"><p>b</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>A</p></td>
<td><p>null</p></td>
</tr>
</tbody>
</table>
<p>The following query uses <code class="docutils literal notranslate"><span class="pre">PLAN</span></code> to specify an <code class="docutils literal notranslate"><span class="pre">INNER</span></code> join between a parent path
and a child path:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">      </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">      </span><span class="n">JSON_TABLE</span><span class="p">(</span>
<span class="w">                </span><span class="s1">'[]'</span><span class="p">,</span>
<span class="w">                </span><span class="s1">'lax $'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="ss">"root_path"</span>
<span class="w">                </span><span class="n">COLUMNS</span><span class="p">(</span>
<span class="w">                    </span><span class="n">a</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax "A"'</span><span class="p">,</span>
<span class="w">                    </span><span class="n">NESTED</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax $[*]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="ss">"nested_path"</span>
<span class="w">                            </span><span class="n">COLUMNS</span><span class="w"> </span><span class="p">(</span><span class="n">b</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="n">PATH</span><span class="w"> </span><span class="s1">'lax "B"'</span><span class="p">))</span>
<span class="w">                </span><span class="n">PLAN</span><span class="w"> </span><span class="p">(</span><span class="ss">"root_path"</span><span class="w"> </span><span class="k">INNER</span><span class="w"> </span><span class="ss">"nested_path"</span><span class="p">)</span>
<span class="w">                </span><span class="p">);</span>
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>a</p></th>
<th class="head"><p>b</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>null</p></td>
<td><p>null</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="json-array">
<span id="id11"></span><h2 id="json-array">json_array<a class="headerlink" href="json.html#json-array" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_array</span></code> function creates a JSON array containing given elements.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_ARRAY(
    [ array_element [, ...]
      [ { NULL ON NULL | ABSENT ON NULL } ] ],
    [ RETURNING type [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ] ]
    )
</pre></div>
</div>
<section id="argument-types">
<h3 id="argument-types">Argument types<a class="headerlink" href="json.html#argument-types" title="Link to this heading">#</a></h3>
<p>The array elements can be arbitrary expressions. Each passed value is converted
into a JSON item according to its type, and optional <code class="docutils literal notranslate"><span class="pre">FORMAT</span></code> and
<code class="docutils literal notranslate"><span class="pre">ENCODING</span></code> specification.</p>
<p>You can pass SQL values of types boolean, numeric, and character string. They
are converted to corresponding JSON literals:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="n">e</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'text'</span><span class="p">)</span>
<span class="c1">--&gt; '[true,1.2,"text"]'</span>
</pre></div>
</div>
<p>Additionally to SQL values, you can pass JSON values. They are character or
binary strings with a specified format and optional encoding:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span>
<span class="w">                  </span><span class="s1">'[  "text"  ] '</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="p">,</span>
<span class="w">                  </span><span class="n">X</span><span class="s1">'5B0035005D00'</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF16</span>
<span class="w">                 </span><span class="p">)</span>
<span class="c1">--&gt; '[["text"],[5]]'</span>
</pre></div>
</div>
<p>You can also nest other JSON-returning functions. In that case, the <code class="docutils literal notranslate"><span class="pre">FORMAT</span></code>
option is implicit:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span>
<span class="w">                  </span><span class="n">json_query</span><span class="p">(</span><span class="s1">'{"key" : [  "value"  ]}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'lax $.key'</span><span class="p">)</span>
<span class="w">                 </span><span class="p">)</span>
<span class="c1">--&gt; '[["value"]]'</span>
</pre></div>
</div>
<p>Other passed values are cast to varchar, and they become JSON text literals:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span>
<span class="w">                  </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2001-01-31'</span><span class="p">,</span>
<span class="w">                  </span><span class="n">UUID</span><span class="w"> </span><span class="s1">'12151fd2-7586-11e9-8f9e-2a86e4085a59'</span>
<span class="w">                 </span><span class="p">)</span>
<span class="c1">--&gt; '["2001-01-31","12151fd2-7586-11e9-8f9e-2a86e4085a59"]'</span>
</pre></div>
</div>
<p>You can omit the arguments altogether to get an empty array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">()</span><span class="w"> </span><span class="c1">--&gt; '[]'</span>
</pre></div>
</div>
</section>
<section id="null-handling">
<h3 id="null-handling">Null handling<a class="headerlink" href="json.html#null-handling" title="Link to this heading">#</a></h3>
<p>If a value passed for an array element is <code class="docutils literal notranslate"><span class="pre">null</span></code>, it is treated according to
the specified null treatment option. If <code class="docutils literal notranslate"><span class="pre">ABSENT</span> <span class="pre">ON</span> <span class="pre">NULL</span></code> is specified, the
null element is omitted in the result. If <code class="docutils literal notranslate"><span class="pre">NULL</span> <span class="pre">ON</span> <span class="pre">NULL</span></code> is specified, JSON
<code class="docutils literal notranslate"><span class="pre">null</span></code> is added to the result. <code class="docutils literal notranslate"><span class="pre">ABSENT</span> <span class="pre">ON</span> <span class="pre">NULL</span></code> is the default
configuration:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span>
<span class="c1">--&gt; '[true,1]'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">ABSENT</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="c1">--&gt; '[true,1]'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="c1">--&gt; '[true,null,1]'</span>
</pre></div>
</div>
</section>
<section id="returned-type">
<h3 id="returned-type">Returned type<a class="headerlink" href="json.html#returned-type" title="Link to this heading">#</a></h3>
<p>The SQL standard imposes that there is no dedicated data type to represent JSON
data in SQL. Instead, JSON data is represented as character or binary strings.
By default, the <code class="docutils literal notranslate"><span class="pre">json_array</span></code> function returns varchar containing the textual
representation of the JSON array. With the <code class="docutils literal notranslate"><span class="pre">RETURNING</span></code> clause, you can
specify other character string type:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">))</span>
<span class="c1">--&gt; '[true,1]'</span>
</pre></div>
</div>
<p>You can also specify to use varbinary and the required encoding as return type.
The default encoding is UTF8:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="p">)</span>
<span class="c1">--&gt; X'5b 74 72 75 65 2c 31 5d'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF8</span><span class="p">)</span>
<span class="c1">--&gt; X'5b 74 72 75 65 2c 31 5d'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF16</span><span class="p">)</span>
<span class="c1">--&gt; X'5b 00 74 00 72 00 75 00 65 00 2c 00 31 00 5d 00'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array</span><span class="p">(</span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF32</span><span class="p">)</span>
<span class="c1">--&gt; X'5b 00 00 00 74 00 00 00 72 00 00 00 75 00 00 00 65 00 00 00 2c 00 00 00 31 00 00 00 5d 00 00 00'</span>
</pre></div>
</div>
</section>
</section>
<section id="json-object">
<span id="id12"></span><h2 id="json-object">json_object<a class="headerlink" href="json.html#json-object" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">json_object</span></code> function creates a JSON object containing given key-value pairs.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>JSON_OBJECT(
    [ key_value [, ...]
      [ { NULL ON NULL | ABSENT ON NULL } ] ],
      [ { WITH UNIQUE [ KEYS ] | WITHOUT UNIQUE [ KEYS ] } ]
    [ RETURNING type [ FORMAT JSON [ ENCODING { UTF8 | UTF16 | UTF32 } ] ] ]
    )
</pre></div>
</div>
<section id="argument-passing-conventions">
<h3 id="argument-passing-conventions">Argument passing conventions<a class="headerlink" href="json.html#argument-passing-conventions" title="Link to this heading">#</a></h3>
<p>There are two conventions for passing keys and values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'key1'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'key2'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">true</span><span class="p">)</span>
<span class="c1">--&gt; '{"key1":1,"key2":true}'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="k">KEY</span><span class="w"> </span><span class="s1">'key1'</span><span class="w"> </span><span class="n">VALUE</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="s1">'key2'</span><span class="w"> </span><span class="n">VALUE</span><span class="w"> </span><span class="k">true</span><span class="p">)</span>
<span class="c1">--&gt; '{"key1":1,"key2":true}'</span>
</pre></div>
</div>
<p>In the second convention, you can omit the <code class="docutils literal notranslate"><span class="pre">KEY</span></code> keyword:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'key1'</span><span class="w"> </span><span class="n">VALUE</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'key2'</span><span class="w"> </span><span class="n">VALUE</span><span class="w"> </span><span class="k">true</span><span class="p">)</span>
<span class="c1">--&gt; '{"key1":1,"key2":true}'</span>
</pre></div>
</div>
</section>
<section id="id13">
<h3 id="id13">Argument types<a class="headerlink" href="json.html#id13" title="Link to this heading">#</a></h3>
<p>The keys can be arbitrary expressions. They must be of character string type.
Each key is converted into a JSON text item, and it becomes a key in the
created JSON object. Keys must not be null.</p>
<p>The values can be arbitrary expressions. Each passed value is converted
into a JSON item according to its type, and optional <code class="docutils literal notranslate"><span class="pre">FORMAT</span></code> and
<code class="docutils literal notranslate"><span class="pre">ENCODING</span></code> specification.</p>
<p>You can pass SQL values of types boolean, numeric, and character string. They
are converted to corresponding JSON literals:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="n">e</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'z'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s1">'text'</span><span class="p">)</span>
<span class="c1">--&gt; '{"x":true,"y":1.2,"z":"text"}'</span>
</pre></div>
</div>
<p>Additionally to SQL values, you can pass JSON values. They are character or
binary strings with a specified format and optional encoding:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span>
<span class="w">                   </span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s1">'[  "text"  ] '</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="p">,</span>
<span class="w">                   </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">X</span><span class="s1">'5B0035005D00'</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF16</span>
<span class="w">                  </span><span class="p">)</span>
<span class="c1">--&gt; '{"x":["text"],"y":[5]}'</span>
</pre></div>
</div>
<p>You can also nest other JSON-returning functions. In that case, the <code class="docutils literal notranslate"><span class="pre">FORMAT</span></code>
option is implicit:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span>
<span class="w">                   </span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">json_query</span><span class="p">(</span><span class="s1">'{"key" : [  "value"  ]}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'lax $.key'</span><span class="p">)</span>
<span class="w">                  </span><span class="p">)</span>
<span class="c1">--&gt; '{"x":["value"]}'</span>
</pre></div>
</div>
<p>Other passed values are cast to varchar, and they become JSON text literals:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span>
<span class="w">                   </span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2001-01-31'</span><span class="p">,</span>
<span class="w">                   </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="s1">'12151fd2-7586-11e9-8f9e-2a86e4085a59'</span>
<span class="w">                  </span><span class="p">)</span>
<span class="c1">--&gt; '{"x":"2001-01-31","y":"12151fd2-7586-11e9-8f9e-2a86e4085a59"}'</span>
</pre></div>
</div>
<p>You can omit the arguments altogether to get an empty object:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">()</span><span class="w"> </span><span class="c1">--&gt; '{}'</span>
</pre></div>
</div>
</section>
<section id="id14">
<h3 id="id14">Null handling<a class="headerlink" href="json.html#id14" title="Link to this heading">#</a></h3>
<p>The values passed for JSON object keys must not be null. It is allowed to pass
<code class="docutils literal notranslate"><span class="pre">null</span></code> for JSON object values. A null value is treated according to the
specified null treatment option. If <code class="docutils literal notranslate"><span class="pre">NULL</span> <span class="pre">ON</span> <span class="pre">NULL</span></code> is specified, a JSON
object entry with <code class="docutils literal notranslate"><span class="pre">null</span></code> value is added to the result. If <code class="docutils literal notranslate"><span class="pre">ABSENT</span> <span class="pre">ON</span> <span class="pre">NULL</span></code>
is specified, the entry is omitted in the result. <code class="docutils literal notranslate"><span class="pre">NULL</span> <span class="pre">ON</span> <span class="pre">NULL</span></code> is the
default configuration.:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span>
<span class="c1">--&gt; '{"x":null,"y":1}'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="c1">--&gt; '{"x":null,"y":1}'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="s1">'y'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">ABSENT</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="c1">--&gt; '{"y":1}'</span>
</pre></div>
</div>
</section>
<section id="key-uniqueness">
<h3 id="key-uniqueness">Key uniqueness<a class="headerlink" href="json.html#key-uniqueness" title="Link to this heading">#</a></h3>
<p>If a duplicate key is encountered, it is handled according to the specified key
uniqueness constraint.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">WITH</span> <span class="pre">UNIQUE</span> <span class="pre">KEYS</span></code> is specified, a duplicate key results in a query
failure:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">UNIQUE</span><span class="w"> </span><span class="n">KEYS</span><span class="p">)</span>
<span class="c1">--&gt; failure: "duplicate key passed to JSON_OBJECT function"</span>
</pre></div>
</div>
<p>Note that this option is not supported if any of the arguments has a
<code class="docutils literal notranslate"><span class="pre">FORMAT</span></code> specification.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">WITHOUT</span> <span class="pre">UNIQUE</span> <span class="pre">KEYS</span></code> is specified, duplicate keys are not supported due
to implementation limitation. <code class="docutils literal notranslate"><span class="pre">WITHOUT</span> <span class="pre">UNIQUE</span> <span class="pre">KEYS</span></code> is the default
configuration.</p>
</section>
<section id="id15">
<h3 id="id15">Returned type<a class="headerlink" href="json.html#id15" title="Link to this heading">#</a></h3>
<p>The SQL standard imposes that there is no dedicated data type to represent JSON
data in SQL. Instead, JSON data is represented as character or binary strings.
By default, the <code class="docutils literal notranslate"><span class="pre">json_object</span></code> function returns varchar containing the textual
representation of the JSON object. With the <code class="docutils literal notranslate"><span class="pre">RETURNING</span></code> clause, you can
specify other character string type:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">))</span>
<span class="c1">--&gt; '{"x":1}'</span>
</pre></div>
</div>
<p>You can also specify to use varbinary and the required encoding as return type.
The default encoding is UTF8:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="p">)</span>
<span class="c1">--&gt; X'7b 22 78 22 3a 31 7d'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF8</span><span class="p">)</span>
<span class="c1">--&gt; X'7b 22 78 22 3a 31 7d'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF16</span><span class="p">)</span>
<span class="c1">--&gt; X'7b 00 22 00 78 00 22 00 3a 00 31 00 7d 00'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">json_object</span><span class="p">(</span><span class="s1">'x'</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="n">RETURNING</span><span class="w"> </span><span class="n">VARBINARY</span><span class="w"> </span><span class="n">FORMAT</span><span class="w"> </span><span class="n">JSON</span><span class="w"> </span><span class="k">ENCODING</span><span class="w"> </span><span class="n">UTF32</span><span class="p">)</span>
<span class="c1">--&gt; X'7b 00 00 00 22 00 00 00 78 00 00 00 22 00 00 00 3a 00 00 00 31 00 00 00 7d 00 00 00'</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The following functions and operators are not compliant with the SQL
standard, and should be considered deprecated. According to the SQL
standard, there shall be no <code class="docutils literal notranslate"><span class="pre">JSON</span></code> data type. Instead, JSON values
should be represented as string values. The remaining functionality of the
following functions is covered by the functions described previously.</p>
</div>
</section>
</section>
<section id="cast-to-json">
<h2 id="cast-to-json">Cast to JSON<a class="headerlink" href="json.html#cast-to-json" title="Link to this heading">#</a></h2>
<p>The following types can be cast to JSON:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></li>
</ul>
<p>Additionally, <code class="docutils literal notranslate"><span class="pre">ARRAY</span></code>, <code class="docutils literal notranslate"><span class="pre">MAP</span></code>, and <code class="docutils literal notranslate"><span class="pre">ROW</span></code> types can be cast to JSON when
the following requirements are met:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code> types can be cast when the element type of the array is one
of the supported types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code> types can be cast when the key type of the map is <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> and
the value type of the map is a supported type,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code> types can be cast when every field type of the row is a supported
type.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Cast operations with supported <a class="reference internal" href="../language/types.html#string-data-types"><span class="std std-ref">character string types</span></a> treat the input as a string, not validated as JSON.
This means that a cast operation with a string-type input of invalid JSON
results in a successful cast to invalid JSON.</p>
<p>Instead, consider using the <a class="reference internal" href="json.html#json_parse" title="json_parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_parse()</span></code></a> function to
create validated JSON from a string.</p>
</div>
<p>The following examples show the behavior of casting to JSON with these types:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">NULL</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- NULL</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="mi">1</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '1'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="mi">9223372036854775807</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '9223372036854775807'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'abc'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '"abc"'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">true</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON 'true'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="mi">1</span><span class="p">.</span><span class="mi">234</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '1.234'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span><span class="w"> </span><span class="mi">456</span><span class="p">]</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '[1,23,456]'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">456</span><span class="p">]</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '[1,null,456]'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">23</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">456</span><span class="p">]]</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '[[1,23],[456]]'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">MAP</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'k1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'k2'</span><span class="p">,</span><span class="w"> </span><span class="s1">'k3'</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span><span class="w"> </span><span class="mi">456</span><span class="p">])</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '{"k1":1,"k2":23,"k3":456}'</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">CAST</span><span class="p">(</span><span class="k">ROW</span><span class="p">(</span><span class="mi">123</span><span class="p">,</span><span class="w"> </span><span class="s1">'abc'</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span>
<span class="w">            </span><span class="k">ROW</span><span class="p">(</span><span class="n">v1</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span><span class="w"> </span><span class="n">v2</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">v3</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span>
<span class="c1">-- JSON '{"v1":123,"v2":"abc","v3":true}'</span>
</pre></div>
</div>
<p>Casting from NULL to <code class="docutils literal notranslate"><span class="pre">JSON</span></code> is not straightforward. Casting
from a standalone <code class="docutils literal notranslate"><span class="pre">NULL</span></code> will produce SQL <code class="docutils literal notranslate"><span class="pre">NULL</span></code> instead of
<code class="docutils literal notranslate"><span class="pre">JSON</span> <span class="pre">'null'</span></code>. However, when casting from arrays or map containing
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>s, the produced <code class="docutils literal notranslate"><span class="pre">JSON</span></code> will have <code class="docutils literal notranslate"><span class="pre">null</span></code>s in it.</p>
</section>
<section id="cast-from-json">
<h2 id="cast-from-json">Cast from JSON<a class="headerlink" href="json.html#cast-from-json" title="Link to this heading">#</a></h2>
<p>Casting to <code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>,
<code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">REAL</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> or <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> is supported.
Casting to <code class="docutils literal notranslate"><span class="pre">ARRAY</span></code> and <code class="docutils literal notranslate"><span class="pre">MAP</span></code> is supported when the element type of
the array is one of the supported types, or when the key type of the map
is <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> and value type of the map is one of the supported types.
Behaviors of the casts are shown with the examples below:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'null'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span>
<span class="c1">-- NULL</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'1'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">);</span>
<span class="c1">-- 1</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'9223372036854775807'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">);</span>
<span class="c1">-- 9223372036854775807</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'"abc"'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span>
<span class="c1">-- abc</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'true'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="p">);</span>
<span class="c1">-- true</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'1.234'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">);</span>
<span class="c1">-- 1.234</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[1,23,456]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">(</span><span class="nb">INTEGER</span><span class="p">));</span>
<span class="c1">-- [1, 23, 456]</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[1,null,456]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">(</span><span class="nb">INTEGER</span><span class="p">));</span>
<span class="c1">-- [1, NULL, 456]</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[[1,23],[456]]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">(</span><span class="nb">INTEGER</span><span class="p">)));</span>
<span class="c1">-- [[1, 23], [456]]</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'{"k1":1,"k2":23,"k3":456}'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">MAP</span><span class="p">(</span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">));</span>
<span class="c1">-- {k1=1, k2=23, k3=456}</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'{"v1":123,"v2":"abc","v3":true}'</span><span class="w"> </span><span class="k">AS</span>
<span class="w">            </span><span class="k">ROW</span><span class="p">(</span><span class="n">v1</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span><span class="w"> </span><span class="n">v2</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">v3</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="p">));</span>
<span class="c1">-- {v1=123, v2=abc, v3=true}</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[123,"abc",true]'</span><span class="w"> </span><span class="k">AS</span>
<span class="w">            </span><span class="k">ROW</span><span class="p">(</span><span class="n">v1</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span><span class="w"> </span><span class="n">v2</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">v3</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="p">));</span>
<span class="c1">-- {v1=123, v2=abc, v3=true}</span>
</pre></div>
</div>
<p>JSON arrays can have mixed element types and JSON maps can have mixed
value types. This makes it impossible to cast them to SQL arrays and maps in
some cases. To address this, Trino supports partial casting of arrays and maps:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[[1, 23], 456]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">(</span><span class="n">JSON</span><span class="p">));</span>
<span class="c1">-- [JSON '[1,23]', JSON '456']</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'{"k1": [1, 23], "k2": 456}'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">MAP</span><span class="p">(</span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">JSON</span><span class="p">));</span>
<span class="c1">-- {k1 = JSON '[1,23]', k2 = JSON '456'}</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[null]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">(</span><span class="n">JSON</span><span class="p">));</span>
<span class="c1">-- [JSON 'null']</span>
</pre></div>
</div>
<p>When casting from <code class="docutils literal notranslate"><span class="pre">JSON</span></code> to <code class="docutils literal notranslate"><span class="pre">ROW</span></code>, both JSON array and JSON object are supported.</p>
</section>
<section id="other-json-functions">
<h2 id="other-json-functions">Other JSON functions<a class="headerlink" href="json.html#other-json-functions" title="Link to this heading">#</a></h2>
<p>In addition to the functions explained in more details in the preceding
sections, the following functions are available:</p>
<dl class="py function">
<dt class="sig sig-object py" id="is_json_scalar">
<span class="sig-name descname"><span class="pre">is_json_scalar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="json.html#is_json_scalar" title="Link to this definition">#</a></dt>
<dd><p>Determine if <code class="docutils literal notranslate"><span class="pre">json</span></code> is a scalar (i.e. a JSON number, a JSON string, <code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code> or <code class="docutils literal notranslate"><span class="pre">null</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">is_json_scalar</span><span class="p">(</span><span class="s1">'1'</span><span class="p">);</span><span class="w">         </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">is_json_scalar</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- false</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_array_contains">
<span class="sig-name descname"><span class="pre">json_array_contains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="json.html#json_array_contains" title="Link to this definition">#</a></dt>
<dd><p>Determine if <code class="docutils literal notranslate"><span class="pre">value</span></code> exists in <code class="docutils literal notranslate"><span class="pre">json</span></code> (a string containing a JSON array):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_contains</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_array_get">
<span class="sig-name descname"><span class="pre">json_array_get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json_array</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">json</span></span></span><a class="headerlink" href="json.html#json_array_get" title="Link to this definition">#</a></dt>
<dd><div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The semantics of this function are broken. If the extracted element
is a string, it will be converted into an invalid <code class="docutils literal notranslate"><span class="pre">JSON</span></code> value that
is not properly quoted (the value will not be surrounded by quotes
and any interior quotes will not be escaped).</p>
<p>We recommend against using this function. It cannot be fixed without
impacting existing usages and may be removed in a future release.</p>
</div>
<p>Returns the element at the specified index into the <code class="docutils literal notranslate"><span class="pre">json_array</span></code>.
The index is zero-based:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["a", [3, 9], "c"]'</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON 'a' (invalid JSON)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["a", [3, 9], "c"]'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON '[3,9]'</span>
</pre></div>
</div>
<p>This function also supports negative indexes for fetching element indexed
from the end of an array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["c", [3, 9], "a"]'</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON 'a' (invalid JSON)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["c", [3, 9], "a"]'</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON '[3,9]'</span>
</pre></div>
</div>
<p>If the element at the specified index doesn’t exist, the function returns null:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'[]'</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w">                </span><span class="c1">-- NULL</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["a", "b", "c"]'</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">);</span><span class="w">  </span><span class="c1">-- NULL</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_get</span><span class="p">(</span><span class="s1">'["c", "b", "a"]'</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">);</span><span class="w"> </span><span class="c1">-- NULL</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_array_length">
<span class="sig-name descname"><span class="pre">json_array_length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="json.html#json_array_length" title="Link to this definition">#</a></dt>
<dd><p>Returns the array length of <code class="docutils literal notranslate"><span class="pre">json</span></code> (a string containing a JSON array):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_array_length</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 3</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_extract">
<span class="sig-name descname"><span class="pre">json_extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">json_path</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">json</span></span></span><a class="headerlink" href="json.html#json_extract" title="Link to this definition">#</a></dt>
<dd><p>Evaluates the <a class="reference external" href="http://goessner.net/articles/JsonPath/">JSONPath</a>-like expression <code class="docutils literal notranslate"><span class="pre">json_path</span></code> on <code class="docutils literal notranslate"><span class="pre">json</span></code>
(a string containing JSON) and returns the result as a JSON string:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_extract</span><span class="p">(</span><span class="n">json</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.store.book'</span><span class="p">);</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_extract</span><span class="p">(</span><span class="n">json</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.store[book]'</span><span class="p">);</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_extract</span><span class="p">(</span><span class="n">json</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.store["book name"]'</span><span class="p">);</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="json.html#json-query"><span class="std std-ref">json_query function</span></a> provides a more powerful and
feature-rich alternative to parse and extract JSON data.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_extract_scalar">
<span class="sig-name descname"><span class="pre">json_extract_scalar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">json_path</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="json.html#json_extract_scalar" title="Link to this definition">#</a></dt>
<dd><p>Like <a class="reference internal" href="json.html#json_extract" title="json_extract"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_extract()</span></code></a>, but returns the result value as a string (as opposed
to being encoded as JSON). The value referenced by <code class="docutils literal notranslate"><span class="pre">json_path</span></code> must be a
scalar (boolean, number or string).</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_extract_scalar</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">,</span><span class="w"> </span><span class="s1">'$[2]'</span><span class="p">);</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_extract_scalar</span><span class="p">(</span><span class="n">json</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.store.book[0].author'</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_format">
<span class="sig-name descname"><span class="pre">json_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="json.html#json_format" title="Link to this definition">#</a></dt>
<dd><p>Returns the JSON text serialized from the input JSON value.
This is inverse function to <a class="reference internal" href="json.html#json_parse" title="json_parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_parse()</span></code></a>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- '[1,2,3]'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'"a"'</span><span class="p">);</span><span class="w">       </span><span class="c1">-- '"a"'</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="json.html#json_format" title="json_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_format()</span></code></a> and <code class="docutils literal notranslate"><span class="pre">CAST(json</span> <span class="pre">AS</span> <span class="pre">VARCHAR)</span></code> have completely
different semantics.</p>
<p><a class="reference internal" href="json.html#json_format" title="json_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_format()</span></code></a> serializes the input JSON value to JSON text conforming to
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7159.html"><strong>RFC 7159</strong></a>. The JSON value can be a JSON object, a JSON array, a JSON string,
a JSON number, <code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code> or <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'{"a": 1, "b": 2}'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- '{"a":1,"b":2}'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w">        </span><span class="c1">-- '[1,2,3]'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'"abc"'</span><span class="p">);</span><span class="w">            </span><span class="c1">-- '"abc"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'42'</span><span class="p">);</span><span class="w">               </span><span class="c1">-- '42'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'true'</span><span class="p">);</span><span class="w">             </span><span class="c1">-- 'true'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_format</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'null'</span><span class="p">);</span><span class="w">             </span><span class="c1">-- 'null'</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">CAST(json</span> <span class="pre">AS</span> <span class="pre">VARCHAR)</span></code> casts the JSON value to the corresponding SQL VARCHAR value.
For JSON string, JSON number, <code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code> or <code class="docutils literal notranslate"><span class="pre">null</span></code>, the cast
behavior is same as the corresponding SQL type. JSON object and JSON array
cannot be cast to VARCHAR.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'{"a": 1, "b": 2}'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w"> </span><span class="c1">-- ERROR!</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'[1, 2, 3]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w">        </span><span class="c1">-- ERROR!</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'"abc"'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w">            </span><span class="c1">-- 'abc' (the double quote is gone)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'42'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w">               </span><span class="c1">-- '42'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'true'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w">             </span><span class="c1">-- 'true'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">JSON</span><span class="w"> </span><span class="s1">'null'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span><span class="w">             </span><span class="c1">-- NULL</span>
</pre></div>
</div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_parse">
<span class="sig-name descname"><span class="pre">json_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">json</span></span></span><a class="headerlink" href="json.html#json_parse" title="Link to this definition">#</a></dt>
<dd><p>Returns the JSON value deserialized from the input JSON text.
This is inverse function to <a class="reference internal" href="json.html#json_format" title="json_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_format()</span></code></a>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w">   </span><span class="c1">-- JSON '[1,2,3]'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'"abc"'</span><span class="p">);</span><span class="w">       </span><span class="c1">-- JSON '"abc"'</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="json.html#json_parse" title="json_parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_parse()</span></code></a> and <code class="docutils literal notranslate"><span class="pre">CAST(string</span> <span class="pre">AS</span> <span class="pre">JSON)</span></code> have completely
different semantics.</p>
<p><a class="reference internal" href="json.html#json_parse" title="json_parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_parse()</span></code></a> expects a JSON text conforming to <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7159.html"><strong>RFC 7159</strong></a>, and returns
the JSON value deserialized from the JSON text.
The JSON value can be a JSON object, a JSON array, a JSON string, a JSON number,
<code class="docutils literal notranslate"><span class="pre">true</span></code>, <code class="docutils literal notranslate"><span class="pre">false</span></code> or <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'not_json'</span><span class="p">);</span><span class="w">         </span><span class="c1">-- ERROR!</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'["a": 1, "b": 2]'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON '["a": 1, "b": 2]'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="p">);</span><span class="w">        </span><span class="c1">-- JSON '[1,2,3]'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'"abc"'</span><span class="p">);</span><span class="w">            </span><span class="c1">-- JSON '"abc"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'42'</span><span class="p">);</span><span class="w">               </span><span class="c1">-- JSON '42'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'true'</span><span class="p">);</span><span class="w">             </span><span class="c1">-- JSON 'true'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_parse</span><span class="p">(</span><span class="s1">'null'</span><span class="p">);</span><span class="w">             </span><span class="c1">-- JSON 'null'</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">CAST(string</span> <span class="pre">AS</span> <span class="pre">JSON)</span></code> takes any VARCHAR value as input, and returns
a JSON string with its value set to input string.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'not_json'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">         </span><span class="c1">-- JSON '"not_json"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'["a": 1, "b": 2]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w"> </span><span class="c1">-- JSON '"[\"a\": 1, \"b\": 2]"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'[1, 2, 3]'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">        </span><span class="c1">-- JSON '"[1, 2, 3]"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'"abc"'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">            </span><span class="c1">-- JSON '"\"abc\""'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'42'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">               </span><span class="c1">-- JSON '"42"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'true'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">             </span><span class="c1">-- JSON '"true"'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'null'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">JSON</span><span class="p">);</span><span class="w">             </span><span class="c1">-- JSON '"null"'</span>
</pre></div>
</div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="json_size">
<span class="sig-name descname"><span class="pre">json_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">json_path</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="json.html#json_size" title="Link to this definition">#</a></dt>
<dd><p>Like <a class="reference internal" href="json.html#json_extract" title="json_extract"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_extract()</span></code></a>, but returns the size of the value.
For objects or arrays, the size is the number of members,
and the size of a scalar value is zero.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">json_size</span><span class="p">(</span><span class="s1">'{"x": {"a": 1, "b": 2}}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.x'</span><span class="p">);</span><span class="w">   </span><span class="c1">-- 2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_size</span><span class="p">(</span><span class="s1">'{"x": [1, 2, 3]}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.x'</span><span class="p">);</span><span class="w">          </span><span class="c1">-- 3</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">json_size</span><span class="p">(</span><span class="s1">'{"x": {"a": 1, "b": 2}}'</span><span class="p">,</span><span class="w"> </span><span class="s1">'$.x.a'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
</pre></div>
</div>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="ipaddress.html" title="IP Address Functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> IP Address Functions </span>
              </div>
            </a>
          
          
            <a href="lambda.html" title="Lambda expressions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Lambda expressions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>