<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Comparison functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="comparison.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Conditional expressions" href="conditional.html" />
    <link rel="prev" title="Color functions" href="color.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="comparison.html#functions/comparison" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Comparison functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Comparison </label>
    
      <a href="comparison.html#" class="md-nav__link md-nav__link--active">Comparison</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="comparison.html#comparison-operators" class="md-nav__link">Comparison operators</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#range-operator-between" class="md-nav__link">Range operator: BETWEEN</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#is-null-and-is-not-null" class="md-nav__link">IS NULL and IS NOT NULL</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#is-distinct-from-and-is-not-distinct-from" class="md-nav__link">IS DISTINCT FROM and IS NOT DISTINCT FROM</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#greatest-and-least" class="md-nav__link">GREATEST and LEAST</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="comparison.html#greatest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">greatest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#least" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">least()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="comparison.html#quantified-comparison-predicates-all-any-and-some" class="md-nav__link">Quantified comparison predicates: ALL, ANY and SOME</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#pattern-comparison-like" class="md-nav__link">Pattern comparison: LIKE</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#row-comparison-in" class="md-nav__link">Row comparison: IN</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#examples" class="md-nav__link">Examples</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="comparison.html#comparison-operators" class="md-nav__link">Comparison operators</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#range-operator-between" class="md-nav__link">Range operator: BETWEEN</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#is-null-and-is-not-null" class="md-nav__link">IS NULL and IS NOT NULL</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#is-distinct-from-and-is-not-distinct-from" class="md-nav__link">IS DISTINCT FROM and IS NOT DISTINCT FROM</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#greatest-and-least" class="md-nav__link">GREATEST and LEAST</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="comparison.html#greatest" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">greatest()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#least" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">least()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="comparison.html#quantified-comparison-predicates-all-any-and-some" class="md-nav__link">Quantified comparison predicates: ALL, ANY and SOME</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#pattern-comparison-like" class="md-nav__link">Pattern comparison: LIKE</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#row-comparison-in" class="md-nav__link">Row comparison: IN</a>
        </li>
        <li class="md-nav__item"><a href="comparison.html#examples" class="md-nav__link">Examples</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="comparison-functions-and-operators">
<h1 id="functions-comparison--page-root">Comparison functions and operators<a class="headerlink" href="comparison.html#functions-comparison--page-root" title="Link to this heading">#</a></h1>
<section id="comparison-operators">
<span id="id1"></span><h2 id="comparison-operators">Comparison operators<a class="headerlink" href="comparison.html#comparison-operators" title="Link to this heading">#</a></h2>
<table>
<colgroup>
<col style="width: 30%"/>
<col style="width: 70%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Operator</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&lt;</span></code></p></td>
<td><p>Less than</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&gt;</span></code></p></td>
<td><p>Greater than</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">&lt;=</span></code></p></td>
<td><p>Less than or equal to</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&gt;=</span></code></p></td>
<td><p>Greater than or equal to</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">=</span></code></p></td>
<td><p>Equal</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">&lt;&gt;</span></code></p></td>
<td><p>Not equal</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">!=</span></code></p></td>
<td><p>Not equal (non-standard but popular syntax)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="range-operator-between">
<span id="range-operator"></span><h2 id="range-operator-between">Range operator: BETWEEN<a class="headerlink" href="comparison.html#range-operator-between" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code> operator tests if a value is within a specified range. It uses the
syntax <code class="docutils literal notranslate"><span class="pre">value</span> <span class="pre">BETWEEN</span> <span class="pre">min</span> <span class="pre">AND</span> <span class="pre">max</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding statement is equivalent to the following statement:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span>
</pre></div>
</div>
<p>To test if a value does not fall within the specified range use <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">BETWEEN</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span>
</pre></div>
</div>
<p>The statement shown above is equivalent to the following statement:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span>
</pre></div>
</div>
<p>A <code class="docutils literal notranslate"><span class="pre">NULL</span></code> in a <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code> or <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">BETWEEN</span></code> statement is evaluated using the
standard <code class="docutils literal notranslate"><span class="pre">NULL</span></code> evaluation rules applied to the equivalent expression above:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">4</span><span class="p">;</span><span class="w"> </span><span class="c1">-- null</span>

<span class="k">SELECT</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span><span class="w"> </span><span class="c1">-- null</span>

<span class="k">SELECT</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span><span class="w"> </span><span class="c1">-- false</span>

<span class="k">SELECT</span><span class="w"> </span><span class="mi">8</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="mi">6</span><span class="p">;</span><span class="w"> </span><span class="c1">-- false</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code> and <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">BETWEEN</span></code> operators can also be used to evaluate any
orderable type. For example, a <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="s1">'Paul'</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="s1">'John'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="s1">'Ringo'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
<p>Note that the value, min, and max parameters to <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code> and <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">BETWEEN</span></code> must
be the same type. For example, Trino produces an error if you ask it if <code class="docutils literal notranslate"><span class="pre">John</span></code>
is between <code class="docutils literal notranslate"><span class="pre">2.3</span></code> and <code class="docutils literal notranslate"><span class="pre">35.2</span></code>.</p>
</section>
<section id="is-null-and-is-not-null">
<span id="is-null-operator"></span><h2 id="is-null-and-is-not-null">IS NULL and IS NOT NULL<a class="headerlink" href="comparison.html#is-null-and-is-not-null" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NULL</span></code> and <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">NULL</span></code> operators test whether a value is null
(undefined).  Both operators work for all data types.</p>
<p>Using <code class="docutils literal notranslate"><span class="pre">NULL</span></code> with <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NULL</span></code> evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
<p>But any other constant does not:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="mi">3</span><span class="p">.</span><span class="mi">0</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span><span class="w"> </span><span class="c1">-- false</span>
</pre></div>
</div>
</section>
<section id="is-distinct-from-and-is-not-distinct-from">
<span id="is-distinct-operator"></span><h2 id="is-distinct-from-and-is-not-distinct-from">IS DISTINCT FROM and IS NOT DISTINCT FROM<a class="headerlink" href="comparison.html#is-distinct-from-and-is-not-distinct-from" title="Link to this heading">#</a></h2>
<p>In SQL a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value signifies an unknown value, so any comparison involving a
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> produces <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  The  <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code> and <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code>
operators treat <code class="docutils literal notranslate"><span class="pre">NULL</span></code> as a known value and both operators guarantee either a
true or false outcome even in the presence of <code class="docutils literal notranslate"><span class="pre">NULL</span></code> input:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">DISTINCT</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span><span class="w"> </span><span class="c1">-- false</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">DISTINCT</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
<p>In the preceding example a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value is not considered distinct from <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
When you are comparing values which may include <code class="docutils literal notranslate"><span class="pre">NULL</span></code> use these operators to
guarantee either a <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> or <code class="docutils literal notranslate"><span class="pre">FALSE</span></code> result.</p>
<p>The following truth table demonstrate the handling of <code class="docutils literal notranslate"><span class="pre">NULL</span></code> in
<code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code> and <code class="docutils literal notranslate"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">DISTINCT</span> <span class="pre">FROM</span></code>:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>a</p></th>
<th class="head"><p>b</p></th>
<th class="head"><p>a = b</p></th>
<th class="head"><p>a &lt;&gt; b</p></th>
<th class="head"><p>a DISTINCT b</p></th>
<th class="head"><p>a NOT DISTINCT b</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FALSE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TRUE</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="greatest-and-least">
<h2 id="greatest-and-least">GREATEST and LEAST<a class="headerlink" href="comparison.html#greatest-and-least" title="Link to this heading">#</a></h2>
<p>These functions are not in the SQL standard, but are a common extension.
Like most other functions in Trino, they return null if any argument is
null. Note that in some other databases, such as PostgreSQL, they only
return null if all arguments are null.</p>
<p>The following types are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></li>
</ul>
<dl class="py function">
<dt class="sig sig-object py" id="greatest">
<span class="sig-name descname"><span class="pre">greatest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">valueN</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="comparison.html#greatest" title="Link to this definition">#</a></dt>
<dd><p>Returns the largest of the provided values.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="least">
<span class="sig-name descname"><span class="pre">least</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">valueN</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="comparison.html#least" title="Link to this definition">#</a></dt>
<dd><p>Returns the smallest of the provided values.</p>
</dd></dl>
</section>
<section id="quantified-comparison-predicates-all-any-and-some">
<span id="quantified-comparison-predicates"></span><h2 id="quantified-comparison-predicates-all-any-and-some">Quantified comparison predicates: ALL, ANY and SOME<a class="headerlink" href="comparison.html#quantified-comparison-predicates-all-any-and-some" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">ALL</span></code>, <code class="docutils literal notranslate"><span class="pre">ANY</span></code> and <code class="docutils literal notranslate"><span class="pre">SOME</span></code> quantifiers can be used together with comparison
operators in the following way:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>expression operator quantifier ( subquery )
</pre></div>
</div>
<p>For example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="s1">'hello'</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">ANY</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="s1">'hello'</span><span class="p">,</span><span class="w"> </span><span class="s1">'world'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- true</span>

<span class="k">SELECT</span><span class="w"> </span><span class="mi">21</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="k">ALL</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="mi">19</span><span class="p">,</span><span class="w"> </span><span class="mi">20</span><span class="p">,</span><span class="w"> </span><span class="mi">21</span><span class="p">);</span><span class="w"> </span><span class="c1">-- false</span>

<span class="k">SELECT</span><span class="w"> </span><span class="mi">42</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="k">SOME</span><span class="w"> </span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="mi">41</span><span class="w"> </span><span class="k">UNION</span><span class="w"> </span><span class="k">ALL</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="mi">42</span><span class="w"> </span><span class="k">UNION</span><span class="w"> </span><span class="k">ALL</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="mi">43</span><span class="p">);</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
<p>Following are the meanings of some quantifier and comparison operator
combinations:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Expression</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">=</span> <span class="pre">ALL</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> is equal to all values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">&lt;&gt;</span> <span class="pre">ALL</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> doesn’t match any value.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">&lt;</span> <span class="pre">ALL</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> is smaller than the smallest value.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">=</span> <span class="pre">ANY</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> is equal to any of the values. This form
is equivalent to <code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">IN</span> <span class="pre">(...)</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">&lt;&gt;</span> <span class="pre">ANY</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> doesn’t match one or more values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">&lt;</span> <span class="pre">ANY</span> <span class="pre">(...)</span></code></p></td>
<td><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true</span></code> when <code class="docutils literal notranslate"><span class="pre">A</span></code> is smaller than the biggest value.</p></td>
</tr>
</tbody>
</table>
<p><code class="docutils literal notranslate"><span class="pre">ANY</span></code> and <code class="docutils literal notranslate"><span class="pre">SOME</span></code> have the same meaning and can be used interchangeably.</p>
</section>
<section id="pattern-comparison-like">
<span id="like-operator"></span><h2 id="pattern-comparison-like">Pattern comparison: LIKE<a class="headerlink" href="comparison.html#pattern-comparison-like" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">LIKE</span></code> operator can be used to compare values with a pattern:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="p">...</span><span class="w"> </span><span class="k">column</span><span class="w"> </span><span class="p">[</span><span class="k">NOT</span><span class="p">]</span><span class="w"> </span><span class="k">LIKE</span><span class="w"> </span><span class="s1">'pattern'</span><span class="w"> </span><span class="k">ESCAPE</span><span class="w"> </span><span class="s1">'character'</span><span class="p">;</span>
</pre></div>
</div>
<p>Matching characters is case sensitive, and the pattern supports two symbols for
matching:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">_</span></code> matches any single character</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">%</span></code> matches zero or more characters</p></li>
</ul>
<p>Typically it is often used as a condition in <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> statements. An example is
a query to find all continents starting with <code class="docutils literal notranslate"><span class="pre">E</span></code>, which returns <code class="docutils literal notranslate"><span class="pre">Europe</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="s1">'America'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Asia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Africa'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Europe'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Australia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Antarctica'</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="p">(</span><span class="n">continent</span><span class="p">)</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">continent</span><span class="w"> </span><span class="k">LIKE</span><span class="w"> </span><span class="s1">'E%'</span><span class="p">;</span>
</pre></div>
</div>
<p>You can negate the result by adding <code class="docutils literal notranslate"><span class="pre">NOT</span></code>, and get all other continents, all
not starting with <code class="docutils literal notranslate"><span class="pre">E</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="s1">'America'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Asia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Africa'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Europe'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Australia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Antarctica'</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="p">(</span><span class="n">continent</span><span class="p">)</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">continent</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">LIKE</span><span class="w"> </span><span class="s1">'E%'</span><span class="p">;</span>
</pre></div>
</div>
<p>If you only have one specific character to match, you can use the <code class="docutils literal notranslate"><span class="pre">_</span></code> symbol
for each character. The following query uses two underscores and produces only
<code class="docutils literal notranslate"><span class="pre">Asia</span></code> as result:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">VALUES</span><span class="w"> </span><span class="s1">'America'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Asia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Africa'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Europe'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Australia'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Antarctica'</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="p">(</span><span class="n">continent</span><span class="p">)</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">continent</span><span class="w"> </span><span class="k">LIKE</span><span class="w"> </span><span class="s1">'A__A'</span><span class="p">;</span>
</pre></div>
</div>
<p>The wildcard characters <code class="docutils literal notranslate"><span class="pre">_</span></code> and <code class="docutils literal notranslate"><span class="pre">%</span></code> must be escaped to allow you to match
them as literals. This can be achieved by specifying the <code class="docutils literal notranslate"><span class="pre">ESCAPE</span></code> character to
use:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="s1">'South_America'</span><span class="w"> </span><span class="k">LIKE</span><span class="w"> </span><span class="s1">'South\_America'</span><span class="w"> </span><span class="k">ESCAPE</span><span class="w"> </span><span class="s1">'\'</span><span class="p">;</span>
</pre></div>
</div>
<p>The above query returns <code class="docutils literal notranslate"><span class="pre">true</span></code> since the escaped underscore symbol matches. If
you need to match the used escape character as well, you can escape it.</p>
<p>If you want to match for the chosen escape character, you simply escape itself.
For example, you can use <code class="docutils literal notranslate"><span class="pre">\\</span></code> to match for <code class="docutils literal notranslate"><span class="pre">\</span></code>.</p>
</section>
<section id="row-comparison-in">
<span id="in-operator"></span><h2 id="row-comparison-in">Row comparison: IN<a class="headerlink" href="comparison.html#row-comparison-in" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">IN</span></code> operator can be used in a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause to compare column values with
a list of values. The list of values can be supplied by a subquery or directly
as static values in an array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="p">...</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="k">column</span><span class="w"> </span><span class="p">[</span><span class="k">NOT</span><span class="p">]</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'value1'</span><span class="p">,</span><span class="s1">'value2'</span><span class="p">);</span>
<span class="p">...</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="k">column</span><span class="w"> </span><span class="p">[</span><span class="k">NOT</span><span class="p">]</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="n">subquery</span><span class="w"> </span><span class="p">);</span>
</pre></div>
</div>
<p>Use the optional <code class="docutils literal notranslate"><span class="pre">NOT</span></code> keyword to negate the condition.</p>
<p>The following example shows a simple usage with a static array:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">region</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'AMERICA'</span><span class="p">,</span><span class="w"> </span><span class="s1">'EUROPE'</span><span class="p">);</span>
</pre></div>
</div>
<p>The values in the clause are used for multiple comparisons that are combined as
a logical <code class="docutils literal notranslate"><span class="pre">OR</span></code>. The preceding query is equivalent to the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">region</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'AMERICA'</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'EUROPE'</span><span class="p">;</span>
</pre></div>
</div>
<p>You can negate the comparisons by adding <code class="docutils literal notranslate"><span class="pre">NOT</span></code>, and get all other regions
except the values in list:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">region</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'AMERICA'</span><span class="p">,</span><span class="w"> </span><span class="s1">'EUROPE'</span><span class="p">);</span>
</pre></div>
</div>
<p>When using a subquery to determine the values to use in the comparison, the
subquery must return a single column and one or more rows. For example, the
following query returns nation name of countries in regions starting with the
letter <code class="docutils literal notranslate"><span class="pre">A</span></code>, specifically Africa, America, and Asia:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">nation</span><span class="p">.</span><span class="n">name</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">nation</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">regionkey</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span>
<span class="w">  </span><span class="k">SELECT</span><span class="w"> </span><span class="n">regionkey</span>
<span class="w">  </span><span class="k">FROM</span><span class="w"> </span><span class="n">region</span>
<span class="w">  </span><span class="k">WHERE</span><span class="w"> </span><span class="n">starts_with</span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="s1">'A'</span><span class="p">)</span>
<span class="p">)</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">by</span><span class="w"> </span><span class="n">nation</span><span class="p">.</span><span class="n">name</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="examples">
<h2 id="examples">Examples<a class="headerlink" href="comparison.html#examples" title="Link to this heading">#</a></h2>
<p>The following example queries showcase aspects of using comparison functions and
operators related to implied ordering of values, implicit casting, and different
types.</p>
<p>Ordering:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="s1">'M'</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="s1">'Z'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="s1">'B'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="s1">'a'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">TRUE</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="k">FALSE</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'M'</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="s1">'Z'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="s1">'m'</span><span class="w"> </span><span class="k">BETWEEN</span><span class="w"> </span><span class="s1">'A'</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="s1">'Z'</span><span class="p">;</span><span class="w"> </span><span class="c1">-- false</span>
</pre></div>
</div>
<p>The following queries show a subtle difference between <code class="docutils literal notranslate"><span class="pre">char</span></code> and <code class="docutils literal notranslate"><span class="pre">varchar</span></code>
types. The length parameter for <code class="docutils literal notranslate"><span class="pre">varchar</span></code> is an optional maximum length
parameter and comparison is based on the data only, ignoring the length:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">25</span><span class="p">));</span><span class="w"> </span><span class="c1">--true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test   '</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">varchar</span><span class="p">(</span><span class="mi">25</span><span class="p">));</span><span class="w"> </span><span class="c1">--false</span>
</pre></div>
</div>
<p>The length parameter for <code class="docutils literal notranslate"><span class="pre">char</span></code> defines a fixed length character array.
Comparison with different length automatically includes a cast to the same
larger length. The cast is performed as automatic padding with spaces, and
therefore both queries in the following return <code class="docutils literal notranslate"><span class="pre">true</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">char</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">char</span><span class="p">(</span><span class="mi">25</span><span class="p">));</span><span class="w"> </span><span class="c1">-- true</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">char</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'Test   '</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">char</span><span class="p">(</span><span class="mi">25</span><span class="p">));</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
<p>The following queries show how date types are ordered, and how date is
implicitly cast to timestamp with zero time values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2024-08-22'</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2024-08-31'</span><span class="p">;</span>
<span class="k">SELECT</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2024-08-22'</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2024-08-22 8:00:00'</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="color.html" title="Color functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Color functions </span>
              </div>
            </a>
          
          
            <a href="conditional.html" title="Conditional expressions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Conditional expressions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>