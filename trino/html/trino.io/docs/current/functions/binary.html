<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Binary functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="binary.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Bitwise functions" href="bitwise.html" />
    <link rel="prev" title="Array functions and operators" href="array.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="binary.html#functions/binary" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Binary functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Binary </label>
    
      <a href="binary.html#" class="md-nav__link md-nav__link--active">Binary</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="binary.html#binary-operators" class="md-nav__link">Binary operators</a>
        </li>
        <li class="md-nav__item"><a href="binary.html#binary-functions" class="md-nav__link">Binary functions</a>
        </li>
        <li class="md-nav__item"><a href="binary.html#base64-encoding-functions" class="md-nav__link">Base64 encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_base64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_base64url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base64url()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base64url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base64url()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_base32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base32()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hex-encoding-functions" class="md-nav__link">Hex encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_hex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_hex()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_hex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_hex()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#integer-encoding-functions" class="md-nav__link">Integer encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_big_endian_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_big_endian_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_big_endian_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_big_endian_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_big_endian_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_big_endian_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_big_endian_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_big_endian_64()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#floating-point-encoding-functions" class="md-nav__link">Floating-point encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_ieee754_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_ieee754_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_ieee754_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_ieee754_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_ieee754_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_ieee754_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_ieee754_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_ieee754_64()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hashing-functions" class="md-nav__link">Hashing functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#crc32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">crc32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#md5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">md5()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha1()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha256" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha256()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha512" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha512()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#spooky_hash_v2_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">spooky_hash_v2_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#spooky_hash_v2_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">spooky_hash_v2_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#xxhash64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">xxhash64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#murmur3" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">murmur3()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac-functions" class="md-nav__link">HMAC functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#hmac_md5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_md5()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha1()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha256" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha256()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha512" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha512()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="binary.html#binary-operators" class="md-nav__link">Binary operators</a>
        </li>
        <li class="md-nav__item"><a href="binary.html#binary-functions" class="md-nav__link">Binary functions</a>
        </li>
        <li class="md-nav__item"><a href="binary.html#base64-encoding-functions" class="md-nav__link">Base64 encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_base64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_base64url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base64url()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base64url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base64url()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_base32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_base32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base32()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hex-encoding-functions" class="md-nav__link">Hex encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_hex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_hex()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_hex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_hex()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#integer-encoding-functions" class="md-nav__link">Integer encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_big_endian_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_big_endian_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_big_endian_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_big_endian_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_big_endian_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_big_endian_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_big_endian_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_big_endian_64()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#floating-point-encoding-functions" class="md-nav__link">Floating-point encoding functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#from_ieee754_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_ieee754_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_ieee754_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_ieee754_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#from_ieee754_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_ieee754_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#to_ieee754_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_ieee754_64()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hashing-functions" class="md-nav__link">Hashing functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#crc32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">crc32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#md5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">md5()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha1()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha256" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha256()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#sha512" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sha512()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#spooky_hash_v2_32" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">spooky_hash_v2_32()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#spooky_hash_v2_64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">spooky_hash_v2_64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#xxhash64" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">xxhash64()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#murmur3" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">murmur3()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac-functions" class="md-nav__link">HMAC functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="binary.html#hmac_md5" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_md5()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha1()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha256" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha256()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="binary.html#hmac_sha512" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hmac_sha512()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="binary-functions-and-operators">
<h1 id="functions-binary--page-root">Binary functions and operators<a class="headerlink" href="binary.html#functions-binary--page-root" title="Link to this heading">#</a></h1>
<section id="binary-operators">
<h2 id="binary-operators">Binary operators<a class="headerlink" href="binary.html#binary-operators" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">||</span></code> operator performs concatenation.</p>
</section>
<section id="binary-functions">
<h2 id="binary-functions">Binary functions<a class="headerlink" href="binary.html#binary-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">concat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">binaryN</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Returns the concatenation of <code class="docutils literal notranslate"><span class="pre">binary1</span></code>, <code class="docutils literal notranslate"><span class="pre">binary2</span></code>, <code class="docutils literal notranslate"><span class="pre">...</span></code>, <code class="docutils literal notranslate"><span class="pre">binaryN</span></code>.
This function provides the same functionality as the
SQL-standard concatenation operator (<code class="docutils literal notranslate"><span class="pre">||</span></code>).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span></dt>
<dd><p>Returns the length of <code class="docutils literal notranslate"><span class="pre">binary</span></code> in bytes.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">lpad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padbinary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Left pads <code class="docutils literal notranslate"><span class="pre">binary</span></code> to <code class="docutils literal notranslate"><span class="pre">size</span></code> bytes with <code class="docutils literal notranslate"><span class="pre">padbinary</span></code>.
If <code class="docutils literal notranslate"><span class="pre">size</span></code> is less than the length of <code class="docutils literal notranslate"><span class="pre">binary</span></code>, the result is
truncated to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters. <code class="docutils literal notranslate"><span class="pre">size</span></code> must not be negative
and <code class="docutils literal notranslate"><span class="pre">padbinary</span></code> must be non-empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">rpad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padbinary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Right pads <code class="docutils literal notranslate"><span class="pre">binary</span></code> to <code class="docutils literal notranslate"><span class="pre">size</span></code> bytes with <code class="docutils literal notranslate"><span class="pre">padbinary</span></code>.
If <code class="docutils literal notranslate"><span class="pre">size</span></code> is less than the length of <code class="docutils literal notranslate"><span class="pre">binary</span></code>, the result is
truncated to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters. <code class="docutils literal notranslate"><span class="pre">size</span></code> must not be negative
and <code class="docutils literal notranslate"><span class="pre">padbinary</span></code> must be non-empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">substr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Returns the rest of <code class="docutils literal notranslate"><span class="pre">binary</span></code> from the starting position <code class="docutils literal notranslate"><span class="pre">start</span></code>,
measured in bytes. Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. A negative starting position
is interpreted as being relative to the end of the string.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">substr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Returns a substring from <code class="docutils literal notranslate"><span class="pre">binary</span></code> of length <code class="docutils literal notranslate"><span class="pre">length</span></code> from the starting
position <code class="docutils literal notranslate"><span class="pre">start</span></code>, measured in bytes. Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. A
negative starting position is interpreted as being relative to the end of
the string.</p>
</dd></dl>
<dl class="py function" id="function-reverse-varbinary">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">reverse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">binary</span></code> with the bytes in reverse order.</p>
</dd></dl>
</section>
<section id="base64-encoding-functions">
<h2 id="base64-encoding-functions">Base64 encoding functions<a class="headerlink" href="binary.html#base64-encoding-functions" title="Link to this heading">#</a></h2>
<p>The Base64 functions implement the encoding specified in <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4648.html"><strong>RFC 4648</strong></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="from_base64">
<span class="sig-name descname"><span class="pre">from_base64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#from_base64" title="Link to this definition">#</a></dt>
<dd><p>Decodes binary data from the base64 encoded <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_base64">
<span class="sig-name descname"><span class="pre">to_base64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="binary.html#to_base64" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">binary</span></code> into a base64 string representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_base64url">
<span class="sig-name descname"><span class="pre">from_base64url</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#from_base64url" title="Link to this definition">#</a></dt>
<dd><p>Decodes binary data from the base64 encoded <code class="docutils literal notranslate"><span class="pre">string</span></code> using the URL safe alphabet.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_base64url">
<span class="sig-name descname"><span class="pre">to_base64url</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="binary.html#to_base64url" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">binary</span></code> into a base64 string representation using the URL safe alphabet.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_base32">
<span class="sig-name descname"><span class="pre">from_base32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#from_base32" title="Link to this definition">#</a></dt>
<dd><p>Decodes binary data from the base32 encoded <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_base32">
<span class="sig-name descname"><span class="pre">to_base32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="binary.html#to_base32" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">binary</span></code> into a base32 string representation.</p>
</dd></dl>
</section>
<section id="hex-encoding-functions">
<h2 id="hex-encoding-functions">Hex encoding functions<a class="headerlink" href="binary.html#hex-encoding-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="from_hex">
<span class="sig-name descname"><span class="pre">from_hex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#from_hex" title="Link to this definition">#</a></dt>
<dd><p>Decodes binary data from the hex encoded <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_hex">
<span class="sig-name descname"><span class="pre">to_hex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="binary.html#to_hex" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">binary</span></code> into a hex string representation.</p>
</dd></dl>
</section>
<section id="integer-encoding-functions">
<h2 id="integer-encoding-functions">Integer encoding functions<a class="headerlink" href="binary.html#integer-encoding-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="from_big_endian_32">
<span class="sig-name descname"><span class="pre">from_big_endian_32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">integer</span></span></span><a class="headerlink" href="binary.html#from_big_endian_32" title="Link to this definition">#</a></dt>
<dd><p>Decodes the 32-bit two’s complement big-endian <code class="docutils literal notranslate"><span class="pre">binary</span></code>.
The input must be exactly 4 bytes.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_big_endian_32">
<span class="sig-name descname"><span class="pre">to_big_endian_32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integer</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#to_big_endian_32" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">integer</span></code> into a 32-bit two’s complement big-endian format.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_big_endian_64">
<span class="sig-name descname"><span class="pre">from_big_endian_64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="binary.html#from_big_endian_64" title="Link to this definition">#</a></dt>
<dd><p>Decodes the 64-bit two’s complement big-endian <code class="docutils literal notranslate"><span class="pre">binary</span></code>.
The input must be exactly 8 bytes.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_big_endian_64">
<span class="sig-name descname"><span class="pre">to_big_endian_64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bigint</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#to_big_endian_64" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">bigint</span></code> into a 64-bit two’s complement big-endian format.</p>
</dd></dl>
</section>
<section id="floating-point-encoding-functions">
<h2 id="floating-point-encoding-functions">Floating-point encoding functions<a class="headerlink" href="binary.html#floating-point-encoding-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="from_ieee754_32">
<span class="sig-name descname"><span class="pre">from_ieee754_32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">real</span></span></span><a class="headerlink" href="binary.html#from_ieee754_32" title="Link to this definition">#</a></dt>
<dd><p>Decodes the 32-bit big-endian <code class="docutils literal notranslate"><span class="pre">binary</span></code> in IEEE 754 single-precision floating-point format.
The input must be exactly 4 bytes.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_ieee754_32">
<span class="sig-name descname"><span class="pre">to_ieee754_32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">real</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#to_ieee754_32" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">real</span></code> into a 32-bit big-endian binary according to IEEE 754 single-precision floating-point format.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_ieee754_64">
<span class="sig-name descname"><span class="pre">from_ieee754_64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="binary.html#from_ieee754_64" title="Link to this definition">#</a></dt>
<dd><p>Decodes the 64-bit big-endian <code class="docutils literal notranslate"><span class="pre">binary</span></code> in IEEE 754 double-precision floating-point format.
The input must be exactly 8 bytes.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_ieee754_64">
<span class="sig-name descname"><span class="pre">to_ieee754_64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">double</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#to_ieee754_64" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">double</span></code> into a 64-bit big-endian binary according to IEEE 754 double-precision floating-point format.</p>
</dd></dl>
</section>
<section id="hashing-functions">
<h2 id="hashing-functions">Hashing functions<a class="headerlink" href="binary.html#hashing-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="crc32">
<span class="sig-name descname"><span class="pre">crc32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="binary.html#crc32" title="Link to this definition">#</a></dt>
<dd><p>Computes the CRC-32 of <code class="docutils literal notranslate"><span class="pre">binary</span></code>. For general purpose hashing, use
<a class="reference internal" href="binary.html#xxhash64" title="xxhash64"><code class="xref py py-func docutils literal notranslate"><span class="pre">xxhash64()</span></code></a>, as it is much faster and produces a better quality hash.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="md5">
<span class="sig-name descname"><span class="pre">md5</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#md5" title="Link to this definition">#</a></dt>
<dd><p>Computes the MD5 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sha1">
<span class="sig-name descname"><span class="pre">sha1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#sha1" title="Link to this definition">#</a></dt>
<dd><p>Computes the SHA1 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sha256">
<span class="sig-name descname"><span class="pre">sha256</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#sha256" title="Link to this definition">#</a></dt>
<dd><p>Computes the SHA256 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sha512">
<span class="sig-name descname"><span class="pre">sha512</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#sha512" title="Link to this definition">#</a></dt>
<dd><p>Computes the SHA512 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="spooky_hash_v2_32">
<span class="sig-name descname"><span class="pre">spooky_hash_v2_32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#spooky_hash_v2_32" title="Link to this definition">#</a></dt>
<dd><p>Computes the 32-bit SpookyHashV2 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="spooky_hash_v2_64">
<span class="sig-name descname"><span class="pre">spooky_hash_v2_64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#spooky_hash_v2_64" title="Link to this definition">#</a></dt>
<dd><p>Computes the 64-bit SpookyHashV2 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="xxhash64">
<span class="sig-name descname"><span class="pre">xxhash64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#xxhash64" title="Link to this definition">#</a></dt>
<dd><p>Computes the xxHash64 hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="murmur3">
<span class="sig-name descname"><span class="pre">murmur3</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#murmur3" title="Link to this definition">#</a></dt>
<dd><p>Computes the 128-bit <a class="reference external" href="https://wikipedia.org/wiki/MurmurHash">MurmurHash3</a>
hash of <code class="docutils literal notranslate"><span class="pre">binary</span></code>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">murmur3</span><span class="p">(</span><span class="n">from_base64</span><span class="p">(</span><span class="s1">'aaaaaa'</span><span class="p">));</span>
<span class="c1">-- ba 58 55 63 55 69 b4 2f 49 20 37 2c a0 e3 96 ef</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="hmac-functions">
<h2 id="hmac-functions">HMAC functions<a class="headerlink" href="binary.html#hmac-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="hmac_md5">
<span class="sig-name descname"><span class="pre">hmac_md5</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#hmac_md5" title="Link to this definition">#</a></dt>
<dd><p>Computes HMAC with MD5 of <code class="docutils literal notranslate"><span class="pre">binary</span></code> with the given <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hmac_sha1">
<span class="sig-name descname"><span class="pre">hmac_sha1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#hmac_sha1" title="Link to this definition">#</a></dt>
<dd><p>Computes HMAC with SHA1 of <code class="docutils literal notranslate"><span class="pre">binary</span></code> with the given <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hmac_sha256">
<span class="sig-name descname"><span class="pre">hmac_sha256</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#hmac_sha256" title="Link to this definition">#</a></dt>
<dd><p>Computes HMAC with SHA256 of <code class="docutils literal notranslate"><span class="pre">binary</span></code> with the given <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hmac_sha512">
<span class="sig-name descname"><span class="pre">hmac_sha512</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="binary.html#hmac_sha512" title="Link to this definition">#</a></dt>
<dd><p>Computes HMAC with SHA512 of <code class="docutils literal notranslate"><span class="pre">binary</span></code> with the given <code class="docutils literal notranslate"><span class="pre">key</span></code>.</p>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="array.html" title="Array functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Array functions and operators </span>
              </div>
            </a>
          
          
            <a href="bitwise.html" title="Bitwise functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Bitwise functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>