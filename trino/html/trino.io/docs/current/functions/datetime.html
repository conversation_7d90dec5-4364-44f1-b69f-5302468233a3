<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Date and time functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="datetime.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Decimal functions and operators" href="decimal.html" />
    <link rel="prev" title="Conversion functions" href="conversion.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="datetime.html#functions/datetime" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Date and time functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Date and time </label>
    
      <a href="datetime.html#" class="md-nav__link md-nav__link--active">Date and time</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="datetime.html#date-and-time-operators" class="md-nav__link">Date and time operators</a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#time-zone-conversion" class="md-nav__link">Time zone conversion</a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date-and-time-functions" class="md-nav__link">Date and time functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#current_date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_date</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_timestamp</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#last_day_of_month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">last_day_of_month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_timestamp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_timestamp_nanos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_timestamp_nanos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_date()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#at_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">at_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#with_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">with_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_unixtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_unixtime()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_unixtime_nanos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_unixtime_nanos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#localtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">localtime</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#localtimestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">localtimestamp</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#now" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">now()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_iso8601" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_iso8601()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_milliseconds" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_milliseconds()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_unixtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_unixtime()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#truncation-function" class="md-nav__link">Truncation function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_trunc" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_trunc()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#interval-functions" class="md-nav__link">Interval functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_add" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_add()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date_diff" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_diff()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#duration-function" class="md-nav__link">Duration function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#parse_duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">parse_duration()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#human_readable_seconds" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">human_readable_seconds()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#mysql-date-functions" class="md-nav__link">MySQL date functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_format" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_format()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date_parse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_parse()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#java-date-functions" class="md-nav__link">Java date functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#format_datetime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">format_datetime()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#parse_datetime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">parse_datetime()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#extraction-function" class="md-nav__link">Extraction function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">extract()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#convenience-extraction-functions" class="md-nav__link">Convenience extraction functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#day" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#dow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">dow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#doy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">doy()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#hour" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hour()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#millisecond" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">millisecond()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#minute" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">minute()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#quarter" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">quarter()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#second" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">second()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone_hour" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone_hour()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone_minute" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone_minute()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#week_of_year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">week_of_year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#year_of_week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">year_of_week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#yow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">yow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="datetime.html#date-and-time-operators" class="md-nav__link">Date and time operators</a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#time-zone-conversion" class="md-nav__link">Time zone conversion</a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date-and-time-functions" class="md-nav__link">Date and time functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#current_date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_date</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_timestamp</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#current_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">current_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#last_day_of_month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">last_day_of_month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_timestamp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_timestamp_nanos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_timestamp_nanos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_iso8601_date" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_iso8601_date()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#at_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">at_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#with_timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">with_timezone()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_unixtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_unixtime()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#from_unixtime_nanos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_unixtime_nanos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#localtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">localtime</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#localtimestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">localtimestamp</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#now" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">now()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_iso8601" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_iso8601()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_milliseconds" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_milliseconds()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#to_unixtime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_unixtime()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#truncation-function" class="md-nav__link">Truncation function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_trunc" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_trunc()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#interval-functions" class="md-nav__link">Interval functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_add" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_add()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date_diff" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_diff()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#duration-function" class="md-nav__link">Duration function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#parse_duration" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">parse_duration()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#human_readable_seconds" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">human_readable_seconds()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#mysql-date-functions" class="md-nav__link">MySQL date functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#date_format" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_format()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#date_parse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">date_parse()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#java-date-functions" class="md-nav__link">Java date functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#format_datetime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">format_datetime()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#parse_datetime" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">parse_datetime()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#extraction-function" class="md-nav__link">Extraction function</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">extract()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="datetime.html#convenience-extraction-functions" class="md-nav__link">Convenience extraction functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="datetime.html#day" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#day_of_year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">day_of_year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#dow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">dow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#doy" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">doy()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#hour" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hour()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#millisecond" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">millisecond()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#minute" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">minute()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#month" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">month()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#quarter" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">quarter()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#second" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">second()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone_hour" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone_hour()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone_minute" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone_minute()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#week_of_year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">week_of_year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#year" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">year()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#year_of_week" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">year_of_week()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#yow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">yow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="datetime.html#timezone" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timezone()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="date-and-time-functions-and-operators">
<h1 id="functions-datetime--page-root">Date and time functions and operators<a class="headerlink" href="datetime.html#functions-datetime--page-root" title="Link to this heading">#</a></h1>
<p>These functions and operators operate on <a class="reference internal" href="../language/types.html#date-time-data-types"><span class="std std-ref">date and time data types</span></a>.</p>
<section id="date-and-time-operators">
<h2 id="date-and-time-operators">Date and time operators<a class="headerlink" href="datetime.html#date-and-time-operators" title="Link to this heading">#</a></h2>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Operator</p></th>
<th class="head"><p>Example</p></th>
<th class="head"><p>Result</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">date</span> <span class="pre">'2012-08-08'</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'2'</span> <span class="pre">day</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-08-10</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">time</span> <span class="pre">'01:00'</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">04:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span> <span class="pre">'2012-08-08</span> <span class="pre">01:00'</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'29'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-08-09</span> <span class="pre">06:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span> <span class="pre">'2012-10-31</span> <span class="pre">01:00'</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'1'</span> <span class="pre">month</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-11-30</span> <span class="pre">01:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">interval</span> <span class="pre">'2'</span> <span class="pre">day</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2</span> <span class="pre">03:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">year</span> <span class="pre">+</span> <span class="pre">interval</span> <span class="pre">'5'</span> <span class="pre">month</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">3-5</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">date</span> <span class="pre">'2012-08-08'</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'2'</span> <span class="pre">day</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-08-06</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">time</span> <span class="pre">'01:00'</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">22:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span> <span class="pre">'2012-08-08</span> <span class="pre">01:00'</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'29'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-08-06</span> <span class="pre">20:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span> <span class="pre">'2012-10-31</span> <span class="pre">01:00'</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'1'</span> <span class="pre">month</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2012-09-30</span> <span class="pre">01:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">interval</span> <span class="pre">'2'</span> <span class="pre">day</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1</span> <span class="pre">21:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">interval</span> <span class="pre">'3'</span> <span class="pre">year</span> <span class="pre">-</span> <span class="pre">interval</span> <span class="pre">'5'</span> <span class="pre">month</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2-7</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="time-zone-conversion">
<span id="at-time-zone-operator"></span><h2 id="time-zone-conversion">Time zone conversion<a class="headerlink" href="datetime.html#time-zone-conversion" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">AT</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> operator sets the time zone of a timestamp:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">timestamp</span><span class="w"> </span><span class="s1">'2012-10-31 01:00 UTC'</span><span class="p">;</span>
<span class="c1">-- 2012-10-31 01:00:00.000 UTC</span>

<span class="k">SELECT</span><span class="w"> </span><span class="k">timestamp</span><span class="w"> </span><span class="s1">'2012-10-31 01:00 UTC'</span><span class="w"> </span><span class="k">AT</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="s1">'America/Los_Angeles'</span><span class="p">;</span>
<span class="c1">-- 2012-10-30 18:00:00.000 America/Los_Angeles</span>
</pre></div>
</div>
</section>
<section id="date-and-time-functions">
<h2 id="date-and-time-functions">Date and time functions<a class="headerlink" href="datetime.html#date-and-time-functions" title="Link to this heading">#</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="current_date">
<span class="sig-name descname"><span class="pre">current_date</span></span><a class="headerlink" href="datetime.html#current_date" title="Link to this definition">#</a></dt>
<dd><p>Returns the current date as of the start of the query.</p>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py" id="current_time">
<span class="sig-name descname"><span class="pre">current_time</span></span><a class="headerlink" href="datetime.html#current_time" title="Link to this definition">#</a></dt>
<dd><p>Returns the current time with time zone as of the start of the query.</p>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py" id="current_timestamp">
<span class="sig-name descname"><span class="pre">current_timestamp</span></span><a class="headerlink" href="datetime.html#current_timestamp" title="Link to this definition">#</a></dt>
<dd><p>Returns the current timestamp with time zone as of the start of the query,
with <code class="docutils literal notranslate"><span class="pre">3</span></code> digits of subsecond precision,</p>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">current_timestamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Returns the current <a class="reference internal" href="../language/types.html#timestamp-with-time-zone-data-type"><span class="std std-ref">timestamp with time zone</span></a> as of the start of the query, with
<code class="docutils literal notranslate"><span class="pre">p</span></code> digits of subsecond precision:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">current_timestamp</span><span class="p">(</span><span class="mi">6</span><span class="p">);</span>
<span class="c1">-- 2020-06-24 08:25:31.759993 America/Los_Angeles</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="current_timezone">
<span class="sig-name descname"><span class="pre">current_timezone</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#current_timezone" title="Link to this definition">#</a></dt>
<dd><p>Returns the current time zone in the format defined by IANA
(e.g., <code class="docutils literal notranslate"><span class="pre">America/Los_Angeles</span></code>) or as fixed offset from UTC (e.g., <code class="docutils literal notranslate"><span class="pre">+08:35</span></code>)</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="date">
<span class="sig-name descname"><span class="pre">date</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">date</span></span></span><a class="headerlink" href="datetime.html#date" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <code class="docutils literal notranslate"><span class="pre">CAST(x</span> <span class="pre">AS</span> <span class="pre">date)</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="last_day_of_month">
<span class="sig-name descname"><span class="pre">last_day_of_month</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">date</span></span></span><a class="headerlink" href="datetime.html#last_day_of_month" title="Link to this definition">#</a></dt>
<dd><p>Returns the last day of the month.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_iso8601_timestamp">
<span class="sig-name descname"><span class="pre">from_iso8601_timestamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#from_iso8601_timestamp" title="Link to this definition">#</a></dt>
<dd><p>Parses the ISO 8601 formatted date <code class="docutils literal notranslate"><span class="pre">string</span></code>, optionally with time and time
zone, into a <code class="docutils literal notranslate"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></code>. The time defaults to
<code class="docutils literal notranslate"><span class="pre">00:00:00.000</span></code>, and the time zone defaults to the session time zone:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_timestamp</span><span class="p">(</span><span class="s1">'2020-05-11'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11 00:00:00.000 America/Vancouver</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_timestamp</span><span class="p">(</span><span class="s1">'2020-05-11T11:15:05'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11 11:15:05.000 America/Vancouver</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_timestamp</span><span class="p">(</span><span class="s1">'2020-05-11T11:15:05.055+01:00'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11 11:15:05.055 +01:00</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_iso8601_timestamp_nanos">
<span class="sig-name descname"><span class="pre">from_iso8601_timestamp_nanos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(9)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#from_iso8601_timestamp_nanos" title="Link to this definition">#</a></dt>
<dd><p>Parses the ISO 8601 formatted date and time <code class="docutils literal notranslate"><span class="pre">string</span></code>. The time zone
defaults to the session time zone:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_timestamp_nanos</span><span class="p">(</span><span class="s1">'2020-05-11T11:15:05'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11 11:15:05.000000000 America/Vancouver</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_timestamp_nanos</span><span class="p">(</span><span class="s1">'2020-05-11T11:15:05.123456789+01:00'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11 11:15:05.123456789 +01:00</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_iso8601_date">
<span class="sig-name descname"><span class="pre">from_iso8601_date</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">date</span></span></span><a class="headerlink" href="datetime.html#from_iso8601_date" title="Link to this definition">#</a></dt>
<dd><p>Parses the ISO 8601 formatted date <code class="docutils literal notranslate"><span class="pre">string</span></code> into a <code class="docutils literal notranslate"><span class="pre">date</span></code>. The date can
be a calendar date, a week date using ISO week numbering, or year and day
of year combined:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_date</span><span class="p">(</span><span class="s1">'2020-05-11'</span><span class="p">);</span>
<span class="c1">-- 2020-05-11</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_date</span><span class="p">(</span><span class="s1">'2020-W10'</span><span class="p">);</span>
<span class="c1">-- 2020-03-02</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_iso8601_date</span><span class="p">(</span><span class="s1">'2020-123'</span><span class="p">);</span>
<span class="c1">-- 2020-05-02</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="at_timezone">
<span class="sig-name descname"><span class="pre">at_timezone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zone</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#at_timezone" title="Link to this definition">#</a></dt>
<dd><p>Converts a <code class="docutils literal notranslate"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></code> to a time zone specified in <code class="docutils literal notranslate"><span class="pre">zone</span></code>.</p>
<p>In the following example, the input timezone is <code class="docutils literal notranslate"><span class="pre">GMT</span></code>, which is seven hours
ahead of <code class="docutils literal notranslate"><span class="pre">America/Los_Angeles</span></code> in November 2022:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">at_timezone</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-11-01 09:08:07.321 GMT'</span><span class="p">,</span><span class="w"> </span><span class="s1">'America/Los_Angeles'</span><span class="p">)</span>
<span class="c1">-- 2022-11-01 02:08:07.321 America/Los_Angeles</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="with_timezone">
<span class="sig-name descname"><span class="pre">with_timezone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp(p)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zone</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#with_timezone" title="Link to this definition">#</a></dt>
<dd><p>Returns the timestamp specified in <code class="docutils literal notranslate"><span class="pre">timestamp</span></code> with the time zone
specified in <code class="docutils literal notranslate"><span class="pre">zone</span></code> with precision <code class="docutils literal notranslate"><span class="pre">p</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">current_timezone</span><span class="p">()</span>
<span class="c1">-- America/New_York</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">with_timezone</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-11-01 09:08:07.321'</span><span class="p">,</span><span class="w"> </span><span class="s1">'America/Los_Angeles'</span><span class="p">)</span>
<span class="c1">-- 2022-11-01 09:08:07.321 America/Los_Angeles</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_unixtime">
<span class="sig-name descname"><span class="pre">from_unixtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unixtime</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#from_unixtime" title="Link to this definition">#</a></dt>
<dd><p>Returns the UNIX timestamp <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> as a timestamp with time zone. <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> is the
number of seconds since <code class="docutils literal notranslate"><span class="pre">1970-01-01</span> <span class="pre">00:00:00</span> <span class="pre">UTC</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">from_unixtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unixtime</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zone</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span></dt>
<dd><p>Returns the UNIX timestamp <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> as a timestamp with time zone
using <code class="docutils literal notranslate"><span class="pre">zone</span></code> for the time zone. <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> is the number of seconds
since <code class="docutils literal notranslate"><span class="pre">1970-01-01</span> <span class="pre">00:00:00</span> <span class="pre">UTC</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">from_unixtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unixtime</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hours</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">minutes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span></dt>
<dd><p>Returns the UNIX timestamp <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> as a timestamp with time zone
using <code class="docutils literal notranslate"><span class="pre">hours</span></code> and <code class="docutils literal notranslate"><span class="pre">minutes</span></code> for the time zone offset. <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> is
the number of seconds since <code class="docutils literal notranslate"><span class="pre">1970-01-01</span> <span class="pre">00:00:00</span></code> in <code class="docutils literal notranslate"><span class="pre">double</span></code> data type.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_unixtime_nanos">
<span class="sig-name descname"><span class="pre">from_unixtime_nanos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unixtime</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(9)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#from_unixtime_nanos" title="Link to this definition">#</a></dt>
<dd><p>Returns the UNIX timestamp <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> as a timestamp with time zone. <code class="docutils literal notranslate"><span class="pre">unixtime</span></code> is the
number of nanoseconds since <code class="docutils literal notranslate"><span class="pre">1970-01-01</span> <span class="pre">00:00:00.000000000</span> <span class="pre">UTC</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">from_unixtime_nanos</span><span class="p">(</span><span class="mi">100</span><span class="p">);</span>
<span class="c1">-- 1970-01-01 00:00:00.000000100 UTC</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_unixtime_nanos</span><span class="p">(</span><span class="nb">DECIMAL</span><span class="w"> </span><span class="s1">'1234'</span><span class="p">);</span>
<span class="c1">-- 1970-01-01 00:00:00.000001234 UTC</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_unixtime_nanos</span><span class="p">(</span><span class="nb">DECIMAL</span><span class="w"> </span><span class="s1">'1234.499'</span><span class="p">);</span>
<span class="c1">-- 1970-01-01 00:00:00.000001234 UTC</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">from_unixtime_nanos</span><span class="p">(</span><span class="nb">DECIMAL</span><span class="w"> </span><span class="s1">'-1234'</span><span class="p">);</span>
<span class="c1">-- 1969-12-31 23:59:59.999998766 UTC</span>
</pre></div>
</div>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py" id="localtime">
<span class="sig-name descname"><span class="pre">localtime</span></span><a class="headerlink" href="datetime.html#localtime" title="Link to this definition">#</a></dt>
<dd><p>Returns the current time as of the start of the query.</p>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py" id="localtimestamp">
<span class="sig-name descname"><span class="pre">localtimestamp</span></span><a class="headerlink" href="datetime.html#localtimestamp" title="Link to this definition">#</a></dt>
<dd><p>Returns the current timestamp as of the start of the query, with <code class="docutils literal notranslate"><span class="pre">3</span></code>
digits of subsecond precision.</p>
</dd></dl>
<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">localtimestamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Returns the current <a class="reference internal" href="../language/types.html#timestamp-data-type"><span class="std std-ref">timestamp</span></a> as of the start
of the query, with <code class="docutils literal notranslate"><span class="pre">p</span></code> digits of subsecond precision:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">localtimestamp</span><span class="p">(</span><span class="mi">6</span><span class="p">);</span>
<span class="c1">-- 2020-06-10 15:55:23.383628</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="now">
<span class="sig-name descname"><span class="pre">now</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp(3)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#now" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <code class="docutils literal notranslate"><span class="pre">current_timestamp</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_iso8601">
<span class="sig-name descname"><span class="pre">to_iso8601</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#to_iso8601" title="Link to this definition">#</a></dt>
<dd><p>Formats <code class="docutils literal notranslate"><span class="pre">x</span></code> as an ISO 8601 string. <code class="docutils literal notranslate"><span class="pre">x</span></code> can be date, timestamp, or
timestamp with time zone.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_milliseconds">
<span class="sig-name descname"><span class="pre">to_milliseconds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interval</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#to_milliseconds" title="Link to this definition">#</a></dt>
<dd><p>Returns the day-to-second <code class="docutils literal notranslate"><span class="pre">interval</span></code> as milliseconds.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_unixtime">
<span class="sig-name descname"><span class="pre">to_unixtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="datetime.html#to_unixtime" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">timestamp</span></code> as a UNIX timestamp.</p>
</dd></dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The following SQL-standard functions do not use parenthesis:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">current_date</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">current_time</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">current_timestamp</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">localtime</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">localtimestamp</span></code></p></li>
</ul>
</div>
</section>
<section id="truncation-function">
<h2 id="truncation-function">Truncation function<a class="headerlink" href="datetime.html#truncation-function" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">date_trunc</span></code> function supports the following units:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Unit</p></th>
<th class="head"><p>Example Truncated Value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">millisecond</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">03:04:05.321</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">second</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">03:04:05.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">minute</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">03:04:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hour</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">03:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">day</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">00:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">week</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-20</span> <span class="pre">00:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">month</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-08-01</span> <span class="pre">00:00:00.000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">quarter</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-07-01</span> <span class="pre">00:00:00.000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">year</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2001-01-01</span> <span class="pre">00:00:00.000</span></code></p></td>
</tr>
</tbody>
</table>
<p>The above examples use the timestamp <code class="docutils literal notranslate"><span class="pre">2001-08-22</span> <span class="pre">03:04:05.321</span></code> as the input.</p>
<dl class="py function">
<dt class="sig sig-object py" id="date_trunc">
<span class="sig-name descname"><span class="pre">date_trunc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="datetime.html#date_trunc" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> truncated to <code class="docutils literal notranslate"><span class="pre">unit</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_trunc</span><span class="p">(</span><span class="s1">'day'</span><span class="w"> </span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-10-20 05:10:00'</span><span class="p">);</span>
<span class="c1">-- 2022-10-20 00:00:00.000</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_trunc</span><span class="p">(</span><span class="s1">'month'</span><span class="w"> </span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-10-20 05:10:00'</span><span class="p">);</span>
<span class="c1">-- 2022-10-01 00:00:00.000</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_trunc</span><span class="p">(</span><span class="s1">'year'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-10-20 05:10:00'</span><span class="p">);</span>
<span class="c1">-- 2022-01-01 00:00:00.000</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="interval-functions">
<span id="datetime-interval-functions"></span><h2 id="interval-functions">Interval functions<a class="headerlink" href="datetime.html#interval-functions" title="Link to this heading">#</a></h2>
<p>The functions in this section support the following interval units:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Unit</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">millisecond</span></code></p></td>
<td><p>Milliseconds</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">second</span></code></p></td>
<td><p>Seconds</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">minute</span></code></p></td>
<td><p>Minutes</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hour</span></code></p></td>
<td><p>Hours</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">day</span></code></p></td>
<td><p>Days</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">week</span></code></p></td>
<td><p>Weeks</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">month</span></code></p></td>
<td><p>Months</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">quarter</span></code></p></td>
<td><p>Quarters of a year</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">year</span></code></p></td>
<td><p>Years</p></td>
</tr>
</tbody>
</table>
<dl class="py function">
<dt class="sig sig-object py" id="date_add">
<span class="sig-name descname"><span class="pre">date_add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="datetime.html#date_add" title="Link to this definition">#</a></dt>
<dd><p>Adds an interval <code class="docutils literal notranslate"><span class="pre">value</span></code> of type <code class="docutils literal notranslate"><span class="pre">unit</span></code> to <code class="docutils literal notranslate"><span class="pre">timestamp</span></code>.
Subtraction can be performed by using a negative value:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_add</span><span class="p">(</span><span class="s1">'second'</span><span class="p">,</span><span class="w"> </span><span class="mi">86</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-01 00:00:00'</span><span class="p">);</span>
<span class="c1">-- 2020-03-01 00:01:26.000</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_add</span><span class="p">(</span><span class="s1">'hour'</span><span class="p">,</span><span class="w"> </span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-01 00:00:00'</span><span class="p">);</span>
<span class="c1">-- 2020-03-01 09:00:00.000</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_add</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-01 00:00:00 UTC'</span><span class="p">);</span>
<span class="c1">-- 2020-02-29 00:00:00.000 UTC</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="date_diff">
<span class="sig-name descname"><span class="pre">date_diff</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp2</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#date_diff" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">timestamp2</span> <span class="pre">-</span> <span class="pre">timestamp1</span></code> expressed in terms of <code class="docutils literal notranslate"><span class="pre">unit</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'second'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-01 00:00:00'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-02 00:00:00'</span><span class="p">);</span>
<span class="c1">-- 86400</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'hour'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-01 00:00:00 UTC'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-03-02 00:00:00 UTC'</span><span class="p">);</span>
<span class="c1">-- 24</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2020-03-01'</span><span class="p">,</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2020-03-02'</span><span class="p">);</span>
<span class="c1">-- 1</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'second'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-01 12:30:45.000000000'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-02 12:30:45.123456789'</span><span class="p">);</span>
<span class="c1">-- 86400</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'millisecond'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-01 12:30:45.000000000'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-06-02 12:30:45.123456789'</span><span class="p">);</span>
<span class="c1">-- 86400123</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="duration-function">
<h2 id="duration-function">Duration function<a class="headerlink" href="datetime.html#duration-function" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">parse_duration</span></code> function supports the following units:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Unit</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ns</span></code></p></td>
<td><p>Nanoseconds</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">us</span></code></p></td>
<td><p>Microseconds</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ms</span></code></p></td>
<td><p>Milliseconds</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">s</span></code></p></td>
<td><p>Seconds</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">m</span></code></p></td>
<td><p>Minutes</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">h</span></code></p></td>
<td><p>Hours</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">d</span></code></p></td>
<td><p>Days</p></td>
</tr>
</tbody>
</table>
<dl class="py function">
<dt class="sig sig-object py" id="parse_duration">
<span class="sig-name descname"><span class="pre">parse_duration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">interval</span></span></span><a class="headerlink" href="datetime.html#parse_duration" title="Link to this definition">#</a></dt>
<dd><p>Parses <code class="docutils literal notranslate"><span class="pre">string</span></code> of format <code class="docutils literal notranslate"><span class="pre">value</span> <span class="pre">unit</span></code> into an interval, where
<code class="docutils literal notranslate"><span class="pre">value</span></code> is fractional number of <code class="docutils literal notranslate"><span class="pre">unit</span></code> values:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">parse_duration</span><span class="p">(</span><span class="s1">'42.8ms'</span><span class="p">);</span>
<span class="c1">-- 0 00:00:00.043</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">parse_duration</span><span class="p">(</span><span class="s1">'3.81 d'</span><span class="p">);</span>
<span class="c1">-- 3 19:26:24.000</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">parse_duration</span><span class="p">(</span><span class="s1">'5m'</span><span class="p">);</span>
<span class="c1">-- 0 00:05:00.000</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="human_readable_seconds">
<span class="sig-name descname"><span class="pre">human_readable_seconds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">double</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#human_readable_seconds" title="Link to this definition">#</a></dt>
<dd><p>Formats the double value of <code class="docutils literal notranslate"><span class="pre">seconds</span></code> into a human-readable string containing
<code class="docutils literal notranslate"><span class="pre">weeks</span></code>, <code class="docutils literal notranslate"><span class="pre">days</span></code>, <code class="docutils literal notranslate"><span class="pre">hours</span></code>, <code class="docutils literal notranslate"><span class="pre">minutes</span></code>, and <code class="docutils literal notranslate"><span class="pre">seconds</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">human_readable_seconds</span><span class="p">(</span><span class="mi">96</span><span class="p">);</span>
<span class="c1">-- 1 minute, 36 seconds</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">human_readable_seconds</span><span class="p">(</span><span class="mi">3762</span><span class="p">);</span>
<span class="c1">-- 1 hour, 2 minutes, 42 seconds</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">human_readable_seconds</span><span class="p">(</span><span class="mi">56363463</span><span class="p">);</span>
<span class="c1">-- 93 weeks, 1 day, 8 hours, 31 minutes, 3 seconds</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="mysql-date-functions">
<h2 id="mysql-date-functions">MySQL date functions<a class="headerlink" href="datetime.html#mysql-date-functions" title="Link to this heading">#</a></h2>
<p>The functions in this section use a format string that is compatible with
the MySQL <code class="docutils literal notranslate"><span class="pre">date_parse</span></code> and <code class="docutils literal notranslate"><span class="pre">str_to_date</span></code> functions. The following table,
based on the MySQL manual, describes the format specifiers:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Specifier</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%a</span></code></p></td>
<td><p>Abbreviated weekday name (<code class="docutils literal notranslate"><span class="pre">Sun</span></code> .. <code class="docutils literal notranslate"><span class="pre">Sat</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%b</span></code></p></td>
<td><p>Abbreviated month name (<code class="docutils literal notranslate"><span class="pre">Jan</span></code> .. <code class="docutils literal notranslate"><span class="pre">Dec</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%c</span></code></p></td>
<td><p>Month, numeric (<code class="docutils literal notranslate"><span class="pre">1</span></code> .. <code class="docutils literal notranslate"><span class="pre">12</span></code>), this specifier does not support <code class="docutils literal notranslate"><span class="pre">0</span></code> as a month.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%D</span></code></p></td>
<td><p>Day of the month with English suffix (<code class="docutils literal notranslate"><span class="pre">0th</span></code>, <code class="docutils literal notranslate"><span class="pre">1st</span></code>, <code class="docutils literal notranslate"><span class="pre">2nd</span></code>, <code class="docutils literal notranslate"><span class="pre">3rd</span></code>, …)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%d</span></code></p></td>
<td><p>Day of the month, numeric (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">31</span></code>), this specifier does not support <code class="docutils literal notranslate"><span class="pre">0</span></code> as a month or day.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%e</span></code></p></td>
<td><p>Day of the month, numeric (<code class="docutils literal notranslate"><span class="pre">1</span></code> .. <code class="docutils literal notranslate"><span class="pre">31</span></code>), this specifier does not support <code class="docutils literal notranslate"><span class="pre">0</span></code> as a day.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%f</span></code></p></td>
<td><p>Fraction of second (6 digits for printing: <code class="docutils literal notranslate"><span class="pre">000000</span></code> .. <code class="docutils literal notranslate"><span class="pre">999000</span></code>; 1 - 9 digits for parsing: <code class="docutils literal notranslate"><span class="pre">0</span></code> .. <code class="docutils literal notranslate"><span class="pre">999999999</span></code>), timestamp is truncated to milliseconds.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%H</span></code></p></td>
<td><p>Hour (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">23</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%h</span></code></p></td>
<td><p>Hour (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">12</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%I</span></code></p></td>
<td><p>Hour (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">12</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%i</span></code></p></td>
<td><p>Minutes, numeric (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">59</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%j</span></code></p></td>
<td><p>Day of year (<code class="docutils literal notranslate"><span class="pre">001</span></code> .. <code class="docutils literal notranslate"><span class="pre">366</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%k</span></code></p></td>
<td><p>Hour (<code class="docutils literal notranslate"><span class="pre">0</span></code> .. <code class="docutils literal notranslate"><span class="pre">23</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%l</span></code></p></td>
<td><p>Hour (<code class="docutils literal notranslate"><span class="pre">1</span></code> .. <code class="docutils literal notranslate"><span class="pre">12</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%M</span></code></p></td>
<td><p>Month name (<code class="docutils literal notranslate"><span class="pre">January</span></code> .. <code class="docutils literal notranslate"><span class="pre">December</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%m</span></code></p></td>
<td><p>Month, numeric (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">12</span></code>), this specifier does not support <code class="docutils literal notranslate"><span class="pre">0</span></code> as a month.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%p</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">AM</span></code> or <code class="docutils literal notranslate"><span class="pre">PM</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%r</span></code></p></td>
<td><p>Time of day, 12-hour (equivalent to <code class="docutils literal notranslate"><span class="pre">%h:%i:%s</span> <span class="pre">%p</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%S</span></code></p></td>
<td><p>Seconds (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">59</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%s</span></code></p></td>
<td><p>Seconds (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">59</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%T</span></code></p></td>
<td><p>Time of day, 24-hour (equivalent to <code class="docutils literal notranslate"><span class="pre">%H:%i:%s</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%U</span></code></p></td>
<td><p>Week (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">53</span></code>), where Sunday is the first day of the week</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%u</span></code></p></td>
<td><p>Week (<code class="docutils literal notranslate"><span class="pre">00</span></code> .. <code class="docutils literal notranslate"><span class="pre">53</span></code>), where Monday is the first day of the week</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%V</span></code></p></td>
<td><p>Week (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">53</span></code>), where Sunday is the first day of the week; used with <code class="docutils literal notranslate"><span class="pre">%X</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%v</span></code></p></td>
<td><p>Week (<code class="docutils literal notranslate"><span class="pre">01</span></code> .. <code class="docutils literal notranslate"><span class="pre">53</span></code>), where Monday is the first day of the week; used with <code class="docutils literal notranslate"><span class="pre">%x</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%W</span></code></p></td>
<td><p>Weekday name (<code class="docutils literal notranslate"><span class="pre">Sunday</span></code> .. <code class="docutils literal notranslate"><span class="pre">Saturday</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%w</span></code></p></td>
<td><p>Day of the week (<code class="docutils literal notranslate"><span class="pre">0</span></code> .. <code class="docutils literal notranslate"><span class="pre">6</span></code>), where Sunday is the first day of the week, this specifier is not supported,consider using <a class="reference internal" href="datetime.html#day_of_week" title="day_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_week()</span></code></a> (it uses <code class="docutils literal notranslate"><span class="pre">1-7</span></code> instead of <code class="docutils literal notranslate"><span class="pre">0-6</span></code>).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%X</span></code></p></td>
<td><p>Year for the week where Sunday is the first day of the week, numeric, four digits; used with <code class="docutils literal notranslate"><span class="pre">%V</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%x</span></code></p></td>
<td><p>Year for the week, where Monday is the first day of the week, numeric, four digits; used with <code class="docutils literal notranslate"><span class="pre">%v</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%Y</span></code></p></td>
<td><p>Year, numeric, four digits</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%y</span></code></p></td>
<td><p>Year, numeric (two digits), when parsing, two-digit year format assumes range <code class="docutils literal notranslate"><span class="pre">1970</span></code> .. <code class="docutils literal notranslate"><span class="pre">2069</span></code>, so “70” will result in year <code class="docutils literal notranslate"><span class="pre">1970</span></code> but “69” will produce <code class="docutils literal notranslate"><span class="pre">2069</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%%</span></code></p></td>
<td><p>A literal <code class="docutils literal notranslate"><span class="pre">%</span></code> character</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%x</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">x</span></code>, for any <code class="docutils literal notranslate"><span class="pre">x</span></code> not listed above</p></td>
</tr>
</tbody>
</table>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The following specifiers are not currently supported: <code class="docutils literal notranslate"><span class="pre">%D</span> <span class="pre">%U</span> <span class="pre">%u</span> <span class="pre">%V</span> <span class="pre">%w</span> <span class="pre">%X</span></code></p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="date_format">
<span class="sig-name descname"><span class="pre">date_format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#date_format" title="Link to this definition">#</a></dt>
<dd><p>Formats <code class="docutils literal notranslate"><span class="pre">timestamp</span></code> as a string using <code class="docutils literal notranslate"><span class="pre">format</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_format</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-10-20 05:10:00'</span><span class="p">,</span><span class="w"> </span><span class="s1">'%m-%d-%Y %H'</span><span class="p">);</span>
<span class="c1">-- 10-20-2022 05</span>
</pre></div>
</div>
</dd></dl>
<dl class="js function">
<dt class="sig sig-object js" id="date_parse">
<span class="sig-name descname"><span class="n"><span class="pre">date_parse</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format)</span> <span class="pre">→</span> <span class="pre">timestamp(3</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="datetime.html#date_parse" title="Link to this definition">#</a></dt>
<dd><p>Parses <code class="docutils literal notranslate"><span class="pre">string</span></code> into a timestamp using <code class="docutils literal notranslate"><span class="pre">format</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">date_parse</span><span class="p">(</span><span class="s1">'2022/10/20/05'</span><span class="p">,</span><span class="w"> </span><span class="s1">'%Y/%m/%d/%H'</span><span class="p">);</span>
<span class="c1">-- 2022-10-20 05:00:00.000</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="java-date-functions">
<h2 id="java-date-functions">Java date functions<a class="headerlink" href="datetime.html#java-date-functions" title="Link to this heading">#</a></h2>
<p>The functions in this section use a format string that is compatible with
JodaTime’s <a class="reference external" href="http://joda-time.sourceforge.net/apidocs/org/joda/time/format/DateTimeFormat.html">DateTimeFormat</a> pattern format.</p>
<dl class="py function">
<dt class="sig sig-object py" id="format_datetime">
<span class="sig-name descname"><span class="pre">format_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#format_datetime" title="Link to this definition">#</a></dt>
<dd><p>Formats <code class="docutils literal notranslate"><span class="pre">timestamp</span></code> as a string using <code class="docutils literal notranslate"><span class="pre">format</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="parse_datetime">
<span class="sig-name descname"><span class="pre">parse_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></span><a class="headerlink" href="datetime.html#parse_datetime" title="Link to this definition">#</a></dt>
<dd><p>Parses <code class="docutils literal notranslate"><span class="pre">string</span></code> into a timestamp with time zone using <code class="docutils literal notranslate"><span class="pre">format</span></code>.</p>
</dd></dl>
</section>
<section id="extraction-function">
<h2 id="extraction-function">Extraction function<a class="headerlink" href="datetime.html#extraction-function" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">extract</span></code> function supports the following fields:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">YEAR</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#year" title="year"><code class="xref py py-func docutils literal notranslate"><span class="pre">year()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">QUARTER</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#quarter" title="quarter"><code class="xref py py-func docutils literal notranslate"><span class="pre">quarter()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MONTH</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#month" title="month"><code class="xref py py-func docutils literal notranslate"><span class="pre">month()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">WEEK</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#week" title="week"><code class="xref py py-func docutils literal notranslate"><span class="pre">week()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DAY</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day" title="day"><code class="xref py py-func docutils literal notranslate"><span class="pre">day()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DAY_OF_MONTH</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day" title="day"><code class="xref py py-func docutils literal notranslate"><span class="pre">day()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DAY_OF_WEEK</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day_of_week" title="day_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_week()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOW</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day_of_week" title="day_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_week()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DAY_OF_YEAR</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day_of_year" title="day_of_year"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_year()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOY</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#day_of_year" title="day_of_year"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_year()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">YEAR_OF_WEEK</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#year_of_week" title="year_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">year_of_week()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">YOW</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#year_of_week" title="year_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">year_of_week()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">HOUR</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#hour" title="hour"><code class="xref py py-func docutils literal notranslate"><span class="pre">hour()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MINUTE</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#minute" title="minute"><code class="xref py py-func docutils literal notranslate"><span class="pre">minute()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SECOND</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#second" title="second"><code class="xref py py-func docutils literal notranslate"><span class="pre">second()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMEZONE_HOUR</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#timezone_hour" title="timezone_hour"><code class="xref py py-func docutils literal notranslate"><span class="pre">timezone_hour()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMEZONE_MINUTE</span></code></p></td>
<td><p><a class="reference internal" href="datetime.html#timezone_minute" title="timezone_minute"><code class="xref py py-func docutils literal notranslate"><span class="pre">timezone_minute()</span></code></a></p></td>
</tr>
</tbody>
</table>
<p>The types supported by the <code class="docutils literal notranslate"><span class="pre">extract</span></code> function vary depending on the
field to be extracted. Most fields support all date and time types.</p>
<dl class="py function">
<dt class="sig sig-object py" id="extract">
<span class="sig-name descname"><span class="pre">extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">field</span> <span class="pre">FROM</span> <span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#extract" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">field</span></code> from <code class="docutils literal notranslate"><span class="pre">x</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">extract</span><span class="p">(</span><span class="k">YEAR</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-10-20 05:10:00'</span><span class="p">);</span>
<span class="c1">-- 2022</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This SQL-standard function uses special syntax for specifying the arguments.</p>
</div>
</dd></dl>
</section>
<section id="convenience-extraction-functions">
<h2 id="convenience-extraction-functions">Convenience extraction functions<a class="headerlink" href="datetime.html#convenience-extraction-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="day">
<span class="sig-name descname"><span class="pre">day</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#day" title="Link to this definition">#</a></dt>
<dd><p>Returns the day of the month from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="day_of_month">
<span class="sig-name descname"><span class="pre">day_of_month</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#day_of_month" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="datetime.html#day" title="day"><code class="xref py py-func docutils literal notranslate"><span class="pre">day()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="day_of_week">
<span class="sig-name descname"><span class="pre">day_of_week</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#day_of_week" title="Link to this definition">#</a></dt>
<dd><p>Returns the ISO day of the week from <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The value ranges from <code class="docutils literal notranslate"><span class="pre">1</span></code> (Monday) to <code class="docutils literal notranslate"><span class="pre">7</span></code> (Sunday).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="day_of_year">
<span class="sig-name descname"><span class="pre">day_of_year</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#day_of_year" title="Link to this definition">#</a></dt>
<dd><p>Returns the day of the year from <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The value ranges from <code class="docutils literal notranslate"><span class="pre">1</span></code> to <code class="docutils literal notranslate"><span class="pre">366</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="dow">
<span class="sig-name descname"><span class="pre">dow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#dow" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="datetime.html#day_of_week" title="day_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_week()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="doy">
<span class="sig-name descname"><span class="pre">doy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#doy" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="datetime.html#day_of_year" title="day_of_year"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_year()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hour">
<span class="sig-name descname"><span class="pre">hour</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#hour" title="Link to this definition">#</a></dt>
<dd><p>Returns the hour of the day from <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The value ranges from <code class="docutils literal notranslate"><span class="pre">0</span></code> to <code class="docutils literal notranslate"><span class="pre">23</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="millisecond">
<span class="sig-name descname"><span class="pre">millisecond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#millisecond" title="Link to this definition">#</a></dt>
<dd><p>Returns the millisecond of the second from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="minute">
<span class="sig-name descname"><span class="pre">minute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#minute" title="Link to this definition">#</a></dt>
<dd><p>Returns the minute of the hour from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="month">
<span class="sig-name descname"><span class="pre">month</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#month" title="Link to this definition">#</a></dt>
<dd><p>Returns the month of the year from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="quarter">
<span class="sig-name descname"><span class="pre">quarter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#quarter" title="Link to this definition">#</a></dt>
<dd><p>Returns the quarter of the year from <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The value ranges from <code class="docutils literal notranslate"><span class="pre">1</span></code> to <code class="docutils literal notranslate"><span class="pre">4</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="second">
<span class="sig-name descname"><span class="pre">second</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#second" title="Link to this definition">#</a></dt>
<dd><p>Returns the second of the minute from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="timezone_hour">
<span class="sig-name descname"><span class="pre">timezone_hour</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#timezone_hour" title="Link to this definition">#</a></dt>
<dd><p>Returns the hour of the time zone offset from <code class="docutils literal notranslate"><span class="pre">timestamp</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="timezone_minute">
<span class="sig-name descname"><span class="pre">timezone_minute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#timezone_minute" title="Link to this definition">#</a></dt>
<dd><p>Returns the minute of the time zone offset from <code class="docutils literal notranslate"><span class="pre">timestamp</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="week">
<span class="sig-name descname"><span class="pre">week</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#week" title="Link to this definition">#</a></dt>
<dd><p>Returns the <a class="reference external" href="https://wikipedia.org/wiki/ISO_week_date">ISO week</a> of the year from <code class="docutils literal notranslate"><span class="pre">x</span></code>.
The value ranges from <code class="docutils literal notranslate"><span class="pre">1</span></code> to <code class="docutils literal notranslate"><span class="pre">53</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="week_of_year">
<span class="sig-name descname"><span class="pre">week_of_year</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#week_of_year" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="datetime.html#week" title="week"><code class="xref py py-func docutils literal notranslate"><span class="pre">week()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="year">
<span class="sig-name descname"><span class="pre">year</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#year" title="Link to this definition">#</a></dt>
<dd><p>Returns the year from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="year_of_week">
<span class="sig-name descname"><span class="pre">year_of_week</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#year_of_week" title="Link to this definition">#</a></dt>
<dd><p>Returns the year of the <a class="reference external" href="https://wikipedia.org/wiki/ISO_week_date">ISO week</a> from <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="yow">
<span class="sig-name descname"><span class="pre">yow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="datetime.html#yow" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="datetime.html#year_of_week" title="year_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">year_of_week()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="timezone">
<span class="sig-name descname"><span class="pre">timezone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="datetime.html#timezone" title="Link to this definition">#</a></dt>
<dd><p>Returns the timezone identifier from <code class="docutils literal notranslate"><span class="pre">timestamp(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></code>. The format
of the returned identifier is identical to the <a class="reference internal" href="../language/types.html#timestamp-p-with-time-zone-data-type"><span class="std std-ref">format used in the input
timestamp</span></a>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">timezone</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2024-01-01 12:00:00 Asia/Tokyo'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- Asia/Tokyo</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">timezone</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2024-01-01 12:00:00 +01:00'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- +01:00</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">timezone</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2024-02-29 12:00:00 UTC'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- UTC</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">timezone</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">time(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Returns the timezone identifier from a <code class="docutils literal notranslate"><span class="pre">time(p)</span> <span class="pre">with</span> <span class="pre">time</span> <span class="pre">zone</span></code>. The format
of the returned identifier is identical to the <a class="reference internal" href="../language/types.html#time-with-time-zone-data-type"><span class="std std-ref">format used in the input
time</span></a>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">timezone</span><span class="p">(</span><span class="k">TIME</span><span class="w"> </span><span class="s1">'12:00:00+09:00'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- +09:00</span>
</pre></div>
</div>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="conversion.html" title="Conversion functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Conversion functions </span>
              </div>
            </a>
          
          
            <a href="decimal.html" title="Decimal functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Decimal functions and operators </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>