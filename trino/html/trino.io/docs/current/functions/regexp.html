<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Regular expression functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="regexp.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Session information" href="session.html" />
    <link rel="prev" title="Quantile digest functions" href="qdigest.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="regexp.html#functions/regexp" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Regular expression functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Regular expression </label>
    
      <a href="regexp.html#" class="md-nav__link md-nav__link--active">Regular expression</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="regexp.html#regexp_count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_count()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_extract_all" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_extract_all()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_extract()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_like" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_like()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_position" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_position()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_replace" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_replace()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_split" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_split()</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="regexp.html#regexp_count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_count()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_extract_all" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_extract_all()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_extract" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_extract()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_like" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_like()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_position" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_position()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_replace" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_replace()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="regexp.html#regexp_split" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">regexp_split()</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="regular-expression-functions">
<h1 id="functions-regexp--page-root">Regular expression functions<a class="headerlink" href="regexp.html#functions-regexp--page-root" title="Link to this heading">#</a></h1>
<p>All the regular expression functions use the <a class="reference external" href="https://docs.oracle.com/en/java/javase/23/docs/api/java.base/java/util/regex/Pattern.html">Java pattern</a> syntax,
with a few notable exceptions:</p>
<ul class="simple">
<li><p>When using multi-line mode (enabled via the <code class="docutils literal notranslate"><span class="pre">(?m)</span></code> flag),
only <code class="docutils literal notranslate"><span class="pre">\n</span></code> is recognized as a line terminator. Additionally,
the <code class="docutils literal notranslate"><span class="pre">(?d)</span></code> flag is not supported and must not be used.</p></li>
<li><p>Case-insensitive matching (enabled via the <code class="docutils literal notranslate"><span class="pre">(?i)</span></code> flag) is always
performed in a Unicode-aware manner. However, context-sensitive and
local-sensitive matching is not supported. Additionally, the
<code class="docutils literal notranslate"><span class="pre">(?u)</span></code> flag is not supported and must not be used.</p></li>
<li><p>Surrogate pairs are not supported. For example, <code class="docutils literal notranslate"><span class="pre">\uD800\uDC00</span></code> is
not treated as <code class="docutils literal notranslate"><span class="pre">U+10000</span></code> and must be specified as <code class="docutils literal notranslate"><span class="pre">\x{10000}</span></code>.</p></li>
<li><p>Boundaries (<code class="docutils literal notranslate"><span class="pre">\b</span></code>) are incorrectly handled for a non-spacing mark
without a base character.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">\Q</span></code> and <code class="docutils literal notranslate"><span class="pre">\E</span></code> are not supported in character classes
(such as <code class="docutils literal notranslate"><span class="pre">[A-Z123]</span></code>) and are instead treated as literals.</p></li>
<li><p>Unicode character classes (<code class="docutils literal notranslate"><span class="pre">\p{prop}</span></code>) are supported with
the following differences:</p>
<ul>
<li><p>All underscores in names must be removed. For example, use
<code class="docutils literal notranslate"><span class="pre">OldItalic</span></code> instead of <code class="docutils literal notranslate"><span class="pre">Old_Italic</span></code>.</p></li>
<li><p>Scripts must be specified directly, without the
<code class="docutils literal notranslate"><span class="pre">Is</span></code>, <code class="docutils literal notranslate"><span class="pre">script=</span></code> or <code class="docutils literal notranslate"><span class="pre">sc=</span></code> prefixes.
Example: <code class="docutils literal notranslate"><span class="pre">\p{Hiragana}</span></code></p></li>
<li><p>Blocks must be specified with the <code class="docutils literal notranslate"><span class="pre">In</span></code> prefix.
The <code class="docutils literal notranslate"><span class="pre">block=</span></code> and <code class="docutils literal notranslate"><span class="pre">blk=</span></code> prefixes are not supported.
Example: <code class="docutils literal notranslate"><span class="pre">\p{Mongolian}</span></code></p></li>
<li><p>Categories must be specified directly, without the <code class="docutils literal notranslate"><span class="pre">Is</span></code>,
<code class="docutils literal notranslate"><span class="pre">general_category=</span></code> or <code class="docutils literal notranslate"><span class="pre">gc=</span></code> prefixes.
Example: <code class="docutils literal notranslate"><span class="pre">\p{L}</span></code></p></li>
<li><p>Binary properties must be specified directly, without the <code class="docutils literal notranslate"><span class="pre">Is</span></code>.
Example: <code class="docutils literal notranslate"><span class="pre">\p{NoncharacterCodePoint}</span></code></p></li>
</ul>
</li>
</ul>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_count">
<span class="sig-name descname"><span class="pre">regexp_count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="regexp.html#regexp_count" title="Link to this definition">#</a></dt>
<dd><p>Returns the number of occurrence of <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_count</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\s*[a-z]+\s*'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 3</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_extract_all">
<span class="sig-name descname"><span class="pre">regexp_extract_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="regexp.html#regexp_extract_all" title="Link to this definition">#</a></dt>
<dd><p>Returns the substring(s) matched by the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code>
in <code class="docutils literal notranslate"><span class="pre">string</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_extract_all</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\d+'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- [1, 2, 14]</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_extract_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Finds all occurrences of the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>
and returns the <a class="reference external" href="https://docs.oracle.com/en/java/javase/23/docs/api/java.base/java/util/regex/Pattern.html#gnumber">capturing group number</a> <code class="docutils literal notranslate"><span class="pre">group</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_extract_all</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'(\d+)([a-z]+)'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- ['a', 'b', 'm']</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_extract">
<span class="sig-name descname"><span class="pre">regexp_extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="regexp.html#regexp_extract" title="Link to this definition">#</a></dt>
<dd><p>Returns the first substring matched by the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code>
in <code class="docutils literal notranslate"><span class="pre">string</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_extract</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\d+'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">group</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Finds the first occurrence of the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in
<code class="docutils literal notranslate"><span class="pre">string</span></code> and returns the <a class="reference external" href="https://docs.oracle.com/en/java/javase/23/docs/api/java.base/java/util/regex/Pattern.html#gnumber">capturing group number</a> <code class="docutils literal notranslate"><span class="pre">group</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_extract</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'(\d+)([a-z]+)'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'a'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_like">
<span class="sig-name descname"><span class="pre">regexp_like</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="regexp.html#regexp_like" title="Link to this definition">#</a></dt>
<dd><p>Evaluates the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code> and determines if it is
contained within <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">pattern</span></code> only needs to be contained within
<code class="docutils literal notranslate"><span class="pre">string</span></code>, rather than needing to match all of <code class="docutils literal notranslate"><span class="pre">string</span></code>. In other words,
this performs a <em>contains</em> operation rather than a <em>match</em> operation. You can
match the entire string by anchoring the pattern using <code class="docutils literal notranslate"><span class="pre">^</span></code> and <code class="docutils literal notranslate"><span class="pre">$</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_like</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\d+b'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- true</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_position">
<span class="sig-name descname"><span class="pre">regexp_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">integer</span></span></span><a class="headerlink" href="regexp.html#regexp_position" title="Link to this definition">#</a></dt>
<dd><p>Returns the index of the first occurrence (counting from 1) of <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>.
Returns -1 if not found:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 8</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">integer</span></span></span></dt>
<dd><p>Returns the index of the first occurrence of <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>,
starting from <code class="docutils literal notranslate"><span class="pre">start</span></code> (include <code class="docutils literal notranslate"><span class="pre">start</span></code>). Returns -1 if not found:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 8</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 19</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">occurrence</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">integer</span></span></span></dt>
<dd><p>Returns the index of the nth <code class="docutils literal notranslate"><span class="pre">occurrence</span></code> of <code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>,
starting from <code class="docutils literal notranslate"><span class="pre">start</span></code> (include <code class="docutils literal notranslate"><span class="pre">start</span></code>). Returns -1 if not found:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 19</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 31</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_position</span><span class="p">(</span><span class="s1">'I have 23 apples, 5 pears and 13 oranges'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\b\d+\b'</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span><span class="w"> </span><span class="c1">-- -1</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_replace">
<span class="sig-name descname"><span class="pre">regexp_replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="regexp.html#regexp_replace" title="Link to this definition">#</a></dt>
<dd><p>Removes every instance of the substring matched by the regular expression
<code class="docutils literal notranslate"><span class="pre">pattern</span></code> from <code class="docutils literal notranslate"><span class="pre">string</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_replace</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\d+[ab] '</span><span class="p">);</span><span class="w"> </span><span class="c1">-- '14m'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replacement</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Replaces every instance of the substring matched by the regular expression
<code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code> with <code class="docutils literal notranslate"><span class="pre">replacement</span></code>. <a class="reference external" href="https://docs.oracle.com/en/java/javase/23/docs/api/java.base/java/util/regex/Pattern.html#cg">Capturing groups</a> can be
referenced in <code class="docutils literal notranslate"><span class="pre">replacement</span></code> using <code class="docutils literal notranslate"><span class="pre">$g</span></code> for a numbered group or
<code class="docutils literal notranslate"><span class="pre">${name}</span></code> for a named group. A dollar sign (<code class="docutils literal notranslate"><span class="pre">$</span></code>) may be included in the
replacement by escaping it with a backslash (<code class="docutils literal notranslate"><span class="pre">\$</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_replace</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'(\d+)([ab]) '</span><span class="p">,</span><span class="w"> </span><span class="s1">'3c$2 '</span><span class="p">);</span><span class="w"> </span><span class="c1">-- '3ca 3cb 14m'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">regexp_replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Replaces every instance of the substring matched by the regular expression
<code class="docutils literal notranslate"><span class="pre">pattern</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code> using <code class="docutils literal notranslate"><span class="pre">function</span></code>. The <a class="reference internal" href="lambda.html"><span class="doc">lambda expression</span></a>
<code class="docutils literal notranslate"><span class="pre">function</span></code> is invoked for each match with the <a class="reference external" href="https://docs.oracle.com/en/java/javase/23/docs/api/java.base/java/util/regex/Pattern.html#cg">capturing groups</a> passed as an
array. Capturing group numbers start at one; there is no group for the entire match
(if you need this, surround the entire expression with parenthesis).</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_replace</span><span class="p">(</span><span class="s1">'new york'</span><span class="p">,</span><span class="w"> </span><span class="s1">'(\w)(\w*)'</span><span class="p">,</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">upper</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">lower</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="mi">2</span><span class="p">]));</span><span class="w"> </span><span class="c1">--'New York'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="regexp_split">
<span class="sig-name descname"><span class="pre">regexp_split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="regexp.html#regexp_split" title="Link to this definition">#</a></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> using the regular expression <code class="docutils literal notranslate"><span class="pre">pattern</span></code> and returns an
array. Trailing empty strings are preserved:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regexp_split</span><span class="p">(</span><span class="s1">'1a 2b 14m'</span><span class="p">,</span><span class="w"> </span><span class="s1">'\s*[a-z]+\s*'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- [1, 2, 14, ]</span>
</pre></div>
</div>
</dd></dl>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="qdigest.html" title="Quantile digest functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Quantile digest functions </span>
              </div>
            </a>
          
          
            <a href="session.html" title="Session information"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Session information </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>