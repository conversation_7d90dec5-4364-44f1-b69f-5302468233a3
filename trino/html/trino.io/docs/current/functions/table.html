<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Table functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="table.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Teradata functions" href="teradata.html" />
    <link rel="prev" title="System information" href="system.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="table.html#functions/table" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Table functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Table </label>
    
      <a href="table.html#" class="md-nav__link md-nav__link--active">Table</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="table.html#built-in-table-functions" class="md-nav__link">Built-in table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table.html#exclude-columns-table-function" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">exclude_columns</span></code> table function</a>
        </li>
        <li class="md-nav__item"><a href="table.html#sequence-table-function" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sequence</span></code> table function</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table.html#table-function-invocation" class="md-nav__link">Table function invocation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table.html#function-resolution" class="md-nav__link">Function resolution</a>
        </li>
        <li class="md-nav__item"><a href="table.html#arguments" class="md-nav__link">Arguments</a>
        </li>
        <li class="md-nav__item"><a href="table.html#argument-passing-conventions" class="md-nav__link">Argument passing conventions</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="table.html#built-in-table-functions" class="md-nav__link">Built-in table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table.html#exclude-columns-table-function" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">exclude_columns</span></code> table function</a>
        </li>
        <li class="md-nav__item"><a href="table.html#sequence-table-function" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sequence</span></code> table function</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table.html#table-function-invocation" class="md-nav__link">Table function invocation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table.html#function-resolution" class="md-nav__link">Function resolution</a>
        </li>
        <li class="md-nav__item"><a href="table.html#arguments" class="md-nav__link">Arguments</a>
        </li>
        <li class="md-nav__item"><a href="table.html#argument-passing-conventions" class="md-nav__link">Argument passing conventions</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="table-functions">
<h1 id="functions-table--page-root">Table functions<a class="headerlink" href="table.html#functions-table--page-root" title="Link to this heading">#</a></h1>
<p>A table function is a function returning a table. It can be invoked inside the
<code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause of a query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">my_function</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">))</span>
</pre></div>
</div>
<p>The row type of the returned table can depend on the arguments passed with
invocation of the function. If different row types can be returned, the
function is a <strong>polymorphic table function</strong>.</p>
<p>Polymorphic table functions allow you to dynamically invoke custom logic from
within the SQL query. They can be used for working with external systems as
well as for enhancing Trino with capabilities going beyond the SQL standard.</p>
<p>For the list of built-in table functions available in Trino, see <a class="reference internal" href="table.html#built-in-table-functions"><span class="std std-ref">built-in
table functions</span></a>.</p>
<p>Trino supports adding custom table functions. They are declared by connectors
through implementing dedicated interfaces. For guidance on adding new table
functions, see the <a class="reference internal" href="../develop/table-functions.html"><span class="doc std std-doc">developer guide</span></a>.</p>
<p>Connectors offer support for different functions on a per-connector basis. For
more information about supported table functions, refer to the <a class="reference internal" href="../connector.html"><span class="doc std std-doc">connector
documentation</span></a>.</p>
<section id="built-in-table-functions">
<span id="id1"></span><h2 id="built-in-table-functions">Built-in table functions<a class="headerlink" href="table.html#built-in-table-functions" title="Link to this heading">#</a></h2>
<section id="exclude-columns-table-function">
<span id="id2"></span><h3 id="exclude-columns-table-function"><code class="docutils literal notranslate"><span class="pre">exclude_columns</span></code> table function<a class="headerlink" href="table.html#exclude-columns-table-function" title="Link to this heading">#</a></h3>
<p>Use the <code class="docutils literal notranslate"><span class="pre">exclude_columns</span></code> table function to return a new table based on an input
table <code class="docutils literal notranslate"><span class="pre">table</span></code>, with the exclusion of all columns specified in <code class="docutils literal notranslate"><span class="pre">descriptor</span></code>:</p>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">exclude_columns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">input</span> <span class="pre">=&gt;</span> <span class="pre">table</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">columns</span> <span class="pre">=&gt;</span> <span class="pre">descriptor</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">table</span></span></span></dt>
<dd><p>The argument <code class="docutils literal notranslate"><span class="pre">input</span></code> is a table or a query.
The argument <code class="docutils literal notranslate"><span class="pre">columns</span></code> is a descriptor without types.</p>
</dd></dl>
<p>Example query using the orders table from the TPC-H dataset, provided by the
<a class="reference internal" href="../connector/tpch.html"><span class="doc std std-doc">TPC-H connector</span></a>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">exclude_columns</span><span class="p">(</span>
<span class="w">                        </span><span class="k">input</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">orders</span><span class="p">),</span>
<span class="w">                        </span><span class="n">columns</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">DESCRIPTOR</span><span class="p">(</span><span class="n">clerk</span><span class="p">,</span><span class="w"> </span><span class="k">comment</span><span class="p">)));</span>
</pre></div>
</div>
<p>The table function is useful for queries where you want to return nearly all
columns from tables with many columns. You can avoid enumerating all columns,
and only need to specify the columns to exclude.</p>
</section>
<section id="sequence-table-function">
<span id="id3"></span><h3 id="sequence-table-function"><code class="docutils literal notranslate"><span class="pre">sequence</span></code> table function<a class="headerlink" href="table.html#sequence-table-function" title="Link to this heading">#</a></h3>
<p>Use the <code class="docutils literal notranslate"><span class="pre">sequence</span></code> table function to return a table with a single column
<code class="docutils literal notranslate"><span class="pre">sequential_number</span></code> containing a sequence of bigint:</p>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">sequence</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span> <span class="pre">=&gt;</span> <span class="pre">bigint</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span> <span class="pre">=&gt;</span> <span class="pre">bigint</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">step</span> <span class="pre">=&gt;</span> <span class="pre">bigint)</span> <span class="pre">-&gt;</span> <span class="pre">table(sequential_number</span> <span class="pre">bigint</span></span></em><span class="sig-paren">)</span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">start</span></code> is the first element in the sequence. The default value is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">stop</span></code> is the end of the range, inclusive. The last element in the
sequence is equal to <code class="docutils literal notranslate"><span class="pre">stop</span></code>, or it is the last value within range,
reachable by steps.</p>
<p><code class="docutils literal notranslate"><span class="pre">step</span></code> is the difference between subsequent values. The default value is
<code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>
<p>Example query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span>
<span class="w">                </span><span class="k">start</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">1000000</span><span class="p">,</span>
<span class="w">                </span><span class="n">stop</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="o">-</span><span class="mi">2000000</span><span class="p">,</span>
<span class="w">                </span><span class="n">step</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="o">-</span><span class="mi">3</span><span class="p">));</span>
</pre></div>
</div>
<p>The result of the <code class="docutils literal notranslate"><span class="pre">sequence</span></code> table function might not be ordered. If required,
enforce ordering in the enclosing query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">sequence</span><span class="p">(</span>
<span class="w">                </span><span class="k">start</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">                </span><span class="n">stop</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">                </span><span class="n">step</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">5</span><span class="p">))</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">sequential_number</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
<section id="table-function-invocation">
<h2 id="table-function-invocation">Table function invocation<a class="headerlink" href="table.html#table-function-invocation" title="Link to this heading">#</a></h2>
<p>You invoke a table function in the <code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause of a query. Table function
invocation syntax is similar to a scalar function call.</p>
<section id="function-resolution">
<h3 id="function-resolution">Function resolution<a class="headerlink" href="table.html#function-resolution" title="Link to this heading">#</a></h3>
<p>Every table function is provided by a catalog, and it belongs to a schema in
the catalog. You can qualify the function name with a schema name, or with
catalog and schema names:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="k">schema_name</span><span class="p">.</span><span class="n">my_function</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">))</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="k">catalog_name</span><span class="p">.</span><span class="k">schema_name</span><span class="p">.</span><span class="n">my_function</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">))</span>
</pre></div>
</div>
<p>Otherwise, the standard Trino name resolution is applied. The connection
between the function and the catalog must be identified, because the function
is executed by the corresponding connector. If the function is not registered
by the specified catalog, the query fails.</p>
<p>The table function name is resolved case-insensitive, analogically to scalar
function and table resolution in Trino.</p>
</section>
<section id="arguments">
<h3 id="arguments">Arguments<a class="headerlink" href="table.html#arguments" title="Link to this heading">#</a></h3>
<p>There are three types of arguments.</p>
<ol class="arabic simple">
<li><p>Scalar arguments</p></li>
</ol>
<p>They must be constant expressions, and they can be of any SQL type, which is
compatible with the declared argument type:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">factor</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">42</span>
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p>Descriptor arguments</p></li>
</ol>
<p>Descriptors consist of fields with names and optional data types:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">schema</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">DESCRIPTOR</span><span class="p">(</span><span class="n">id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="n">columns</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">DESCRIPTOR</span><span class="p">(</span><span class="nb">date</span><span class="p">,</span><span class="w"> </span><span class="n">status</span><span class="p">,</span><span class="w"> </span><span class="k">comment</span><span class="p">)</span>
</pre></div>
</div>
<p>To pass <code class="docutils literal notranslate"><span class="pre">null</span></code> for a descriptor, use:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">schema</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="k">null</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">DESCRIPTOR</span><span class="p">)</span>
</pre></div>
</div>
<ol class="arabic simple" start="3">
<li><p>Table arguments</p></li>
</ol>
<p>You can pass a table name, or a query. Use the keyword <code class="docutils literal notranslate"><span class="pre">TABLE</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">input</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">orders</span><span class="p">)</span>
<span class="k">data</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">region</span><span class="p">,</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">region</span><span class="p">.</span><span class="n">regionkey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">nation</span><span class="p">.</span><span class="n">regionkey</span><span class="p">)</span>
</pre></div>
</div>
<p>If the table argument is declared as <a class="reference internal" href="../develop/table-functions.html#tf-set-or-row-semantics"><span class="std std-ref">set semantics</span></a>,
you can specify partitioning and ordering. Each partition is processed
independently by the table function. If you do not specify partitioning, the
argument is processed as a single partition. You can also specify
<code class="docutils literal notranslate"><span class="pre">PRUNE</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code> or <code class="docutils literal notranslate"><span class="pre">KEEP</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code>. With <code class="docutils literal notranslate"><span class="pre">PRUNE</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code> you
declare that you are not interested in the function result if the argument is
empty. This information is used by the Trino engine to optimize the query. The
<code class="docutils literal notranslate"><span class="pre">KEEP</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code> option indicates that the function should be executed even
if the table argument is empty. By specifying <code class="docutils literal notranslate"><span class="pre">KEEP</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code> or
<code class="docutils literal notranslate"><span class="pre">PRUNE</span> <span class="pre">WHEN</span> <span class="pre">EMPTY</span></code>, you override the property set for the argument by the
function author.</p>
<p>The following example shows how the table argument properties should be ordered:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">input</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">orders</span><span class="p">)</span>
<span class="w">                    </span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">orderstatus</span>
<span class="w">                    </span><span class="n">KEEP</span><span class="w"> </span><span class="k">WHEN</span><span class="w"> </span><span class="n">EMPTY</span>
<span class="w">                    </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">orderdate</span>
</pre></div>
</div>
</section>
<section id="argument-passing-conventions">
<h3 id="argument-passing-conventions">Argument passing conventions<a class="headerlink" href="table.html#argument-passing-conventions" title="Link to this heading">#</a></h3>
<p>There are two conventions of passing arguments to a table function:</p>
<ul>
<li><p><strong>Arguments passed by name</strong>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">my_function</span><span class="p">(</span><span class="k">row_count</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="n">column_count</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">1</span><span class="p">))</span>
</pre></div>
</div>
</li>
</ul>
<p>In this convention, you can pass the arguments in arbitrary order. Arguments
declared with default values can be skipped. Argument names are resolved
case-sensitive, and with automatic uppercasing of unquoted names.</p>
<ul>
<li><p><strong>Arguments passed positionally</strong>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">my_function</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">))</span>
</pre></div>
</div>
</li>
</ul>
<p>In this convention, you must follow the order in which the arguments are
declared. You can skip a suffix of the argument list, provided that all the
skipped arguments are declared with default values.</p>
<p>You cannot mix the argument conventions in one invocation.</p>
<p>You can also use parameters in arguments:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">PREPARE</span><span class="w"> </span><span class="n">stmt</span><span class="w"> </span><span class="k">FROM</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">TABLE</span><span class="p">(</span><span class="n">my_function</span><span class="p">(</span><span class="k">row_count</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">column_count</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="o">?</span><span class="p">));</span>

<span class="k">EXECUTE</span><span class="w"> </span><span class="n">stmt</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="system.html" title="System information"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> System information </span>
              </div>
            </a>
          
          
            <a href="teradata.html" title="Teradata functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Teradata functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>