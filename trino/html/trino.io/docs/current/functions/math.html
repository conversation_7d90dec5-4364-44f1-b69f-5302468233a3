<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Mathematical functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="math.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Quantile digest functions" href="qdigest.html" />
    <link rel="prev" title="Map functions and operators" href="map.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="math.html#functions/math" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Mathematical functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Math </label>
    
      <a href="math.html#" class="md-nav__link md-nav__link--active">Math</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="math.html#mathematical-operators" class="md-nav__link">Mathematical operators</a>
        </li>
        <li class="md-nav__item"><a href="math.html#mathematical-functions" class="md-nav__link">Mathematical functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#abs" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">abs()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cbrt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cbrt()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ceil" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ceil()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ceiling" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ceiling()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#degrees" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">degrees()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#e" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">e()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#exp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">exp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#floor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">floor()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ln" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ln()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log2()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log10" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log10()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#mod" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mod()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#pi" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">pi()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#pow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">pow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#power" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">power()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#radians" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">radians()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#round" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">round()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sign" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sign()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sqrt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sqrt()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#truncate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">truncate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#width_bucket" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">width_bucket()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#random-functions" class="md-nav__link">Random functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#rand" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rand()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#random" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">random()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#trigonometric-functions" class="md-nav__link">Trigonometric functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#acos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">acos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#asin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">asin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#atan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">atan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#atan2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">atan2()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cosh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosh()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sinh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sinh()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#tan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">tan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#tanh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">tanh()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#geometric-functions" class="md-nav__link">Geometric functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#cosine_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosine_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cosine_similarity" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosine_similarity()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#floating-point-functions" class="md-nav__link">Floating point functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#infinity" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">infinity()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_finite" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_finite()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_infinite" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_infinite()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_nan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_nan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#nan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nan()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#base-conversion-functions" class="md-nav__link">Base conversion functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#from_base" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#to_base" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#statistical-functions" class="md-nav__link">Statistical functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#t_pdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">t_pdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#wilson_interval_lower" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">wilson_interval_lower()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#wilson_interval_upper" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">wilson_interval_upper()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#cumulative-distribution-functions" class="md-nav__link">Cumulative distribution functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#beta_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">beta_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#inverse_beta_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">inverse_beta_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#inverse_normal_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">inverse_normal_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#normal_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">normal_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#t_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">t_cdf()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="math.html#mathematical-operators" class="md-nav__link">Mathematical operators</a>
        </li>
        <li class="md-nav__item"><a href="math.html#mathematical-functions" class="md-nav__link">Mathematical functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#abs" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">abs()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cbrt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cbrt()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ceil" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ceil()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ceiling" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ceiling()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#degrees" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">degrees()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#e" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">e()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#exp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">exp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#floor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">floor()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#ln" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ln()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log2()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#log10" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">log10()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#mod" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mod()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#pi" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">pi()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#pow" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">pow()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#power" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">power()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#radians" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">radians()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#round" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">round()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sign" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sign()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sqrt" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sqrt()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#truncate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">truncate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#width_bucket" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">width_bucket()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#random-functions" class="md-nav__link">Random functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#rand" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rand()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#random" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">random()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#trigonometric-functions" class="md-nav__link">Trigonometric functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#acos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">acos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#asin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">asin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#atan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">atan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#atan2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">atan2()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cosh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosh()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#sinh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sinh()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#tan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">tan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#tanh" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">tanh()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#geometric-functions" class="md-nav__link">Geometric functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#cosine_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosine_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#cosine_similarity" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cosine_similarity()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#floating-point-functions" class="md-nav__link">Floating point functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#infinity" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">infinity()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_finite" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_finite()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_infinite" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_infinite()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#is_nan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">is_nan()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#nan" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nan()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#base-conversion-functions" class="md-nav__link">Base conversion functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#from_base" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_base()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#to_base" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_base()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#statistical-functions" class="md-nav__link">Statistical functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#t_pdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">t_pdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#wilson_interval_lower" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">wilson_interval_lower()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#wilson_interval_upper" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">wilson_interval_upper()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="math.html#cumulative-distribution-functions" class="md-nav__link">Cumulative distribution functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="math.html#beta_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">beta_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#inverse_beta_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">inverse_beta_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#inverse_normal_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">inverse_normal_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#normal_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">normal_cdf()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="math.html#t_cdf" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">t_cdf()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="mathematical-functions-and-operators">
<h1 id="functions-math--page-root">Mathematical functions and operators<a class="headerlink" href="math.html#functions-math--page-root" title="Link to this heading">#</a></h1>
<section id="mathematical-operators">
<span id="id1"></span><h2 id="mathematical-operators">Mathematical operators<a class="headerlink" href="math.html#mathematical-operators" title="Link to this heading">#</a></h2>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Operator</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">+</span></code></p></td>
<td><p>Addition</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">-</span></code></p></td>
<td><p>Subtraction</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">*</span></code></p></td>
<td><p>Multiplication</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">/</span></code></p></td>
<td><p>Division (integer division performs truncation)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%</span></code></p></td>
<td><p>Modulus (remainder)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="mathematical-functions">
<h2 id="mathematical-functions">Mathematical functions<a class="headerlink" href="math.html#mathematical-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="abs">
<span class="sig-name descname"><span class="pre">abs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#abs" title="Link to this definition">#</a></dt>
<dd><p>Returns the absolute value of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="cbrt">
<span class="sig-name descname"><span class="pre">cbrt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#cbrt" title="Link to this definition">#</a></dt>
<dd><p>Returns the cube root of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ceil">
<span class="sig-name descname"><span class="pre">ceil</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#ceil" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="math.html#ceiling" title="ceiling"><code class="xref py py-func docutils literal notranslate"><span class="pre">ceiling()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ceiling">
<span class="sig-name descname"><span class="pre">ceiling</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#ceiling" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> rounded up to the nearest integer.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="degrees">
<span class="sig-name descname"><span class="pre">degrees</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#degrees" title="Link to this definition">#</a></dt>
<dd><p>Converts angle <code class="docutils literal notranslate"><span class="pre">x</span></code> in radians to degrees.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="e">
<span class="sig-name descname"><span class="pre">e</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#e" title="Link to this definition">#</a></dt>
<dd><p>Returns the constant Euler’s number.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="exp">
<span class="sig-name descname"><span class="pre">exp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#exp" title="Link to this definition">#</a></dt>
<dd><p>Returns Euler’s number raised to the power of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="floor">
<span class="sig-name descname"><span class="pre">floor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#floor" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> rounded down to the nearest integer.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ln">
<span class="sig-name descname"><span class="pre">ln</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#ln" title="Link to this definition">#</a></dt>
<dd><p>Returns the natural logarithm of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="log">
<span class="sig-name descname"><span class="pre">log</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#log" title="Link to this definition">#</a></dt>
<dd><p>Returns the base <code class="docutils literal notranslate"><span class="pre">b</span></code> logarithm of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="log2">
<span class="sig-name descname"><span class="pre">log2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#log2" title="Link to this definition">#</a></dt>
<dd><p>Returns the base 2 logarithm of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="log10">
<span class="sig-name descname"><span class="pre">log10</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#log10" title="Link to this definition">#</a></dt>
<dd><p>Returns the base 10 logarithm of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="mod">
<span class="sig-name descname"><span class="pre">mod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">m</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#mod" title="Link to this definition">#</a></dt>
<dd><p>Returns the modulus (remainder) of <code class="docutils literal notranslate"><span class="pre">n</span></code> divided by <code class="docutils literal notranslate"><span class="pre">m</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="pi">
<span class="sig-name descname"><span class="pre">pi</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#pi" title="Link to this definition">#</a></dt>
<dd><p>Returns the constant Pi.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="pow">
<span class="sig-name descname"><span class="pre">pow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#pow" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="math.html#power" title="power"><code class="xref py py-func docutils literal notranslate"><span class="pre">power()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="power">
<span class="sig-name descname"><span class="pre">power</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#power" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> raised to the power of <code class="docutils literal notranslate"><span class="pre">p</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="radians">
<span class="sig-name descname"><span class="pre">radians</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#radians" title="Link to this definition">#</a></dt>
<dd><p>Converts angle <code class="docutils literal notranslate"><span class="pre">x</span></code> in degrees to radians.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="round">
<span class="sig-name descname"><span class="pre">round</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#round" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> rounded to the nearest integer.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">round</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">d</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> rounded to <code class="docutils literal notranslate"><span class="pre">d</span></code> decimal places.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sign">
<span class="sig-name descname"><span class="pre">sign</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="math.html#sign" title="Link to this definition">#</a></dt>
<dd><p>Returns the signum function of <code class="docutils literal notranslate"><span class="pre">x</span></code>, that is:</p>
<ul class="simple">
<li><p>0 if the argument is 0,</p></li>
<li><p>1 if the argument is greater than 0,</p></li>
<li><p>-1 if the argument is less than 0.</p></li>
</ul>
<p>For floating point arguments, the function additionally returns:</p>
<ul class="simple">
<li><p>-0 if the argument is -0,</p></li>
<li><p>NaN if the argument is NaN,</p></li>
<li><p>1 if the argument is +Infinity,</p></li>
<li><p>-1 if the argument is -Infinity.</p></li>
</ul>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sqrt">
<span class="sig-name descname"><span class="pre">sqrt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#sqrt" title="Link to this definition">#</a></dt>
<dd><p>Returns the square root of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="truncate">
<span class="sig-name descname"><span class="pre">truncate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#truncate" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">x</span></code> rounded to integer by dropping digits after decimal point.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="width_bucket">
<span class="sig-name descname"><span class="pre">width_bucket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bound1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bound2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="math.html#width_bucket" title="Link to this definition">#</a></dt>
<dd><p>Returns the bin number of <code class="docutils literal notranslate"><span class="pre">x</span></code> in an equi-width histogram with the
specified <code class="docutils literal notranslate"><span class="pre">bound1</span></code> and <code class="docutils literal notranslate"><span class="pre">bound2</span></code> bounds and <code class="docutils literal notranslate"><span class="pre">n</span></code> number of buckets.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">width_bucket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bins</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span></dt>
<dd><p>Returns the bin number of <code class="docutils literal notranslate"><span class="pre">x</span></code> according to the bins specified by the
array <code class="docutils literal notranslate"><span class="pre">bins</span></code>. The <code class="docutils literal notranslate"><span class="pre">bins</span></code> parameter must be an array of doubles and is
assumed to be in sorted ascending order.</p>
</dd></dl>
</section>
<section id="random-functions">
<h2 id="random-functions">Random functions<a class="headerlink" href="math.html#random-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="rand">
<span class="sig-name descname"><span class="pre">rand</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#rand" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="math.html#random" title="random"><code class="xref py py-func docutils literal notranslate"><span class="pre">random()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="random">
<span class="sig-name descname"><span class="pre">random</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#random" title="Link to this definition">#</a></dt>
<dd><p>Returns a pseudo-random value in the range 0.0 &lt;= x &lt; 1.0.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">random</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span></dt>
<dd><p>Returns a pseudo-random number between 0 and n (exclusive).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">random</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">m</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span></dt>
<dd><p>Returns a pseudo-random number between m and n (exclusive).</p>
</dd></dl>
</section>
<section id="trigonometric-functions">
<h2 id="trigonometric-functions">Trigonometric functions<a class="headerlink" href="math.html#trigonometric-functions" title="Link to this heading">#</a></h2>
<p>All trigonometric function arguments are expressed in radians.
See unit conversion functions <a class="reference internal" href="math.html#degrees" title="degrees"><code class="xref py py-func docutils literal notranslate"><span class="pre">degrees()</span></code></a> and <a class="reference internal" href="math.html#radians" title="radians"><code class="xref py py-func docutils literal notranslate"><span class="pre">radians()</span></code></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="acos">
<span class="sig-name descname"><span class="pre">acos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#acos" title="Link to this definition">#</a></dt>
<dd><p>Returns the arc cosine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="asin">
<span class="sig-name descname"><span class="pre">asin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#asin" title="Link to this definition">#</a></dt>
<dd><p>Returns the arc sine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="atan">
<span class="sig-name descname"><span class="pre">atan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#atan" title="Link to this definition">#</a></dt>
<dd><p>Returns the arc tangent of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="atan2">
<span class="sig-name descname"><span class="pre">atan2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#atan2" title="Link to this definition">#</a></dt>
<dd><p>Returns the arc tangent of <code class="docutils literal notranslate"><span class="pre">y</span> <span class="pre">/</span> <span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="cos">
<span class="sig-name descname"><span class="pre">cos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#cos" title="Link to this definition">#</a></dt>
<dd><p>Returns the cosine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="cosh">
<span class="sig-name descname"><span class="pre">cosh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#cosh" title="Link to this definition">#</a></dt>
<dd><p>Returns the hyperbolic cosine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sin">
<span class="sig-name descname"><span class="pre">sin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#sin" title="Link to this definition">#</a></dt>
<dd><p>Returns the sine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="sinh">
<span class="sig-name descname"><span class="pre">sinh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#sinh" title="Link to this definition">#</a></dt>
<dd><p>Returns the hyperbolic sine of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="tan">
<span class="sig-name descname"><span class="pre">tan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#tan" title="Link to this definition">#</a></dt>
<dd><p>Returns the tangent of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="tanh">
<span class="sig-name descname"><span class="pre">tanh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#tanh" title="Link to this definition">#</a></dt>
<dd><p>Returns the hyperbolic tangent of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
</section>
<section id="geometric-functions">
<h2 id="geometric-functions">Geometric functions<a class="headerlink" href="math.html#geometric-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="cosine_distance">
<span class="sig-name descname"><span class="pre">cosine_distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array(double)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">array(double)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#cosine_distance" title="Link to this definition">#</a></dt>
<dd><p>Calculates the cosine distance between two dense vectors:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">cosine_distance</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">.</span><span class="mi">0</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">3</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">.</span><span class="mi">0</span><span class="p">]);</span>
<span class="c1">-- 0.01613008990009257</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="cosine_similarity">
<span class="sig-name descname"><span class="pre">cosine_similarity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array(double)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">array(double)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#cosine_similarity" title="Link to this definition">#</a></dt>
<dd><p>Calculates the cosine similarity of two dense vectors:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">cosine_similarity</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">.</span><span class="mi">0</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">3</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">.</span><span class="mi">0</span><span class="p">]);</span>
<span class="c1">-- 0.9838699100999074</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">cosine_similarity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span></dt>
<dd><p>Calculates the cosine similarity of two sparse vectors:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">cosine_similarity</span><span class="p">(</span><span class="k">MAP</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'a'</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">]),</span><span class="w"> </span><span class="k">MAP</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'a'</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">2</span><span class="p">.</span><span class="mi">0</span><span class="p">]));</span>
<span class="c1">-- 1.0</span>
</pre></div>
</div>
</dd></dl>
</section>
<section id="floating-point-functions">
<h2 id="floating-point-functions">Floating point functions<a class="headerlink" href="math.html#floating-point-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="infinity">
<span class="sig-name descname"><span class="pre">infinity</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#infinity" title="Link to this definition">#</a></dt>
<dd><p>Returns the constant representing positive infinity.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="is_finite">
<span class="sig-name descname"><span class="pre">is_finite</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="math.html#is_finite" title="Link to this definition">#</a></dt>
<dd><p>Determine if <code class="docutils literal notranslate"><span class="pre">x</span></code> is finite.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="is_infinite">
<span class="sig-name descname"><span class="pre">is_infinite</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="math.html#is_infinite" title="Link to this definition">#</a></dt>
<dd><p>Determine if <code class="docutils literal notranslate"><span class="pre">x</span></code> is infinite.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="is_nan">
<span class="sig-name descname"><span class="pre">is_nan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="math.html#is_nan" title="Link to this definition">#</a></dt>
<dd><p>Determine if <code class="docutils literal notranslate"><span class="pre">x</span></code> is not-a-number.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="nan">
<span class="sig-name descname"><span class="pre">nan</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#nan" title="Link to this definition">#</a></dt>
<dd><p>Returns the constant representing not-a-number.</p>
</dd></dl>
</section>
<section id="base-conversion-functions">
<h2 id="base-conversion-functions">Base conversion functions<a class="headerlink" href="math.html#base-conversion-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="from_base">
<span class="sig-name descname"><span class="pre">from_base</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">radix</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="math.html#from_base" title="Link to this definition">#</a></dt>
<dd><p>Returns the value of <code class="docutils literal notranslate"><span class="pre">string</span></code> interpreted as a base-<code class="docutils literal notranslate"><span class="pre">radix</span></code> number.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_base">
<span class="sig-name descname"><span class="pre">to_base</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">radix</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="math.html#to_base" title="Link to this definition">#</a></dt>
<dd><p>Returns the base-<code class="docutils literal notranslate"><span class="pre">radix</span></code> representation of <code class="docutils literal notranslate"><span class="pre">x</span></code>.</p>
</dd></dl>
</section>
<section id="statistical-functions">
<h2 id="statistical-functions">Statistical functions<a class="headerlink" href="math.html#statistical-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="t_pdf">
<span class="sig-name descname"><span class="pre">t_pdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#t_pdf" title="Link to this definition">#</a></dt>
<dd><p>Computes the Student’s t-distribution probability density function for given x and
degrees of freedom (df). The x must be a real value and degrees of freedom must be
an integer and positive value.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="wilson_interval_lower">
<span class="sig-name descname"><span class="pre">wilson_interval_lower</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">successes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trials</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">z</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#wilson_interval_lower" title="Link to this definition">#</a></dt>
<dd><p>Returns the lower bound of the Wilson score interval of a Bernoulli trial process
at a confidence specified by the z-score <code class="docutils literal notranslate"><span class="pre">z</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="wilson_interval_upper">
<span class="sig-name descname"><span class="pre">wilson_interval_upper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">successes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trials</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">z</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#wilson_interval_upper" title="Link to this definition">#</a></dt>
<dd><p>Returns the upper bound of the Wilson score interval of a Bernoulli trial process
at a confidence specified by the z-score <code class="docutils literal notranslate"><span class="pre">z</span></code>.</p>
</dd></dl>
</section>
<section id="cumulative-distribution-functions">
<h2 id="cumulative-distribution-functions">Cumulative distribution functions<a class="headerlink" href="math.html#cumulative-distribution-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="beta_cdf">
<span class="sig-name descname"><span class="pre">beta_cdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#beta_cdf" title="Link to this definition">#</a></dt>
<dd><p>Compute the Beta cdf with given a, b parameters:  P(N &lt; v; a, b).
The a, b parameters must be positive real numbers and value v must be a real value.
The value v must lie on the interval [0, 1].</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="inverse_beta_cdf">
<span class="sig-name descname"><span class="pre">inverse_beta_cdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#inverse_beta_cdf" title="Link to this definition">#</a></dt>
<dd><p>Compute the inverse of the Beta cdf with given a, b parameters for the cumulative
probability (p): P(N &lt; n). The a, b parameters must be positive real values.
The probability p must lie on the interval [0, 1].</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="inverse_normal_cdf">
<span class="sig-name descname"><span class="pre">inverse_normal_cdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mean</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#inverse_normal_cdf" title="Link to this definition">#</a></dt>
<dd><p>Compute the inverse of the Normal cdf with given mean and standard
deviation (sd) for the cumulative probability (p): P(N &lt; n). The mean must be
a real value and the standard deviation must be a real and positive value.
The probability p must lie on the interval (0, 1).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="normal_cdf">
<span class="sig-name descname"><span class="pre">normal_cdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mean</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#normal_cdf" title="Link to this definition">#</a></dt>
<dd><p>Compute the Normal cdf with given mean and standard deviation (sd):  P(N &lt; v; mean, sd).
The mean and value v must be real values and the standard deviation must be a real
and positive value.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="t_cdf">
<span class="sig-name descname"><span class="pre">t_cdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="math.html#t_cdf" title="Link to this definition">#</a></dt>
<dd><p>Compute the Student’s t-distribution cumulative density function for given x and degrees of freedom (df).
The x must be a real value and degrees of freedom must be an integer and positive value.</p>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="map.html" title="Map functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Map functions and operators </span>
              </div>
            </a>
          
          
            <a href="qdigest.html" title="Quantile digest functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Quantile digest functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>