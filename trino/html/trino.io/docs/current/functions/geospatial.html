<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Geospatial functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="geospatial.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="HyperLogLog functions" href="hyperloglog.html" />
    <link rel="prev" title="Decimal functions and operators" href="decimal.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="geospatial.html#functions/geospatial" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Geospatial functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Geospatial </label>
    
      <a href="geospatial.html#" class="md-nav__link md-nav__link--active">Geospatial</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="geospatial.html#constructors" class="md-nav__link">Constructors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_AsBinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_AsBinary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_AsText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_AsText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryFromText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryFromText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeomFromBinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeomFromBinary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeomFromKML" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeomFromKML()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_from_hadoop_shape" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_from_hadoop_shape()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_LineFromText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_LineFromText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_LineString" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_LineString()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_MultiPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_MultiPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Polygon" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Polygon()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_spherical_geography" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_spherical_geography()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_geometry()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#relationship-tests" class="md-nav__link">Relationship tests</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_Contains" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Contains()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Crosses" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Crosses()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Disjoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Disjoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Equals" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Equals()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Intersects" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Intersects()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Overlaps" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Overlaps()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Relate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Relate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Touches" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Touches()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Within" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Within()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#operations" class="md-nav__link">Operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#geometry_nearest_points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_nearest_points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_union()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Boundary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Boundary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Buffer" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Buffer()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Difference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Difference()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Envelope" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Envelope()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_EnvelopeAsPts" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_EnvelopeAsPts()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_ExteriorRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_ExteriorRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Intersection" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Intersection()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_SymDifference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_SymDifference()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Union()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#accessors" class="md-nav__link">Accessors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_Area" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Area()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Centroid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Centroid()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_ConvexHull" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_ConvexHull()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_CoordDim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_CoordDim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Dimension" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Dimension()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_InteriorRingN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_InteriorRingN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryType" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryType()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsClosed" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsClosed()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsEmpty" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsEmpty()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsSimple" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsSimple()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsValid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsValid()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_PointN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_PointN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_XMax" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_XMax()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_YMax" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_YMax()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_XMin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_XMin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_YMin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_YMin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_StartPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_StartPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#simplify_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">simplify_geometry()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_EndPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_EndPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_X" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_X()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Y" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Y()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_InteriorRings" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_InteriorRings()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumGeometries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumGeometries()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Geometries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Geometries()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumPoints" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumPoints()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumInteriorRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumInteriorRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_interpolate_point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_interpolate_point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_interpolate_points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_interpolate_points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_locate_point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_locate_point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_invalid_reason" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_invalid_reason()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#great_circle_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">great_circle_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_geojson_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_geojson_geometry()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#from_geojson_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_geojson_geometry()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#aggregations" class="md-nav__link">Aggregations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#convex_hull_agg" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">convex_hull_agg()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_union_agg" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_union_agg()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing-tiles" class="md-nav__link">Bing tiles</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#bing_tile" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_at" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_at()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tiles_around" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tiles_around()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_coordinates" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_coordinates()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_polygon" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_polygon()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_quadkey" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_quadkey()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_zoom_level" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_zoom_level()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_to_bing_tiles" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_to_bing_tiles()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#encoded-polylines" class="md-nav__link">Encoded polylines</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#to_encoded_polyline" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_encoded_polyline()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#from_encoded_polyline" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_encoded_polyline()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="geospatial.html#constructors" class="md-nav__link">Constructors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_AsBinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_AsBinary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_AsText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_AsText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryFromText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryFromText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeomFromBinary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeomFromBinary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeomFromKML" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeomFromKML()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_from_hadoop_shape" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_from_hadoop_shape()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_LineFromText" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_LineFromText()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_LineString" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_LineString()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_MultiPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_MultiPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Polygon" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Polygon()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_spherical_geography" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_spherical_geography()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_geometry()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#relationship-tests" class="md-nav__link">Relationship tests</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_Contains" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Contains()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Crosses" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Crosses()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Disjoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Disjoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Equals" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Equals()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Intersects" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Intersects()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Overlaps" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Overlaps()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Relate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Relate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Touches" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Touches()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Within" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Within()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#operations" class="md-nav__link">Operations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#geometry_nearest_points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_nearest_points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_union()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Boundary" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Boundary()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Buffer" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Buffer()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Difference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Difference()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Envelope" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Envelope()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_EnvelopeAsPts" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_EnvelopeAsPts()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_ExteriorRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_ExteriorRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Intersection" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Intersection()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_SymDifference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_SymDifference()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Union" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Union()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#accessors" class="md-nav__link">Accessors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#ST_Area" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Area()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Centroid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Centroid()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_ConvexHull" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_ConvexHull()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_CoordDim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_CoordDim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Dimension" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Dimension()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_InteriorRingN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_InteriorRingN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_GeometryType" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_GeometryType()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsClosed" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsClosed()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsEmpty" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsEmpty()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsSimple" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsSimple()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_IsValid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_IsValid()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_PointN" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_PointN()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_XMax" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_XMax()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_YMax" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_YMax()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_XMin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_XMin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_YMin" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_YMin()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_StartPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_StartPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#simplify_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">simplify_geometry()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_EndPoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_EndPoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_X" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_X()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Y" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Y()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_InteriorRings" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_InteriorRings()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumGeometries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumGeometries()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_Geometries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_Geometries()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumPoints" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumPoints()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#ST_NumInteriorRing" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ST_NumInteriorRing()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_interpolate_point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_interpolate_point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_interpolate_points" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_interpolate_points()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#line_locate_point" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">line_locate_point()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_invalid_reason" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_invalid_reason()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#great_circle_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">great_circle_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#to_geojson_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_geojson_geometry()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#from_geojson_geometry" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_geojson_geometry()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#aggregations" class="md-nav__link">Aggregations</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#convex_hull_agg" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">convex_hull_agg()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_union_agg" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_union_agg()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing-tiles" class="md-nav__link">Bing tiles</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#bing_tile" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_at" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_at()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tiles_around" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tiles_around()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_coordinates" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_coordinates()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_polygon" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_polygon()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_quadkey" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_quadkey()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#bing_tile_zoom_level" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bing_tile_zoom_level()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#geometry_to_bing_tiles" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">geometry_to_bing_tiles()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#encoded-polylines" class="md-nav__link">Encoded polylines</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="geospatial.html#to_encoded_polyline" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_encoded_polyline()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="geospatial.html#from_encoded_polyline" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_encoded_polyline()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="geospatial-functions">
<h1 id="functions-geospatial--page-root">Geospatial functions<a class="headerlink" href="geospatial.html#functions-geospatial--page-root" title="Link to this heading">#</a></h1>
<p>Trino Geospatial functions that begin with the <code class="docutils literal notranslate"><span class="pre">ST_</span></code> prefix support the SQL/MM specification
and are compliant with the Open Geospatial Consortium’s (OGC) OpenGIS Specifications.
As such, many Trino Geospatial functions require, or more accurately, assume that
geometries that are operated on are both simple and valid. For example, it does not
make sense to calculate the area of a polygon that has a hole defined outside the
polygon, or to construct a polygon from a non-simple boundary line.</p>
<p>Trino Geospatial functions support the Well-Known Text (WKT) and Well-Known Binary (WKB) form of spatial objects:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">POINT</span> <span class="pre">(0</span> <span class="pre">0)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LINESTRING</span> <span class="pre">(0</span> <span class="pre">0,</span> <span class="pre">1</span> <span class="pre">1,</span> <span class="pre">1</span> <span class="pre">2)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POLYGON</span> <span class="pre">((0</span> <span class="pre">0,</span> <span class="pre">4</span> <span class="pre">0,</span> <span class="pre">4</span> <span class="pre">4,</span> <span class="pre">0</span> <span class="pre">4,</span> <span class="pre">0</span> <span class="pre">0),</span> <span class="pre">(1</span> <span class="pre">1,</span> <span class="pre">2</span> <span class="pre">1,</span> <span class="pre">2</span> <span class="pre">2,</span> <span class="pre">1</span> <span class="pre">2,</span> <span class="pre">1</span> <span class="pre">1))</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MULTIPOINT</span> <span class="pre">(0</span> <span class="pre">0,</span> <span class="pre">1</span> <span class="pre">2)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MULTILINESTRING</span> <span class="pre">((0</span> <span class="pre">0,</span> <span class="pre">1</span> <span class="pre">1,</span> <span class="pre">1</span> <span class="pre">2),</span> <span class="pre">(2</span> <span class="pre">3,</span> <span class="pre">3</span> <span class="pre">2,</span> <span class="pre">5</span> <span class="pre">4))</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MULTIPOLYGON</span> <span class="pre">(((0</span> <span class="pre">0,</span> <span class="pre">4</span> <span class="pre">0,</span> <span class="pre">4</span> <span class="pre">4,</span> <span class="pre">0</span> <span class="pre">4,</span> <span class="pre">0</span> <span class="pre">0),</span> <span class="pre">(1</span> <span class="pre">1,</span> <span class="pre">2</span> <span class="pre">1,</span> <span class="pre">2</span> <span class="pre">2,</span> <span class="pre">1</span> <span class="pre">2,</span> <span class="pre">1</span> <span class="pre">1)),</span> <span class="pre">((-1</span> <span class="pre">-1,</span> <span class="pre">-1</span> <span class="pre">-2,</span> <span class="pre">-2</span> <span class="pre">-2,</span> <span class="pre">-2</span> <span class="pre">-1,</span> <span class="pre">-1</span> <span class="pre">-1)))</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GEOMETRYCOLLECTION</span> <span class="pre">(POINT(2</span> <span class="pre">3),</span> <span class="pre">LINESTRING</span> <span class="pre">(2</span> <span class="pre">3,</span> <span class="pre">3</span> <span class="pre">4))</span></code></p></li>
</ul>
<p>Use <a class="reference internal" href="geospatial.html#ST_GeometryFromText" title="ST_GeometryFromText"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeometryFromText()</span></code></a> and <a class="reference internal" href="geospatial.html#ST_GeomFromBinary" title="ST_GeomFromBinary"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeomFromBinary()</span></code></a> functions to create geometry
objects from WKT or WKB.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">SphericalGeography</span></code> type provides native support for spatial features represented on
<em>geographic</em> coordinates (sometimes called <em>geodetic</em> coordinates, or <em>lat/lon</em>, or <em>lon/lat</em>).
Geographic coordinates are spherical coordinates expressed in angular units (degrees).</p>
<p>The basis for the <code class="docutils literal notranslate"><span class="pre">Geometry</span></code> type is a plane. The shortest path between two points on the plane is a
straight line. That means calculations on geometries (areas, distances, lengths, intersections, etc.)
can be calculated using cartesian mathematics and straight line vectors.</p>
<p>The basis for the <code class="docutils literal notranslate"><span class="pre">SphericalGeography</span></code> type is a sphere. The shortest path between two points on the
sphere is a great circle arc. That means that calculations on geographies (areas, distances,
lengths, intersections, etc.) must be calculated on the sphere, using more complicated mathematics.
More accurate measurements that take the actual spheroidal shape of the world into account are not
supported.</p>
<p>Values returned by the measurement functions <a class="reference internal" href="geospatial.html#ST_Distance" title="ST_Distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Distance()</span></code></a> and <a class="reference internal" href="geospatial.html#ST_Length" title="ST_Length"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Length()</span></code></a> are in the unit of meters;
values returned by <a class="reference internal" href="geospatial.html#ST_Area" title="ST_Area"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Area()</span></code></a> are in square meters.</p>
<p>Use <a class="reference internal" href="geospatial.html#to_spherical_geography" title="to_spherical_geography"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_spherical_geography()</span></code></a> function to convert a geometry object to geography object.</p>
<p>For example, <code class="docutils literal notranslate"><span class="pre">ST_Distance(ST_Point(-71.0882,</span> <span class="pre">42.3607),</span> <span class="pre">ST_Point(-74.1197,</span> <span class="pre">40.6976))</span></code> returns
<code class="docutils literal notranslate"><span class="pre">3.4577</span></code> in the unit of the passed-in values on the Euclidean plane, while
<code class="docutils literal notranslate"><span class="pre">ST_Distance(to_spherical_geography(ST_Point(-71.0882,</span> <span class="pre">42.3607)),</span> <span class="pre">to_spherical_geography(ST_Point(-74.1197,</span> <span class="pre">40.6976)))</span></code>
returns <code class="docutils literal notranslate"><span class="pre">312822.179</span></code> in meters.</p>
<section id="constructors">
<h2 id="constructors">Constructors<a class="headerlink" href="geospatial.html#constructors" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="ST_AsBinary">
<span class="sig-name descname"><span class="pre">ST_AsBinary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="geospatial.html#ST_AsBinary" title="Link to this definition">#</a></dt>
<dd><p>Returns the WKB representation of the geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_AsText">
<span class="sig-name descname"><span class="pre">ST_AsText</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#ST_AsText" title="Link to this definition">#</a></dt>
<dd><p>Returns the WKT representation of the geometry. For empty geometries,
<code class="docutils literal notranslate"><span class="pre">ST_AsText(ST_LineFromText('LINESTRING</span> <span class="pre">EMPTY'))</span></code> will produce <code class="docutils literal notranslate"><span class="pre">'MULTILINESTRING</span> <span class="pre">EMPTY'</span></code>
and <code class="docutils literal notranslate"><span class="pre">ST_AsText(ST_Polygon('POLYGON</span> <span class="pre">EMPTY'))</span></code> will produce <code class="docutils literal notranslate"><span class="pre">'MULTIPOLYGON</span> <span class="pre">EMPTY'</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_GeometryFromText">
<span class="sig-name descname"><span class="pre">ST_GeometryFromText</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_GeometryFromText" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type object from WKT representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_GeomFromBinary">
<span class="sig-name descname"><span class="pre">ST_GeomFromBinary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varbinary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_GeomFromBinary" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type object from WKB or EWKB representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_GeomFromKML">
<span class="sig-name descname"><span class="pre">ST_GeomFromKML</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_GeomFromKML" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type object from KML representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_from_hadoop_shape">
<span class="sig-name descname"><span class="pre">geometry_from_hadoop_shape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varbinary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#geometry_from_hadoop_shape" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type object from Spatial Framework for Hadoop representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_LineFromText">
<span class="sig-name descname"><span class="pre">ST_LineFromText</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">LineString</span></span></span><a class="headerlink" href="geospatial.html#ST_LineFromText" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type linestring object from WKT representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_LineString">
<span class="sig-name descname"><span class="pre">ST_LineString</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array(Point)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">LineString</span></span></span><a class="headerlink" href="geospatial.html#ST_LineString" title="Link to this definition">#</a></dt>
<dd><p>Returns a LineString formed from an array of points. If there are fewer than
two non-empty points in the input array, an empty LineString will be returned.
Array elements must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or the same as the previous element.
The returned geometry may not be simple, e.g. may self-intersect or may contain
duplicate vertexes depending on the input.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_MultiPoint">
<span class="sig-name descname"><span class="pre">ST_MultiPoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array(Point)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">MultiPoint</span></span></span><a class="headerlink" href="geospatial.html#ST_MultiPoint" title="Link to this definition">#</a></dt>
<dd><p>Returns a MultiPoint geometry object formed from the specified points. Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if input array is empty.
Array elements must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or empty.
The returned geometry may not be simple and may contain duplicate points if input array has duplicates.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Point">
<span class="sig-name descname"><span class="pre">ST_Point</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">lon</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">double</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lat</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">double</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Point</span></span></span><a class="headerlink" href="geospatial.html#ST_Point" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type point object with the given coordinate values.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Polygon">
<span class="sig-name descname"><span class="pre">ST_Polygon</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Polygon</span></span></span><a class="headerlink" href="geospatial.html#ST_Polygon" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry type polygon object from WKT representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_spherical_geography">
<span class="sig-name descname"><span class="pre">to_spherical_geography</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">SphericalGeography</span></span></span><a class="headerlink" href="geospatial.html#to_spherical_geography" title="Link to this definition">#</a></dt>
<dd><p>Converts a Geometry object to a SphericalGeography object on the sphere of the Earth’s radius. This
function is only applicable to <code class="docutils literal notranslate"><span class="pre">POINT</span></code>, <code class="docutils literal notranslate"><span class="pre">MULTIPOINT</span></code>, <code class="docutils literal notranslate"><span class="pre">LINESTRING</span></code>, <code class="docutils literal notranslate"><span class="pre">MULTILINESTRING</span></code>,
<code class="docutils literal notranslate"><span class="pre">POLYGON</span></code>, <code class="docutils literal notranslate"><span class="pre">MULTIPOLYGON</span></code> geometries defined in 2D space, or <code class="docutils literal notranslate"><span class="pre">GEOMETRYCOLLECTION</span></code> of such
geometries. For each point of the input geometry, it verifies that <code class="docutils literal notranslate"><span class="pre">point.x</span></code> is within
<code class="docutils literal notranslate"><span class="pre">[-180.0,</span> <span class="pre">180.0]</span></code> and <code class="docutils literal notranslate"><span class="pre">point.y</span></code> is within <code class="docutils literal notranslate"><span class="pre">[-90.0,</span> <span class="pre">90.0]</span></code>, and uses them as (longitude, latitude)
degrees to construct the shape of the <code class="docutils literal notranslate"><span class="pre">SphericalGeography</span></code> result.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_geometry">
<span class="sig-name descname"><span class="pre">to_geometry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">SphericalGeography</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#to_geometry" title="Link to this definition">#</a></dt>
<dd><p>Converts a SphericalGeography object to a Geometry object.</p>
</dd></dl>
</section>
<section id="relationship-tests">
<h2 id="relationship-tests">Relationship tests<a class="headerlink" href="geospatial.html#relationship-tests" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Contains">
<span class="sig-name descname"><span class="pre">ST_Contains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">geometryA</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">geometryB</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Contains" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if and only if no points of the second geometry lie in the exterior
of the first geometry, and at least one point of the interior of the first geometry
lies in the interior of the second geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Crosses">
<span class="sig-name descname"><span class="pre">ST_Crosses</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Crosses" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the supplied geometries have some, but not all, interior points in common.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Disjoint">
<span class="sig-name descname"><span class="pre">ST_Disjoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Disjoint" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the give geometries do not <em>spatially intersect</em> –
if they do not share any space together.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Equals">
<span class="sig-name descname"><span class="pre">ST_Equals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Equals" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the given geometries represent the same geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Intersects">
<span class="sig-name descname"><span class="pre">ST_Intersects</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Intersects" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the given geometries spatially intersect in two dimensions
(share any portion of space) and <code class="docutils literal notranslate"><span class="pre">false</span></code> if they do not (they are disjoint).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Overlaps">
<span class="sig-name descname"><span class="pre">ST_Overlaps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Overlaps" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the given geometries share space, are of the same dimension,
but are not completely contained by each other.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Relate">
<span class="sig-name descname"><span class="pre">ST_Relate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Relate" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if first geometry is spatially related to second geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Touches">
<span class="sig-name descname"><span class="pre">ST_Touches</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Touches" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the given geometries have at least one point in common,
but their interiors do not intersect.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Within">
<span class="sig-name descname"><span class="pre">ST_Within</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_Within" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if first geometry is completely inside second geometry.</p>
</dd></dl>
</section>
<section id="operations">
<h2 id="operations">Operations<a class="headerlink" href="geospatial.html#operations" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_nearest_points">
<span class="sig-name descname"><span class="pre">geometry_nearest_points</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#geometry_nearest_points" title="Link to this definition">#</a></dt>
<dd><p>Returns the points on each geometry nearest the other.  If either geometry
is empty, return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  Otherwise, return a row of two Points that have
the minimum distance of any two points on the geometries.  The first Point
will be from the first Geometry argument, the second from the second Geometry
argument.  If there are multiple pairs with the minimum distance, one pair
is chosen arbitrarily.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_union">
<span class="sig-name descname"><span class="pre">geometry_union</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array(Geometry)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#geometry_union" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry that represents the point set union of the input geometries. Performance
of this function, in conjunction with <a class="reference internal" href="aggregate.html#array_agg" title="array_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_agg()</span></code></a> to first aggregate the input geometries,
may be better than <a class="reference internal" href="geospatial.html#geometry_union_agg" title="geometry_union_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_union_agg()</span></code></a>, at the expense of higher memory utilization.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Boundary">
<span class="sig-name descname"><span class="pre">ST_Boundary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Boundary" title="Link to this definition">#</a></dt>
<dd><p>Returns the closure of the combinatorial boundary of this geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Buffer">
<span class="sig-name descname"><span class="pre">ST_Buffer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Buffer" title="Link to this definition">#</a></dt>
<dd><p>Returns the geometry that represents all points whose distance from the specified geometry
is less than or equal to the specified distance. If the points of the geometry are extremely
close together (<code class="docutils literal notranslate"><span class="pre">delta</span> <span class="pre">&lt;</span> <span class="pre">1e-8</span></code>), this might return an empty geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Difference">
<span class="sig-name descname"><span class="pre">ST_Difference</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Difference" title="Link to this definition">#</a></dt>
<dd><p>Returns the geometry value that represents the point set difference of the given geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Envelope">
<span class="sig-name descname"><span class="pre">ST_Envelope</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Envelope" title="Link to this definition">#</a></dt>
<dd><p>Returns the bounding rectangular polygon of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_EnvelopeAsPts">
<span class="sig-name descname"><span class="pre">ST_EnvelopeAsPts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#ST_EnvelopeAsPts" title="Link to this definition">#</a></dt>
<dd><p>Returns an array of two points: the lower left and upper right corners of the bounding
rectangular polygon of a geometry. Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if input geometry is empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_ExteriorRing">
<span class="sig-name descname"><span class="pre">ST_ExteriorRing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_ExteriorRing" title="Link to this definition">#</a></dt>
<dd><p>Returns a line string representing the exterior ring of the input polygon.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Intersection">
<span class="sig-name descname"><span class="pre">ST_Intersection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Intersection" title="Link to this definition">#</a></dt>
<dd><p>Returns the geometry value that represents the point set intersection of two geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_SymDifference">
<span class="sig-name descname"><span class="pre">ST_SymDifference</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_SymDifference" title="Link to this definition">#</a></dt>
<dd><p>Returns the geometry value that represents the point set symmetric difference of two geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Union">
<span class="sig-name descname"><span class="pre">ST_Union</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Union" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry that represents the point set union of the input geometries.</p>
<p>See also:  <a class="reference internal" href="geospatial.html#geometry_union" title="geometry_union"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_union()</span></code></a>, <a class="reference internal" href="geospatial.html#geometry_union_agg" title="geometry_union_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_union_agg()</span></code></a></p>
</dd></dl>
</section>
<section id="accessors">
<h2 id="accessors">Accessors<a class="headerlink" href="geospatial.html#accessors" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Area">
<span class="sig-name descname"><span class="pre">ST_Area</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_Area" title="Link to this definition">#</a></dt>
<dd><p>Returns the 2D Euclidean area of a geometry.</p>
<p>For Point and LineString types, returns 0.0.
For GeometryCollection types, returns the sum of the areas of the individual
geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ST_Area</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">SphericalGeography</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span></dt>
<dd><p>Returns the area of a polygon or multi-polygon in square meters using a spherical model for Earth.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Centroid">
<span class="sig-name descname"><span class="pre">ST_Centroid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_Centroid" title="Link to this definition">#</a></dt>
<dd><p>Returns the point value that is the mathematical centroid of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_ConvexHull">
<span class="sig-name descname"><span class="pre">ST_ConvexHull</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_ConvexHull" title="Link to this definition">#</a></dt>
<dd><p>Returns the minimum convex geometry that encloses all input geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_CoordDim">
<span class="sig-name descname"><span class="pre">ST_CoordDim</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="geospatial.html#ST_CoordDim" title="Link to this definition">#</a></dt>
<dd><p>Returns the coordinate dimension of the geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Dimension">
<span class="sig-name descname"><span class="pre">ST_Dimension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="geospatial.html#ST_Dimension" title="Link to this definition">#</a></dt>
<dd><p>Returns the inherent dimension of this geometry object, which must be
less than or equal to the coordinate dimension.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ST_Distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span></dt>
<dd><p>Returns the 2-dimensional cartesian minimum distance (based on spatial ref)
between two geometries in projected units.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Distance">
<span class="sig-name descname"><span class="pre">ST_Distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SphericalGeography</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">second</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SphericalGeography</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_Distance" title="Link to this definition">#</a></dt>
<dd><p>Returns the great-circle distance in meters between two SphericalGeography points.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_GeometryN">
<span class="sig-name descname"><span class="pre">ST_GeometryN</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_GeometryN" title="Link to this definition">#</a></dt>
<dd><p>Returns the geometry element at a given index (indices start at 1).
If the geometry is a collection of geometries (e.g., GEOMETRYCOLLECTION or MULTI*),
returns the geometry at a given index.
If the given index is less than 1 or greater than the total number of elements in the collection,
returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Use <a class="reference internal" href="geospatial.html#ST_NumGeometries" title="ST_NumGeometries"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_NumGeometries()</span></code></a> to find out the total number of elements.
Singular geometries (e.g., POINT, LINESTRING, POLYGON), are treated as collections of one element.
Empty geometries are treated as empty collections.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_InteriorRingN">
<span class="sig-name descname"><span class="pre">ST_InteriorRingN</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#ST_InteriorRingN" title="Link to this definition">#</a></dt>
<dd><p>Returns the interior ring element at the specified index (indices start at 1). If
the given index is less than 1 or greater than the total number of interior rings
in the input geometry, returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. The input geometry must be a polygon.
Use <a class="reference internal" href="geospatial.html#ST_NumInteriorRing" title="ST_NumInteriorRing"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_NumInteriorRing()</span></code></a> to find out the total number of elements.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_GeometryType">
<span class="sig-name descname"><span class="pre">ST_GeometryType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#ST_GeometryType" title="Link to this definition">#</a></dt>
<dd><p>Returns the type of the geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_IsClosed">
<span class="sig-name descname"><span class="pre">ST_IsClosed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_IsClosed" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the linestring’s start and end points are coincident.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_IsEmpty">
<span class="sig-name descname"><span class="pre">ST_IsEmpty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_IsEmpty" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if this Geometry is an empty geometrycollection, polygon, point etc.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_IsSimple">
<span class="sig-name descname"><span class="pre">ST_IsSimple</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_IsSimple" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if this Geometry has no anomalous geometric points, such as self intersection or self tangency.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_IsRing">
<span class="sig-name descname"><span class="pre">ST_IsRing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_IsRing" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if and only if the line is closed and simple.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_IsValid">
<span class="sig-name descname"><span class="pre">ST_IsValid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="geospatial.html#ST_IsValid" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if and only if the input geometry is well-formed.
Use <a class="reference internal" href="geospatial.html#geometry_invalid_reason" title="geometry_invalid_reason"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_invalid_reason()</span></code></a> to determine why the geometry is not well-formed.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Length">
<span class="sig-name descname"><span class="pre">ST_Length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_Length" title="Link to this definition">#</a></dt>
<dd><p>Returns the length of a linestring or multi-linestring using Euclidean measurement on a
two-dimensional plane (based on spatial ref) in projected units.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ST_Length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">SphericalGeography</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span></dt>
<dd><p>Returns the length of a linestring or multi-linestring on a spherical model of the Earth.
This is equivalent to the sum of great-circle distances between adjacent points on the linestring.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_PointN">
<span class="sig-name descname"><span class="pre">ST_PointN</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">LineString</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Point</span></span></span><a class="headerlink" href="geospatial.html#ST_PointN" title="Link to this definition">#</a></dt>
<dd><p>Returns the vertex of a linestring at a given index (indices start at 1).
If the given index is less than 1 or greater than the total number of elements in the collection,
returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Use <a class="reference internal" href="geospatial.html#ST_NumPoints" title="ST_NumPoints"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_NumPoints()</span></code></a> to find out the total number of elements.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Points">
<span class="sig-name descname"><span class="pre">ST_Points</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#ST_Points" title="Link to this definition">#</a></dt>
<dd><p>Returns an array of points in a linestring.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_XMax">
<span class="sig-name descname"><span class="pre">ST_XMax</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_XMax" title="Link to this definition">#</a></dt>
<dd><p>Returns X maxima of a bounding box of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_YMax">
<span class="sig-name descname"><span class="pre">ST_YMax</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_YMax" title="Link to this definition">#</a></dt>
<dd><p>Returns Y maxima of a bounding box of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_XMin">
<span class="sig-name descname"><span class="pre">ST_XMin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_XMin" title="Link to this definition">#</a></dt>
<dd><p>Returns X minima of a bounding box of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_YMin">
<span class="sig-name descname"><span class="pre">ST_YMin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_YMin" title="Link to this definition">#</a></dt>
<dd><p>Returns Y minima of a bounding box of a geometry.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_StartPoint">
<span class="sig-name descname"><span class="pre">ST_StartPoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">point</span></span></span><a class="headerlink" href="geospatial.html#ST_StartPoint" title="Link to this definition">#</a></dt>
<dd><p>Returns the first point of a LineString geometry as a Point.
This is a shortcut for <code class="docutils literal notranslate"><span class="pre">ST_PointN(geometry,</span> <span class="pre">1)</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="simplify_geometry">
<span class="sig-name descname"><span class="pre">simplify_geometry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">double</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#simplify_geometry" title="Link to this definition">#</a></dt>
<dd><p>Returns a “simplified” version of the input geometry using the Douglas-Peucker algorithm.
Will avoid creating derived geometries (polygons in particular) that are invalid.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_EndPoint">
<span class="sig-name descname"><span class="pre">ST_EndPoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">point</span></span></span><a class="headerlink" href="geospatial.html#ST_EndPoint" title="Link to this definition">#</a></dt>
<dd><p>Returns the last point of a LineString geometry as a Point.
This is a shortcut for <code class="docutils literal notranslate"><span class="pre">ST_PointN(geometry,</span> <span class="pre">ST_NumPoints(geometry))</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_X">
<span class="sig-name descname"><span class="pre">ST_X</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Point</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_X" title="Link to this definition">#</a></dt>
<dd><p>Returns the X coordinate of the point.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Y">
<span class="sig-name descname"><span class="pre">ST_Y</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Point</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#ST_Y" title="Link to this definition">#</a></dt>
<dd><p>Returns the Y coordinate of the point.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_InteriorRings">
<span class="sig-name descname"><span class="pre">ST_InteriorRings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#ST_InteriorRings" title="Link to this definition">#</a></dt>
<dd><p>Returns an array of all interior rings found in the input geometry, or an empty
array if the polygon has no interior rings. Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the input geometry
is empty. The input geometry must be a polygon.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_NumGeometries">
<span class="sig-name descname"><span class="pre">ST_NumGeometries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="geospatial.html#ST_NumGeometries" title="Link to this definition">#</a></dt>
<dd><p>Returns the number of geometries in the collection.
If the geometry is a collection of geometries (e.g., GEOMETRYCOLLECTION or MULTI*),
returns the number of geometries,
for single geometries returns 1,
for empty geometries returns 0.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_Geometries">
<span class="sig-name descname"><span class="pre">ST_Geometries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#ST_Geometries" title="Link to this definition">#</a></dt>
<dd><p>Returns an array of geometries in the specified collection. Returns a one-element array
if the input geometry is not a multi-geometry. Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if input geometry is empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_NumPoints">
<span class="sig-name descname"><span class="pre">ST_NumPoints</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="geospatial.html#ST_NumPoints" title="Link to this definition">#</a></dt>
<dd><p>Returns the number of points in a geometry. This is an extension to the SQL/MM
<code class="docutils literal notranslate"><span class="pre">ST_NumPoints</span></code> function which only applies to point and linestring.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ST_NumInteriorRing">
<span class="sig-name descname"><span class="pre">ST_NumInteriorRing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="geospatial.html#ST_NumInteriorRing" title="Link to this definition">#</a></dt>
<dd><p>Returns the cardinality of the collection of interior rings of a polygon.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="line_interpolate_point">
<span class="sig-name descname"><span class="pre">line_interpolate_point</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">LineString</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">double</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#line_interpolate_point" title="Link to this definition">#</a></dt>
<dd><p>Returns a Point interpolated along a LineString at the fraction given. The fraction
must be between 0 and 1, inclusive.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="line_interpolate_points">
<span class="sig-name descname"><span class="pre">line_interpolate_points</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">LineString</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">double</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">repeated</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#line_interpolate_points" title="Link to this definition">#</a></dt>
<dd><p>Returns an array of Points interpolated along a LineString. The fraction must be
between 0 and 1, inclusive.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="line_locate_point">
<span class="sig-name descname"><span class="pre">line_locate_point</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">LineString</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">Point</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#line_locate_point" title="Link to this definition">#</a></dt>
<dd><p>Returns a float between 0 and 1 representing the location of the closest point on
the LineString to the given Point, as a fraction of total 2d line length.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if a LineString or a Point is empty or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_invalid_reason">
<span class="sig-name descname"><span class="pre">geometry_invalid_reason</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#geometry_invalid_reason" title="Link to this definition">#</a></dt>
<dd><p>Returns the reason for why the input geometry is not valid.
Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the input is valid.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="great_circle_distance">
<span class="sig-name descname"><span class="pre">great_circle_distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">latitude1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">latitude2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude2</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="geospatial.html#great_circle_distance" title="Link to this definition">#</a></dt>
<dd><p>Returns the great-circle distance between two points on Earth’s surface in kilometers.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_geojson_geometry">
<span class="sig-name descname"><span class="pre">to_geojson_geometry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">SphericalGeography</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#to_geojson_geometry" title="Link to this definition">#</a></dt>
<dd><p>Returns the GeoJSON encoded defined by the input spherical geography.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_geojson_geometry">
<span class="sig-name descname"><span class="pre">from_geojson_geometry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">SphericalGeography</span></span></span><a class="headerlink" href="geospatial.html#from_geojson_geometry" title="Link to this definition">#</a></dt>
<dd><p>Returns the spherical geography type object from the GeoJSON representation stripping non geometry key/values.
Feature and FeatureCollection are not supported.</p>
</dd></dl>
</section>
<section id="aggregations">
<h2 id="aggregations">Aggregations<a class="headerlink" href="geospatial.html#aggregations" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="convex_hull_agg">
<span class="sig-name descname"><span class="pre">convex_hull_agg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#convex_hull_agg" title="Link to this definition">#</a></dt>
<dd><p>Returns the minimum convex geometry that encloses all input geometries.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_union_agg">
<span class="sig-name descname"><span class="pre">geometry_union_agg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#geometry_union_agg" title="Link to this definition">#</a></dt>
<dd><p>Returns a geometry that represents the point set union of all input geometries.</p>
</dd></dl>
</section>
<section id="bing-tiles">
<h2 id="bing-tiles">Bing tiles<a class="headerlink" href="geospatial.html#bing-tiles" title="Link to this heading">#</a></h2>
<p>These functions convert between geometries and
<a class="reference external" href="https://msdn.microsoft.com/library/bb259689.aspx">Bing tiles</a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile">
<span class="sig-name descname"><span class="pre">bing_tile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zoom_level</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">BingTile</span></span></span><a class="headerlink" href="geospatial.html#bing_tile" title="Link to this definition">#</a></dt>
<dd><p>Creates a Bing tile object from XY coordinates and a zoom level.
Zoom levels from 1 to 23 are supported.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">bing_tile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">quadKey</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">BingTile</span></span></span></dt>
<dd><p>Creates a Bing tile object from a quadkey.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile_at">
<span class="sig-name descname"><span class="pre">bing_tile_at</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">latitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zoom_level</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">BingTile</span></span></span><a class="headerlink" href="geospatial.html#bing_tile_at" title="Link to this definition">#</a></dt>
<dd><p>Returns a Bing tile at a given zoom level containing a point at a given latitude
and longitude. Latitude must be within <code class="docutils literal notranslate"><span class="pre">[-85.05112878,</span> <span class="pre">85.05112878]</span></code> range.
Longitude must be within <code class="docutils literal notranslate"><span class="pre">[-180,</span> <span class="pre">180]</span></code> range. Zoom levels from 1 to 23 are supported.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tiles_around">
<span class="sig-name descname"><span class="pre">bing_tiles_around</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">latitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zoom_level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#bing_tiles_around" title="Link to this definition">#</a></dt>
<dd><p>Returns a collection of Bing tiles that surround the point specified
by the latitude and longitude arguments at a given zoom level.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">bing_tiles_around</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">latitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zoom_level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">radius_in_km</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Returns a minimum set of Bing tiles at specified zoom level that cover a circle of specified
radius in km around a specified (latitude, longitude) point.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile_coordinates">
<span class="sig-name descname"><span class="pre">bing_tile_coordinates</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tile</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">row&lt;x,</span> <span class="pre">y&gt;</span></span></span><a class="headerlink" href="geospatial.html#bing_tile_coordinates" title="Link to this definition">#</a></dt>
<dd><p>Returns the XY coordinates of a given Bing tile.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile_polygon">
<span class="sig-name descname"><span class="pre">bing_tile_polygon</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tile</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#bing_tile_polygon" title="Link to this definition">#</a></dt>
<dd><p>Returns the polygon representation of a given Bing tile.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile_quadkey">
<span class="sig-name descname"><span class="pre">bing_tile_quadkey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tile</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#bing_tile_quadkey" title="Link to this definition">#</a></dt>
<dd><p>Returns the quadkey of a given Bing tile.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bing_tile_zoom_level">
<span class="sig-name descname"><span class="pre">bing_tile_zoom_level</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tile</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">tinyint</span></span></span><a class="headerlink" href="geospatial.html#bing_tile_zoom_level" title="Link to this definition">#</a></dt>
<dd><p>Returns the zoom level of a given Bing tile.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="geometry_to_bing_tiles">
<span class="sig-name descname"><span class="pre">geometry_to_bing_tiles</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">geometry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zoom_level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="geospatial.html#geometry_to_bing_tiles" title="Link to this definition">#</a></dt>
<dd><p>Returns the minimum set of Bing tiles that fully covers a given geometry at
a given zoom level. Zoom levels from 1 to 23 are supported.</p>
</dd></dl>
</section>
<section id="encoded-polylines">
<h2 id="encoded-polylines">Encoded polylines<a class="headerlink" href="geospatial.html#encoded-polylines" title="Link to this heading">#</a></h2>
<p>These functions convert between geometries and
<a class="reference external" href="https://developers.google.com/maps/documentation/utilities/polylinealgorithm">encoded polylines</a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="to_encoded_polyline">
<span class="sig-name descname"><span class="pre">to_encoded_polyline</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">Geometry</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="geospatial.html#to_encoded_polyline" title="Link to this definition">#</a></dt>
<dd><p>Encodes a linestring or multipoint to a polyline.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_encoded_polyline">
<span class="sig-name descname"><span class="pre">from_encoded_polyline</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">varchar</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">Geometry</span></span></span><a class="headerlink" href="geospatial.html#from_encoded_polyline" title="Link to this definition">#</a></dt>
<dd><p>Decodes a polyline to a linestring.</p>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="decimal.html" title="Decimal functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Decimal functions and operators </span>
              </div>
            </a>
          
          
            <a href="hyperloglog.html" title="HyperLogLog functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> HyperLogLog functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>