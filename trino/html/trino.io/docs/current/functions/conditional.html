<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Conditional expressions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="conditional.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Conversion functions" href="conversion.html" />
    <link rel="prev" title="Comparison functions and operators" href="comparison.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="conditional.html#functions/conditional" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Conditional expressions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Conditional </label>
    
      <a href="conditional.html#" class="md-nav__link md-nav__link--active">Conditional</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="conditional.html#case" class="md-nav__link">CASE</a>
        </li>
        <li class="md-nav__item"><a href="conditional.html#if" class="md-nav__link">IF</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id0" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">if()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#coalesce" class="md-nav__link">COALESCE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">coalesce()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#nullif" class="md-nav__link">NULLIF</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nullif()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#try" class="md-nav__link">TRY</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id3" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">try()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="conditional.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="conditional.html#case" class="md-nav__link">CASE</a>
        </li>
        <li class="md-nav__item"><a href="conditional.html#if" class="md-nav__link">IF</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id0" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">if()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#coalesce" class="md-nav__link">COALESCE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id1" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">coalesce()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#nullif" class="md-nav__link">NULLIF</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id2" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nullif()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="conditional.html#try" class="md-nav__link">TRY</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="conditional.html#id3" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">try()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="conditional.html#examples" class="md-nav__link">Examples</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="conditional-expressions">
<h1 id="functions-conditional--page-root">Conditional expressions<a class="headerlink" href="conditional.html#functions-conditional--page-root" title="Link to this heading">#</a></h1>
<section id="case">
<span id="case-expression"></span><h2 id="case">CASE<a class="headerlink" href="conditional.html#case" title="Link to this heading">#</a></h2>
<p>The standard SQL <code class="docutils literal notranslate"><span class="pre">CASE</span></code> expression has two forms.
The “simple” form searches each <code class="docutils literal notranslate"><span class="pre">value</span></code> expression from left to right
until it finds one that equals <code class="docutils literal notranslate"><span class="pre">expression</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>CASE expression
    WHEN value THEN result
    [ WHEN ... ]
    [ ELSE result ]
END
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">result</span></code> for the matching <code class="docutils literal notranslate"><span class="pre">value</span></code> is returned.
If no match is found, the <code class="docutils literal notranslate"><span class="pre">result</span></code> from the <code class="docutils literal notranslate"><span class="pre">ELSE</span></code> clause is
returned if it exists, otherwise null is returned. Example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">a</span><span class="p">,</span>
<span class="w">       </span><span class="k">CASE</span><span class="w"> </span><span class="n">a</span>
<span class="w">           </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">'one'</span>
<span class="w">           </span><span class="k">WHEN</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">'two'</span>
<span class="w">           </span><span class="k">ELSE</span><span class="w"> </span><span class="s1">'many'</span>
<span class="w">       </span><span class="k">END</span>
</pre></div>
</div>
<p>The “searched” form evaluates each boolean <code class="docutils literal notranslate"><span class="pre">condition</span></code> from left
to right until one is true and returns the matching <code class="docutils literal notranslate"><span class="pre">result</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>CASE
    WHEN condition THEN result
    [ WHEN ... ]
    [ ELSE result ]
END
</pre></div>
</div>
<p>If no conditions are true, the <code class="docutils literal notranslate"><span class="pre">result</span></code> from the <code class="docutils literal notranslate"><span class="pre">ELSE</span></code> clause is
returned if it exists, otherwise null is returned. Example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">a</span><span class="p">,</span><span class="w"> </span><span class="n">b</span><span class="p">,</span>
<span class="w">       </span><span class="k">CASE</span>
<span class="w">           </span><span class="k">WHEN</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">'aaa'</span>
<span class="w">           </span><span class="k">WHEN</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">'bbb'</span>
<span class="w">           </span><span class="k">ELSE</span><span class="w"> </span><span class="s1">'ccc'</span>
<span class="w">       </span><span class="k">END</span>
</pre></div>
</div>
<p>SQL UDFs can use <a class="reference internal" href="../udf/sql/case.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">CASE</span></code> statements</span></a> that use a slightly
different syntax from the CASE expressions. Specifically note the requirements
for terminating each clause with a semicolon <code class="docutils literal notranslate"><span class="pre">;</span></code> and the usage of <code class="docutils literal notranslate"><span class="pre">END</span> <span class="pre">CASE</span></code>.</p>
</section>
<section id="if">
<span id="if-expression"></span><h2 id="if">IF<a class="headerlink" href="conditional.html#if" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">IF</span></code> expression has two forms, one supplying only a
<code class="docutils literal notranslate"><span class="pre">true_value</span></code> and the other supplying both a <code class="docutils literal notranslate"><span class="pre">true_value</span></code> and a
<code class="docutils literal notranslate"><span class="pre">false_value</span></code>:</p>
<dl class="py function">
<dt class="sig sig-object py" id="id0">
<span class="sig-name descname"><span class="pre">if</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">true_value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="conditional.html#id0" title="Link to this definition">#</a></dt>
<dd><p>Evaluates and returns <code class="docutils literal notranslate"><span class="pre">true_value</span></code> if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is true,
otherwise null is returned and <code class="docutils literal notranslate"><span class="pre">true_value</span></code> is not evaluated.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">if</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">true_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">false_value</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Evaluates and returns <code class="docutils literal notranslate"><span class="pre">true_value</span></code> if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is true,
otherwise evaluates and returns <code class="docutils literal notranslate"><span class="pre">false_value</span></code>.</p>
</dd></dl>
<p>The following <code class="docutils literal notranslate"><span class="pre">IF</span></code> and <code class="docutils literal notranslate"><span class="pre">CASE</span></code> expressions are equivalent:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="n">orderkey</span><span class="p">,</span>
<span class="w">  </span><span class="n">totalprice</span><span class="p">,</span>
<span class="w">  </span><span class="k">IF</span><span class="p">(</span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">150000</span><span class="p">,</span><span class="w"> </span><span class="s1">'High Value'</span><span class="p">,</span><span class="w"> </span><span class="s1">'Low Value'</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">tpch</span><span class="p">.</span><span class="n">sf1</span><span class="p">.</span><span class="n">orders</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="n">orderkey</span><span class="p">,</span>
<span class="w">  </span><span class="n">totalprice</span><span class="p">,</span>
<span class="w">  </span><span class="k">CASE</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">150000</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">'High Value'</span>
<span class="w">    </span><span class="k">ELSE</span><span class="w"> </span><span class="s1">'Low Value'</span>
<span class="w">  </span><span class="k">END</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">tpch</span><span class="p">.</span><span class="n">sf1</span><span class="p">.</span><span class="n">orders</span><span class="p">;</span>
</pre></div>
</div>
<p>SQL UDFs can use <a class="reference internal" href="../udf/sql/if.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">IF</span></code> statements</span></a> that use a slightly different
syntax from <code class="docutils literal notranslate"><span class="pre">IF</span></code> expressions. Specifically note the requirement for terminating
each clause with a semicolon <code class="docutils literal notranslate"><span class="pre">;</span></code> and the usage of <code class="docutils literal notranslate"><span class="pre">END</span> <span class="pre">IF</span></code>.</p>
</section>
<section id="coalesce">
<span id="coalesce-function"></span><h2 id="coalesce">COALESCE<a class="headerlink" href="conditional.html#coalesce" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">coalesce</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value2</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="conditional.html#id1" title="Link to this definition">#</a></dt>
<dd><p>Returns the first non-null <code class="docutils literal notranslate"><span class="pre">value</span></code> in the argument list.
Like a <code class="docutils literal notranslate"><span class="pre">CASE</span></code> expression, arguments are only evaluated if necessary.</p>
</dd></dl>
</section>
<section id="nullif">
<span id="nullif-function"></span><h2 id="nullif">NULLIF<a class="headerlink" href="conditional.html#nullif" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">nullif</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="conditional.html#id2" title="Link to this definition">#</a></dt>
<dd><p>Returns null if <code class="docutils literal notranslate"><span class="pre">value1</span></code> equals <code class="docutils literal notranslate"><span class="pre">value2</span></code>, otherwise returns <code class="docutils literal notranslate"><span class="pre">value1</span></code>.</p>
</dd></dl>
</section>
<section id="try">
<span id="try-function"></span><h2 id="try">TRY<a class="headerlink" href="conditional.html#try" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">try</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="conditional.html#id3" title="Link to this definition">#</a></dt>
<dd><p>Evaluate an expression and handle certain types of errors by returning
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>
<p>In cases where it is preferable that queries produce <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or default values
instead of failing when corrupt or invalid data is encountered, the <code class="docutils literal notranslate"><span class="pre">TRY</span></code>
function may be useful. To specify default values, the <code class="docutils literal notranslate"><span class="pre">TRY</span></code> function can be
used in conjunction with the <code class="docutils literal notranslate"><span class="pre">COALESCE</span></code> function.</p>
<p>The following errors are handled by <code class="docutils literal notranslate"><span class="pre">TRY</span></code>:</p>
<ul class="simple">
<li><p>Division by zero</p></li>
<li><p>Invalid cast or function argument</p></li>
<li><p>Numeric value out of range</p></li>
</ul>
<section id="examples">
<h3 id="examples">Examples<a class="headerlink" href="conditional.html#examples" title="Link to this heading">#</a></h3>
<p>Source table with some invalid data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">shipping</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> origin_state | origin_zip | packages | total_cost
--------------+------------+----------+------------
 California   |      94131 |       25 |        100
 California   |      P332a |        5 |         72
 California   |      94025 |        0 |        155
 New Jersey   |      08544 |      225 |        490
(4 rows)
</pre></div>
</div>
<p>Query failure without <code class="docutils literal notranslate"><span class="pre">TRY</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">origin_zip</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">)</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">shipping</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Query failed: Cannot cast 'P332a' to BIGINT
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">NULL</span></code> values with <code class="docutils literal notranslate"><span class="pre">TRY</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">TRY</span><span class="p">(</span><span class="k">CAST</span><span class="p">(</span><span class="n">origin_zip</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">))</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">shipping</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> origin_zip
------------
      94131
 <USER>
      <GROUP>
      08544
(4 rows)
</pre></div>
</div>
<p>Query failure without <code class="docutils literal notranslate"><span class="pre">TRY</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">total_cost</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">packages</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">per_package</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">shipping</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Query failed: Division by zero
</pre></div>
</div>
<p>Default values with <code class="docutils literal notranslate"><span class="pre">TRY</span></code> and <code class="docutils literal notranslate"><span class="pre">COALESCE</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">COALESCE</span><span class="p">(</span><span class="n">TRY</span><span class="p">(</span><span class="n">total_cost</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">packages</span><span class="p">),</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">per_package</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">shipping</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> per_package
-------------
          4
         <USER>
          <GROUP>
         19
(4 rows)
</pre></div>
</div>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="comparison.html" title="Comparison functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Comparison functions and operators </span>
              </div>
            </a>
          
          
            <a href="conversion.html" title="Conversion functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Conversion functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>