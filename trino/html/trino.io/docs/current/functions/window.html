<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Window functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="window.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="User-defined functions" href="../udf.html" />
    <link rel="prev" title="UUID functions" href="uuid.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="window.html#functions/window" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Window functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Window </label>
    
      <a href="window.html#" class="md-nav__link md-nav__link--active">Window</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="window.html#aggregate-functions" class="md-nav__link">Aggregate functions</a>
        </li>
        <li class="md-nav__item"><a href="window.html#ranking-functions" class="md-nav__link">Ranking functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="window.html#cume_dist" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cume_dist()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#dense_rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">dense_rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#ntile" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ntile()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#percent_rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">percent_rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#row_number" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">row_number()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="window.html#value-functions" class="md-nav__link">Value functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="window.html#first_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">first_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#last_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">last_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#nth_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nth_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#lead" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lead()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#lag" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lag()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="window.html#aggregate-functions" class="md-nav__link">Aggregate functions</a>
        </li>
        <li class="md-nav__item"><a href="window.html#ranking-functions" class="md-nav__link">Ranking functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="window.html#cume_dist" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">cume_dist()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#dense_rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">dense_rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#ntile" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ntile()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#percent_rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">percent_rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#rank" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rank()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#row_number" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">row_number()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="window.html#value-functions" class="md-nav__link">Value functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="window.html#first_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">first_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#last_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">last_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#nth_value" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">nth_value()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#lead" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lead()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="window.html#lag" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lag()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="window-functions">
<h1 id="functions-window--page-root">Window functions<a class="headerlink" href="window.html#functions-window--page-root" title="Link to this heading">#</a></h1>
<p>Window functions perform calculations across rows of the query result.
They run after the <code class="docutils literal notranslate"><span class="pre">HAVING</span></code> clause but before the <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause.
Invoking a window function requires special syntax using the <code class="docutils literal notranslate"><span class="pre">OVER</span></code>
clause to specify the window.
For example, the following query ranks orders for each clerk by price:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">orderkey</span><span class="p">,</span><span class="w"> </span><span class="n">clerk</span><span class="p">,</span><span class="w"> </span><span class="n">totalprice</span><span class="p">,</span>
<span class="w">       </span><span class="n">rank</span><span class="p">()</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="p">(</span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">clerk</span>
<span class="w">                    </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">totalprice</span><span class="w"> </span><span class="k">DESC</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">rnk</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">orders</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">clerk</span><span class="p">,</span><span class="w"> </span><span class="n">rnk</span>
</pre></div>
</div>
<p>The window can be specified in two ways (see <a class="reference internal" href="../sql/select.html#window-clause"><span class="std std-ref">WINDOW clause</span></a>):</p>
<ul class="simple">
<li><p>By a reference to a named window specification defined in the <code class="docutils literal notranslate"><span class="pre">WINDOW</span></code> clause,</p></li>
<li><p>By an in-line window specification which allows to define window components
as well as refer to the window components pre-defined in the <code class="docutils literal notranslate"><span class="pre">WINDOW</span></code> clause.</p></li>
</ul>
<section id="aggregate-functions">
<h2 id="aggregate-functions">Aggregate functions<a class="headerlink" href="window.html#aggregate-functions" title="Link to this heading">#</a></h2>
<p>All <a class="reference internal" href="aggregate.html"><span class="doc">Aggregate functions</span></a> can be used as window functions by adding the <code class="docutils literal notranslate"><span class="pre">OVER</span></code>
clause. The aggregate function is computed for each row over the rows within the
current row’s window frame. Note that <a class="reference internal" href="aggregate.html#aggregate-function-ordering-during-aggregation"><span class="std std-ref">ordering during
aggregation</span></a> is not supported.</p>
<p>For example, the following query produces a rolling sum of order prices
by day for each clerk:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">clerk</span><span class="p">,</span><span class="w"> </span><span class="n">orderdate</span><span class="p">,</span><span class="w"> </span><span class="n">orderkey</span><span class="p">,</span><span class="w"> </span><span class="n">totalprice</span><span class="p">,</span>
<span class="w">       </span><span class="k">sum</span><span class="p">(</span><span class="n">totalprice</span><span class="p">)</span><span class="w"> </span><span class="n">OVER</span><span class="w"> </span><span class="p">(</span><span class="n">PARTITION</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">clerk</span>
<span class="w">                             </span><span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">orderdate</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">rolling_sum</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">orders</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">clerk</span><span class="p">,</span><span class="w"> </span><span class="n">orderdate</span><span class="p">,</span><span class="w"> </span><span class="n">orderkey</span>
</pre></div>
</div>
</section>
<section id="ranking-functions">
<h2 id="ranking-functions">Ranking functions<a class="headerlink" href="window.html#ranking-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="cume_dist">
<span class="sig-name descname"><span class="pre">cume_dist</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="window.html#cume_dist" title="Link to this definition">#</a></dt>
<dd><p>Returns the cumulative distribution of a value in a group of values.
The result is the number of rows preceding or peer with the row in the
window ordering of the window partition divided by the total number of
rows in the window partition. Thus, any tie values in the ordering will
evaluate to the same distribution value. The window frame must not be
specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="dense_rank">
<span class="sig-name descname"><span class="pre">dense_rank</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="window.html#dense_rank" title="Link to this definition">#</a></dt>
<dd><p>Returns the rank of a value in a group of values. This is similar to
<a class="reference internal" href="window.html#rank" title="rank"><code class="xref py py-func docutils literal notranslate"><span class="pre">rank()</span></code></a>, except that tie values do not produce gaps in the sequence.
The window frame must not be specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ntile">
<span class="sig-name descname"><span class="pre">ntile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="window.html#ntile" title="Link to this definition">#</a></dt>
<dd><p>Divides the rows for each window partition into <code class="docutils literal notranslate"><span class="pre">n</span></code> buckets ranging
from <code class="docutils literal notranslate"><span class="pre">1</span></code> to at most <code class="docutils literal notranslate"><span class="pre">n</span></code>. Bucket values will differ by at most <code class="docutils literal notranslate"><span class="pre">1</span></code>.
If the number of rows in the partition does not divide evenly into the
number of buckets, then the remainder values are distributed one per
bucket, starting with the first bucket.</p>
<p>For example, with <code class="docutils literal notranslate"><span class="pre">6</span></code> rows and <code class="docutils literal notranslate"><span class="pre">4</span></code> buckets, the bucket values would
be as follows: <code class="docutils literal notranslate"><span class="pre">1</span></code> <code class="docutils literal notranslate"><span class="pre">1</span></code> <code class="docutils literal notranslate"><span class="pre">2</span></code> <code class="docutils literal notranslate"><span class="pre">2</span></code> <code class="docutils literal notranslate"><span class="pre">3</span></code> <code class="docutils literal notranslate"><span class="pre">4</span></code></p>
<p>For the <a class="reference internal" href="window.html#ntile" title="ntile"><code class="xref py py-func docutils literal notranslate"><span class="pre">ntile()</span></code></a> function, the window frame must not be specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="percent_rank">
<span class="sig-name descname"><span class="pre">percent_rank</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">double</span></span></span><a class="headerlink" href="window.html#percent_rank" title="Link to this definition">#</a></dt>
<dd><p>Returns the percentage ranking of a value in group of values. The result
is <code class="docutils literal notranslate"><span class="pre">(r</span> <span class="pre">-</span> <span class="pre">1)</span> <span class="pre">/</span> <span class="pre">(n</span> <span class="pre">-</span> <span class="pre">1)</span></code> where <code class="docutils literal notranslate"><span class="pre">r</span></code> is the <a class="reference internal" href="window.html#rank" title="rank"><code class="xref py py-func docutils literal notranslate"><span class="pre">rank()</span></code></a> of the row and
<code class="docutils literal notranslate"><span class="pre">n</span></code> is the total number of rows in the window partition. The window frame
must not be specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="rank">
<span class="sig-name descname"><span class="pre">rank</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="window.html#rank" title="Link to this definition">#</a></dt>
<dd><p>Returns the rank of a value in a group of values. The rank is one plus
the number of rows preceding the row that are not peer with the row.
Thus, tie values in the ordering will produce gaps in the sequence.
The ranking is performed for each window partition. The window frame must
not be specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="row_number">
<span class="sig-name descname"><span class="pre">row_number</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="window.html#row_number" title="Link to this definition">#</a></dt>
<dd><p>Returns a unique, sequential number for each row, starting with one,
according to the ordering of rows within the window partition.
The window frame must not be specified.</p>
</dd></dl>
</section>
<section id="value-functions">
<h2 id="value-functions">Value functions<a class="headerlink" href="window.html#value-functions" title="Link to this heading">#</a></h2>
<p>By default, null values are respected. If <code class="docutils literal notranslate"><span class="pre">IGNORE</span> <span class="pre">NULLS</span></code> is specified, all rows where
<code class="docutils literal notranslate"><span class="pre">x</span></code> is null are excluded from the calculation. If <code class="docutils literal notranslate"><span class="pre">IGNORE</span> <span class="pre">NULLS</span></code> is specified and <code class="docutils literal notranslate"><span class="pre">x</span></code>
is null for all rows, the <code class="docutils literal notranslate"><span class="pre">default_value</span></code> is returned, or if it is not specified,
<code class="docutils literal notranslate"><span class="pre">null</span></code> is returned.</p>
<dl class="py function">
<dt class="sig sig-object py" id="first_value">
<span class="sig-name descname"><span class="pre">first_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="window.html#first_value" title="Link to this definition">#</a></dt>
<dd><p>Returns the first value of the window.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="last_value">
<span class="sig-name descname"><span class="pre">last_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="window.html#last_value" title="Link to this definition">#</a></dt>
<dd><p>Returns the last value of the window.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="nth_value">
<span class="sig-name descname"><span class="pre">nth_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="window.html#nth_value" title="Link to this definition">#</a></dt>
<dd><p>Returns the value at the specified offset from the beginning of the window.
Offsets start at <code class="docutils literal notranslate"><span class="pre">1</span></code>. The offset can be any scalar
expression.  If the offset is null or greater than the number of values in
the window, <code class="docutils literal notranslate"><span class="pre">null</span></code> is returned.  It is an error for the offset to be zero or
negative.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="lead">
<span class="sig-name descname"><span class="pre">lead</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default_value</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="window.html#lead" title="Link to this definition">#</a></dt>
<dd><p>Returns the value at <code class="docutils literal notranslate"><span class="pre">offset</span></code> rows after the current row in the window partition.
Offsets start at <code class="docutils literal notranslate"><span class="pre">0</span></code>, which is the current row. The
offset can be any scalar expression.  The default <code class="docutils literal notranslate"><span class="pre">offset</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>. If the
offset is null, an error is raised. If the offset refers to a row that is not
within the partition, the <code class="docutils literal notranslate"><span class="pre">default_value</span></code> is returned, or if it is not specified
<code class="docutils literal notranslate"><span class="pre">null</span></code> is returned.
The <a class="reference internal" href="window.html#lead" title="lead"><code class="xref py py-func docutils literal notranslate"><span class="pre">lead()</span></code></a> function requires that the window ordering be specified.
Window frame must not be specified.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="lag">
<span class="sig-name descname"><span class="pre">lag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default_value</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">input]</span></span></span><a class="headerlink" href="window.html#lag" title="Link to this definition">#</a></dt>
<dd><p>Returns the value at <code class="docutils literal notranslate"><span class="pre">offset</span></code> rows before the current row in the window partition.
Offsets start at <code class="docutils literal notranslate"><span class="pre">0</span></code>, which is the current row. The
offset can be any scalar expression.  The default <code class="docutils literal notranslate"><span class="pre">offset</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>. If the
offset is null, an error is raised. If the offset refers to a row that is not
within the partition, the <code class="docutils literal notranslate"><span class="pre">default_value</span></code> is returned, or if it is not specified
<code class="docutils literal notranslate"><span class="pre">null</span></code> is returned.
The <a class="reference internal" href="window.html#lag" title="lag"><code class="xref py py-func docutils literal notranslate"><span class="pre">lag()</span></code></a> function requires that the window ordering be specified.
Window frame must not be specified.</p>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="uuid.html" title="UUID functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> UUID functions </span>
              </div>
            </a>
          
          
            <a href="../udf.html" title="User-defined functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> User-defined functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>