<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>String functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="string.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="System information" href="system.html" />
    <link rel="prev" title="Set Digest functions" href="setdigest.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="string.html#functions/string" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> String functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> String </label>
    
      <a href="string.html#" class="md-nav__link md-nav__link--active">String</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="string.html#string-operators" class="md-nav__link">String operators</a>
        </li>
        <li class="md-nav__item"><a href="string.html#string-functions" class="md-nav__link">String functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="string.html#chr" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">chr()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#codepoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">codepoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#concat" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">concat()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#concat_ws" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">concat_ws()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#hamming_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hamming_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#levenshtein_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">levenshtein_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#lower" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lower()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#lpad" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lpad()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#ltrim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ltrim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#luhn_check" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">luhn_check()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#position" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">position()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#replace" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">replace()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#reverse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">reverse()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#rpad" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rpad()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#rtrim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rtrim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#soundex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">soundex()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_part" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_part()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_to_map" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_to_map()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_to_multimap" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_to_multimap()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#strpos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">strpos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#starts_with" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">starts_with()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#substr" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">substr()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#substring" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">substring()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#translate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">translate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#trim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#upper" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">upper()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#word_stem" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">word_stem()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="string.html#unicode-functions" class="md-nav__link">Unicode functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="string.html#normalize" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">normalize()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#to_utf8" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_utf8()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#from_utf8" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_utf8()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="string.html#string-operators" class="md-nav__link">String operators</a>
        </li>
        <li class="md-nav__item"><a href="string.html#string-functions" class="md-nav__link">String functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="string.html#chr" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">chr()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#codepoint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">codepoint()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#concat" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">concat()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#concat_ws" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">concat_ws()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#hamming_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">hamming_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#length" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">length()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#levenshtein_distance" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">levenshtein_distance()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#lower" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lower()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#lpad" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">lpad()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#ltrim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ltrim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#luhn_check" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">luhn_check()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#position" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">position()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#replace" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">replace()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#reverse" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">reverse()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#rpad" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rpad()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#rtrim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">rtrim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#soundex" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">soundex()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_part" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_part()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_to_map" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_to_map()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#split_to_multimap" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">split_to_multimap()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#strpos" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">strpos()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#starts_with" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">starts_with()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#substr" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">substr()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#substring" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">substring()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#translate" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">translate()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#trim" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trim()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#upper" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">upper()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#word_stem" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">word_stem()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="string.html#unicode-functions" class="md-nav__link">Unicode functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="string.html#normalize" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">normalize()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#to_utf8" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">to_utf8()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="string.html#from_utf8" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">from_utf8()</span></code></a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="string-functions-and-operators">
<h1 id="functions-string--page-root">String functions and operators<a class="headerlink" href="string.html#functions-string--page-root" title="Link to this heading">#</a></h1>
<section id="string-operators">
<h2 id="string-operators">String operators<a class="headerlink" href="string.html#string-operators" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">||</span></code> operator performs concatenation.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">LIKE</span></code> statement can be used for pattern matching and is documented in
<a class="reference internal" href="comparison.html#like-operator"><span class="std std-ref">Pattern comparison: LIKE</span></a>.</p>
</section>
<section id="string-functions">
<h2 id="string-functions">String functions<a class="headerlink" href="string.html#string-functions" title="Link to this heading">#</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These functions assume that the input strings contain valid UTF-8 encoded
Unicode code points.  There are no explicit checks for valid UTF-8 and
the functions may return incorrect results on invalid UTF-8.
Invalid UTF-8 data can be corrected with <a class="reference internal" href="string.html#from_utf8" title="from_utf8"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_utf8()</span></code></a>.</p>
<p>Additionally, the functions operate on Unicode code points and not user
visible <em>characters</em> (or <em>grapheme clusters</em>).  Some languages combine
multiple code points into a single user-perceived <em>character</em>, the basic
unit of a writing system for a language, but the functions will treat each
code point as a separate unit.</p>
<p>The <a class="reference internal" href="string.html#lower" title="lower"><code class="xref py py-func docutils literal notranslate"><span class="pre">lower()</span></code></a> and <a class="reference internal" href="string.html#upper" title="upper"><code class="xref py py-func docutils literal notranslate"><span class="pre">upper()</span></code></a> functions do not perform
locale-sensitive, context-sensitive, or one-to-many mappings required for
some languages. Specifically, this will return incorrect results for
Lithuanian, Turkish and Azeri.</p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="chr">
<span class="sig-name descname"><span class="pre">chr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#chr" title="Link to this definition">#</a></dt>
<dd><p>Returns the Unicode code point <code class="docutils literal notranslate"><span class="pre">n</span></code> as a single character string.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="codepoint">
<span class="sig-name descname"><span class="pre">codepoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">integer</span></span></span><a class="headerlink" href="string.html#codepoint" title="Link to this definition">#</a></dt>
<dd><p>Returns the Unicode code point of the only character of <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="concat">
<span class="sig-name descname"><span class="pre">concat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stringN</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#concat" title="Link to this definition">#</a></dt>
<dd><p>Returns the concatenation of <code class="docutils literal notranslate"><span class="pre">string1</span></code>, <code class="docutils literal notranslate"><span class="pre">string2</span></code>, <code class="docutils literal notranslate"><span class="pre">...</span></code>, <code class="docutils literal notranslate"><span class="pre">stringN</span></code>.
This function provides the same functionality as the
SQL-standard concatenation operator (<code class="docutils literal notranslate"><span class="pre">||</span></code>).</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="concat_ws">
<span class="sig-name descname"><span class="pre">concat_ws</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stringN</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#concat_ws" title="Link to this definition">#</a></dt>
<dd><p>Returns the concatenation of <code class="docutils literal notranslate"><span class="pre">string1</span></code>, <code class="docutils literal notranslate"><span class="pre">string2</span></code>, <code class="docutils literal notranslate"><span class="pre">...</span></code>, <code class="docutils literal notranslate"><span class="pre">stringN</span></code>
using <code class="docutils literal notranslate"><span class="pre">string0</span></code> as a separator. If <code class="docutils literal notranslate"><span class="pre">string0</span></code> is null, then the return
value is null. Any null values provided in the arguments after the
separator are skipped.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">concat_ws</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">array(varchar)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Returns the concatenation of elements in the array using <code class="docutils literal notranslate"><span class="pre">string0</span></code> as a
separator. If <code class="docutils literal notranslate"><span class="pre">string0</span></code> is null, then the return value is null. Any
null values in the array are skipped.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args...</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>See <a class="reference internal" href="conversion.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="hamming_distance">
<span class="sig-name descname"><span class="pre">hamming_distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string2</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="string.html#hamming_distance" title="Link to this definition">#</a></dt>
<dd><p>Returns the Hamming distance of <code class="docutils literal notranslate"><span class="pre">string1</span></code> and <code class="docutils literal notranslate"><span class="pre">string2</span></code>,
i.e. the number of positions at which the corresponding characters are different.
Note that the two strings must have the same length.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="length">
<span class="sig-name descname"><span class="pre">length</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="string.html#length" title="Link to this definition">#</a></dt>
<dd><p>Returns the length of <code class="docutils literal notranslate"><span class="pre">string</span></code> in characters.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="levenshtein_distance">
<span class="sig-name descname"><span class="pre">levenshtein_distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string2</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="string.html#levenshtein_distance" title="Link to this definition">#</a></dt>
<dd><p>Returns the Levenshtein edit distance of <code class="docutils literal notranslate"><span class="pre">string1</span></code> and <code class="docutils literal notranslate"><span class="pre">string2</span></code>,
i.e. the minimum number of single-character edits (insertions,
deletions or substitutions) needed to change <code class="docutils literal notranslate"><span class="pre">string1</span></code> into <code class="docutils literal notranslate"><span class="pre">string2</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="lower">
<span class="sig-name descname"><span class="pre">lower</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#lower" title="Link to this definition">#</a></dt>
<dd><p>Converts <code class="docutils literal notranslate"><span class="pre">string</span></code> to lowercase.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="lpad">
<span class="sig-name descname"><span class="pre">lpad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padstring</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#lpad" title="Link to this definition">#</a></dt>
<dd><p>Left pads <code class="docutils literal notranslate"><span class="pre">string</span></code> to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters with <code class="docutils literal notranslate"><span class="pre">padstring</span></code>.
If <code class="docutils literal notranslate"><span class="pre">size</span></code> is less than the length of <code class="docutils literal notranslate"><span class="pre">string</span></code>, the result is
truncated to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters. <code class="docutils literal notranslate"><span class="pre">size</span></code> must not be negative
and <code class="docutils literal notranslate"><span class="pre">padstring</span></code> must be non-empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="ltrim">
<span class="sig-name descname"><span class="pre">ltrim</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#ltrim" title="Link to this definition">#</a></dt>
<dd><p>Removes leading whitespace from <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="luhn_check">
<span class="sig-name descname"><span class="pre">luhn_check</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="string.html#luhn_check" title="Link to this definition">#</a></dt>
<dd><p>Tests whether a <code class="docutils literal notranslate"><span class="pre">string</span></code> of digits is valid according to the
<a class="reference external" href="https://wikipedia.org/wiki/Luhn_algorithm">Luhn algorithm</a>.</p>
<p>This checksum function, also known as <code class="docutils literal notranslate"><span class="pre">modulo</span> <span class="pre">10</span></code> or <code class="docutils literal notranslate"><span class="pre">mod</span> <span class="pre">10</span></code>, is
widely applied on credit card numbers and government identification numbers
to distinguish valid numbers from mistyped, incorrect numbers.</p>
<p>Valid identification number:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">select</span><span class="w"> </span><span class="n">luhn_check</span><span class="p">(</span><span class="s1">'79927398713'</span><span class="p">);</span>
<span class="c1">-- true</span>
</pre></div>
</div>
<p>Invalid identification number:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">select</span><span class="w"> </span><span class="n">luhn_check</span><span class="p">(</span><span class="s1">'79927398714'</span><span class="p">);</span>
<span class="c1">-- false</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="position">
<span class="sig-name descname"><span class="pre">position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">substring</span> <span class="pre">IN</span> <span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="string.html#position" title="Link to this definition">#</a></dt>
<dd><p>Returns the starting position of the first instance of <code class="docutils literal notranslate"><span class="pre">substring</span></code> in
<code class="docutils literal notranslate"><span class="pre">string</span></code>. Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. If not found, <code class="docutils literal notranslate"><span class="pre">0</span></code> is returned.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This SQL-standard function has special syntax and uses the
<code class="docutils literal notranslate"><span class="pre">IN</span></code> keyword for the arguments. See also <a class="reference internal" href="string.html#strpos" title="strpos"><code class="xref py py-func docutils literal notranslate"><span class="pre">strpos()</span></code></a>.</p>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="replace">
<span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#replace" title="Link to this definition">#</a></dt>
<dd><p>Removes all instances of <code class="docutils literal notranslate"><span class="pre">search</span></code> from <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replace</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Replaces all instances of <code class="docutils literal notranslate"><span class="pre">search</span></code> with <code class="docutils literal notranslate"><span class="pre">replace</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="reverse">
<span class="sig-name descname"><span class="pre">reverse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#reverse" title="Link to this definition">#</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">string</span></code> with the characters in reverse order.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="rpad">
<span class="sig-name descname"><span class="pre">rpad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padstring</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#rpad" title="Link to this definition">#</a></dt>
<dd><p>Right pads <code class="docutils literal notranslate"><span class="pre">string</span></code> to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters with <code class="docutils literal notranslate"><span class="pre">padstring</span></code>.
If <code class="docutils literal notranslate"><span class="pre">size</span></code> is less than the length of <code class="docutils literal notranslate"><span class="pre">string</span></code>, the result is
truncated to <code class="docutils literal notranslate"><span class="pre">size</span></code> characters. <code class="docutils literal notranslate"><span class="pre">size</span></code> must not be negative
and <code class="docutils literal notranslate"><span class="pre">padstring</span></code> must be non-empty.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="rtrim">
<span class="sig-name descname"><span class="pre">rtrim</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#rtrim" title="Link to this definition">#</a></dt>
<dd><p>Removes trailing whitespace from <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="soundex">
<span class="sig-name descname"><span class="pre">soundex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">char</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">string</span></span></span><a class="headerlink" href="string.html#soundex" title="Link to this definition">#</a></dt>
<dd><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">soundex</span></code> returns a character string containing the phonetic representation of <code class="docutils literal notranslate"><span class="pre">char</span></code>.</dt><dd><p>It is typically used to evaluate the similarity of two expressions phonetically, that is
how the string sounds when spoken:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">name</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">nation</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">SOUNDEX</span><span class="p">(</span><span class="n">name</span><span class="p">)</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="n">SOUNDEX</span><span class="p">(</span><span class="s1">'CHYNA'</span><span class="p">);</span>

<span class="w"> </span><span class="n">name</span><span class="w">  </span><span class="o">|</span>
<span class="c1">-------+----</span>
<span class="w"> </span><span class="n">CHINA</span><span class="w"> </span><span class="o">|</span>
<span class="p">(</span><span class="mi">1</span><span class="w"> </span><span class="k">row</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="split">
<span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delimiter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="string.html#split" title="Link to this definition">#</a></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> on <code class="docutils literal notranslate"><span class="pre">delimiter</span></code> and returns an array.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">split</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delimiter</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> on <code class="docutils literal notranslate"><span class="pre">delimiter</span></code> and returns an array of size at most
<code class="docutils literal notranslate"><span class="pre">limit</span></code>. The last element in the array always contain everything
left in the <code class="docutils literal notranslate"><span class="pre">string</span></code>. <code class="docutils literal notranslate"><span class="pre">limit</span></code> must be a positive number.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="split_part">
<span class="sig-name descname"><span class="pre">split_part</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delimiter</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#split_part" title="Link to this definition">#</a></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> on <code class="docutils literal notranslate"><span class="pre">delimiter</span></code> and returns the field <code class="docutils literal notranslate"><span class="pre">index</span></code>.
Field indexes start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. If the index is larger than
the number of fields, then null is returned.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="split_to_map">
<span class="sig-name descname"><span class="pre">split_to_map</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entryDelimiter</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">keyValueDelimiter</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">map&lt;varchar,</span> <span class="pre">varchar&gt;</span></span></span><a class="headerlink" href="string.html#split_to_map" title="Link to this definition">#</a></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> by <code class="docutils literal notranslate"><span class="pre">entryDelimiter</span></code> and <code class="docutils literal notranslate"><span class="pre">keyValueDelimiter</span></code> and returns a map.
<code class="docutils literal notranslate"><span class="pre">entryDelimiter</span></code> splits <code class="docutils literal notranslate"><span class="pre">string</span></code> into key-value pairs. <code class="docutils literal notranslate"><span class="pre">keyValueDelimiter</span></code> splits
each pair into key and value.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="split_to_multimap">
<span class="sig-name descname"><span class="pre">split_to_multimap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entryDelimiter</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">keyValueDelimiter</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="string.html#split_to_multimap" title="Link to this definition">#</a></dt>
<dd><p>Splits <code class="docutils literal notranslate"><span class="pre">string</span></code> by <code class="docutils literal notranslate"><span class="pre">entryDelimiter</span></code> and <code class="docutils literal notranslate"><span class="pre">keyValueDelimiter</span></code> and returns a map
containing an array of values for each unique key. <code class="docutils literal notranslate"><span class="pre">entryDelimiter</span></code> splits <code class="docutils literal notranslate"><span class="pre">string</span></code>
into key-value pairs. <code class="docutils literal notranslate"><span class="pre">keyValueDelimiter</span></code> splits each pair into key and value. The
values for each key will be in the same order as they appeared in <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="strpos">
<span class="sig-name descname"><span class="pre">strpos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">substring</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="string.html#strpos" title="Link to this definition">#</a></dt>
<dd><p>Returns the starting position of the first instance of <code class="docutils literal notranslate"><span class="pre">substring</span></code> in
<code class="docutils literal notranslate"><span class="pre">string</span></code>. Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. If not found, <code class="docutils literal notranslate"><span class="pre">0</span></code> is returned.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">strpos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">substring</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">instance</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span></dt>
<dd><p>Returns the position of the N-th <code class="docutils literal notranslate"><span class="pre">instance</span></code> of <code class="docutils literal notranslate"><span class="pre">substring</span></code> in <code class="docutils literal notranslate"><span class="pre">string</span></code>.
When <code class="docutils literal notranslate"><span class="pre">instance</span></code> is a negative number the search will start from the end of <code class="docutils literal notranslate"><span class="pre">string</span></code>.
Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. If not found, <code class="docutils literal notranslate"><span class="pre">0</span></code> is returned.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="starts_with">
<span class="sig-name descname"><span class="pre">starts_with</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">substring</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">boolean</span></span></span><a class="headerlink" href="string.html#starts_with" title="Link to this definition">#</a></dt>
<dd><p>Tests whether <code class="docutils literal notranslate"><span class="pre">substring</span></code> is a prefix of <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="substr">
<span class="sig-name descname"><span class="pre">substr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#substr" title="Link to this definition">#</a></dt>
<dd><p>This is an alias for <a class="reference internal" href="string.html#substring" title="substring"><code class="xref py py-func docutils literal notranslate"><span class="pre">substring()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="substring">
<span class="sig-name descname"><span class="pre">substring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#substring" title="Link to this definition">#</a></dt>
<dd><p>Returns the rest of <code class="docutils literal notranslate"><span class="pre">string</span></code> from the starting position <code class="docutils literal notranslate"><span class="pre">start</span></code>.
Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. A negative starting position is interpreted
as being relative to the end of the string.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">substr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>This is an alias for <a class="reference internal" href="string.html#substring" title="substring"><code class="xref py py-func docutils literal notranslate"><span class="pre">substring()</span></code></a>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">substring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Returns a substring from <code class="docutils literal notranslate"><span class="pre">string</span></code> of length <code class="docutils literal notranslate"><span class="pre">length</span></code> from the starting
position <code class="docutils literal notranslate"><span class="pre">start</span></code>. Positions start with <code class="docutils literal notranslate"><span class="pre">1</span></code>. A negative starting
position is interpreted as being relative to the end of the string.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="translate">
<span class="sig-name descname"><span class="pre">translate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">from</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">to</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#translate" title="Link to this definition">#</a></dt>
<dd><p>Returns the <code class="docutils literal notranslate"><span class="pre">source</span></code> string translated by replacing characters found in the
<code class="docutils literal notranslate"><span class="pre">from</span></code> string with the corresponding characters in the <code class="docutils literal notranslate"><span class="pre">to</span></code> string.  If the <code class="docutils literal notranslate"><span class="pre">from</span></code>
string contains duplicates, only the first is used.  If the <code class="docutils literal notranslate"><span class="pre">source</span></code> character
does not exist in the <code class="docutils literal notranslate"><span class="pre">from</span></code> string, the <code class="docutils literal notranslate"><span class="pre">source</span></code> character will be copied
without translation.  If the index of the matching character in the <code class="docutils literal notranslate"><span class="pre">from</span></code>
string is beyond the length of the <code class="docutils literal notranslate"><span class="pre">to</span></code> string, the <code class="docutils literal notranslate"><span class="pre">source</span></code> character will
be omitted from the resulting string.</p>
<p>Here are some examples illustrating the translate function:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">''</span><span class="p">,</span><span class="w"> </span><span class="s1">''</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'abcd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'a'</span><span class="p">,</span><span class="w"> </span><span class="s1">'z'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'zbcd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcda'</span><span class="p">,</span><span class="w"> </span><span class="s1">'a'</span><span class="p">,</span><span class="w"> </span><span class="s1">'z'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'zbcdz'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'Palhoça'</span><span class="p">,</span><span class="w"> </span><span class="s1">'ç'</span><span class="p">,</span><span class="s1">'c'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'Palhoca'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'b'</span><span class="p">,</span><span class="w"> </span><span class="n">U</span><span class="o">&amp;</span><span class="s1">'\+01F600'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- a😀cd</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'a'</span><span class="p">,</span><span class="w"> </span><span class="s1">''</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'bcd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'a'</span><span class="p">,</span><span class="w"> </span><span class="s1">'zy'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'zbcd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'ac'</span><span class="p">,</span><span class="w"> </span><span class="s1">'z'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'zbd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">translate</span><span class="p">(</span><span class="s1">'abcd'</span><span class="p">,</span><span class="w"> </span><span class="s1">'aac'</span><span class="p">,</span><span class="w"> </span><span class="s1">'zq'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'zbd'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">trim</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Removes leading and trailing whitespace from <code class="docutils literal notranslate"><span class="pre">string</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="trim">
<span class="sig-name descname"><span class="pre">trim</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">[</span> <span class="pre">[</span> <span class="pre">specification</span> <span class="pre">]</span> <span class="pre">[</span> <span class="pre">string</span> <span class="pre">]</span> <span class="pre">FROM</span> <span class="pre">]</span> <span class="pre">source</span> </em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#trim" title="Link to this definition">#</a></dt>
<dd><p>Removes any leading and/or trailing characters as specified up to and
including <code class="docutils literal notranslate"><span class="pre">string</span></code> from <code class="docutils literal notranslate"><span class="pre">source</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">trim</span><span class="p">(</span><span class="s1">'!'</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="s1">'!foo!'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'foo'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">trim</span><span class="p">(</span><span class="k">LEADING</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="s1">'  abcd'</span><span class="p">);</span><span class="w">  </span><span class="c1">-- 'abcd'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">trim</span><span class="p">(</span><span class="k">BOTH</span><span class="w"> </span><span class="s1">'$'</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="s1">'$var$'</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 'var'</span>
<span class="k">SELECT</span><span class="w"> </span><span class="k">trim</span><span class="p">(</span><span class="k">TRAILING</span><span class="w"> </span><span class="s1">'ER'</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">upper</span><span class="p">(</span><span class="s1">'worker'</span><span class="p">));</span><span class="w"> </span><span class="c1">-- 'WORK'</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="upper">
<span class="sig-name descname"><span class="pre">upper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#upper" title="Link to this definition">#</a></dt>
<dd><p>Converts <code class="docutils literal notranslate"><span class="pre">string</span></code> to uppercase.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="word_stem">
<span class="sig-name descname"><span class="pre">word_stem</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">word</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#word_stem" title="Link to this definition">#</a></dt>
<dd><p>Returns the stem of <code class="docutils literal notranslate"><span class="pre">word</span></code> in the English language.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">word_stem</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">word</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lang</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Returns the stem of <code class="docutils literal notranslate"><span class="pre">word</span></code> in the <code class="docutils literal notranslate"><span class="pre">lang</span></code> language.</p>
</dd></dl>
</section>
<section id="unicode-functions">
<h2 id="unicode-functions">Unicode functions<a class="headerlink" href="string.html#unicode-functions" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="normalize">
<span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#normalize" title="Link to this definition">#</a></dt>
<dd><p>Transforms <code class="docutils literal notranslate"><span class="pre">string</span></code> with NFC normalization form.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">form</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Transforms <code class="docutils literal notranslate"><span class="pre">string</span></code> with the specified normalization form.
<code class="docutils literal notranslate"><span class="pre">form</span></code> must be one of the following keywords:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Form</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">NFD</span></code></p></td>
<td><p>Canonical Decomposition</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">NFC</span></code></p></td>
<td><p>Canonical Decomposition, followed by Canonical Composition</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">NFKD</span></code></p></td>
<td><p>Compatibility Decomposition</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">NFKC</span></code></p></td>
<td><p>Compatibility Decomposition, followed by Canonical Composition</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This SQL-standard function has special syntax and requires
specifying <code class="docutils literal notranslate"><span class="pre">form</span></code> as a keyword, not as a string.</p>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="to_utf8">
<span class="sig-name descname"><span class="pre">to_utf8</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varbinary</span></span></span><a class="headerlink" href="string.html#to_utf8" title="Link to this definition">#</a></dt>
<dd><p>Encodes <code class="docutils literal notranslate"><span class="pre">string</span></code> into a UTF-8 varbinary representation.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="from_utf8">
<span class="sig-name descname"><span class="pre">from_utf8</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span><a class="headerlink" href="string.html#from_utf8" title="Link to this definition">#</a></dt>
<dd><p>Decodes a UTF-8 encoded string from <code class="docutils literal notranslate"><span class="pre">binary</span></code>. Invalid UTF-8 sequences
are replaced with the Unicode replacement character <code class="docutils literal notranslate"><span class="pre">U+FFFD</span></code>.</p>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">from_utf8</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">binary</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replace</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">varchar</span></span></span></dt>
<dd><p>Decodes a UTF-8 encoded string from <code class="docutils literal notranslate"><span class="pre">binary</span></code>. Invalid UTF-8 sequences
are replaced with <code class="docutils literal notranslate"><span class="pre">replace</span></code>. The replacement string <code class="docutils literal notranslate"><span class="pre">replace</span></code> must either
be a single character or empty (in which case invalid characters are
removed).</p>
</dd></dl>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="setdigest.html" title="Set Digest functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Set Digest functions </span>
              </div>
            </a>
          
          
            <a href="system.html" title="System information"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> System information </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>