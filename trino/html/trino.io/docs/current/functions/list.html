<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>List of functions and operators &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="list.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="List of functions by topic" href="list-by-topic.html" />
    <link rel="prev" title="Functions and operators" href="../functions.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="list.html#functions/list" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> List of functions and operators </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> List of functions and operators </label>
    
      <a href="list.html#" class="md-nav__link md-nav__link--active">List of functions and operators</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="list.html#id1" class="md-nav__link">#</a>
        </li>
        <li class="md-nav__item"><a href="list.html#a" class="md-nav__link">A</a>
        </li>
        <li class="md-nav__item"><a href="list.html#b" class="md-nav__link">B</a>
        </li>
        <li class="md-nav__item"><a href="list.html#c" class="md-nav__link">C</a>
        </li>
        <li class="md-nav__item"><a href="list.html#d" class="md-nav__link">D</a>
        </li>
        <li class="md-nav__item"><a href="list.html#e" class="md-nav__link">E</a>
        </li>
        <li class="md-nav__item"><a href="list.html#f" class="md-nav__link">F</a>
        </li>
        <li class="md-nav__item"><a href="list.html#g" class="md-nav__link">G</a>
        </li>
        <li class="md-nav__item"><a href="list.html#h" class="md-nav__link">H</a>
        </li>
        <li class="md-nav__item"><a href="list.html#i" class="md-nav__link">I</a>
        </li>
        <li class="md-nav__item"><a href="list.html#j" class="md-nav__link">J</a>
        </li>
        <li class="md-nav__item"><a href="list.html#k" class="md-nav__link">K</a>
        </li>
        <li class="md-nav__item"><a href="list.html#l" class="md-nav__link">L</a>
        </li>
        <li class="md-nav__item"><a href="list.html#m" class="md-nav__link">M</a>
        </li>
        <li class="md-nav__item"><a href="list.html#n" class="md-nav__link">N</a>
        </li>
        <li class="md-nav__item"><a href="list.html#o" class="md-nav__link">O</a>
        </li>
        <li class="md-nav__item"><a href="list.html#p" class="md-nav__link">P</a>
        </li>
        <li class="md-nav__item"><a href="list.html#q" class="md-nav__link">Q</a>
        </li>
        <li class="md-nav__item"><a href="list.html#r" class="md-nav__link">R</a>
        </li>
        <li class="md-nav__item"><a href="list.html#s" class="md-nav__link">S</a>
        </li>
        <li class="md-nav__item"><a href="list.html#t" class="md-nav__link">T</a>
        </li>
        <li class="md-nav__item"><a href="list.html#u" class="md-nav__link">U</a>
        </li>
        <li class="md-nav__item"><a href="list.html#v" class="md-nav__link">V</a>
        </li>
        <li class="md-nav__item"><a href="list.html#w" class="md-nav__link">W</a>
        </li>
        <li class="md-nav__item"><a href="list.html#x" class="md-nav__link">X</a>
        </li>
        <li class="md-nav__item"><a href="list.html#y" class="md-nav__link">Y</a>
        </li>
        <li class="md-nav__item"><a href="list.html#z" class="md-nav__link">Z</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="bitwise.html" class="md-nav__link">Bitwise</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="list.html#id1" class="md-nav__link">#</a>
        </li>
        <li class="md-nav__item"><a href="list.html#a" class="md-nav__link">A</a>
        </li>
        <li class="md-nav__item"><a href="list.html#b" class="md-nav__link">B</a>
        </li>
        <li class="md-nav__item"><a href="list.html#c" class="md-nav__link">C</a>
        </li>
        <li class="md-nav__item"><a href="list.html#d" class="md-nav__link">D</a>
        </li>
        <li class="md-nav__item"><a href="list.html#e" class="md-nav__link">E</a>
        </li>
        <li class="md-nav__item"><a href="list.html#f" class="md-nav__link">F</a>
        </li>
        <li class="md-nav__item"><a href="list.html#g" class="md-nav__link">G</a>
        </li>
        <li class="md-nav__item"><a href="list.html#h" class="md-nav__link">H</a>
        </li>
        <li class="md-nav__item"><a href="list.html#i" class="md-nav__link">I</a>
        </li>
        <li class="md-nav__item"><a href="list.html#j" class="md-nav__link">J</a>
        </li>
        <li class="md-nav__item"><a href="list.html#k" class="md-nav__link">K</a>
        </li>
        <li class="md-nav__item"><a href="list.html#l" class="md-nav__link">L</a>
        </li>
        <li class="md-nav__item"><a href="list.html#m" class="md-nav__link">M</a>
        </li>
        <li class="md-nav__item"><a href="list.html#n" class="md-nav__link">N</a>
        </li>
        <li class="md-nav__item"><a href="list.html#o" class="md-nav__link">O</a>
        </li>
        <li class="md-nav__item"><a href="list.html#p" class="md-nav__link">P</a>
        </li>
        <li class="md-nav__item"><a href="list.html#q" class="md-nav__link">Q</a>
        </li>
        <li class="md-nav__item"><a href="list.html#r" class="md-nav__link">R</a>
        </li>
        <li class="md-nav__item"><a href="list.html#s" class="md-nav__link">S</a>
        </li>
        <li class="md-nav__item"><a href="list.html#t" class="md-nav__link">T</a>
        </li>
        <li class="md-nav__item"><a href="list.html#u" class="md-nav__link">U</a>
        </li>
        <li class="md-nav__item"><a href="list.html#v" class="md-nav__link">V</a>
        </li>
        <li class="md-nav__item"><a href="list.html#w" class="md-nav__link">W</a>
        </li>
        <li class="md-nav__item"><a href="list.html#x" class="md-nav__link">X</a>
        </li>
        <li class="md-nav__item"><a href="list.html#y" class="md-nav__link">Y</a>
        </li>
        <li class="md-nav__item"><a href="list.html#z" class="md-nav__link">Z</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="list-of-functions-and-operators">
<h1 id="functions-list--page-root">List of functions and operators<a class="headerlink" href="list.html#functions-list--page-root" title="Link to this heading">#</a></h1>
<section id="id1">
<h2 id="id1">#<a class="headerlink" href="list.html#id1" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="array.html#subscript-operator"><span class="std std-ref">[] substring operator</span></a></p></li>
<li><p><a class="reference internal" href="array.html#concatenation-operator"><span class="std std-ref">|| concatenation operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">&lt; comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">&gt; comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">&lt;= comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">&gt;= comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">= comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">&lt;&gt; comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#comparison-operators"><span class="std std-ref">!= comparison operator</span></a></p></li>
<li><p><a class="reference internal" href="lambda.html#lambda-expressions"><span class="std std-ref">-&gt; lambda expression</span></a></p></li>
<li><p><a class="reference internal" href="math.html#mathematical-operators"><span class="std std-ref">+ mathematical operator</span></a></p></li>
<li><p><a class="reference internal" href="math.html#mathematical-operators"><span class="std std-ref">- mathematical operator</span></a></p></li>
<li><p><a class="reference internal" href="math.html#mathematical-operators"><span class="std std-ref">* mathematical operator</span></a></p></li>
<li><p><a class="reference internal" href="math.html#mathematical-operators"><span class="std std-ref">/ mathematical operator</span></a></p></li>
<li><p><a class="reference internal" href="math.html#mathematical-operators"><span class="std std-ref">% mathematical operator</span></a></p></li>
</ul>
</section>
<section id="a">
<h2 id="a">A<a class="headerlink" href="list.html#a" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="math.html#abs" title="abs"><code class="xref py py-func docutils literal notranslate"><span class="pre">abs()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#acos" title="acos"><code class="xref py py-func docutils literal notranslate"><span class="pre">acos()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#quantified-comparison-predicates"><span class="std std-ref">ALL</span></a></p></li>
<li><p><a class="reference internal" href="array.html#all_match" title="all_match"><code class="xref py py-func docutils literal notranslate"><span class="pre">all_match()</span></code></a></p></li>
<li><p><a class="reference internal" href="logical.html#logical-operators"><span class="std std-ref">AND</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#quantified-comparison-predicates"><span class="std std-ref">ANY</span></a></p></li>
<li><p><a class="reference internal" href="array.html#any_match" title="any_match"><code class="xref py py-func docutils literal notranslate"><span class="pre">any_match()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#any_value" title="any_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">any_value()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#approx_distinct" title="approx_distinct"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_distinct()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#approx_most_frequent" title="approx_most_frequent"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_most_frequent()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#approx_percentile" title="approx_percentile"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_percentile()</span></code></a></p></li>
<li><p><a class="reference internal" href="hyperloglog.html#approx_set" title="approx_set"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_set()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#arbitrary" title="arbitrary"><code class="xref py py-func docutils literal notranslate"><span class="pre">arbitrary()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#array_agg" title="array_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_distinct" title="array_distinct"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_distinct()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_except" title="array_except"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_except()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_intersect" title="array_intersect"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_intersect()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_join" title="array_join"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_join()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_max" title="array_max"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_max()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_min" title="array_min"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_min()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_position" title="array_position"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_position()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_remove" title="array_remove"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_remove()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_sort" title="array_sort"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_sort()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#array_union" title="array_union"><code class="xref py py-func docutils literal notranslate"><span class="pre">array_union()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#arrays_overlap" title="arrays_overlap"><code class="xref py py-func docutils literal notranslate"><span class="pre">arrays_overlap()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#asin" title="asin"><code class="xref py py-func docutils literal notranslate"><span class="pre">asin()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#at-time-zone-operator"><span class="std std-ref">AT TIME ZONE</span></a></p></li>
<li><p><a class="reference internal" href="datetime.html#at_timezone" title="at_timezone"><code class="xref py py-func docutils literal notranslate"><span class="pre">at_timezone()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#atan" title="atan"><code class="xref py py-func docutils literal notranslate"><span class="pre">atan()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#atan2" title="atan2"><code class="xref py py-func docutils literal notranslate"><span class="pre">atan2()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#avg" title="avg"><code class="xref py py-func docutils literal notranslate"><span class="pre">avg()</span></code></a></p></li>
</ul>
</section>
<section id="b">
<h2 id="b">B<a class="headerlink" href="list.html#b" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="color.html#bar" title="bar"><code class="xref py py-func docutils literal notranslate"><span class="pre">bar()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#beta_cdf" title="beta_cdf"><code class="xref py py-func docutils literal notranslate"><span class="pre">beta_cdf()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#range-operator"><span class="std std-ref">BETWEEN</span></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile" title="bing_tile"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile_at" title="bing_tile_at"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile_at()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile_coordinates" title="bing_tile_coordinates"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile_coordinates()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile_polygon" title="bing_tile_polygon"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile_polygon()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile_quadkey" title="bing_tile_quadkey"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile_quadkey()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tile_zoom_level" title="bing_tile_zoom_level"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tile_zoom_level()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#bing_tiles_around" title="bing_tiles_around"><code class="xref py py-func docutils literal notranslate"><span class="pre">bing_tiles_around()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bit_count" title="bit_count"><code class="xref py py-func docutils literal notranslate"><span class="pre">bit_count()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_and" title="bitwise_and"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_and()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#bitwise_and_agg" title="bitwise_and_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_and_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_left_shift" title="bitwise_left_shift"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_left_shift()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_not" title="bitwise_not"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_not()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_or" title="bitwise_or"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_or()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#bitwise_or_agg" title="bitwise_or_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_or_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_right_shift" title="bitwise_right_shift"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_right_shift()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_right_shift_arithmetic" title="bitwise_right_shift_arithmetic"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_right_shift_arithmetic()</span></code></a></p></li>
<li><p><a class="reference internal" href="bitwise.html#bitwise_xor" title="bitwise_xor"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_xor()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#bool_and" title="bool_and"><code class="xref py py-func docutils literal notranslate"><span class="pre">bool_and()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#bool_or" title="bool_or"><code class="xref py py-func docutils literal notranslate"><span class="pre">bool_or()</span></code></a></p></li>
</ul>
</section>
<section id="c">
<h2 id="c">C<a class="headerlink" href="list.html#c" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="array.html#cardinality" title="cardinality"><code class="xref py py-func docutils literal notranslate"><span class="pre">cardinality()</span></code></a></p></li>
<li><p><a class="reference internal" href="conditional.html#case-expression"><span class="std std-ref">CASE</span></a></p></li>
<li><p><a class="reference internal" href="conversion.html#cast" title="cast"><code class="xref py py-func docutils literal notranslate"><span class="pre">cast()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#cbrt" title="cbrt"><code class="xref py py-func docutils literal notranslate"><span class="pre">cbrt()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#ceil" title="ceil"><code class="xref py py-func docutils literal notranslate"><span class="pre">ceil()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#ceiling" title="ceiling"><code class="xref py py-func docutils literal notranslate"><span class="pre">ceiling()</span></code></a></p></li>
<li><p><a class="reference internal" href="teradata.html#char2hexint" title="char2hexint"><code class="xref py py-func docutils literal notranslate"><span class="pre">char2hexint()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#checksum" title="checksum"><code class="xref py py-func docutils literal notranslate"><span class="pre">checksum()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#chr" title="chr"><code class="xref py py-func docutils literal notranslate"><span class="pre">chr()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#classify" title="classify"><code class="xref py py-func docutils literal notranslate"><span class="pre">classify()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#classifier-function"><span class="std std-ref">classifier</span></a></p></li>
<li><p><a class="reference internal" href="conditional.html#coalesce-function"><span class="std std-ref">coalesce</span></a></p></li>
<li><p><a class="reference internal" href="string.html#codepoint" title="codepoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">codepoint()</span></code></a></p></li>
<li><p><a class="reference internal" href="color.html#color" title="color"><code class="xref py py-func docutils literal notranslate"><span class="pre">color()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#combinations" title="combinations"><code class="xref py py-func docutils literal notranslate"><span class="pre">combinations()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#concat" title="concat"><code class="xref py py-func docutils literal notranslate"><span class="pre">concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#concat_ws" title="concat_ws"><code class="xref py py-func docutils literal notranslate"><span class="pre">concat_ws()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#contains" title="contains"><code class="xref py py-func docutils literal notranslate"><span class="pre">contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#contains_sequence" title="contains_sequence"><code class="xref py py-func docutils literal notranslate"><span class="pre">contains_sequence()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#convex_hull_agg" title="convex_hull_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">convex_hull_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#corr" title="corr"><code class="xref py py-func docutils literal notranslate"><span class="pre">corr()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#cos" title="cos"><code class="xref py py-func docutils literal notranslate"><span class="pre">cos()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#cosh" title="cosh"><code class="xref py py-func docutils literal notranslate"><span class="pre">cosh()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#cosine_distance" title="cosine_distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">cosine_distance()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#cosine_similarity" title="cosine_similarity"><code class="xref py py-func docutils literal notranslate"><span class="pre">cosine_similarity()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#count" title="count"><code class="xref py py-func docutils literal notranslate"><span class="pre">count()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#count_if" title="count_if"><code class="xref py py-func docutils literal notranslate"><span class="pre">count_if()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#covar_pop" title="covar_pop"><code class="xref py py-func docutils literal notranslate"><span class="pre">covar_pop()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#covar_samp" title="covar_samp"><code class="xref py py-func docutils literal notranslate"><span class="pre">covar_samp()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#crc32" title="crc32"><code class="xref py py-func docutils literal notranslate"><span class="pre">crc32()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#cume_dist" title="cume_dist"><code class="xref py py-func docutils literal notranslate"><span class="pre">cume_dist()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#current_date" title="current_date"><code class="xref py py-data docutils literal notranslate"><span class="pre">current_date</span></code></a></p></li>
<li><p><a class="reference internal" href="session.html#current_groups" title="current_groups"><code class="xref py py-func docutils literal notranslate"><span class="pre">current_groups()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#current_time" title="current_time"><code class="xref py py-data docutils literal notranslate"><span class="pre">current_time</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#current_timestamp" title="current_timestamp"><code class="xref py py-data docutils literal notranslate"><span class="pre">current_timestamp</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#current_timezone" title="current_timezone"><code class="xref py py-func docutils literal notranslate"><span class="pre">current_timezone()</span></code></a></p></li>
<li><p><a class="reference internal" href="session.html#current_user" title="current_user"><code class="xref py py-data docutils literal notranslate"><span class="pre">current_user</span></code></a></p></li>
</ul>
</section>
<section id="d">
<h2 id="d">D<a class="headerlink" href="list.html#d" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="datetime.html#date" title="date"><code class="xref py py-func docutils literal notranslate"><span class="pre">date()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#date_add" title="date_add"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_add()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#date_diff" title="date_diff"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_diff()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#date_format" title="date_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_format()</span></code></a></p></li>
<li><p><code class="xref py py-func docutils literal notranslate"><span class="pre">date_parse()</span></code></p></li>
<li><p><a class="reference internal" href="datetime.html#date_trunc" title="date_trunc"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_trunc()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#day" title="day"><code class="xref py py-func docutils literal notranslate"><span class="pre">day()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#day_of_month" title="day_of_month"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_month()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#day_of_week" title="day_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_week()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#day_of_year" title="day_of_year"><code class="xref py py-func docutils literal notranslate"><span class="pre">day_of_year()</span></code></a></p></li>
<li><p><a class="reference internal" href="decimal.html#decimal-literal"><span class="std std-ref">DECIMAL</span></a></p></li>
<li><p><a class="reference internal" href="math.html#degrees" title="degrees"><code class="xref py py-func docutils literal notranslate"><span class="pre">degrees()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#dense_rank" title="dense_rank"><code class="xref py py-func docutils literal notranslate"><span class="pre">dense_rank()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#dow" title="dow"><code class="xref py py-func docutils literal notranslate"><span class="pre">dow()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#doy" title="doy"><code class="xref py py-func docutils literal notranslate"><span class="pre">doy()</span></code></a></p></li>
</ul>
</section>
<section id="e">
<h2 id="e">E<a class="headerlink" href="list.html#e" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="math.html#e" title="e"><code class="xref py py-func docutils literal notranslate"><span class="pre">e()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#element_at" title="element_at"><code class="xref py py-func docutils literal notranslate"><span class="pre">element_at()</span></code></a></p></li>
<li><p><a class="reference internal" href="hyperloglog.html#empty_approx_set" title="empty_approx_set"><code class="xref py py-func docutils literal notranslate"><span class="pre">empty_approx_set()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">evaluate_classifier_predictions</span></code></p></li>
<li><p><a class="reference internal" href="aggregate.html#every" title="every"><code class="xref py py-func docutils literal notranslate"><span class="pre">every()</span></code></a></p></li>
<li><p><code class="xref py py-func docutils literal notranslate"><span class="pre">exclude_columns()</span></code></p></li>
<li><p><a class="reference internal" href="datetime.html#extract" title="extract"><code class="xref py py-func docutils literal notranslate"><span class="pre">extract()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#exp" title="exp"><code class="xref py py-func docutils literal notranslate"><span class="pre">exp()</span></code></a></p></li>
</ul>
</section>
<section id="f">
<h2 id="f">F<a class="headerlink" href="list.html#f" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="ml.html#features" title="features"><code class="xref py py-func docutils literal notranslate"><span class="pre">features()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#filter" title="filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">filter()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#logical-navigation-functions"><span class="std std-ref">first</span></a></p></li>
<li><p><a class="reference internal" href="window.html#first_value" title="first_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">first_value()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#flatten" title="flatten"><code class="xref py py-func docutils literal notranslate"><span class="pre">flatten()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#floor" title="floor"><code class="xref py py-func docutils literal notranslate"><span class="pre">floor()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#format_datetime" title="format_datetime"><code class="xref py py-func docutils literal notranslate"><span class="pre">format_datetime()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#format_number" title="format_number"><code class="xref py py-func docutils literal notranslate"><span class="pre">format_number()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#from_base" title="from_base"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_base()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_base32" title="from_base32"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_base32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_base64" title="from_base64"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_base64()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_base64url" title="from_base64url"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_base64url()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_big_endian_32" title="from_big_endian_32"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_big_endian_32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_big_endian_64" title="from_big_endian_64"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_big_endian_64()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#from_encoded_polyline" title="from_encoded_polyline"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_encoded_polyline()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">from_geojson_geometry</span></code></p></li>
<li><p><a class="reference internal" href="binary.html#from_hex" title="from_hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_hex()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_ieee754_32" title="from_ieee754_32"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_ieee754_32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#from_ieee754_64" title="from_ieee754_64"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_ieee754_64()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#from_iso8601_date" title="from_iso8601_date"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_iso8601_date()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#from_iso8601_timestamp" title="from_iso8601_timestamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_iso8601_timestamp()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#from_iso8601_timestamp_nanos" title="from_iso8601_timestamp_nanos"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_iso8601_timestamp_nanos()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#from_unixtime" title="from_unixtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_unixtime()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#from_unixtime_nanos" title="from_unixtime_nanos"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_unixtime_nanos()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#from_utf8" title="from_utf8"><code class="xref py py-func docutils literal notranslate"><span class="pre">from_utf8()</span></code></a></p></li>
</ul>
</section>
<section id="g">
<h2 id="g">G<a class="headerlink" href="list.html#g" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="aggregate.html#geometric_mean" title="geometric_mean"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometric_mean()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_from_hadoop_shape" title="geometry_from_hadoop_shape"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_from_hadoop_shape()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_invalid_reason" title="geometry_invalid_reason"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_invalid_reason()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_nearest_points" title="geometry_nearest_points"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_nearest_points()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_to_bing_tiles" title="geometry_to_bing_tiles"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_to_bing_tiles()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_union" title="geometry_union"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_union()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#geometry_union_agg" title="geometry_union_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">geometry_union_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#great_circle_distance" title="great_circle_distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">great_circle_distance()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#greatest" title="greatest"><code class="xref py py-func docutils literal notranslate"><span class="pre">greatest()</span></code></a></p></li>
</ul>
</section>
<section id="h">
<h2 id="h">H<a class="headerlink" href="list.html#h" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="string.html#hamming_distance" title="hamming_distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">hamming_distance()</span></code></a></p></li>
<li><p><a class="reference internal" href="setdigest.html#hash_counts" title="hash_counts"><code class="xref py py-func docutils literal notranslate"><span class="pre">hash_counts()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#histogram" title="histogram"><code class="xref py py-func docutils literal notranslate"><span class="pre">histogram()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#hmac_md5" title="hmac_md5"><code class="xref py py-func docutils literal notranslate"><span class="pre">hmac_md5()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#hmac_sha1" title="hmac_sha1"><code class="xref py py-func docutils literal notranslate"><span class="pre">hmac_sha1()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#hmac_sha256" title="hmac_sha256"><code class="xref py py-func docutils literal notranslate"><span class="pre">hmac_sha256()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#hmac_sha512" title="hmac_sha512"><code class="xref py py-func docutils literal notranslate"><span class="pre">hmac_sha512()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#hour" title="hour"><code class="xref py py-func docutils literal notranslate"><span class="pre">hour()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#human_readable_seconds" title="human_readable_seconds"><code class="xref py py-func docutils literal notranslate"><span class="pre">human_readable_seconds()</span></code></a></p></li>
</ul>
</section>
<section id="i">
<h2 id="i">I<a class="headerlink" href="list.html#i" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="conditional.html#if-expression"><span class="std std-ref">if</span></a></p></li>
<li><p><a class="reference internal" href="teradata.html#index" title="index"><code class="xref py py-func docutils literal notranslate"><span class="pre">index()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#infinity" title="infinity"><code class="xref py py-func docutils literal notranslate"><span class="pre">infinity()</span></code></a></p></li>
<li><p><a class="reference internal" href="setdigest.html#intersection_cardinality" title="intersection_cardinality"><code class="xref py py-func docutils literal notranslate"><span class="pre">intersection_cardinality()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#inverse_beta_cdf" title="inverse_beta_cdf"><code class="xref py py-func docutils literal notranslate"><span class="pre">inverse_beta_cdf()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#inverse_normal_cdf" title="inverse_normal_cdf"><code class="xref py py-func docutils literal notranslate"><span class="pre">inverse_normal_cdf()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#is_finite" title="is_finite"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_finite()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#is_infinite" title="is_infinite"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_infinite()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#is_json_scalar" title="is_json_scalar"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_json_scalar()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#is_nan" title="is_nan"><code class="xref py py-func docutils literal notranslate"><span class="pre">is_nan()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#is-distinct-operator"><span class="std std-ref">IS NOT DISTINCT</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#is-null-operator"><span class="std std-ref">IS NOT NULL</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#is-distinct-operator"><span class="std std-ref">IS DISTINCT</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#is-null-operator"><span class="std std-ref">IS NULL</span></a></p></li>
</ul>
</section>
<section id="j">
<h2 id="j">J<a class="headerlink" href="list.html#j" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="setdigest.html#jaccard_index" title="jaccard_index"><code class="xref py py-func docutils literal notranslate"><span class="pre">jaccard_index()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json-array"><span class="std std-ref">json_array()</span></a></p></li>
<li><p><a class="reference internal" href="json.html#json_array_contains" title="json_array_contains"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_array_contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json_array_get" title="json_array_get"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_array_get()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json_array_length" title="json_array_length"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_array_length()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json-exists"><span class="std std-ref">json_exists()</span></a></p></li>
<li><p><a class="reference internal" href="json.html#json_extract" title="json_extract"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_extract()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json_extract_scalar" title="json_extract_scalar"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_extract_scalar()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json_format" title="json_format"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_format()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json-object"><span class="std std-ref">json_object()</span></a></p></li>
<li><p><a class="reference internal" href="json.html#json_parse" title="json_parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_parse()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json-query"><span class="std std-ref">json_query()</span></a></p></li>
<li><p><a class="reference internal" href="json.html#json_size" title="json_size"><code class="xref py py-func docutils literal notranslate"><span class="pre">json_size()</span></code></a></p></li>
<li><p><a class="reference internal" href="json.html#json-value"><span class="std std-ref">json_value()</span></a></p></li>
</ul>
</section>
<section id="k">
<h2 id="k">K<a class="headerlink" href="list.html#k" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="aggregate.html#kurtosis" title="kurtosis"><code class="xref py py-func docutils literal notranslate"><span class="pre">kurtosis()</span></code></a></p></li>
</ul>
</section>
<section id="l">
<h2 id="l">L<a class="headerlink" href="list.html#l" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="window.html#lag" title="lag"><code class="xref py py-func docutils literal notranslate"><span class="pre">lag()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#logical-navigation-functions"><span class="std std-ref">last</span></a></p></li>
<li><p><a class="reference internal" href="datetime.html#last_day_of_month" title="last_day_of_month"><code class="xref py py-func docutils literal notranslate"><span class="pre">last_day_of_month()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#last_value" title="last_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">last_value()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#lead" title="lead"><code class="xref py py-func docutils literal notranslate"><span class="pre">lead()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#learn_classifier" title="learn_classifier"><code class="xref py py-func docutils literal notranslate"><span class="pre">learn_classifier()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#learn_libsvm_classifier" title="learn_libsvm_classifier"><code class="xref py py-func docutils literal notranslate"><span class="pre">learn_libsvm_classifier()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#learn_libsvm_regressor" title="learn_libsvm_regressor"><code class="xref py py-func docutils literal notranslate"><span class="pre">learn_libsvm_regressor()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#learn_regressor" title="learn_regressor"><code class="xref py py-func docutils literal notranslate"><span class="pre">learn_regressor()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#least" title="least"><code class="xref py py-func docutils literal notranslate"><span class="pre">least()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#length" title="length"><code class="xref py py-func docutils literal notranslate"><span class="pre">length()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#levenshtein_distance" title="levenshtein_distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">levenshtein_distance()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#line_interpolate_point" title="line_interpolate_point"><code class="xref py py-func docutils literal notranslate"><span class="pre">line_interpolate_point()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#line_interpolate_points" title="line_interpolate_points"><code class="xref py py-func docutils literal notranslate"><span class="pre">line_interpolate_points()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#line_locate_point" title="line_locate_point"><code class="xref py py-func docutils literal notranslate"><span class="pre">line_locate_point()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#listagg" title="listagg"><code class="xref py py-func docutils literal notranslate"><span class="pre">listagg()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#ln" title="ln"><code class="xref py py-func docutils literal notranslate"><span class="pre">ln()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#localtime" title="localtime"><code class="xref py py-data docutils literal notranslate"><span class="pre">localtime</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#localtimestamp" title="localtimestamp"><code class="xref py py-data docutils literal notranslate"><span class="pre">localtimestamp</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#log" title="log"><code class="xref py py-func docutils literal notranslate"><span class="pre">log()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#log10" title="log10"><code class="xref py py-func docutils literal notranslate"><span class="pre">log10()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#log2" title="log2"><code class="xref py py-func docutils literal notranslate"><span class="pre">log2()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#lower" title="lower"><code class="xref py py-func docutils literal notranslate"><span class="pre">lower()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#lpad" title="lpad"><code class="xref py py-func docutils literal notranslate"><span class="pre">lpad()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#ltrim" title="ltrim"><code class="xref py py-func docutils literal notranslate"><span class="pre">ltrim()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#luhn_check" title="luhn_check"><code class="xref py py-func docutils literal notranslate"><span class="pre">luhn_check()</span></code></a></p></li>
</ul>
</section>
<section id="m">
<h2 id="m">M<a class="headerlink" href="list.html#m" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="setdigest.html#make_set_digest" title="make_set_digest"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_set_digest()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#map_agg" title="map_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_concat" title="map_concat"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_entries" title="map_entries"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_entries()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_filter" title="map_filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_filter()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_from_entries" title="map_from_entries"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_from_entries()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_keys" title="map_keys"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_keys()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#map_union" title="map_union"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_union()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_values" title="map_values"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_values()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#map_zip_with" title="map_zip_with"><code class="xref py py-func docutils literal notranslate"><span class="pre">map_zip_with()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#match-number-function"><span class="std std-ref">match_number</span></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#max" title="max"><code class="xref py py-func docutils literal notranslate"><span class="pre">max()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#max_by" title="max_by"><code class="xref py py-func docutils literal notranslate"><span class="pre">max_by()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#md5" title="md5"><code class="xref py py-func docutils literal notranslate"><span class="pre">md5()</span></code></a></p></li>
<li><p><a class="reference internal" href="hyperloglog.html#merge" title="merge"><code class="xref py py-func docutils literal notranslate"><span class="pre">merge()</span></code></a></p></li>
<li><p><a class="reference internal" href="setdigest.html#merge_set_digest" title="merge_set_digest"><code class="xref py py-func docutils literal notranslate"><span class="pre">merge_set_digest()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#millisecond" title="millisecond"><code class="xref py py-func docutils literal notranslate"><span class="pre">millisecond()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#min" title="min"><code class="xref py py-func docutils literal notranslate"><span class="pre">min()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#min_by" title="min_by"><code class="xref py py-func docutils literal notranslate"><span class="pre">min_by()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#minute" title="minute"><code class="xref py py-func docutils literal notranslate"><span class="pre">minute()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#mod" title="mod"><code class="xref py py-func docutils literal notranslate"><span class="pre">mod()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#month" title="month"><code class="xref py py-func docutils literal notranslate"><span class="pre">month()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#multimap_agg" title="multimap_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">multimap_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#multimap_from_entries" title="multimap_from_entries"><code class="xref py py-func docutils literal notranslate"><span class="pre">multimap_from_entries()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#murmur3" title="murmur3"><code class="xref py py-func docutils literal notranslate"><span class="pre">murmur3()</span></code></a></p></li>
</ul>
</section>
<section id="n">
<h2 id="n">N<a class="headerlink" href="list.html#n" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="math.html#nan" title="nan"><code class="xref py py-func docutils literal notranslate"><span class="pre">nan()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#physical-navigation-functions"><span class="std std-ref">next</span></a></p></li>
<li><p><a class="reference internal" href="array.html#ngrams" title="ngrams"><code class="xref py py-func docutils literal notranslate"><span class="pre">ngrams()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#none_match" title="none_match"><code class="xref py py-func docutils literal notranslate"><span class="pre">none_match()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#normal_cdf" title="normal_cdf"><code class="xref py py-func docutils literal notranslate"><span class="pre">normal_cdf()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#normalize" title="normalize"><code class="xref py py-func docutils literal notranslate"><span class="pre">normalize()</span></code></a></p></li>
<li><p><a class="reference internal" href="logical.html#logical-operators"><span class="std std-ref">NOT</span></a></p></li>
<li><p><a class="reference internal" href="comparison.html#range-operator"><span class="std std-ref">NOT BETWEEN</span></a></p></li>
<li><p><a class="reference internal" href="datetime.html#now" title="now"><code class="xref py py-func docutils literal notranslate"><span class="pre">now()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#nth_value" title="nth_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">nth_value()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#ntile" title="ntile"><code class="xref py py-func docutils literal notranslate"><span class="pre">ntile()</span></code></a></p></li>
<li><p><a class="reference internal" href="conditional.html#nullif-function"><span class="std std-ref">nullif</span></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#numeric_histogram" title="numeric_histogram"><code class="xref py py-func docutils literal notranslate"><span class="pre">numeric_histogram()</span></code></a></p></li>
</ul>
</section>
<section id="o">
<h2 id="o">O<a class="headerlink" href="list.html#o" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">objectid</span></code></p></li>
<li><p><a class="reference internal" href="../connector/mongodb.html#objectid_timestamp" title="objectid_timestamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">objectid_timestamp()</span></code></a></p></li>
<li><p><a class="reference internal" href="logical.html#logical-operators"><span class="std std-ref">OR</span></a></p></li>
</ul>
</section>
<section id="p">
<h2 id="p">P<a class="headerlink" href="list.html#p" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="datetime.html#parse_datetime" title="parse_datetime"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse_datetime()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#parse_duration" title="parse_duration"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse_duration()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#parse_data_size" title="parse_data_size"><code class="xref py py-func docutils literal notranslate"><span class="pre">parse_data_size()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#percent_rank" title="percent_rank"><code class="xref py py-func docutils literal notranslate"><span class="pre">percent_rank()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#permute-function"><span class="std std-ref">permute</span></a></p></li>
<li><p><a class="reference internal" href="math.html#pi" title="pi"><code class="xref py py-func docutils literal notranslate"><span class="pre">pi()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#position" title="position"><code class="xref py py-func docutils literal notranslate"><span class="pre">position()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#pow" title="pow"><code class="xref py py-func docutils literal notranslate"><span class="pre">pow()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#power" title="power"><code class="xref py py-func docutils literal notranslate"><span class="pre">power()</span></code></a></p></li>
<li><p><a class="reference internal" href="../sql/match-recognize.html#physical-navigation-functions"><span class="std std-ref">prev</span></a></p></li>
</ul>
</section>
<section id="q">
<h2 id="q">Q<a class="headerlink" href="list.html#q" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="qdigest.html#qdigest_agg" title="qdigest_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">qdigest_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#quarter" title="quarter"><code class="xref py py-func docutils literal notranslate"><span class="pre">quarter()</span></code></a></p></li>
</ul>
</section>
<section id="r">
<h2 id="r">R<a class="headerlink" href="list.html#r" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="math.html#radians" title="radians"><code class="xref py py-func docutils literal notranslate"><span class="pre">radians()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#rand" title="rand"><code class="xref py py-func docutils literal notranslate"><span class="pre">rand()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#random" title="random"><code class="xref py py-func docutils literal notranslate"><span class="pre">random()</span></code></a></p></li>
<li><p><a class="reference internal" href="../connector/faker.html#random_string" title="random_string"><code class="xref py py-func docutils literal notranslate"><span class="pre">random_string()</span></code></a>, catalog function of the <a class="reference internal" href="../connector/faker.html"><span class="doc std std-doc">Faker connector</span></a></p></li>
<li><p><a class="reference internal" href="window.html#rank" title="rank"><code class="xref py py-func docutils literal notranslate"><span class="pre">rank()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#reduce" title="reduce"><code class="xref py py-func docutils literal notranslate"><span class="pre">reduce()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#reduce_agg" title="reduce_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">reduce_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_count" title="regexp_count"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_count()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_extract" title="regexp_extract"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_extract()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_extract_all" title="regexp_extract_all"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_extract_all()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_like" title="regexp_like"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_like()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_position" title="regexp_position"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_position()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_replace" title="regexp_replace"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_replace()</span></code></a></p></li>
<li><p><a class="reference internal" href="regexp.html#regexp_split" title="regexp_split"><code class="xref py py-func docutils literal notranslate"><span class="pre">regexp_split()</span></code></a></p></li>
<li><p><a class="reference internal" href="ml.html#regress" title="regress"><code class="xref py py-func docutils literal notranslate"><span class="pre">regress()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#regr_intercept" title="regr_intercept"><code class="xref py py-func docutils literal notranslate"><span class="pre">regr_intercept()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#regr_slope" title="regr_slope"><code class="xref py py-func docutils literal notranslate"><span class="pre">regr_slope()</span></code></a></p></li>
<li><p><a class="reference internal" href="color.html#render" title="render"><code class="xref py py-func docutils literal notranslate"><span class="pre">render()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#repeat" title="repeat"><code class="xref py py-func docutils literal notranslate"><span class="pre">repeat()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#replace" title="replace"><code class="xref py py-func docutils literal notranslate"><span class="pre">replace()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#reverse" title="reverse"><code class="xref py py-func docutils literal notranslate"><span class="pre">reverse()</span></code></a></p></li>
<li><p><a class="reference internal" href="color.html#rgb" title="rgb"><code class="xref py py-func docutils literal notranslate"><span class="pre">rgb()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a></p></li>
<li><p><a class="reference internal" href="window.html#row_number" title="row_number"><code class="xref py py-func docutils literal notranslate"><span class="pre">row_number()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#rpad" title="rpad"><code class="xref py py-func docutils literal notranslate"><span class="pre">rpad()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#rtrim" title="rtrim"><code class="xref py py-func docutils literal notranslate"><span class="pre">rtrim()</span></code></a></p></li>
</ul>
</section>
<section id="s">
<h2 id="s">S<a class="headerlink" href="list.html#s" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="datetime.html#second" title="second"><code class="xref py py-func docutils literal notranslate"><span class="pre">second()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#sequence" title="sequence"><code class="xref py py-func docutils literal notranslate"><span class="pre">sequence()</span></code></a> (scalar function)</p></li>
<li><p><a class="reference internal" href="table.html#sequence-table-function"><span class="std std-ref">sequence()</span></a> (table function)</p></li>
<li><p><a class="reference internal" href="binary.html#sha1" title="sha1"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha1()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#sha256" title="sha256"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha256()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#sha512" title="sha512"><code class="xref py py-func docutils literal notranslate"><span class="pre">sha512()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#shuffle" title="shuffle"><code class="xref py py-func docutils literal notranslate"><span class="pre">shuffle()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#sign" title="sign"><code class="xref py py-func docutils literal notranslate"><span class="pre">sign()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#simplify_geometry" title="simplify_geometry"><code class="xref py py-func docutils literal notranslate"><span class="pre">simplify_geometry()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#sin" title="sin"><code class="xref py py-func docutils literal notranslate"><span class="pre">sin()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#sinh" title="sinh"><code class="xref py py-func docutils literal notranslate"><span class="pre">sinh()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#skewness" title="skewness"><code class="xref py py-func docutils literal notranslate"><span class="pre">skewness()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#slice" title="slice"><code class="xref py py-func docutils literal notranslate"><span class="pre">slice()</span></code></a></p></li>
<li><p><a class="reference internal" href="comparison.html#quantified-comparison-predicates"><span class="std std-ref">SOME</span></a></p></li>
<li><p><a class="reference internal" href="string.html#soundex" title="soundex"><code class="xref py py-func docutils literal notranslate"><span class="pre">soundex()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">spatial_partitioning</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">spatial_partitions</span></code></p></li>
<li><p><a class="reference internal" href="string.html#split" title="split"><code class="xref py py-func docutils literal notranslate"><span class="pre">split()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#split_part" title="split_part"><code class="xref py py-func docutils literal notranslate"><span class="pre">split_part()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#split_to_map" title="split_to_map"><code class="xref py py-func docutils literal notranslate"><span class="pre">split_to_map()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#split_to_multimap" title="split_to_multimap"><code class="xref py py-func docutils literal notranslate"><span class="pre">split_to_multimap()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#spooky_hash_v2_32" title="spooky_hash_v2_32"><code class="xref py py-func docutils literal notranslate"><span class="pre">spooky_hash_v2_32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#spooky_hash_v2_64" title="spooky_hash_v2_64"><code class="xref py py-func docutils literal notranslate"><span class="pre">spooky_hash_v2_64()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#sqrt" title="sqrt"><code class="xref py py-func docutils literal notranslate"><span class="pre">sqrt()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Area" title="ST_Area"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Area()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_AsBinary" title="ST_AsBinary"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_AsBinary()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_AsText" title="ST_AsText"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_AsText()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Boundary" title="ST_Boundary"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Boundary()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Buffer" title="ST_Buffer"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Buffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Centroid" title="ST_Centroid"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Centroid()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Contains" title="ST_Contains"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_ConvexHull" title="ST_ConvexHull"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_ConvexHull()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_CoordDim" title="ST_CoordDim"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_CoordDim()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Crosses" title="ST_Crosses"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Crosses()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Difference" title="ST_Difference"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Difference()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Dimension" title="ST_Dimension"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Dimension()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Disjoint" title="ST_Disjoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Disjoint()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Distance" title="ST_Distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Distance()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_EndPoint" title="ST_EndPoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_EndPoint()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Envelope" title="ST_Envelope"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Envelope()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_EnvelopeAsPts" title="ST_EnvelopeAsPts"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_EnvelopeAsPts()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Equals" title="ST_Equals"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Equals()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_ExteriorRing" title="ST_ExteriorRing"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_ExteriorRing()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Geometries" title="ST_Geometries"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Geometries()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_GeometryFromText" title="ST_GeometryFromText"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeometryFromText()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_GeometryN" title="ST_GeometryN"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeometryN()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_GeometryType" title="ST_GeometryType"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeometryType()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_GeomFromBinary" title="ST_GeomFromBinary"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_GeomFromBinary()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_InteriorRingN" title="ST_InteriorRingN"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_InteriorRingN()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_InteriorRings" title="ST_InteriorRings"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_InteriorRings()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Intersection" title="ST_Intersection"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Intersection()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Intersects" title="ST_Intersects"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Intersects()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_IsClosed" title="ST_IsClosed"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_IsClosed()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_IsEmpty" title="ST_IsEmpty"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_IsEmpty()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_IsRing" title="ST_IsRing"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_IsRing()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_IsSimple" title="ST_IsSimple"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_IsSimple()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_IsValid" title="ST_IsValid"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_IsValid()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Length" title="ST_Length"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Length()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_LineFromText" title="ST_LineFromText"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_LineFromText()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_LineString" title="ST_LineString"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_LineString()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_MultiPoint" title="ST_MultiPoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_MultiPoint()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_NumGeometries" title="ST_NumGeometries"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_NumGeometries()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ST_NumInteriorRing</span></code></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_NumPoints" title="ST_NumPoints"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_NumPoints()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Overlaps" title="ST_Overlaps"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Overlaps()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Point" title="ST_Point"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Point()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_PointN" title="ST_PointN"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_PointN()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Points" title="ST_Points"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Points()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Polygon" title="ST_Polygon"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Polygon()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Relate" title="ST_Relate"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Relate()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_StartPoint" title="ST_StartPoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_StartPoint()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_SymDifference" title="ST_SymDifference"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_SymDifference()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Touches" title="ST_Touches"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Touches()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Union" title="ST_Union"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Union()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Within" title="ST_Within"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Within()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_X" title="ST_X"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_X()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_XMax" title="ST_XMax"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_XMax()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_XMin" title="ST_XMin"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_XMin()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_Y" title="ST_Y"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_Y()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_YMax" title="ST_YMax"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_YMax()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#ST_YMin" title="ST_YMin"><code class="xref py py-func docutils literal notranslate"><span class="pre">ST_YMin()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#starts_with" title="starts_with"><code class="xref py py-func docutils literal notranslate"><span class="pre">starts_with()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#stddev" title="stddev"><code class="xref py py-func docutils literal notranslate"><span class="pre">stddev()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#stddev_pop" title="stddev_pop"><code class="xref py py-func docutils literal notranslate"><span class="pre">stddev_pop()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#stddev_samp" title="stddev_samp"><code class="xref py py-func docutils literal notranslate"><span class="pre">stddev_samp()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#strpos" title="strpos"><code class="xref py py-func docutils literal notranslate"><span class="pre">strpos()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#substr" title="substr"><code class="xref py py-func docutils literal notranslate"><span class="pre">substr()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#substring" title="substring"><code class="xref py py-func docutils literal notranslate"><span class="pre">substring()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#sum" title="sum"><code class="xref py py-func docutils literal notranslate"><span class="pre">sum()</span></code></a></p></li>
</ul>
</section>
<section id="t">
<h2 id="t">T<a class="headerlink" href="list.html#t" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="math.html#tan" title="tan"><code class="xref py py-func docutils literal notranslate"><span class="pre">tan()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#tanh" title="tanh"><code class="xref py py-func docutils literal notranslate"><span class="pre">tanh()</span></code></a></p></li>
<li><p><a class="reference internal" href="tdigest.html#tdigest_agg" title="tdigest_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">tdigest_agg()</span></code></a></p></li>
<li><p><a class="reference internal" href="../connector/mongodb.html#timestamp_objectid" title="timestamp_objectid"><code class="xref py py-func docutils literal notranslate"><span class="pre">timestamp_objectid()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#timezone" title="timezone"><code class="xref py py-func docutils literal notranslate"><span class="pre">timezone()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#timezone_hour" title="timezone_hour"><code class="xref py py-func docutils literal notranslate"><span class="pre">timezone_hour()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#timezone_minute" title="timezone_minute"><code class="xref py py-func docutils literal notranslate"><span class="pre">timezone_minute()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#to_base" title="to_base"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_base()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_base32" title="to_base32"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_base32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_base64" title="to_base64"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_base64()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_base64url" title="to_base64url"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_base64url()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_big_endian_32" title="to_big_endian_32"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_big_endian_32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_big_endian_64" title="to_big_endian_64"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_big_endian_64()</span></code></a></p></li>
<li><p><a class="reference internal" href="teradata.html#to_char" title="to_char"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_char()</span></code></a></p></li>
<li><p><a class="reference internal" href="teradata.html#to_date" title="to_date"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_date()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#to_encoded_polyline" title="to_encoded_polyline"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_encoded_polyline()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">to_geojson_geometry</span></code></p></li>
<li><p><a class="reference internal" href="geospatial.html#to_geometry" title="to_geometry"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_geometry()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_hex" title="to_hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_hex()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_ieee754_32" title="to_ieee754_32"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_ieee754_32()</span></code></a></p></li>
<li><p><a class="reference internal" href="binary.html#to_ieee754_64" title="to_ieee754_64"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_ieee754_64()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#to_iso8601" title="to_iso8601"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_iso8601()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#to_milliseconds" title="to_milliseconds"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_milliseconds()</span></code></a></p></li>
<li><p><a class="reference internal" href="geospatial.html#to_spherical_geography" title="to_spherical_geography"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_spherical_geography()</span></code></a></p></li>
<li><p><a class="reference internal" href="teradata.html#to_timestamp" title="to_timestamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_timestamp()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#to_unixtime" title="to_unixtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_unixtime()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#to_utf8" title="to_utf8"><code class="xref py py-func docutils literal notranslate"><span class="pre">to_utf8()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#transform" title="transform"><code class="xref py py-func docutils literal notranslate"><span class="pre">transform()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#transform_keys" title="transform_keys"><code class="xref py py-func docutils literal notranslate"><span class="pre">transform_keys()</span></code></a></p></li>
<li><p><a class="reference internal" href="map.html#transform_values" title="transform_values"><code class="xref py py-func docutils literal notranslate"><span class="pre">transform_values()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#translate" title="translate"><code class="xref py py-func docutils literal notranslate"><span class="pre">translate()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#trim" title="trim"><code class="xref py py-func docutils literal notranslate"><span class="pre">trim()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#trim_array" title="trim_array"><code class="xref py py-func docutils literal notranslate"><span class="pre">trim_array()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#truncate" title="truncate"><code class="xref py py-func docutils literal notranslate"><span class="pre">truncate()</span></code></a></p></li>
<li><p><a class="reference internal" href="conditional.html#try-function"><span class="std std-ref">try</span></a></p></li>
<li><p><a class="reference internal" href="conversion.html#try_cast" title="try_cast"><code class="xref py py-func docutils literal notranslate"><span class="pre">try_cast()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#typeof" title="typeof"><code class="xref py py-func docutils literal notranslate"><span class="pre">typeof()</span></code></a></p></li>
</ul>
</section>
<section id="u">
<h2 id="u">U<a class="headerlink" href="list.html#u" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="string.html#upper" title="upper"><code class="xref py py-func docutils literal notranslate"><span class="pre">upper()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_decode" title="url_decode"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_decode()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_encode" title="url_encode"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_encode()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_fragment" title="url_extract_fragment"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_fragment()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_host" title="url_extract_host"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_host()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_parameter" title="url_extract_parameter"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_parameter()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_path" title="url_extract_path"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_path()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_protocol" title="url_extract_protocol"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_protocol()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_port" title="url_extract_port"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_port()</span></code></a></p></li>
<li><p><a class="reference internal" href="url.html#url_extract_query" title="url_extract_query"><code class="xref py py-func docutils literal notranslate"><span class="pre">url_extract_query()</span></code></a></p></li>
<li><p><a class="reference internal" href="uuid.html#uuid" title="uuid"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid()</span></code></a></p></li>
</ul>
</section>
<section id="v">
<h2 id="v">V<a class="headerlink" href="list.html#v" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="qdigest.html#value_at_quantile" title="value_at_quantile"><code class="xref py py-func docutils literal notranslate"><span class="pre">value_at_quantile()</span></code></a></p></li>
<li><p><a class="reference internal" href="qdigest.html#values_at_quantiles" title="values_at_quantiles"><code class="xref py py-func docutils literal notranslate"><span class="pre">values_at_quantiles()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#var_pop" title="var_pop"><code class="xref py py-func docutils literal notranslate"><span class="pre">var_pop()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#var_samp" title="var_samp"><code class="xref py py-func docutils literal notranslate"><span class="pre">var_samp()</span></code></a></p></li>
<li><p><a class="reference internal" href="aggregate.html#variance" title="variance"><code class="xref py py-func docutils literal notranslate"><span class="pre">variance()</span></code></a></p></li>
<li><p><a class="reference internal" href="system.html#version" title="version"><code class="xref py py-func docutils literal notranslate"><span class="pre">version()</span></code></a></p></li>
</ul>
</section>
<section id="w">
<h2 id="w">W<a class="headerlink" href="list.html#w" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="datetime.html#week" title="week"><code class="xref py py-func docutils literal notranslate"><span class="pre">week()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#week_of_year" title="week_of_year"><code class="xref py py-func docutils literal notranslate"><span class="pre">week_of_year()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#width_bucket" title="width_bucket"><code class="xref py py-func docutils literal notranslate"><span class="pre">width_bucket()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#wilson_interval_lower" title="wilson_interval_lower"><code class="xref py py-func docutils literal notranslate"><span class="pre">wilson_interval_lower()</span></code></a></p></li>
<li><p><a class="reference internal" href="math.html#wilson_interval_upper" title="wilson_interval_upper"><code class="xref py py-func docutils literal notranslate"><span class="pre">wilson_interval_upper()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#with_timezone" title="with_timezone"><code class="xref py py-func docutils literal notranslate"><span class="pre">with_timezone()</span></code></a></p></li>
<li><p><a class="reference internal" href="string.html#word_stem" title="word_stem"><code class="xref py py-func docutils literal notranslate"><span class="pre">word_stem()</span></code></a></p></li>
</ul>
</section>
<section id="x">
<h2 id="x">X<a class="headerlink" href="list.html#x" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="binary.html#xxhash64" title="xxhash64"><code class="xref py py-func docutils literal notranslate"><span class="pre">xxhash64()</span></code></a></p></li>
</ul>
</section>
<section id="y">
<h2 id="y">Y<a class="headerlink" href="list.html#y" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="datetime.html#year" title="year"><code class="xref py py-func docutils literal notranslate"><span class="pre">year()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#year_of_week" title="year_of_week"><code class="xref py py-func docutils literal notranslate"><span class="pre">year_of_week()</span></code></a></p></li>
<li><p><a class="reference internal" href="datetime.html#yow" title="yow"><code class="xref py py-func docutils literal notranslate"><span class="pre">yow()</span></code></a></p></li>
</ul>
</section>
<section id="z">
<h2 id="z">Z<a class="headerlink" href="list.html#z" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="array.html#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a></p></li>
<li><p><a class="reference internal" href="array.html#zip_with" title="zip_with"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip_with()</span></code></a></p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../functions.html" title="Functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Functions and operators </span>
              </div>
            </a>
          
          
            <a href="list-by-topic.html" title="List of functions by topic"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> List of functions by topic </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>