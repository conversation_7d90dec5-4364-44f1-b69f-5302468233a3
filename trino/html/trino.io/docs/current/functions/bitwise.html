<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Bitwise functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="bitwise.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Color functions" href="color.html" />
    <link rel="prev" title="Binary functions and operators" href="binary.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="bitwise.html#functions/bitwise" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Bitwise functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="list.html" class="md-nav__link">List of functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="list-by-topic.html" class="md-nav__link">List of functions by topic</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="aggregate.html" class="md-nav__link">Aggregate</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ai.html" class="md-nav__link">AI</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="array.html" class="md-nav__link">Array</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="binary.html" class="md-nav__link">Binary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Bitwise </label>
    
      <a href="bitwise.html#" class="md-nav__link md-nav__link--active">Bitwise</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="bitwise.html#bit_count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bit_count()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_and" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_and()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_not" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_not()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_or" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_or()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_xor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_xor()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_left_shift" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_left_shift()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_right_shift" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_right_shift()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_right_shift_arithmetic" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_right_shift_arithmetic()</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="color.html" class="md-nav__link">Color</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="comparison.html" class="md-nav__link">Comparison</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conditional.html" class="md-nav__link">Conditional</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="conversion.html" class="md-nav__link">Conversion</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="datetime.html" class="md-nav__link">Date and time</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="decimal.html" class="md-nav__link">Decimal</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="geospatial.html" class="md-nav__link">Geospatial</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hyperloglog.html" class="md-nav__link">HyperLogLog</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ipaddress.html" class="md-nav__link">IP Address</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="json.html" class="md-nav__link">JSON</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="lambda.html" class="md-nav__link">Lambda</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="logical.html" class="md-nav__link">Logical</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ml.html" class="md-nav__link">Machine learning</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="map.html" class="md-nav__link">Map</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="math.html" class="md-nav__link">Math</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="qdigest.html" class="md-nav__link">Quantile digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="regexp.html" class="md-nav__link">Regular expression</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="session.html" class="md-nav__link">Session</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="setdigest.html" class="md-nav__link">Set Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="string.html" class="md-nav__link">String</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table.html" class="md-nav__link">Table</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="teradata.html" class="md-nav__link">Teradata</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tdigest.html" class="md-nav__link">T-Digest</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="url.html" class="md-nav__link">URL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="uuid.html" class="md-nav__link">UUID</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="window.html" class="md-nav__link">Window</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="bitwise.html#bit_count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bit_count()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_and" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_and()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_not" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_not()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_or" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_or()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_xor" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_xor()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_left_shift" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_left_shift()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_right_shift" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_right_shift()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="bitwise.html#bitwise_right_shift_arithmetic" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">bitwise_right_shift_arithmetic()</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="bitwise-functions">
<h1 id="functions-bitwise--page-root">Bitwise functions<a class="headerlink" href="bitwise.html#functions-bitwise--page-root" title="Link to this heading">#</a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="bit_count">
<span class="sig-name descname"><span class="pre">bit_count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bits</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="bitwise.html#bit_count" title="Link to this definition">#</a></dt>
<dd><p>Count the number of bits set in <code class="docutils literal notranslate"><span class="pre">x</span></code> (treated as <code class="docutils literal notranslate"><span class="pre">bits</span></code>-bit signed
integer) in 2’s complement representation:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bit_count</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bit_count</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">8</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 2</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bit_count</span><span class="p">(</span><span class="o">-</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 62</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bit_count</span><span class="p">(</span><span class="o">-</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">8</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 6</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_and">
<span class="sig-name descname"><span class="pre">bitwise_and</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="bitwise.html#bitwise_and" title="Link to this definition">#</a></dt>
<dd><p>Returns the bitwise AND of <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> in 2’s complement representation.</p>
<p>Bitwise AND of <code class="docutils literal notranslate"><span class="pre">19</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">10011</span></code>) and <code class="docutils literal notranslate"><span class="pre">25</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">11001</span></code>) results in
<code class="docutils literal notranslate"><span class="pre">17</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">10001</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_and</span><span class="p">(</span><span class="mi">19</span><span class="p">,</span><span class="mi">25</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 17</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_not">
<span class="sig-name descname"><span class="pre">bitwise_not</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="bitwise.html#bitwise_not" title="Link to this definition">#</a></dt>
<dd><p>Returns the bitwise NOT of <code class="docutils literal notranslate"><span class="pre">x</span></code> in 2’s complement representation
(<code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">x</span> <span class="pre">=</span> <span class="pre">-x</span> <span class="pre">-</span> <span class="pre">1</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_not</span><span class="p">(</span><span class="o">-</span><span class="mi">12</span><span class="p">);</span><span class="w"> </span><span class="c1">--  11</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_not</span><span class="p">(</span><span class="mi">19</span><span class="p">);</span><span class="w">  </span><span class="c1">-- -20</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_not</span><span class="p">(</span><span class="mi">25</span><span class="p">);</span><span class="w">  </span><span class="c1">-- -26</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_or">
<span class="sig-name descname"><span class="pre">bitwise_or</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="bitwise.html#bitwise_or" title="Link to this definition">#</a></dt>
<dd><p>Returns the bitwise OR of <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> in 2’s complement representation.</p>
<p>Bitwise OR of <code class="docutils literal notranslate"><span class="pre">19</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">10011</span></code>) and <code class="docutils literal notranslate"><span class="pre">25</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">11001</span></code>) results in
<code class="docutils literal notranslate"><span class="pre">27</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">11011</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_or</span><span class="p">(</span><span class="mi">19</span><span class="p">,</span><span class="mi">25</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 27</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_xor">
<span class="sig-name descname"><span class="pre">bitwise_xor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">bigint</span></span></span><a class="headerlink" href="bitwise.html#bitwise_xor" title="Link to this definition">#</a></dt>
<dd><p>Returns the bitwise XOR of <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> in 2’s complement representation.</p>
<p>Bitwise XOR of <code class="docutils literal notranslate"><span class="pre">19</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">10011</span></code>) and <code class="docutils literal notranslate"><span class="pre">25</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">11001</span></code>) results in
<code class="docutils literal notranslate"><span class="pre">10</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">01010</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_xor</span><span class="p">(</span><span class="mi">19</span><span class="p">,</span><span class="mi">25</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 10</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_left_shift">
<span class="sig-name descname"><span class="pre">bitwise_left_shift</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shift</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">value]</span></span></span><a class="headerlink" href="bitwise.html#bitwise_left_shift" title="Link to this definition">#</a></dt>
<dd><p>Returns the left shifted value of <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">1</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">001</span></code>) by two bits results in <code class="docutils literal notranslate"><span class="pre">4</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">00100</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 4</span>
</pre></div>
</div>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">5</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">0101</span></code>) by two bits results in <code class="docutils literal notranslate"><span class="pre">20</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">010100</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 20</span>
</pre></div>
</div>
<p>Shifting a <code class="docutils literal notranslate"><span class="pre">value</span></code> by <code class="docutils literal notranslate"><span class="pre">0</span></code> always results in the original <code class="docutils literal notranslate"><span class="pre">value</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 20</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">42</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 42</span>
</pre></div>
</div>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">0</span></code> by a <code class="docutils literal notranslate"><span class="pre">shift</span></code> always results in <code class="docutils literal notranslate"><span class="pre">0</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_left_shift</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_right_shift">
<span class="sig-name descname"><span class="pre">bitwise_right_shift</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shift</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">value]</span></span></span><a class="headerlink" href="bitwise.html#bitwise_right_shift" title="Link to this definition">#</a></dt>
<dd><p>Returns the logical right shifted value of <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">8</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">1000</span></code>) by three bits results in <code class="docutils literal notranslate"><span class="pre">1</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">001</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 1</span>
</pre></div>
</div>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">9</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">1001</span></code>) by one bit results in <code class="docutils literal notranslate"><span class="pre">4</span></code> (binary: <code class="docutils literal notranslate"><span class="pre">100</span></code>):</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 4</span>
</pre></div>
</div>
<p>Shifting a <code class="docutils literal notranslate"><span class="pre">value</span></code> by <code class="docutils literal notranslate"><span class="pre">0</span></code> always results in the original <code class="docutils literal notranslate"><span class="pre">value</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 20</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">42</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 42</span>
</pre></div>
</div>
<p>Shifting a <code class="docutils literal notranslate"><span class="pre">value</span></code> by <code class="docutils literal notranslate"><span class="pre">64</span></code> or more bits results in <code class="docutils literal notranslate"><span class="pre">0</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="o">-</span><span class="mi">45</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
</pre></div>
</div>
<p>Shifting <code class="docutils literal notranslate"><span class="pre">0</span></code> by a <code class="docutils literal notranslate"><span class="pre">shift</span></code> always results in <code class="docutils literal notranslate"><span class="pre">0</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"> </span><span class="c1">-- 0</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="bitwise_right_shift_arithmetic">
<span class="sig-name descname"><span class="pre">bitwise_right_shift_arithmetic</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shift</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">[same</span> <span class="pre">as</span> <span class="pre">value]</span></span></span><a class="headerlink" href="bitwise.html#bitwise_right_shift_arithmetic" title="Link to this definition">#</a></dt>
<dd><p>Returns the arithmetic right shifted value of <code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
<p>Returns the same values as <a class="reference internal" href="bitwise.html#bitwise_right_shift" title="bitwise_right_shift"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_right_shift()</span></code></a> when shifting by less than
<code class="docutils literal notranslate"><span class="pre">64</span></code> bits. Shifting by <code class="docutils literal notranslate"><span class="pre">64</span></code> or more bits results in <code class="docutils literal notranslate"><span class="pre">0</span></code> for a positive and
<code class="docutils literal notranslate"><span class="pre">-1</span></code> for a negative <code class="docutils literal notranslate"><span class="pre">value</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift_arithmetic</span><span class="p">(</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">--  0</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">bitwise_right_shift_arithmetic</span><span class="p">(</span><span class="o">-</span><span class="mi">45</span><span class="p">,</span><span class="w"> </span><span class="mi">64</span><span class="p">);</span><span class="w"> </span><span class="c1">-- -1</span>
</pre></div>
</div>
</dd></dl>
<p>See also <a class="reference internal" href="aggregate.html#bitwise_and_agg" title="bitwise_and_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_and_agg()</span></code></a> and <a class="reference internal" href="aggregate.html#bitwise_or_agg" title="bitwise_or_agg"><code class="xref py py-func docutils literal notranslate"><span class="pre">bitwise_or_agg()</span></code></a>.</p>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="binary.html" title="Binary functions and operators"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Binary functions and operators </span>
              </div>
            </a>
          
          
            <a href="color.html" title="Color functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Color functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>