<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Migrating from Hive &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="from-hive.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Legal notices" href="legal-notices.html" />
    <link rel="prev" title="Appendix" href="../appendix.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="from-hive.html#appendix/from-hive" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Migrating from Hive </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Migrating from Hive </label>
    
      <a href="from-hive.html#" class="md-nav__link md-nav__link--active">Migrating from Hive</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="from-hive.html#use-subscript-for-accessing-a-dynamic-index-of-an-array-instead-of-an-udf" class="md-nav__link">Use subscript for accessing a dynamic index of an array instead of an udf</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#avoid-out-of-bounds-access-of-arrays" class="md-nav__link">Avoid out of bounds access of arrays</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-arrays" class="md-nav__link">Use ANSI SQL syntax for arrays</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-identifiers-and-strings" class="md-nav__link">Use ANSI SQL syntax for identifiers and strings</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#quote-identifiers-that-start-with-numbers" class="md-nav__link">Quote identifiers that start with numbers</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-the-standard-string-concatenation-operator" class="md-nav__link">Use the standard string concatenation operator</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-standard-types-for-cast-targets" class="md-nav__link">Use standard types for CAST targets</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-cast-when-dividing-integers" class="md-nav__link">Use CAST when dividing integers</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-with-for-complex-expressions-or-queries" class="md-nav__link">Use WITH for complex expressions or queries</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-unnest-to-expand-arrays-and-maps" class="md-nav__link">Use UNNEST to expand arrays and maps</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-date-and-time-interval-expressions" class="md-nav__link">Use ANSI SQL syntax for date and time INTERVAL expressions</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#caution-with-datediff" class="md-nav__link">Caution with datediff</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#overwriting-data-on-insert" class="md-nav__link">Overwriting data on insert</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="legal-notices.html" class="md-nav__link">Legal notices</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="from-hive.html#use-subscript-for-accessing-a-dynamic-index-of-an-array-instead-of-an-udf" class="md-nav__link">Use subscript for accessing a dynamic index of an array instead of an udf</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#avoid-out-of-bounds-access-of-arrays" class="md-nav__link">Avoid out of bounds access of arrays</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-arrays" class="md-nav__link">Use ANSI SQL syntax for arrays</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-identifiers-and-strings" class="md-nav__link">Use ANSI SQL syntax for identifiers and strings</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#quote-identifiers-that-start-with-numbers" class="md-nav__link">Quote identifiers that start with numbers</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-the-standard-string-concatenation-operator" class="md-nav__link">Use the standard string concatenation operator</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-standard-types-for-cast-targets" class="md-nav__link">Use standard types for CAST targets</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-cast-when-dividing-integers" class="md-nav__link">Use CAST when dividing integers</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-with-for-complex-expressions-or-queries" class="md-nav__link">Use WITH for complex expressions or queries</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-unnest-to-expand-arrays-and-maps" class="md-nav__link">Use UNNEST to expand arrays and maps</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#use-ansi-sql-syntax-for-date-and-time-interval-expressions" class="md-nav__link">Use ANSI SQL syntax for date and time INTERVAL expressions</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#caution-with-datediff" class="md-nav__link">Caution with datediff</a>
        </li>
        <li class="md-nav__item"><a href="from-hive.html#overwriting-data-on-insert" class="md-nav__link">Overwriting data on insert</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="migrating-from-hive">
<h1 id="appendix-from-hive--page-root">Migrating from Hive<a class="headerlink" href="from-hive.html#appendix-from-hive--page-root" title="Link to this heading">#</a></h1>
<p>Trino uses ANSI SQL syntax and semantics, whereas Hive uses a language similar
to SQL called HiveQL which is loosely modeled after MySQL (which itself has many
differences from ANSI SQL).</p>
<section id="use-subscript-for-accessing-a-dynamic-index-of-an-array-instead-of-an-udf">
<h2 id="use-subscript-for-accessing-a-dynamic-index-of-an-array-instead-of-an-udf">Use subscript for accessing a dynamic index of an array instead of an udf<a class="headerlink" href="from-hive.html#use-subscript-for-accessing-a-dynamic-index-of-an-array-instead-of-an-udf" title="Link to this heading">#</a></h2>
<p>The subscript operator in SQL supports full expressions, unlike Hive (which only supports constants). Therefore you can write queries like:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">my_array</span><span class="p">[</span><span class="k">CARDINALITY</span><span class="p">(</span><span class="n">my_array</span><span class="p">)]</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">last_element</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
</section>
<section id="avoid-out-of-bounds-access-of-arrays">
<h2 id="avoid-out-of-bounds-access-of-arrays">Avoid out of bounds access of arrays<a class="headerlink" href="from-hive.html#avoid-out-of-bounds-access-of-arrays" title="Link to this heading">#</a></h2>
<p>Accessing out of bounds elements of an array will result in an exception. You can avoid this with an <code class="docutils literal notranslate"><span class="pre">if</span></code> as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">IF</span><span class="p">(</span><span class="k">CARDINALITY</span><span class="p">(</span><span class="n">my_array</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="n">my_array</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
</section>
<section id="use-ansi-sql-syntax-for-arrays">
<h2 id="use-ansi-sql-syntax-for-arrays">Use ANSI SQL syntax for arrays<a class="headerlink" href="from-hive.html#use-ansi-sql-syntax-for-arrays" title="Link to this heading">#</a></h2>
<p>Arrays are indexed starting from 1, not from 0:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">my_array</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">first_element</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
<p>Construct arrays with ANSI syntax:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">]</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">my_array</span>
</pre></div>
</div>
</section>
<section id="use-ansi-sql-syntax-for-identifiers-and-strings">
<h2 id="use-ansi-sql-syntax-for-identifiers-and-strings">Use ANSI SQL syntax for identifiers and strings<a class="headerlink" href="from-hive.html#use-ansi-sql-syntax-for-identifiers-and-strings" title="Link to this heading">#</a></h2>
<p>Strings are delimited with single quotes and identifiers are quoted with double quotes, not backquotes:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="ss">"User Name"</span>
<span class="k">FROM</span><span class="w"> </span><span class="ss">"7day_active"</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'foo'</span>
</pre></div>
</div>
</section>
<section id="quote-identifiers-that-start-with-numbers">
<h2 id="quote-identifiers-that-start-with-numbers">Quote identifiers that start with numbers<a class="headerlink" href="from-hive.html#quote-identifiers-that-start-with-numbers" title="Link to this heading">#</a></h2>
<p>Identifiers that start with numbers are not legal in ANSI SQL and must be quoted using double quotes:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="ss">"7day_active"</span>
</pre></div>
</div>
</section>
<section id="use-the-standard-string-concatenation-operator">
<h2 id="use-the-standard-string-concatenation-operator">Use the standard string concatenation operator<a class="headerlink" href="from-hive.html#use-the-standard-string-concatenation-operator" title="Link to this heading">#</a></h2>
<p>Use the ANSI SQL string concatenation operator:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">b</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">c</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
</section>
<section id="use-standard-types-for-cast-targets">
<h2 id="use-standard-types-for-cast-targets">Use standard types for CAST targets<a class="headerlink" href="from-hive.html#use-standard-types-for-cast-targets" title="Link to this heading">#</a></h2>
<p>The following standard types are supported for <code class="docutils literal notranslate"><span class="pre">CAST</span></code> targets:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="k">CAST</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">varchar</span><span class="p">)</span>
<span class="p">,</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">bigint</span><span class="p">)</span>
<span class="p">,</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">double</span><span class="p">)</span>
<span class="p">,</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">boolean</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
<p>In particular, use <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> instead of <code class="docutils literal notranslate"><span class="pre">STRING</span></code>.</p>
</section>
<section id="use-cast-when-dividing-integers">
<h2 id="use-cast-when-dividing-integers">Use CAST when dividing integers<a class="headerlink" href="from-hive.html#use-cast-when-dividing-integers" title="Link to this heading">#</a></h2>
<p>Trino follows the standard behavior of performing integer division when dividing two integers. For example, dividing <code class="docutils literal notranslate"><span class="pre">7</span></code> by <code class="docutils literal notranslate"><span class="pre">2</span></code> will result in <code class="docutils literal notranslate"><span class="pre">3</span></code>, not <code class="docutils literal notranslate"><span class="pre">3.5</span></code>.
To perform floating point division on two integers, cast one of them to a double:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="mi">5</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">)</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">2</span>
</pre></div>
</div>
</section>
<section id="use-with-for-complex-expressions-or-queries">
<h2 id="use-with-for-complex-expressions-or-queries">Use WITH for complex expressions or queries<a class="headerlink" href="from-hive.html#use-with-for-complex-expressions-or-queries" title="Link to this heading">#</a></h2>
<p>When you want to re-use a complex output expression as a filter, use either an inline subquery or factor it out using the <code class="docutils literal notranslate"><span class="pre">WITH</span></code> clause:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">  </span><span class="k">SELECT</span><span class="w"> </span><span class="n">substr</span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="n">x</span>
<span class="w">  </span><span class="k">FROM</span><span class="w"> </span><span class="p">...</span>
<span class="p">)</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">a</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'foo'</span>
</pre></div>
</div>
</section>
<section id="use-unnest-to-expand-arrays-and-maps">
<h2 id="use-unnest-to-expand-arrays-and-maps">Use UNNEST to expand arrays and maps<a class="headerlink" href="from-hive.html#use-unnest-to-expand-arrays-and-maps" title="Link to this heading">#</a></h2>
<p>Trino supports <a class="reference internal" href="../sql/select.html#unnest"><span class="std std-ref">UNNEST</span></a> for expanding arrays and maps.
Use <code class="docutils literal notranslate"><span class="pre">UNNEST</span></code> instead of <code class="docutils literal notranslate"><span class="pre">LATERAL</span> <span class="pre">VIEW</span> <span class="pre">explode()</span></code>.</p>
<p>Hive query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">student</span><span class="p">,</span><span class="w"> </span><span class="n">score</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">tests</span>
<span class="k">LATERAL</span><span class="w"> </span><span class="k">VIEW</span><span class="w"> </span><span class="n">explode</span><span class="p">(</span><span class="n">scores</span><span class="p">)</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">score</span><span class="p">;</span>
</pre></div>
</div>
<p>Trino query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">student</span><span class="p">,</span><span class="w"> </span><span class="n">score</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">tests</span>
<span class="k">CROSS</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="k">UNNEST</span><span class="p">(</span><span class="n">scores</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="p">(</span><span class="n">score</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="use-ansi-sql-syntax-for-date-and-time-interval-expressions">
<h2 id="use-ansi-sql-syntax-for-date-and-time-interval-expressions">Use ANSI SQL syntax for date and time INTERVAL expressions<a class="headerlink" href="from-hive.html#use-ansi-sql-syntax-for-date-and-time-interval-expressions" title="Link to this heading">#</a></h2>
<p>Trino supports the ANSI SQL style <code class="docutils literal notranslate"><span class="pre">INTERVAL</span></code> expressions that differs from the implementation used in Hive.</p>
<ul class="simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">INTERVAL</span></code> keyword is required and is not optional.</p></li>
<li><p>Date and time units must be singular. For example <code class="docutils literal notranslate"><span class="pre">day</span></code> and not <code class="docutils literal notranslate"><span class="pre">days</span></code>.</p></li>
<li><p>Values must be quoted.</p></li>
</ul>
<p>Hive query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'2000-08-19'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">date</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">14</span><span class="w"> </span><span class="n">days</span><span class="p">;</span>
</pre></div>
</div>
<p>Equivalent Trino query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">cast</span><span class="p">(</span><span class="s1">'2000-08-19'</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nb">date</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nb">INTERVAL</span><span class="w"> </span><span class="s1">'14'</span><span class="w"> </span><span class="k">day</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="caution-with-datediff">
<h2 id="caution-with-datediff">Caution with datediff<a class="headerlink" href="from-hive.html#caution-with-datediff" title="Link to this heading">#</a></h2>
<p>The Hive <code class="docutils literal notranslate"><span class="pre">datediff</span></code> function returns the difference between the two dates in
days and is declared as:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>datediff(string enddate, string startdate)  -&gt; integer
</pre></div>
</div>
<p>The equivalent Trino function <a class="reference internal" href="../functions/datetime.html#datetime-interval-functions"><span class="std std-ref">date_diff</span></a>
uses a reverse order for the two date parameters and requires a unit. This has
to be taken into account when migrating:</p>
<p>Hive query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">datediff</span><span class="p">(</span><span class="n">enddate</span><span class="p">,</span><span class="w"> </span><span class="n">startdate</span><span class="p">)</span>
</pre></div>
</div>
<p>Trino query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">date_diff</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="n">startdate</span><span class="p">,</span><span class="w"> </span><span class="n">enddate</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="overwriting-data-on-insert">
<h2 id="overwriting-data-on-insert">Overwriting data on insert<a class="headerlink" href="from-hive.html#overwriting-data-on-insert" title="Link to this heading">#</a></h2>
<p>By default, <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> queries are not allowed to overwrite existing data. You
can use the catalog session property <code class="docutils literal notranslate"><span class="pre">insert_existing_partitions_behavior</span></code> to
allow overwrites. Prepend the name of the catalog using the Hive connector, for
example <code class="docutils literal notranslate"><span class="pre">hdfs</span></code>, and set the property in the session before you run the insert
query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SET</span><span class="w"> </span><span class="k">SESSION</span><span class="w"> </span><span class="n">hdfs</span><span class="p">.</span><span class="n">insert_existing_partitions_behavior</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'OVERWRITE'</span><span class="p">;</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">hdfs</span><span class="p">.</span><span class="k">schema</span><span class="p">.</span><span class="k">table</span><span class="w"> </span><span class="p">...</span>
</pre></div>
</div>
<p>The resulting behavior is equivalent to using <a class="reference external" href="https://cwiki.apache.org/confluence/display/Hive/LanguageManual+DML">INSERT OVERWRITE</a> in Hive.</p>
<p>Insert overwrite operation is not supported by Trino when the table is stored on
encrypted HDFS, when the table is unpartitioned or table is transactional.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../appendix.html" title="Appendix"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Appendix </span>
              </div>
            </a>
          
          
            <a href="legal-notices.html" title="Legal notices"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Legal notices </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>