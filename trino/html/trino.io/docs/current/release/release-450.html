<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Release 450 (19 Jun 2024) &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="release-450.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Release 449 (31 May 2024)" href="release-449.html" />
    <link rel="prev" title="Release 451 (27 Jun 2024)" href="release-451.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="release-450.html#release/release-450" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Release 450 (19 Jun 2024) </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="release-476.html" class="md-nav__link">Release 476 (5 Jun 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-475.html" class="md-nav__link">Release 475 (23 Apr 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-474.html" class="md-nav__link">Release 474 (21 Mar 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-473.html" class="md-nav__link">Release 473 (19 Mar 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-472.html" class="md-nav__link">Release 472 (5 Mar 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-471.html" class="md-nav__link">Release 471 (19 Feb 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-470.html" class="md-nav__link">Release 470 (5 Feb 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-469.html" class="md-nav__link">Release 469 (27 Jan 2025)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-468.html" class="md-nav__link">Release 468 (17 Dec 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-467.html" class="md-nav__link">Release 467 (6 Dec 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-466.html" class="md-nav__link">Release 466 (27 Nov 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-465.html" class="md-nav__link">Release 465 (20 Nov 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-464.html" class="md-nav__link">Release 464 (30 Oct 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-463.html" class="md-nav__link">Release 463 (23 Oct 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-462.html" class="md-nav__link">Release 462 (16 Oct 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-461.html" class="md-nav__link">Release 461 (10 Oct 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-460.html" class="md-nav__link">Release 460 (3 Oct 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-459.html" class="md-nav__link">Release 459 (25 Sep 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-458.html" class="md-nav__link">Release 458 (17 Sep 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-457.html" class="md-nav__link">Release 457 (6 Sep 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-456.html" class="md-nav__link">Release 456 (6 Sep 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-455.html" class="md-nav__link">Release 455 (29 Aug 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-454.html" class="md-nav__link">Release 454 (15 Aug 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-453.html" class="md-nav__link">Release 453 (25 Jul 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-452.html" class="md-nav__link">Release 452 (11 Jul 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-451.html" class="md-nav__link">Release 451 (27 Jun 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Release 450 (19 Jun 2024) </label>
    
      <a href="release-450.html#" class="md-nav__link md-nav__link--active">Release 450 (19 Jun 2024)</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="release-450.html#general" class="md-nav__link">General</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#security" class="md-nav__link">Security</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#jdbc-driver" class="md-nav__link">JDBC driver</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#bigquery-connector" class="md-nav__link">BigQuery connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#cassandra-connector" class="md-nav__link">Cassandra connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#clickhouse-connector" class="md-nav__link">ClickHouse connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#delta-lake-connector" class="md-nav__link">Delta Lake connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#hive-connector" class="md-nav__link">Hive connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#hudi-connector" class="md-nav__link">Hudi connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#iceberg-connector" class="md-nav__link">Iceberg connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#mariadb-connector" class="md-nav__link">MariaDB connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#memory-connector" class="md-nav__link">Memory connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#mysql-connector" class="md-nav__link">MySQL connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#pinot-connector" class="md-nav__link">Pinot connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#redshift-connector" class="md-nav__link">Redshift connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#singlestore-connector" class="md-nav__link">SingleStore connector</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-449.html" class="md-nav__link">Release 449 (31 May 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-448.html" class="md-nav__link">Release 448 (15 May 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-447.html" class="md-nav__link">Release 447 (8 May 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-446.html" class="md-nav__link">Release 446 (1 May 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-445.html" class="md-nav__link">Release 445 (17 Apr 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-444.html" class="md-nav__link">Release 444 (3 Apr 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-443.html" class="md-nav__link">Release 443 (21 Mar 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-442.html" class="md-nav__link">Release 442 (14 Mar 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-441.html" class="md-nav__link">Release 441 (13 Mar 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-440.html" class="md-nav__link">Release 440 (8 Mar 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-439.html" class="md-nav__link">Release 439 (15 Feb 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-438.html" class="md-nav__link">Release 438 (1 Feb 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-437.html" class="md-nav__link">Release 437 (24 Jan 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-436.html" class="md-nav__link">Release 436 (11 Jan 2024)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-435.html" class="md-nav__link">Release 435 (13 Dec 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-434.html" class="md-nav__link">Release 434 (29 Nov 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-433.html" class="md-nav__link">Release 433 (10 Nov 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-432.html" class="md-nav__link">Release 432 (2 Nov 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-431.html" class="md-nav__link">Release 431 (27 Oct 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-430.html" class="md-nav__link">Release 430 (20 Oct 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-429.html" class="md-nav__link">Release 429 (11 Oct 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-428.html" class="md-nav__link">Release 428 (4 Oct 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-427.html" class="md-nav__link">Release 427 (26 Sep 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-426.html" class="md-nav__link">Release 426 (5 Sep 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-425.html" class="md-nav__link">Release 425 (24 Aug 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-424.html" class="md-nav__link">Release 424 (17 Aug 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-423.html" class="md-nav__link">Release 423 (10 Aug 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-422.html" class="md-nav__link">Release 422 (13 Jul 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-421.html" class="md-nav__link">Release 421 (6 Jul 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-420.html" class="md-nav__link">Release 420 (22 Jun 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-419.html" class="md-nav__link">Release 419 (5 Jun 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-418.html" class="md-nav__link">Release 418 (17 May 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-417.html" class="md-nav__link">Release 417 (10 May 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-416.html" class="md-nav__link">Release 416 (3 May 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-415.html" class="md-nav__link">Release 415 (28 Apr 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-414.html" class="md-nav__link">Release 414 (19 Apr 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-413.html" class="md-nav__link">Release 413 (12 Apr 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-412.html" class="md-nav__link">Release 412 (5 Apr 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-411.html" class="md-nav__link">Release 411 (29 Mar 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-410.html" class="md-nav__link">Release 410 (8 Mar 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-409.html" class="md-nav__link">Release 409 (3 Mar 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-408.html" class="md-nav__link">Release 408 (23 Feb 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-407.html" class="md-nav__link">Release 407 (16 Feb 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-406.html" class="md-nav__link">Release 406 (25 Jan 2023)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-405.html" class="md-nav__link">Release 405 (28 Dec 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-404.html" class="md-nav__link">Release 404 (???)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-403.html" class="md-nav__link">Release 403 (15 Nov 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-402.html" class="md-nav__link">Release 402 (2 Nov 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-401.html" class="md-nav__link">Release 401 (26 Oct 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-400.html" class="md-nav__link">Release 400 (13 Oct 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-399.html" class="md-nav__link">Release 399 (6 Oct 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-398.html" class="md-nav__link">Release 398 (28 Sep 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-397.html" class="md-nav__link">Release 397 (21 Sep 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-396.html" class="md-nav__link">Release 396 (15 Sep 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-395.html" class="md-nav__link">Release 395 (7 Sep 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-394.html" class="md-nav__link">Release 394 (29 Aug 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-393.html" class="md-nav__link">Release 393 (17 Aug 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-392.html" class="md-nav__link">Release 392 (3 Aug 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-391.html" class="md-nav__link">Release 391 (22 Jul 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-390.html" class="md-nav__link">Release 390 (13 Jul 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-389.html" class="md-nav__link">Release 389 (7 Jul 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-388.html" class="md-nav__link">Release 388 (29 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-387.html" class="md-nav__link">Release 387 (22 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-386.html" class="md-nav__link">Release 386 (15 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-385.html" class="md-nav__link">Release 385 (8 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-384.html" class="md-nav__link">Release 384 (3 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-383.html" class="md-nav__link">Release 383 (1 Jun 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-382.html" class="md-nav__link">Release 382 (25 May 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-381.html" class="md-nav__link">Release 381 (16 May 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-380.html" class="md-nav__link">Release 380 (6 May 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-379.html" class="md-nav__link">Release 379 (28 Apr 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-378.html" class="md-nav__link">Release 378 (21 Apr 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-377.html" class="md-nav__link">Release 377 (13 Apr 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-376.html" class="md-nav__link">Release 376 (7 Apr 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-375.html" class="md-nav__link">Release 375 (28 Mar 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-374.html" class="md-nav__link">Release 374 (17 Mar 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-373.html" class="md-nav__link">Release 373 (9 Mar 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-372.html" class="md-nav__link">Release 372 (2 Mar 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-371.html" class="md-nav__link">Release 371 (16 Feb 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-370.html" class="md-nav__link">Release 370 (3 Feb 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-369.html" class="md-nav__link">Release 369 (24 Jan 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-368.html" class="md-nav__link">Release 368 (11 Jan 2022)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-367.html" class="md-nav__link">Release 367 (22 Dec 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-366.html" class="md-nav__link">Release 366 (14 Dec 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-365.html" class="md-nav__link">Release 365 (3 Dec 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-364.html" class="md-nav__link">Release 364 (1 Nov 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-363.html" class="md-nav__link">Release 363 (6 Oct 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-362.html" class="md-nav__link">Release 362 (20 Sep 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-361.html" class="md-nav__link">Release 361 (27 Aug 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-360.html" class="md-nav__link">Release 360 (30 Jul 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-359.html" class="md-nav__link">Release 359 (1 Jul 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-358.html" class="md-nav__link">Release 358 (1 Jun 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-357.html" class="md-nav__link">Release 357 (21 May 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-356.html" class="md-nav__link">Release 356 (30 Apr 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-355.html" class="md-nav__link">Release 355 (8 Apr 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-354.html" class="md-nav__link">Release 354 (19 Mar 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-353.html" class="md-nav__link">Release 353 (5 Mar 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-352.html" class="md-nav__link">Release 352 (9 Feb 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-351.html" class="md-nav__link">Release 351 (3 Jan 2021)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-350.html" class="md-nav__link">Release 350 (28 Dec 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-349.html" class="md-nav__link">Release 349 (28 Dec 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-348.html" class="md-nav__link">Release 348 (14 Dec 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-347.html" class="md-nav__link">Release 347 (25 Nov 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-346.html" class="md-nav__link">Release 346 (10 Nov 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-345.html" class="md-nav__link">Release 345 (23 Oct 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-344.html" class="md-nav__link">Release 344 (9 Oct 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-343.html" class="md-nav__link">Release 343 (25 Sep 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-342.html" class="md-nav__link">Release 342 (24 Sep 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-341.html" class="md-nav__link">Release 341 (8 Sep 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-340.html" class="md-nav__link">Release 340 (8 Aug 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-339.html" class="md-nav__link">Release 339 (21 Jul 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-338.html" class="md-nav__link">Release 338 (07 Jul 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-337.html" class="md-nav__link">Release 337 (25 Jun 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-336.html" class="md-nav__link">Release 336 (16 Jun 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-335.html" class="md-nav__link">Release 335 (14 Jun 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-334.html" class="md-nav__link">Release 334 (29 May 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-333.html" class="md-nav__link">Release 333 (04 May 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-332.html" class="md-nav__link">Release 332 (08 Apr 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-331.html" class="md-nav__link">Release 331 (16 Mar 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-330.html" class="md-nav__link">Release 330 (18 Feb 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-329.html" class="md-nav__link">Release 329 (23 Jan 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-328.html" class="md-nav__link">Release 328 (10 Jan 2020)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-327.html" class="md-nav__link">Release 327 (20 Dec 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-326.html" class="md-nav__link">Release 326 (27 Nov 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-325.html" class="md-nav__link">Release 325 (14 Nov 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-324.html" class="md-nav__link">Release 324 (1 Nov 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-323.html" class="md-nav__link">Release 323 (23 Oct 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-322.html" class="md-nav__link">Release 322 (16 Oct 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-321.html" class="md-nav__link">Release 321 (15 Oct 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-320.html" class="md-nav__link">Release 320 (10 Oct 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-319.html" class="md-nav__link">Release 319 (22 Sep 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-318.html" class="md-nav__link">Release 318 (26 Aug 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-317.html" class="md-nav__link">Release 317 (1 Aug 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-316.html" class="md-nav__link">Release 316 (8 Jul 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-315.html" class="md-nav__link">Release 315 (14 Jun 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-314.html" class="md-nav__link">Release 314 (7 Jun 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-313.html" class="md-nav__link">Release 313 (31 May 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-312.html" class="md-nav__link">Release 312 (29 May 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-311.html" class="md-nav__link">Release 311 (14 May 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-310.html" class="md-nav__link">Release 310 (3 May 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-309.html" class="md-nav__link">Release 309 (25 Apr 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-308.html" class="md-nav__link">Release 308 (11 Apr 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-307.html" class="md-nav__link">Release 307 (3 Apr 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-306.html" class="md-nav__link">Release 306 (16 Mar 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-305.html" class="md-nav__link">Release 305 (7 Mar 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-304.html" class="md-nav__link">Release 304 (27 Feb 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-303.html" class="md-nav__link">Release 303 (13 Feb 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-302.html" class="md-nav__link">Release 302 (6 Feb 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-301.html" class="md-nav__link">Release 301 (31 Jan 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-300.html" class="md-nav__link">Release 300 (22 Jan 2019)</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.215.html" class="md-nav__link">Release 0.215</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.214.html" class="md-nav__link">Release 0.214</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.213.html" class="md-nav__link">Release 0.213</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.212.html" class="md-nav__link">Release 0.212</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.211.html" class="md-nav__link">Release 0.211</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.210.html" class="md-nav__link">Release 0.210</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.209.html" class="md-nav__link">Release 0.209</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.208.html" class="md-nav__link">Release 0.208</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.207.html" class="md-nav__link">Release 0.207</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.206.html" class="md-nav__link">Release 0.206</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.205.html" class="md-nav__link">Release 0.205</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.204.html" class="md-nav__link">Release 0.204</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.203.html" class="md-nav__link">Release 0.203</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.202.html" class="md-nav__link">Release 0.202</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.201.html" class="md-nav__link">Release 0.201</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.200.html" class="md-nav__link">Release 0.200</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.199.html" class="md-nav__link">Release 0.199</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.198.html" class="md-nav__link">Release 0.198</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.197.html" class="md-nav__link">Release 0.197</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.196.html" class="md-nav__link">Release 0.196</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.195.html" class="md-nav__link">Release 0.195</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.194.html" class="md-nav__link">Release 0.194</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.193.html" class="md-nav__link">Release 0.193</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.192.html" class="md-nav__link">Release 0.192</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.191.html" class="md-nav__link">Release 0.191</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.190.html" class="md-nav__link">Release 0.190</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.189.html" class="md-nav__link">Release 0.189</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.188.html" class="md-nav__link">Release 0.188</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.187.html" class="md-nav__link">Release 0.187</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.186.html" class="md-nav__link">Release 0.186</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.185.html" class="md-nav__link">Release 0.185</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.184.html" class="md-nav__link">Release 0.184</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.183.html" class="md-nav__link">Release 0.183</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.182.html" class="md-nav__link">Release 0.182</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.181.html" class="md-nav__link">Release 0.181</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.180.html" class="md-nav__link">Release 0.180</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.179.html" class="md-nav__link">Release 0.179</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.178.html" class="md-nav__link">Release 0.178</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.177.html" class="md-nav__link">Release 0.177</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.176.html" class="md-nav__link">Release 0.176</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.175.html" class="md-nav__link">Release 0.175</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.174.html" class="md-nav__link">Release 0.174</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.173.html" class="md-nav__link">Release 0.173</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.172.html" class="md-nav__link">Release 0.172</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.171.html" class="md-nav__link">Release 0.171</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.170.html" class="md-nav__link">Release 0.170</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.169.html" class="md-nav__link">Release 0.169</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.168.html" class="md-nav__link">Release 0.168</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.167.html" class="md-nav__link">Release 0.167</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.166.html" class="md-nav__link">Release 0.166</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.165.html" class="md-nav__link">Release 0.165</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.164.html" class="md-nav__link">Release 0.164</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.163.html" class="md-nav__link">Release 0.163</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.162.html" class="md-nav__link">Release 0.162</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.161.html" class="md-nav__link">Release 0.161</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.160.html" class="md-nav__link">Release 0.160</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.159.html" class="md-nav__link">Release 0.159</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.158.html" class="md-nav__link">Release 0.158</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.157.1.html" class="md-nav__link">Release 0.157.1</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.157.html" class="md-nav__link">Release 0.157</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.156.html" class="md-nav__link">Release 0.156</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.155.html" class="md-nav__link">Release 0.155</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.154.html" class="md-nav__link">Release 0.154</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.153.html" class="md-nav__link">Release 0.153</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.152.3.html" class="md-nav__link">Release 0.152.3</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.152.2.html" class="md-nav__link">Release 0.152.2</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.152.1.html" class="md-nav__link">Release 0.152.1</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.152.html" class="md-nav__link">Release 0.152</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.151.html" class="md-nav__link">Release 0.151</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.150.html" class="md-nav__link">Release 0.150</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.149.html" class="md-nav__link">Release 0.149</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.148.html" class="md-nav__link">Release 0.148</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.147.html" class="md-nav__link">Release 0.147</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.146.html" class="md-nav__link">Release 0.146</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.145.html" class="md-nav__link">Release 0.145</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.7.html" class="md-nav__link">Release 0.144.7</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.6.html" class="md-nav__link">Release 0.144.6</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.5.html" class="md-nav__link">Release 0.144.5</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.4.html" class="md-nav__link">Release 0.144.4</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.3.html" class="md-nav__link">Release 0.144.3</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.2.html" class="md-nav__link">Release 0.144.2</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.1.html" class="md-nav__link">Release 0.144.1</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.144.html" class="md-nav__link">Release 0.144</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.143.html" class="md-nav__link">Release 0.143</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.142.html" class="md-nav__link">Release 0.142</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.141.html" class="md-nav__link">Release 0.141</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.140.html" class="md-nav__link">Release 0.140</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.139.html" class="md-nav__link">Release 0.139</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.138.html" class="md-nav__link">Release 0.138</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.137.html" class="md-nav__link">Release 0.137</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.136.html" class="md-nav__link">Release 0.136</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.135.html" class="md-nav__link">Release 0.135</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.134.html" class="md-nav__link">Release 0.134</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.133.html" class="md-nav__link">Release 0.133</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.132.html" class="md-nav__link">Release 0.132</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.131.html" class="md-nav__link">Release 0.131</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.130.html" class="md-nav__link">Release 0.130</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.129.html" class="md-nav__link">Release 0.129</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.128.html" class="md-nav__link">Release 0.128</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.127.html" class="md-nav__link">Release 0.127</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.126.html" class="md-nav__link">Release 0.126</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.125.html" class="md-nav__link">Release 0.125</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.124.html" class="md-nav__link">Release 0.124</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.123.html" class="md-nav__link">Release 0.123</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.122.html" class="md-nav__link">Release 0.122</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.121.html" class="md-nav__link">Release 0.121</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.120.html" class="md-nav__link">Release 0.120</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.119.html" class="md-nav__link">Release 0.119</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.118.html" class="md-nav__link">Release 0.118</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.117.html" class="md-nav__link">Release 0.117</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.116.html" class="md-nav__link">Release 0.116</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.115.html" class="md-nav__link">Release 0.115</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.114.html" class="md-nav__link">Release 0.114</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.113.html" class="md-nav__link">Release 0.113</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.112.html" class="md-nav__link">Release 0.112</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.111.html" class="md-nav__link">Release 0.111</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.110.html" class="md-nav__link">Release 0.110</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.109.html" class="md-nav__link">Release 0.109</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.108.html" class="md-nav__link">Release 0.108</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.107.html" class="md-nav__link">Release 0.107</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.106.html" class="md-nav__link">Release 0.106</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.105.html" class="md-nav__link">Release 0.105</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.104.html" class="md-nav__link">Release 0.104</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.103.html" class="md-nav__link">Release 0.103</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.102.html" class="md-nav__link">Release 0.102</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.101.html" class="md-nav__link">Release 0.101</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.100.html" class="md-nav__link">Release 0.100</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.99.html" class="md-nav__link">Release 0.99</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.98.html" class="md-nav__link">Release 0.98</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.97.html" class="md-nav__link">Release 0.97</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.96.html" class="md-nav__link">Release 0.96</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.95.html" class="md-nav__link">Release 0.95</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.94.html" class="md-nav__link">Release 0.94</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.93.html" class="md-nav__link">Release 0.93</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.92.html" class="md-nav__link">Release 0.92</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.91.html" class="md-nav__link">Release 0.91</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.90.html" class="md-nav__link">Release 0.90</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.89.html" class="md-nav__link">Release 0.89</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.88.html" class="md-nav__link">Release 0.88</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.87.html" class="md-nav__link">Release 0.87</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.86.html" class="md-nav__link">Release 0.86</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.85.html" class="md-nav__link">Release 0.85</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.84.html" class="md-nav__link">Release 0.84</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.83.html" class="md-nav__link">Release 0.83</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.82.html" class="md-nav__link">Release 0.82</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.81.html" class="md-nav__link">Release 0.81</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.80.html" class="md-nav__link">Release 0.80</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.79.html" class="md-nav__link">Release 0.79</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.78.html" class="md-nav__link">Release 0.78</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.77.html" class="md-nav__link">Release 0.77</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.76.html" class="md-nav__link">Release 0.76</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.75.html" class="md-nav__link">Release 0.75</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.74.html" class="md-nav__link">Release 0.74</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.73.html" class="md-nav__link">Release 0.73</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.72.html" class="md-nav__link">Release 0.72</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.71.html" class="md-nav__link">Release 0.71</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.70.html" class="md-nav__link">Release 0.70</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.69.html" class="md-nav__link">Release 0.69</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.68.html" class="md-nav__link">Release 0.68</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.67.html" class="md-nav__link">Release 0.67</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.66.html" class="md-nav__link">Release 0.66</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.65.html" class="md-nav__link">Release 0.65</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.64.html" class="md-nav__link">Release 0.64</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.63.html" class="md-nav__link">Release 0.63</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.62.html" class="md-nav__link">Release 0.62</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.61.html" class="md-nav__link">Release 0.61</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.60.html" class="md-nav__link">Release 0.60</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.59.html" class="md-nav__link">Release 0.59</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.58.html" class="md-nav__link">Release 0.58</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.57.html" class="md-nav__link">Release 0.57</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.56.html" class="md-nav__link">Release 0.56</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.55.html" class="md-nav__link">Release 0.55</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release-0.54.html" class="md-nav__link">Release 0.54</a>
      
    
    </li></ul>
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="release-450.html#general" class="md-nav__link">General</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#security" class="md-nav__link">Security</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#jdbc-driver" class="md-nav__link">JDBC driver</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#bigquery-connector" class="md-nav__link">BigQuery connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#cassandra-connector" class="md-nav__link">Cassandra connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#clickhouse-connector" class="md-nav__link">ClickHouse connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#delta-lake-connector" class="md-nav__link">Delta Lake connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#hive-connector" class="md-nav__link">Hive connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#hudi-connector" class="md-nav__link">Hudi connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#iceberg-connector" class="md-nav__link">Iceberg connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#mariadb-connector" class="md-nav__link">MariaDB connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#memory-connector" class="md-nav__link">Memory connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#mysql-connector" class="md-nav__link">MySQL connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#pinot-connector" class="md-nav__link">Pinot connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#redshift-connector" class="md-nav__link">Redshift connector</a>
        </li>
        <li class="md-nav__item"><a href="release-450.html#singlestore-connector" class="md-nav__link">SingleStore connector</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="release-450-19-jun-2024">
<h1 id="release-release-450--page-root">Release 450 (19 Jun 2024)<a class="headerlink" href="release-450.html#release-release-450--page-root" title="Link to this heading">#</a></h1>
<section id="general">
<h2 id="general">General<a class="headerlink" href="release-450.html#general" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for specifying an Azure blob endpoint for accessing spooling in
fault-tolerant execution with the <code class="docutils literal notranslate"><span class="pre">exchange.azure.endpoint</span></code> configuration
property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22218">#22218</a>)</p></li>
<li><p>Expose driver execution statistics via JMX. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22427">#22427</a>)</p></li>
<li><p>Improve performance of the <a class="reference internal" href="../functions/window.html#first_value" title="first_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">first_value()</span></code></a> and <a class="reference internal" href="../functions/window.html#last_value" title="last_value"><code class="xref py py-func docutils literal notranslate"><span class="pre">last_value()</span></code></a>
functions. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22092">#22092</a>)</p></li>
<li><p>Improve performance for large clusters under heavy workloads. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22039">#22039</a>)</p></li>
<li><p>Improve performance of queries with simple predicates. This optimization can
be disabled using the <code class="docutils literal notranslate"><span class="pre">experimental.columnar-filter-evaluation.enabled</span></code>
configuration property or the <code class="docutils literal notranslate"><span class="pre">columnar_filter_evaluation_enabled</span></code> session
property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21375">#21375</a>)</p></li>
<li><p><a href="../release.html#breaking-changes" title="Breaking change">⚠️ Breaking change:</a> Improve performance of aggregations containing a <code class="docutils literal notranslate"><span class="pre">DISTINCT</span></code>
clause, and replace the <code class="docutils literal notranslate"><span class="pre">optimizer.mark-distinct-strategy</span></code> and
<code class="docutils literal notranslate"><span class="pre">optimizer.optimize-mixed-distinct-aggregations</span></code> configuration properties with
the new <code class="docutils literal notranslate"><span class="pre">optimizer.distinct-aggregations-strategy</span></code> property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21907">#21907</a>)</p></li>
<li><p>Improve performance of reading JSON files. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22348">#22348</a>)</p></li>
<li><p>Improve performance for the <a class="reference internal" href="../functions/datetime.html#date_trunc" title="date_trunc"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_trunc()</span></code></a>, <a class="reference internal" href="../functions/datetime.html#date_add" title="date_add"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_add()</span></code></a>, and
<a class="reference internal" href="../functions/datetime.html#date_diff" title="date_diff"><code class="xref py py-func docutils literal notranslate"><span class="pre">date_diff()</span></code></a> functions. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22192">#22192</a>)</p></li>
<li><p>Fix failure when loading the <a class="reference internal" href="../admin/event-listeners-openlineage.html"><span class="doc std std-doc">OpenLineage event listener</span></a>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22228">#22228</a>)</p></li>
<li><p>Fix potential incorrect results when metadata or table data in certain
connectors is updated or deleted. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22285">#22285</a>)</p></li>
</ul>
</section>
<section id="security">
<h2 id="security">Security<a class="headerlink" href="release-450.html#security" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for using web identity exclusively for authentication when running
on Amazon EKS with the legacy S3 file system enabled. This can be configured
via the <code class="docutils literal notranslate"><span class="pre">trino.s3.use-web-identity-token-credentials-provider</span></code> property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22162">#22162</a>)</p></li>
<li><p>Add support for exclusively using web identity for authentication when using
Amazon EKS with
<a class="reference external" href="https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html">IAM roles</a>
by setting the
<code class="docutils literal notranslate"><span class="pre">s3.use-web-identity-token-credentials-provider</span></code> configuration property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22163">#22163</a>)</p></li>
</ul>
</section>
<section id="jdbc-driver">
<h2 id="jdbc-driver">JDBC driver<a class="headerlink" href="release-450.html#jdbc-driver" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for the <code class="docutils literal notranslate"><span class="pre">assumeNullCatalogMeansCurrent</span></code> connection property. When
enabled, a <code class="docutils literal notranslate"><span class="pre">null</span></code> value for the <code class="docutils literal notranslate"><span class="pre">catalog</span></code> parameter in <code class="docutils literal notranslate"><span class="pre">DatabaseMetaData</span></code>
methods is assumed to mean the current catalog. If no current catalog is
set, the behaviour is unmodified. (<a class="reference external" href="https://github.com/trinodb/trino/issues/20866">#20866</a>)</p></li>
</ul>
</section>
<section id="bigquery-connector">
<h2 id="bigquery-connector">BigQuery connector<a class="headerlink" href="release-450.html#bigquery-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for metadata caching when the
<code class="docutils literal notranslate"><span class="pre">bigquery.case-insensitive-name-matching</span></code> configuration property is enabled. (<a class="reference external" href="https://github.com/trinodb/trino/issues/10740">#10740</a>)</p></li>
<li><p><a href="../release.html#breaking-changes" title="Breaking change">⚠️ Breaking change:</a> Automatically configure BigQuery scan parallelism, and remove the
<code class="docutils literal notranslate"><span class="pre">bigquery.parallelism</span></code> configuration property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22279">#22279</a>)</p></li>
</ul>
</section>
<section id="cassandra-connector">
<h2 id="cassandra-connector">Cassandra connector<a class="headerlink" href="release-450.html#cassandra-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Fix incorrect results when specifying a value for the
<code class="docutils literal notranslate"><span class="pre">cassandra.partition-size-for-batch-select</span></code> configuration property. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21940">#21940</a>)</p></li>
</ul>
</section>
<section id="clickhouse-connector">
<h2 id="clickhouse-connector">ClickHouse connector<a class="headerlink" href="release-450.html#clickhouse-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Improve performance of <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span> <span class="pre">...</span> <span class="pre">LIMIT</span></code> on non-textual types by pushing
execution down to the underlying database. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22174">#22174</a>)</p></li>
</ul>
</section>
<section id="delta-lake-connector">
<h2 id="delta-lake-connector">Delta Lake connector<a class="headerlink" href="release-450.html#delta-lake-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for concurrent <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, and <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> queries. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21727">#21727</a>)</p></li>
<li><p>Add support for using table statistics with <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> types. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21878">#21878</a>)</p></li>
<li><p>Add support for reading tables with
<a class="reference external" href="https://docs.delta.io/latest/delta-type-widening.html">type widening</a>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/21756">#21756</a>)</p></li>
<li><p>Set the default value for the <code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code> configuration property
to 500. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22209">#22209</a>)</p></li>
<li><p>Fix failure when reading a <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> value after the year 9999. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22184">#22184</a>)</p></li>
<li><p>Fix failure when reading tables with the unsupported <code class="docutils literal notranslate"><span class="pre">variant</span></code> type. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22310">#22310</a>)</p></li>
<li><p>Add support for reading
<a class="reference external" href="https://docs.delta.io/latest/delta-uniform.html">UniForm</a> tables. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22106">#22106</a>)</p></li>
</ul>
</section>
<section id="hive-connector">
<h2 id="hive-connector">Hive connector<a class="headerlink" href="release-450.html#hive-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for changing a column’s type from <code class="docutils literal notranslate"><span class="pre">integer</span></code> to <code class="docutils literal notranslate"><span class="pre">varchar</span></code> and
<code class="docutils literal notranslate"><span class="pre">decimal</span></code> to <code class="docutils literal notranslate"><span class="pre">varchar</span></code>, respectively, in unpartitioned tables. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22246">#22246</a>, <a class="reference external" href="https://github.com/trinodb/trino/issues/22293">#22293</a>)</p></li>
<li><p>Add support for changing a column’s type from <code class="docutils literal notranslate"><span class="pre">double</span></code> to <code class="docutils literal notranslate"><span class="pre">varchar</span></code> in
unpartitioned tables
using Parquet files. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22277">#22277</a>)</p></li>
<li><p>Add support for changing a column’s type from <code class="docutils literal notranslate"><span class="pre">float</span></code> to <code class="docutils literal notranslate"><span class="pre">varchar</span></code>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22291">#22291</a>)</p></li>
<li><p>Set the default value for the <code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code> configuration property
to 500. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22209">#22209</a>)</p></li>
</ul>
</section>
<section id="hudi-connector">
<h2 id="hudi-connector">Hudi connector<a class="headerlink" href="release-450.html#hudi-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Set the default value for the <code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code> configuration property
to 500. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22209">#22209</a>)</p></li>
</ul>
</section>
<section id="iceberg-connector">
<h2 id="iceberg-connector">Iceberg connector<a class="headerlink" href="release-450.html#iceberg-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for the <code class="docutils literal notranslate"><span class="pre">TRUNCATE</span></code> statement. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22340">#22340</a>)</p></li>
<li><p><a href="../release.html#breaking-changes" title="Breaking change">⚠️ Breaking change:</a> Add support for V2 of the Nessie REST API. Previous behavior can
be restored by setting the <code class="docutils literal notranslate"><span class="pre">iceberg.nessie-catalog.client-api-version</span></code>
configuration property to <code class="docutils literal notranslate"><span class="pre">V1</span></code>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22215">#22215</a>)</p></li>
<li><p>Improve performance when reading by populating <code class="docutils literal notranslate"><span class="pre">split_offsets</span></code> in file
metadata. (<a class="reference external" href="https://github.com/trinodb/trino/issues/9018">#9018</a>)</p></li>
<li><p>Set the default value for the <code class="docutils literal notranslate"><span class="pre">s3.max-connections</span></code> configuration property
to 500. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22209">#22209</a>)</p></li>
<li><p>Fix failure when reading Parquet files that don’t have <code class="docutils literal notranslate"><span class="pre">field-id</span></code> on
structured types. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22347">#22347</a>)</p></li>
</ul>
</section>
<section id="mariadb-connector">
<h2 id="mariadb-connector">MariaDB connector<a class="headerlink" href="release-450.html#mariadb-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc std std-doc">fault-tolerant execution</span></a>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22328">#22328</a>)</p></li>
<li><p>Improve performance of listing table columns. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22241">#22241</a>)</p></li>
</ul>
</section>
<section id="memory-connector">
<h2 id="memory-connector">Memory connector<a class="headerlink" href="release-450.html#memory-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for the <code class="docutils literal notranslate"><span class="pre">TRUNCATE</span></code> statement. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22337">#22337</a>)</p></li>
</ul>
</section>
<section id="mysql-connector">
<h2 id="mysql-connector">MySQL connector<a class="headerlink" href="release-450.html#mysql-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Improve performance of listing table columns. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22241">#22241</a>)</p></li>
</ul>
</section>
<section id="pinot-connector">
<h2 id="pinot-connector">Pinot connector<a class="headerlink" href="release-450.html#pinot-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Add support for the
<a class="reference external" href="https://docs.pinot.apache.org/developers/advanced/null-value-support#advanced-null-handling-support"><code class="docutils literal notranslate"><span class="pre">enableNullHandling</span></code> query option</a>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22214">#22214</a>)</p></li>
<li><p>Fix failure when using <a class="reference internal" href="../connector/pinot.html#pinot-dynamic-tables"><span class="std std-ref">dynamic tables</span></a>. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22301">#22301</a>)</p></li>
</ul>
</section>
<section id="redshift-connector">
<h2 id="redshift-connector">Redshift connector<a class="headerlink" href="release-450.html#redshift-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Improve performance of listing table columns. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22241">#22241</a>)</p></li>
</ul>
</section>
<section id="singlestore-connector">
<h2 id="singlestore-connector">SingleStore connector<a class="headerlink" href="release-450.html#singlestore-connector" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Improve performance of listing table columns. (<a class="reference external" href="https://github.com/trinodb/trino/issues/22241">#22241</a>)</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="release-451.html" title="Release 451 (27 Jun 2024)"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Release 451 (27 Jun 2024) </span>
              </div>
            </a>
          
          
            <a href="release-449.html" title="Release 449 (31 May 2024)"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Release 449 (31 May 2024) </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>