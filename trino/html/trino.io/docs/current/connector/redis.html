<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Redis connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="redis.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Redshift connector" href="redshift.html" />
    <link rel="prev" title="Prometheus connector" href="prometheus.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="redis.html#connector/redis" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Redis connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Redis </label>
    
      <a href="redis.html#" class="md-nav__link md-nav__link--active">Redis</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="redis.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#multiple-redis-servers" class="md-nav__link">Multiple Redis servers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#redis-table-names" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-names</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-default-schema" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.default-schema</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-scan-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.scan-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-max-keys-per-fetch" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.max-keys-per-fetch</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-key-prefix-schema-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.key-prefix-schema-table</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-key-delimiter" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.key-delimiter</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-table-description-dir" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-table-description-cache-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-description-cache-ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-hide-internal-columns" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.hide-internal-columns</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-database-index" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.database-index</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-user" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.user</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.password</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#internal-columns" class="md-nav__link">Internal columns</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#table-definition-files" class="md-nav__link">Table definition files</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#row-decoding" class="md-nav__link">Row decoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#raw-decoder" class="md-nav__link">Raw decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#csv-decoder" class="md-nav__link">CSV decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#json-decoder" class="md-nav__link">JSON decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#default-field-decoder" class="md-nav__link">Default field decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#date-and-time-decoders" class="md-nav__link">Date and time decoders</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#avro-decoder" class="md-nav__link">Avro decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#avro-schema-evolution" class="md-nav__link">Avro schema evolution</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#pushdown" class="md-nav__link">Pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#predicate-pushdown-support" class="md-nav__link">Predicate pushdown support</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="redis.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#multiple-redis-servers" class="md-nav__link">Multiple Redis servers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#redis-table-names" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-names</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-default-schema" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.default-schema</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-scan-count" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.scan-count</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-max-keys-per-fetch" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.max-keys-per-fetch</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-key-prefix-schema-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.key-prefix-schema-table</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-key-delimiter" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.key-delimiter</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-table-description-dir" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-table-description-cache-ttl" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.table-description-cache-ttl</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-hide-internal-columns" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.hide-internal-columns</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-database-index" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.database-index</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-user" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.user</span></code></a>
        </li>
        <li class="md-nav__item"><a href="redis.html#redis-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">redis.password</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#internal-columns" class="md-nav__link">Internal columns</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#table-definition-files" class="md-nav__link">Table definition files</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#row-decoding" class="md-nav__link">Row decoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#raw-decoder" class="md-nav__link">Raw decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#csv-decoder" class="md-nav__link">CSV decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#json-decoder" class="md-nav__link">JSON decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#default-field-decoder" class="md-nav__link">Default field decoder</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#date-and-time-decoders" class="md-nav__link">Date and time decoders</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#avro-decoder" class="md-nav__link">Avro decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#avro-schema-evolution" class="md-nav__link">Avro schema evolution</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="redis.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
        <li class="md-nav__item"><a href="redis.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#pushdown" class="md-nav__link">Pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="redis.html#predicate-pushdown-support" class="md-nav__link">Predicate pushdown support</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="redis-connector">
<h1 id="connector-redis--page-root">Redis connector<a class="headerlink" href="redis.html#connector-redis--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/redis.png"/><p>The Redis connector allows querying of live data stored in <a class="reference external" href="https://redis.io/">Redis</a>. This can be
used to join data between different systems like Redis and Hive.</p>
<p>Each Redis key/value pair is presented as a single row in Trino. Rows can be
broken down into cells by using table definition files.</p>
<p>Currently, only Redis key of string and zset types are supported, only Redis value of
string and hash types are supported.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="redis.html#requirements" title="Link to this heading">#</a></h2>
<p>Requirements for using the connector in a catalog to connect to a Redis data
source are:</p>
<ul class="simple">
<li><p>Redis 5.0.14 or higher (Redis Cluster is not supported)</p></li>
<li><p>Network access, by default on port 6379, from the Trino coordinator and
workers to Redis.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="redis.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Redis connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following content, replacing the
properties as appropriate:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=redis
redis.table-names=schema1.table1,schema1.table2
redis.nodes=host:port
</pre></div>
</div>
<section id="multiple-redis-servers">
<h3 id="multiple-redis-servers">Multiple Redis servers<a class="headerlink" href="redis.html#multiple-redis-servers" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need. If you have additional
Redis servers, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name, making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>.</p>
</section>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="redis.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.table-names</span></code></p></td>
<td><p>List of all tables provided by the catalog</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.default-schema</span></code></p></td>
<td><p>Default schema name for tables</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.nodes</span></code></p></td>
<td><p>Location of the Redis server</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.scan-count</span></code></p></td>
<td><p>Redis parameter for scanning of the keys</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.max-keys-per-fetch</span></code></p></td>
<td><p>Get values associated with the specified number of keys in the redis command such as MGET(key…)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.key-prefix-schema-table</span></code></p></td>
<td><p>Redis keys have schema-name:table-name prefix</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.key-delimiter</span></code></p></td>
<td><p>Delimiter separating schema_name and table_name if redis.key-prefix-schema-table is used</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code></p></td>
<td><p>Directory containing table description files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.table-description-cache-ttl</span></code></p></td>
<td><p>The cache time for table description files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.hide-internal-columns</span></code></p></td>
<td><p>Controls whether internal columns are part of the table schema or not</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.database-index</span></code></p></td>
<td><p>Redis database index</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">redis.user</span></code></p></td>
<td><p>Redis server username</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">redis.password</span></code></p></td>
<td><p>Redis server password</p></td>
</tr>
</tbody>
</table>
<section id="redis-table-names">
<h3 id="redis-table-names"><code class="docutils literal notranslate"><span class="pre">redis.table-names</span></code><a class="headerlink" href="redis.html#redis-table-names" title="Link to this heading">#</a></h3>
<p>Comma-separated list of all tables provided by this catalog. A table name
can be unqualified (simple name) and is placed into the default schema
(see below), or qualified with a schema name (<code class="docutils literal notranslate"><span class="pre">&lt;schema-name&gt;.&lt;table-name&gt;</span></code>).</p>
<p>For each table defined, a table description file (see below) may
exist. If no table description file exists, the
table only contains internal columns (see below).</p>
<p>This property is optional; the connector relies on the table description files
specified in the <code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code> property.</p>
</section>
<section id="redis-default-schema">
<h3 id="redis-default-schema"><code class="docutils literal notranslate"><span class="pre">redis.default-schema</span></code><a class="headerlink" href="redis.html#redis-default-schema" title="Link to this heading">#</a></h3>
<p>Defines the schema which will contain all tables that were defined without
a qualifying schema name.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">default</span></code>.</p>
</section>
<section id="redis-nodes">
<h3 id="redis-nodes"><code class="docutils literal notranslate"><span class="pre">redis.nodes</span></code><a class="headerlink" href="redis.html#redis-nodes" title="Link to this heading">#</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">hostname:port</span></code> pair for the Redis server.</p>
<p>This property is required; there is no default.</p>
<p>Redis Cluster is not supported.</p>
</section>
<section id="redis-scan-count">
<h3 id="redis-scan-count"><code class="docutils literal notranslate"><span class="pre">redis.scan-count</span></code><a class="headerlink" href="redis.html#redis-scan-count" title="Link to this heading">#</a></h3>
<p>The internal COUNT parameter for the Redis SCAN command when connector is using
SCAN to find keys for the data. This parameter can be used to tune performance
of the Redis connector.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">100</span></code>.</p>
</section>
<section id="redis-max-keys-per-fetch">
<h3 id="redis-max-keys-per-fetch"><code class="docutils literal notranslate"><span class="pre">redis.max-keys-per-fetch</span></code><a class="headerlink" href="redis.html#redis-max-keys-per-fetch" title="Link to this heading">#</a></h3>
<p>The internal number of keys for the Redis MGET command and Pipeline HGETALL command
when connector is using these commands to find values of keys. This parameter can be
used to tune performance of the Redis connector.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">100</span></code>.</p>
</section>
<section id="redis-key-prefix-schema-table">
<h3 id="redis-key-prefix-schema-table"><code class="docutils literal notranslate"><span class="pre">redis.key-prefix-schema-table</span></code><a class="headerlink" href="redis.html#redis-key-prefix-schema-table" title="Link to this heading">#</a></h3>
<p>If true, only keys prefixed with the <code class="docutils literal notranslate"><span class="pre">schema-name:table-name</span></code> are scanned
for a table, and all other keys are filtered out.  If false, all keys are
scanned.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="redis-key-delimiter">
<h3 id="redis-key-delimiter"><code class="docutils literal notranslate"><span class="pre">redis.key-delimiter</span></code><a class="headerlink" href="redis.html#redis-key-delimiter" title="Link to this heading">#</a></h3>
<p>The character used for separating <code class="docutils literal notranslate"><span class="pre">schema-name</span></code> and <code class="docutils literal notranslate"><span class="pre">table-name</span></code> when
<code class="docutils literal notranslate"><span class="pre">redis.key-prefix-schema-table</span></code> is <code class="docutils literal notranslate"><span class="pre">true</span></code></p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">:</span></code>.</p>
</section>
<section id="redis-table-description-dir">
<h3 id="redis-table-description-dir"><code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code><a class="headerlink" href="redis.html#redis-table-description-dir" title="Link to this heading">#</a></h3>
<p>References a folder within Trino deployment that holds one or more JSON
files, which must end with <code class="docutils literal notranslate"><span class="pre">.json</span></code> and contain table description files.</p>
<p>Note that the table description files will only be used by the Trino coordinator
node.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">etc/redis</span></code>.</p>
</section>
<section id="redis-table-description-cache-ttl">
<h3 id="redis-table-description-cache-ttl"><code class="docutils literal notranslate"><span class="pre">redis.table-description-cache-ttl</span></code><a class="headerlink" href="redis.html#redis-table-description-cache-ttl" title="Link to this heading">#</a></h3>
<p>The Redis connector dynamically loads the table description files after waiting
for the time specified by this property. Therefore, there is no need to update
the <code class="docutils literal notranslate"><span class="pre">redis.table-names</span></code> property and restart the Trino service when adding,
updating, or deleting a file end with <code class="docutils literal notranslate"><span class="pre">.json</span></code> to <code class="docutils literal notranslate"><span class="pre">redis.table-description-dir</span></code>
folder.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">5m</span></code>.</p>
</section>
<section id="redis-hide-internal-columns">
<h3 id="redis-hide-internal-columns"><code class="docutils literal notranslate"><span class="pre">redis.hide-internal-columns</span></code><a class="headerlink" href="redis.html#redis-hide-internal-columns" title="Link to this heading">#</a></h3>
<p>In addition to the data columns defined in a table description file, the
connector maintains a number of additional columns for each table. If
these columns are hidden, they can still be used in queries, but they do not
show up in <code class="docutils literal notranslate"><span class="pre">DESCRIBE</span> <span class="pre">&lt;table-name&gt;</span></code> or <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span></code>.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
</section>
<section id="redis-database-index">
<h3 id="redis-database-index"><code class="docutils literal notranslate"><span class="pre">redis.database-index</span></code><a class="headerlink" href="redis.html#redis-database-index" title="Link to this heading">#</a></h3>
<p>The Redis database to query.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</section>
<section id="redis-user">
<h3 id="redis-user"><code class="docutils literal notranslate"><span class="pre">redis.user</span></code><a class="headerlink" href="redis.html#redis-user" title="Link to this heading">#</a></h3>
<p>The username for Redis server.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
</section>
<section id="redis-password">
<h3 id="redis-password"><code class="docutils literal notranslate"><span class="pre">redis.password</span></code><a class="headerlink" href="redis.html#redis-password" title="Link to this heading">#</a></h3>
<p>The password for password-protected Redis server.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
</section>
</section>
<section id="internal-columns">
<h2 id="internal-columns">Internal columns<a class="headerlink" href="redis.html#internal-columns" title="Link to this heading">#</a></h2>
<p>For each defined table, the connector maintains the following columns:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Column name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_key</span></code></p></td>
<td><p>VARCHAR</p></td>
<td><p>Redis key.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_value</span></code></p></td>
<td><p>VARCHAR</p></td>
<td><p>Redis value corresponding to the key.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_key_length</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Number of bytes in the key.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_value_length</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Number of bytes in the value.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_key_corrupt</span></code></p></td>
<td><p>BOOLEAN</p></td>
<td><p>True if the decoder could not decode the key for this row. When true, data columns mapped from the key should be treated as invalid.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_value_corrupt</span></code></p></td>
<td><p>BOOLEAN</p></td>
<td><p>True if the decoder could not decode the message for this row. When true, data columns mapped from the value should be treated as invalid.</p></td>
</tr>
</tbody>
</table>
<p>For tables without a table definition file, the <code class="docutils literal notranslate"><span class="pre">_key_corrupt</span></code> and
<code class="docutils literal notranslate"><span class="pre">_value_corrupt</span></code> columns are <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="table-definition-files">
<h2 id="table-definition-files">Table definition files<a class="headerlink" href="redis.html#table-definition-files" title="Link to this heading">#</a></h2>
<p>With the Redis connector it is possible to further reduce Redis key/value pairs into
granular cells, provided the key/value string follows a particular format. This process
defines new columns that can be further queried from Trino.</p>
<p>A table definition file consists of a JSON definition for a table. The
name of the file can be arbitrary, but must end in <code class="docutils literal notranslate"><span class="pre">.json</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "tableName": ...,
    "schemaName": ...,
    "key": {
        "dataFormat": ...,
        "fields": [
            ...
        ]
    },
    "value": {
        "dataFormat": ...,
        "fields": [
            ...
       ]
    }
}
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">tableName</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Trino table name defined by this file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">schemaName</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Schema which will contain the table. If omitted, the default schema name is used.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">key</span></code></p></td>
<td><p>optional</p></td>
<td><p>JSON object</p></td>
<td><p>Field definitions for data columns mapped to the value key.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">value</span></code></p></td>
<td><p>optional</p></td>
<td><p>JSON object</p></td>
<td><p>Field definitions for data columns mapped to the value itself.</p></td>
</tr>
</tbody>
</table>
<p>Please refer to the <a class="reference internal" href="kafka.html"><span class="doc std std-doc">Kafka connector</span></a> page for the description of the <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> as well as various available decoders.</p>
<p>In addition to the above Kafka types, the Redis connector supports <code class="docutils literal notranslate"><span class="pre">hash</span></code> type for the <code class="docutils literal notranslate"><span class="pre">value</span></code> field which represent data stored in the Redis hash.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "tableName": ...,
    "schemaName": ...,
    "value": {
        "dataFormat": "hash",
        "fields": [
            ...
       ]
    }
}
</pre></div>
</div>
</section>
<section id="type-mapping">
<h2 id="type-mapping">Type mapping<a class="headerlink" href="redis.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Redis each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">maps some types</span></a> when reading data. Type
mapping depends on the RAW, CSV, JSON, and AVRO file formats.</p>
<section id="row-decoding">
<h3 id="row-decoding">Row decoding<a class="headerlink" href="redis.html#row-decoding" title="Link to this heading">#</a></h3>
<p>A decoder is used to map data to table columns.</p>
<p>The connector contains the following decoders:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">raw</span></code>: Message is not interpreted; ranges of raw message bytes are mapped
to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">csv</span></code>: Message is interpreted as comma separated message, and fields are
mapped to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">json</span></code>: Message is parsed as JSON, and JSON fields are mapped to table
columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">avro</span></code>: Message is parsed based on an Avro schema, and Avro fields are
mapped to table columns.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If no table definition file exists for a table, the <code class="docutils literal notranslate"><span class="pre">dummy</span></code> decoder is
used, which does not expose any columns.</p>
</div>
<section id="raw-decoder">
<h4 id="raw-decoder">Raw decoder<a class="headerlink" href="redis.html#raw-decoder" title="Link to this heading">#</a></h4>
<p>The raw decoder supports reading of raw byte-based values from message or key,
and converting it into Trino columns.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Selects the width of the data type converted.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type. See the following table for a list of supported
data types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - <code class="docutils literal notranslate"><span class="pre">&lt;start&gt;[:&lt;end&gt;]</span></code> - Start and end position of bytes to convert
(optional).</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute selects the number of bytes converted. If absent,
<code class="docutils literal notranslate"><span class="pre">BYTE</span></code> is assumed. All values are signed.</p>
<p>Supported values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code> - one byte</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code> - two bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INT</span></code> - four bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code> - eight bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code> - four bytes (IEEE 754 format)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> - eight bytes (IEEE 754 format)</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">type</span></code> attribute defines the Trino data type on which the value is mapped.</p>
<p>Depending on the Trino type assigned to a column, different values of dataFormat
can be used:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> values</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attribute specifies the range of the bytes in a key or message
used for decoding. It can be one or two numbers separated by a colon
(<code class="docutils literal notranslate"><span class="pre">&lt;start&gt;[:&lt;end&gt;]</span></code>).</p>
<p>If only a start position is given:</p>
<ul class="simple">
<li><p>For fixed width types, the column uses the appropriate number of bytes for
the specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> (see above).</p></li>
<li><p>When the <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> value is decoded, all bytes from the start position to
the end of the message is used.</p></li>
</ul>
<p>If start and end position are given:</p>
<ul class="simple">
<li><p>For fixed width types, the size must be equal to the number of bytes used by
specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code>.</p></li>
<li><p>For the <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> data type all bytes between start (inclusive) and end
(exclusive) are used.</p></li>
</ul>
<p>If no <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attribute is specified, it is equivalent to setting the start
position to 0 and leaving the end position undefined.</p>
<p>The decoding scheme of numeric data types (<code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>,
<code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>) is straightforward. A sequence of bytes
is read from input message and decoded according to either:</p>
<ul class="simple">
<li><p>big-endian encoding (for integer types)</p></li>
<li><p>IEEE 754 format for (for <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>).</p></li>
</ul>
<p>The length of a decoded byte sequence is implied by the <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code>.</p>
<p>For the <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> data type, a sequence of bytes is interpreted according to
UTF-8 encoding.</p>
</section>
<section id="csv-decoder">
<h4 id="csv-decoder">CSV decoder<a class="headerlink" href="redis.html#csv-decoder" title="Link to this heading">#</a></h4>
<p>The CSV decoder converts the bytes representing a message or key into a string
using UTF-8 encoding, and interprets the result as a link of comma-separated
values.</p>
<p>For fields, the <code class="docutils literal notranslate"><span class="pre">type</span></code> and <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attributes must be defined:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type. See the following table for a list of supported
data types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - The index of the field in the CSV record.</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> and <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> attributes are not supported and must be
omitted.</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Decoding rules</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p>Decoded using Java <code class="docutils literal notranslate"><span class="pre">Long.parseLong()</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p>Decoded using Java <code class="docutils literal notranslate"><span class="pre">Double.parseDouble()</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p>“true” character sequence maps to <code class="docutils literal notranslate"><span class="pre">true</span></code>. Other character sequences map
to <code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p>Used as is</p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="json-decoder">
<h4 id="json-decoder">JSON decoder<a class="headerlink" href="redis.html#json-decoder" title="Link to this heading">#</a></h4>
<p>The JSON decoder converts the bytes representing a message or key into
Javascript Object Notation (JSON) according to <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4627.html"><strong>RFC 4627</strong></a>. The message or key
must convert into a JSON object, not an array or simple type.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Field decoder to be used for column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - Slash-separated list of field names to select a field from the
JSON object.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">formatHint</span></code> - Only for <code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>.</p></li>
</ul>
<p>The JSON decoder supports multiple field decoders with <code class="docutils literal notranslate"><span class="pre">_default</span></code> being used
for standard table columns and a number of decoders for date and time-based
types.</p>
<p>The following table lists Trino data types, which can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code> and
matching field decoders, and specified via <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> values</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code>,
<code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p>Default field decoder (omitted <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>,
<code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>,
<code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>,
<code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<section id="default-field-decoder">
<h5 id="default-field-decoder">Default field decoder<a class="headerlink" href="redis.html#default-field-decoder" title="Link to this heading">#</a></h5>
<p>This is the standard field decoder. It supports all the Trino physical data
types. A field value is transformed under JSON conversion rules into boolean,
long, double, or string values. This decoder should be used for columns that are
not date or time based.</p>
</section>
<section id="date-and-time-decoders">
<h5 id="date-and-time-decoders">Date and time decoders<a class="headerlink" href="redis.html#date-and-time-decoders" title="Link to this heading">#</a></h5>
<p>To convert values from JSON objects to Trino <code class="docutils literal notranslate"><span class="pre">DATE</span></code>, <code class="docutils literal notranslate"><span class="pre">TIME</span></code>, <code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code>, <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> or <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> columns, select
special decoders using the <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute of a field definition.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">iso8601</span></code> - Text based, parses a text field as an ISO 8601 timestamp.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rfc2822</span></code> - Text based, parses a text field as an <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a> timestamp.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code> - Text based, parses a text field according to Joda
format pattern specified via <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> attribute. The format pattern
should conform to
<a class="reference external" href="https://www.joda.org/joda-time/apidocs/org/joda/time/format/DateTimeFormat.html">https://www.joda.org/joda-time/apidocs/org/joda/time/format/DateTimeFormat.html</a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code> - Number-based, interprets a text or number as
number of milliseconds since the epoch.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code> - Number-based, interprets a text or number as number
of milliseconds since the epoch.</p></li>
</ul>
<p>For <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> and <code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> data types, if
timezone information is present in decoded value, it is used as a Trino value.
Otherwise, the result time zone is set to <code class="docutils literal notranslate"><span class="pre">UTC</span></code>.</p>
</section>
</section>
<section id="avro-decoder">
<h4 id="avro-decoder">Avro decoder<a class="headerlink" href="redis.html#avro-decoder" title="Link to this heading">#</a></h4>
<p>The Avro decoder converts the bytes representing a message or key in Avro format
based on a schema. The message must have the Avro schema embedded. Trino does
not support schemaless Avro decoding.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">dataSchema</span></code> must be defined for any key or message using <code class="docutils literal notranslate"><span class="pre">Avro</span></code>
decoder. <code class="docutils literal notranslate"><span class="pre">Avro</span></code> decoder should point to the location of a valid Avro
schema file of the message which must be decoded. This location can be a remote
web server (e.g.: <code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'http://example.org/schema/avro_data.avsc'</span></code>) or
local file system(e.g.: <code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'/usr/local/schema/avro_data.avsc'</span></code>). The
decoder fails if this location is not accessible from the Trino cluster.</p>
<p>The following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> - Name of the column in the Trino table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - A slash-separated list of field names to select a field from the
Avro schema. If the field specified in <code class="docutils literal notranslate"><span class="pre">mapping</span></code> does not exist in the
original Avro schema, a read operation returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p></li>
</ul>
<p>The following table lists the supported Trino types that can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code>
for the equivalent Avro field types:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed Avro data type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FIXED</span></code>, <code class="docutils literal notranslate"><span class="pre">BYTES</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<section id="avro-schema-evolution">
<h5 id="avro-schema-evolution">Avro schema evolution<a class="headerlink" href="redis.html#avro-schema-evolution" title="Link to this heading">#</a></h5>
<p>The Avro decoder supports schema evolution with backward compatibility. With
backward compatibility, a newer schema can be used to read Avro data created
with an older schema. Any change in the Avro schema must also be reflected in
Trino’s topic definition file. Newly added or renamed fields must have a
default value in the Avro schema file.</p>
<p>The schema evolution behavior is as follows:</p>
<ul class="simple">
<li><p>Column added in new schema: Data created with an older schema produces a
<em>default</em> value when the table is using the new schema.</p></li>
<li><p>Column removed in new schema: Data created with an older schema no longer
outputs the data from the column that was removed.</p></li>
<li><p>Column is renamed in the new schema: This is equivalent to removing the column
and adding a new one, and data created with an older schema produces a
<em>default</em> value when the table is using the new schema.</p></li>
<li><p>Changing type of column in the new schema: If the type coercion is supported
by Avro, then the conversion happens. An error is thrown for incompatible
types.</p></li>
</ul>
</section>
</section>
</section>
</section>
<section id="sql-support">
<span id="redis-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="redis.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements to access data and
metadata in Redis.</p>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="redis.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements, detailed in the
following sections.</p>
<section id="pushdown">
<span id="redis-pushdown"></span><h3 id="pushdown">Pushdown<a class="headerlink" href="redis.html#pushdown" title="Link to this heading">#</a></h3>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The connector performs pushdown where performance may be improved, but in
order to preserve correctness an operation may not be pushed down. When
pushdown of an operation may result in better performance but risks
correctness, the connector prioritizes correctness.</p>
</div>
<section id="predicate-pushdown-support">
<span id="redis-predicate-pushdown"></span><h4 id="predicate-pushdown-support">Predicate pushdown support<a class="headerlink" href="redis.html#predicate-pushdown-support" title="Link to this heading">#</a></h4>
<p>The connector supports pushdown of keys of <code class="docutils literal notranslate"><span class="pre">string</span></code> type only, the <code class="docutils literal notranslate"><span class="pre">zset</span></code>
type is not supported. Key pushdown is not supported when multiple key fields
are defined in the table definition file.</p>
<p>The connector supports pushdown of equality predicates, such as <code class="docutils literal notranslate"><span class="pre">IN</span></code> or <code class="docutils literal notranslate"><span class="pre">=</span></code>.
Inequality predicates, such as <code class="docutils literal notranslate"><span class="pre">!=</span></code>, and range predicates, such as <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, or <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code> are not pushed down.</p>
<p>In the following example, the predicate of the first query is not pushed down
since <code class="docutils literal notranslate"><span class="pre">&gt;</span></code> is a range predicate. The other queries are pushed down:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Not pushed down</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">redis_key</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="s1">'CANADA'</span><span class="p">;</span>
<span class="c1">-- Pushed down</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">redis_key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'CANADA'</span><span class="p">;</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">redis_key</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'CANADA'</span><span class="p">,</span><span class="w"> </span><span class="s1">'POLAND'</span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="prometheus.html" title="Prometheus connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Prometheus connector </span>
              </div>
            </a>
          
          
            <a href="redshift.html" title="Redshift connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Redshift connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>