<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>SingleStore connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="singlestore.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Snowflake connector" href="snowflake.html" />
    <link rel="prev" title="Redshift connector" href="redshift.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="singlestore.html#connector/singlestore" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> SingleStore connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> SingleStore </label>
    
      <a href="singlestore.html#" class="md-nav__link md-nav__link--active">SingleStore</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="singlestore.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#connection-security" class="md-nav__link">Connection security</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#multiple-singlestore-servers" class="md-nav__link">Multiple SingleStore servers</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#appending-query-metadata" class="md-nav__link">Appending query metadata</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#domain-compaction-threshold" class="md-nav__link">Domain compaction threshold</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#case-insensitive-matching" class="md-nav__link">Case insensitive matching</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#querying-singlestore" class="md-nav__link">Querying SingleStore</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#singlestore-to-trino-type-mapping" class="md-nav__link">Singlestore to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#trino-to-singlestore-type-mapping" class="md-nav__link">Trino to Singlestore type mapping</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#decimal-type-handling" class="md-nav__link">Decimal type handling</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#non-transactional-insert" class="md-nav__link">Non-transactional INSERT</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#update-limitation" class="md-nav__link">UPDATE limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#delete-limitation" class="md-nav__link">DELETE limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#alter-table-rename-to-limitation" class="md-nav__link">ALTER TABLE RENAME TO limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#pushdown" class="md-nav__link">Pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#join-pushdown" class="md-nav__link">Join pushdown</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#predicate-pushdown-support" class="md-nav__link">Predicate pushdown support</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="singlestore.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#connection-security" class="md-nav__link">Connection security</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#multiple-singlestore-servers" class="md-nav__link">Multiple SingleStore servers</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#appending-query-metadata" class="md-nav__link">Appending query metadata</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#domain-compaction-threshold" class="md-nav__link">Domain compaction threshold</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#case-insensitive-matching" class="md-nav__link">Case insensitive matching</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#querying-singlestore" class="md-nav__link">Querying SingleStore</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#singlestore-to-trino-type-mapping" class="md-nav__link">Singlestore to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#trino-to-singlestore-type-mapping" class="md-nav__link">Trino to Singlestore type mapping</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#decimal-type-handling" class="md-nav__link">Decimal type handling</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#non-transactional-insert" class="md-nav__link">Non-transactional INSERT</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#update-limitation" class="md-nav__link">UPDATE limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#delete-limitation" class="md-nav__link">DELETE limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#alter-table-rename-to-limitation" class="md-nav__link">ALTER TABLE RENAME TO limitation</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#pushdown" class="md-nav__link">Pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="singlestore.html#join-pushdown" class="md-nav__link">Join pushdown</a>
        </li>
        <li class="md-nav__item"><a href="singlestore.html#predicate-pushdown-support" class="md-nav__link">Predicate pushdown support</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="singlestore-connector">
<h1 id="connector-singlestore--page-root">SingleStore connector<a class="headerlink" href="singlestore.html#connector-singlestore--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/singlestore.png"/><p>The SingleStore (formerly known as MemSQL) connector allows querying and
creating tables in an external SingleStore database.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="singlestore.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to SingleStore, you need:</p>
<ul class="simple">
<li><p>SingleStore version 7.8 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to SingleStore. Port
3306 is the default port.</p></li>
</ul>
</section>
<section id="configuration">
<span id="singlestore-configuration"></span><h2 id="configuration">Configuration<a class="headerlink" href="singlestore.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the SingleStore connector, create a catalog properties file in
<code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code> named, for example, <code class="docutils literal notranslate"><span class="pre">example.properties</span></code>, to mount the
SingleStore connector as the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Create the file with the
following contents, replacing the connection properties as appropriate for your
setup:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=singlestore
connection-url=jdbc:singlestore://example.net:3306
connection-user=root
connection-password=secret
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">connection-url</span></code> defines the connection information and parameters to pass
to the SingleStore JDBC driver. The supported parameters for the URL are
available in the <a class="reference external" href="https://docs.singlestore.com/db/latest/developer-resources/connect-with-application-development-tools/connect-with-java-jdbc/the-singlestore-jdbc-driver/#connection-string-parameters">SingleStore JDBC driver
documentation</a>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">connection-user</span></code> and <code class="docutils literal notranslate"><span class="pre">connection-password</span></code> are typically required and
determine the user credentials for the connection, often a service user. You can
use <a class="reference internal" href="../security/secrets.html"><span class="doc">secrets</span></a> to avoid actual values in the catalog
properties files.</p>
<section id="connection-security">
<span id="singlestore-tls"></span><h3 id="connection-security">Connection security<a class="headerlink" href="singlestore.html#connection-security" title="Link to this heading">#</a></h3>
<p>If you have TLS configured with a globally-trusted certificate installed on your
data source, you can enable TLS between your cluster and the data
source by appending a parameter to the JDBC connection string set in the
<code class="docutils literal notranslate"><span class="pre">connection-url</span></code> catalog configuration property.</p>
<p>Enable TLS between your cluster and SingleStore by appending the <code class="docutils literal notranslate"><span class="pre">useSsl=true</span></code>
parameter to the <code class="docutils literal notranslate"><span class="pre">connection-url</span></code> configuration property:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connection-url</span><span class="o">=</span><span class="s">jdbc:singlestore://example.net:3306/?useSsl=true</span>
</pre></div>
</div>
<p>For more information on TLS configuration options, see the <a class="reference external" href="https://docs.singlestore.com/db/latest/developer-resources/connect-with-application-development-tools/connect-with-java-jdbc/the-singlestore-jdbc-driver/#tls-">JDBC driver
documentation</a>.</p>
</section>
<section id="multiple-singlestore-servers">
<h3 id="multiple-singlestore-servers">Multiple SingleStore servers<a class="headerlink" href="singlestore.html#multiple-singlestore-servers" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need, so if you have additional
SingleStore servers, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name (making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>). For
example, if you name the property file <code class="docutils literal notranslate"><span class="pre">sales.properties</span></code>, Trino
will create a catalog named <code class="docutils literal notranslate"><span class="pre">sales</span></code> using the configured connector.</p>
</section>
<section id="general-configuration-properties">
<h3 id="general-configuration-properties">General configuration properties<a class="headerlink" href="singlestore.html#general-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following table describes general catalog configuration properties for the
connector:</p>
<table>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching</span></code></p></td>
<td><p>Support case insensitive schema and table names. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which case insensitive schema and table
names are cached. Defaults to <code class="docutils literal notranslate"><span class="pre">1m</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file</span></code></p></td>
<td><p>Path to a name mapping configuration file in JSON format that allows
Trino to disambiguate between schemas and tables with similar names in
different cases. Defaults to <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file.refresh-period</span></code></p></td>
<td><p>Frequency with which Trino checks the name matching configuration file
for changes. The <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration value</span></a> defaults to <code class="docutils literal notranslate"><span class="pre">0s</span></code>
(refresh disabled).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which metadata, including table and
column statistics, is cached. Defaults to <code class="docutils literal notranslate"><span class="pre">0s</span></code> (caching disabled).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-missing</span></code></p></td>
<td><p>Cache the fact that metadata, including table and column statistics, is
not available. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.schemas.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which schema metadata is cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.tables.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which table metadata is cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.statistics.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which tables statistics are cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-maximum-size</span></code></p></td>
<td><p>Maximum number of objects stored in the metadata cache. Defaults to <code class="docutils literal notranslate"><span class="pre">10000</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">write.batch-size</span></code></p></td>
<td><p>Maximum number of statements in a batched execution. Do not change
this setting from the default. Non-default values may negatively
impact performance. Defaults to <code class="docutils literal notranslate"><span class="pre">1000</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">dynamic-filtering.enabled</span></code></p></td>
<td><p>Push down dynamic filters into JDBC queries. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">dynamic-filtering.wait-timeout</span></code></p></td>
<td><p>Maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for which Trino waits for dynamic
filters to be collected from the build side of joins before starting a
JDBC query. Using a large timeout can potentially result in more detailed
dynamic filters. However, it can also increase latency for some queries.
Defaults to <code class="docutils literal notranslate"><span class="pre">20s</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="appending-query-metadata">
<h3 id="appending-query-metadata">Appending query metadata<a class="headerlink" href="singlestore.html#appending-query-metadata" title="Link to this heading">#</a></h3>
<p>The optional parameter <code class="docutils literal notranslate"><span class="pre">query.comment-format</span></code> allows you to configure a SQL
comment that is sent to the datasource with each query. The format of this
comment can contain any characters and the following metadata:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$QUERY_ID</span></code>: The identifier of the query.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$USER</span></code>: The name of the user who submits the query to Trino.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$SOURCE</span></code>: The identifier of the client tool used to submit the query, for
example <code class="docutils literal notranslate"><span class="pre">trino-cli</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$TRACE_TOKEN</span></code>: The trace token configured with the client tool.</p></li>
</ul>
<p>The comment can provide more context about the query. This additional
information is available in the logs of the datasource. To include environment
variables from the Trino cluster with the comment , use the
<code class="docutils literal notranslate"><span class="pre">${ENV:VARIABLE-NAME}</span></code> syntax.</p>
<p>The following example sets a simple comment that identifies each query sent by
Trino:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>query.comment-format=Query sent by Trino.
</pre></div>
</div>
<p>With this configuration, a query such as <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span> <span class="pre">FROM</span> <span class="pre">example_table;</span></code> is
sent to the datasource with the comment appended:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT * FROM example_table; /*Query sent by Trino.*/
</pre></div>
</div>
<p>The following example improves on the preceding example by using metadata:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>query.comment-format=Query $QUERY_ID sent by user $USER from Trino.
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">Jane</span></code> sent the query with the query identifier
<code class="docutils literal notranslate"><span class="pre">20230622_180528_00000_bkizg</span></code>, the following comment string is sent to the
datasource:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT * FROM example_table; /*Query 20230622_180528_00000_bkizg sent by user Jane from Trino.*/
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Certain JDBC driver settings and logging configurations might cause the
comment to be removed.</p>
</div>
</section>
<section id="domain-compaction-threshold">
<h3 id="domain-compaction-threshold">Domain compaction threshold<a class="headerlink" href="singlestore.html#domain-compaction-threshold" title="Link to this heading">#</a></h3>
<p>Pushing down a large list of predicates to the data source can compromise
performance. Trino compacts large predicates into a simpler range predicate
by default to ensure a balance between performance and predicate pushdown.
If necessary, the threshold for this compaction can be increased to improve
performance when the data source is capable of taking advantage of large
predicates. Increasing this threshold may improve pushdown of large
<a class="reference internal" href="../admin/dynamic-filtering.html"><span class="doc">dynamic filters</span></a>.
The <code class="docutils literal notranslate"><span class="pre">domain-compaction-threshold</span></code> catalog configuration property or the
<code class="docutils literal notranslate"><span class="pre">domain_compaction_threshold</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">catalog session property</span></a> can be used to adjust the default value of
<code class="docutils literal notranslate"><span class="pre">256</span></code> for this threshold.</p>
</section>
<section id="case-insensitive-matching">
<h3 id="case-insensitive-matching">Case insensitive matching<a class="headerlink" href="singlestore.html#case-insensitive-matching" title="Link to this heading">#</a></h3>
<p>When <code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>, Trino
is able to query non-lowercase schemas and tables by maintaining a mapping of
the lowercase name to the actual name in the remote system. However, if two
schemas and/or tables have names that differ only in case (such as “customers”
and “Customers”) then Trino fails to query them due to ambiguity.</p>
<p>In these cases, use the <code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file</span></code> catalog
configuration property to specify a configuration file that maps these remote
schemas and tables to their respective Trino schemas and tables. Additionally,
the JSON file must include both the <code class="docutils literal notranslate"><span class="pre">schemas</span></code> and <code class="docutils literal notranslate"><span class="pre">tables</span></code> properties, even if
only as empty arrays.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"case_insensitive_1"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cASEsENSITIVEnAME"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"case_insensitive_2"</span>
<span class="w">    </span><span class="p">}],</span>
<span class="w">  </span><span class="nt">"tables"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"remoteTable"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tablex"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table_1"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"remoteTable"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TABLEX"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table_2"</span>
<span class="w">    </span><span class="p">}]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Queries against one of the tables or schemes defined in the <code class="docutils literal notranslate"><span class="pre">mapping</span></code>
attributes are run against the corresponding remote entity. For example, a query
against tables in the <code class="docutils literal notranslate"><span class="pre">case_insensitive_1</span></code> schema is forwarded to the
CaseSensitiveName schema and a query against <code class="docutils literal notranslate"><span class="pre">case_insensitive_2</span></code> is forwarded
to the <code class="docutils literal notranslate"><span class="pre">cASEsENSITIVEnAME</span></code> schema.</p>
<p>At the table mapping level, a query on <code class="docutils literal notranslate"><span class="pre">case_insensitive_1.table_1</span></code> as
configured above is forwarded to <code class="docutils literal notranslate"><span class="pre">CaseSensitiveName.tablex</span></code>, and a query on
<code class="docutils literal notranslate"><span class="pre">case_insensitive_1.table_2</span></code> is forwarded to <code class="docutils literal notranslate"><span class="pre">CaseSensitiveName.TABLEX</span></code>.</p>
<p>By default, when a change is made to the mapping configuration file, Trino must
be restarted to load the changes. Optionally, you can set the
<code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file.refresh-period</span></code> to have Trino
refresh the properties without requiring a restart:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">case-insensitive-name-matching.config-file.refresh-period</span><span class="o">=</span><span class="s">30s</span>
</pre></div>
</div>
</section>
</section>
<section id="querying-singlestore">
<h2 id="querying-singlestore">Querying SingleStore<a class="headerlink" href="singlestore.html#querying-singlestore" title="Link to this heading">#</a></h2>
<p>The SingleStore connector provides a schema for every SingleStore <em>database</em>.
You can see the available SingleStore databases by running <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">SCHEMAS</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span><span class="w"> </span><span class="n">SCHEMAS</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">;</span>
</pre></div>
</div>
<p>If you have a SingleStore database named <code class="docutils literal notranslate"><span class="pre">web</span></code>, you can view the tables
in this database by running <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">TABLES</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span><span class="w"> </span><span class="n">TABLES</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">;</span>
</pre></div>
</div>
<p>You can see a list of the columns in the <code class="docutils literal notranslate"><span class="pre">clicks</span></code> table in the <code class="docutils literal notranslate"><span class="pre">web</span></code>
database using either of the following:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">DESCRIBE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">clicks</span><span class="p">;</span>
<span class="k">SHOW</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">clicks</span><span class="p">;</span>
</pre></div>
</div>
<p>Finally, you can access the <code class="docutils literal notranslate"><span class="pre">clicks</span></code> table in the <code class="docutils literal notranslate"><span class="pre">web</span></code> database:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">clicks</span><span class="p">;</span>
</pre></div>
</div>
<p>If you used a different name for your catalog properties file, use
that catalog name instead of <code class="docutils literal notranslate"><span class="pre">example</span></code> in the above examples.</p>
</section>
<section id="type-mapping">
<span id="singlestore-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="singlestore.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Singlestore each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<section id="singlestore-to-trino-type-mapping">
<h3 id="singlestore-to-trino-type-mapping">Singlestore to Trino type mapping<a class="headerlink" href="singlestore.html#singlestore-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Singlestore types to the corresponding Trino types following
this table:</p>
<table id="id1">
<caption><span class="caption-text">Singlestore to Trino type mapping</span><a class="headerlink" href="singlestore.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Singlestore type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span> <span class="pre">UNSIGNED</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span> <span class="pre">UNSIGNED</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span> <span class="pre">UNSIGNED</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span> <span class="pre">UNSIGNED</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(20,</span> <span class="pre">0)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td><p>See <a class="reference internal" href="singlestore.html#singlestore-decimal-handling"><span class="std std-ref">Singlestore DECIMAL type handling</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CHAR(n)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CHAR(n)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TINYTEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(255)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(65535)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MEDIUMTEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(16777215)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LONGTEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(n)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(n)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LONGBLOB</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(0)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATETIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(0)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATETIME(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-to-singlestore-type-mapping">
<h3 id="trino-to-singlestore-type-mapping">Trino to Singlestore type mapping<a class="headerlink" href="singlestore.html#trino-to-singlestore-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding Singlestore types following
this table:</p>
<table id="id2">
<caption><span class="caption-text">Trino to Singlestore type mapping</span><a class="headerlink" href="singlestore.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>Singlestore type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td><p>See <a class="reference internal" href="singlestore.html#singlestore-decimal-handling"><span class="std std-ref">Singlestore DECIMAL type handling</span></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">CHAR(n)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">CHAR(n)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(65535)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(16777215)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MEDIUMTEXT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LONGTEXT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(n)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR(n)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LONGBLOB</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME(0)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(0)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATETIME</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATETIME(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="decimal-type-handling">
<span id="singlestore-decimal-handling"></span><h3 id="decimal-type-handling">Decimal type handling<a class="headerlink" href="singlestore.html#decimal-type-handling" title="Link to this heading">#</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code> types with unspecified precision or scale are ignored unless the
<code class="docutils literal notranslate"><span class="pre">decimal-mapping</span></code> configuration property or the <code class="docutils literal notranslate"><span class="pre">decimal_mapping</span></code> session
property is set to <code class="docutils literal notranslate"><span class="pre">allow_overflow</span></code>. Then such types are mapped to a Trino
<code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code> with a default precision of 38 and default scale of 0. To change the
scale of the resulting type, use the <code class="docutils literal notranslate"><span class="pre">decimal-default-scale</span></code> configuration
property or the <code class="docutils literal notranslate"><span class="pre">decimal_default_scale</span></code> session property. The precision is
always 38.</p>
<p>By default, values that require rounding or truncation to fit will cause a
failure at runtime. This behavior is controlled via the
<code class="docutils literal notranslate"><span class="pre">decimal-rounding-mode</span></code> configuration property or the
<code class="docutils literal notranslate"><span class="pre">decimal_rounding_mode</span></code> session property, which can be set to <code class="docutils literal notranslate"><span class="pre">UNNECESSARY</span></code>
(the default), <code class="docutils literal notranslate"><span class="pre">UP</span></code>, <code class="docutils literal notranslate"><span class="pre">DOWN</span></code>, <code class="docutils literal notranslate"><span class="pre">CEILING</span></code>, <code class="docutils literal notranslate"><span class="pre">FLOOR</span></code>, <code class="docutils literal notranslate"><span class="pre">HALF_UP</span></code>,
<code class="docutils literal notranslate"><span class="pre">HALF_DOWN</span></code>, or <code class="docutils literal notranslate"><span class="pre">HALF_EVEN</span></code> (see <a class="reference external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/math/RoundingMode.html#enum.constant.summary">RoundingMode</a>).</p>
</section>
<section id="type-mapping-configuration-properties">
<h3 id="type-mapping-configuration-properties">Type mapping configuration properties<a class="headerlink" href="singlestore.html#type-mapping-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following properties can be used to configure how data types from the
connected data source are mapped to Trino data types and how the metadata is
cached in Trino.</p>
<table>
<colgroup>
<col style="width: 30%"/>
<col style="width: 40%"/>
<col style="width: 30%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">unsupported-type-handling</span></code></p></td>
<td><p>Configure how unsupported column data types are handled:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code>, column is not accessible.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CONVERT_TO_VARCHAR</span></code>, column is converted to unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>.</p></li>
</ul>
<p>The respective catalog session property is <code class="docutils literal notranslate"><span class="pre">unsupported_type_handling</span></code>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">jdbc-types-mapped-to-varchar</span></code></p></td>
<td><p>Allow forced mapping of comma separated lists of data types to convert to
unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sql-support">
<span id="singlestore-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="singlestore.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read access and write access to data and metadata in
a SingleStore database.  In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a>
statements, the connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc std std-doc">INSERT</span></a>, see also <a class="reference internal" href="singlestore.html#singlestore-insert"><span class="std std-ref">Non-transactional INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/update.html"><span class="doc std std-doc">UPDATE</span></a>, see also <a class="reference internal" href="singlestore.html#singlestore-update"><span class="std std-ref">UPDATE limitation</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc std std-doc">DELETE</span></a>, see also <a class="reference internal" href="singlestore.html#singlestore-delete"><span class="std std-ref">DELETE limitation</span></a></p></li>
<li><p><a class="reference internal" href="../sql/truncate.html"><span class="doc std std-doc">TRUNCATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc std std-doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc std std-doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc std std-doc">ALTER TABLE</span></a>, see also <a class="reference internal" href="singlestore.html#singlestore-alter-table"><span class="std std-ref">ALTER TABLE RENAME TO limitation</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc std std-doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc std std-doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc std std-doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="singlestore.html#singlestore-procedures"><span class="std std-ref">Procedures</span></a></p></li>
</ul>
<section id="non-transactional-insert">
<span id="singlestore-insert"></span><h3 id="non-transactional-insert">Non-transactional INSERT<a class="headerlink" href="singlestore.html#non-transactional-insert" title="Link to this heading">#</a></h3>
<p>The connector supports adding rows using <a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT statements</span></a>.
By default, data insertion is performed by writing data to a temporary table.
You can skip this step to improve performance and write directly to the target
table. Set the <code class="docutils literal notranslate"><span class="pre">insert.non-transactional-insert.enabled</span></code> catalog property
or the corresponding <code class="docutils literal notranslate"><span class="pre">non_transactional_insert</span></code> catalog session property to
<code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
<p>Note that with this property enabled, data can be corrupted in rare cases where
exceptions occur during the insert operation. With transactions disabled, no
rollback can be performed.</p>
</section>
<section id="update-limitation">
<span id="singlestore-update"></span><h3 id="update-limitation">UPDATE limitation<a class="headerlink" href="singlestore.html#update-limitation" title="Link to this heading">#</a></h3>
<p>Only <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code> statements with constant assignments and predicates are
supported. For example, the following statement is supported because the values
assigned are constants:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">UPDATE</span><span class="w"> </span><span class="k">table</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">col1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span>
</pre></div>
</div>
<p>Arithmetic expressions, function calls, and other non-constant <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>
statements are not supported. For example, the following statement is not
supported because arithmetic expressions cannot be used with the <code class="docutils literal notranslate"><span class="pre">SET</span></code>
command:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">UPDATE</span><span class="w"> </span><span class="k">table</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">col1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">col2</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span>
</pre></div>
</div>
<p>All column values of a table row cannot be updated simultaneously. For a three
column table, the following statement is not supported:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">UPDATE</span><span class="w"> </span><span class="k">table</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">col1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">col2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span>
</pre></div>
</div>
</section>
<section id="delete-limitation">
<span id="singlestore-delete"></span><h3 id="delete-limitation">DELETE limitation<a class="headerlink" href="singlestore.html#delete-limitation" title="Link to this heading">#</a></h3>
<p>If a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause is specified, the <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> operation only works if the
predicate in the clause can be fully pushed down to the data source.</p>
</section>
<section id="alter-table-rename-to-limitation">
<span id="singlestore-alter-table"></span><h3 id="alter-table-rename-to-limitation">ALTER TABLE RENAME TO limitation<a class="headerlink" href="singlestore.html#alter-table-rename-to-limitation" title="Link to this heading">#</a></h3>
<p>The connector does not support renaming tables across multiple schemas. For
example, the following statement is supported:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">schema_one</span><span class="p">.</span><span class="n">table_one</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">schema_one</span><span class="p">.</span><span class="n">table_two</span>
</pre></div>
</div>
<p>The following statement attempts to rename a table across schemas, and therefore
is not supported:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">schema_one</span><span class="p">.</span><span class="n">table_one</span><span class="w"> </span><span class="k">RENAME</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">schema_two</span><span class="p">.</span><span class="n">table_two</span>
</pre></div>
</div>
</section>
<section id="procedures">
<span id="singlestore-procedures"></span><h3 id="procedures">Procedures<a class="headerlink" href="singlestore.html#procedures" title="Link to this heading">#</a></h3>
<section id="system-flush-metadata-cache">
<h4 id="system-flush-metadata-cache"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code><a class="headerlink" href="singlestore.html#system-flush-metadata-cache" title="Link to this heading">#</a></h4>
<p>Flush JDBC metadata caches. For example, the following system call
flushes the metadata caches for all schemas in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">flush_metadata_cache</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="system-execute-query">
<h4 id="system-execute-query"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code><a class="headerlink" href="singlestore.html#system-execute-query" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">execute</span></code> procedure allows you to execute a query in the underlying data
source directly. The query must use supported syntax of the connected data
source. Use the procedure to access features which are not available in Trino
or to execute queries that return no result set and therefore can not be used
with the <code class="docutils literal notranslate"><span class="pre">query</span></code> or <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> pass-through table function. Typical use cases
are statements that create or alter objects, and require native feature such
as constraints, default values, automatic identifier creation, or indexes.
Queries can also invoke statements that insert, update, or delete data, and do
not return any data as a result.</p>
<p>The query text is not parsed by Trino, only passed through, and therefore only
subject to any security or access control of the underlying data source.</p>
<p>The following example sets the current database to the <code class="docutils literal notranslate"><span class="pre">example_schema</span></code> of the
<code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Then it calls the procedure in that schema to drop the
default value from <code class="docutils literal notranslate"><span class="pre">your_column</span></code> on <code class="docutils literal notranslate"><span class="pre">your_table</span></code> table using the standard SQL
syntax in the parameter value assigned for <code class="docutils literal notranslate"><span class="pre">query</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="k">execute</span><span class="p">(</span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ALTER TABLE your_table ALTER COLUMN your_column DROP DEFAULT'</span><span class="p">);</span>
</pre></div>
</div>
<p>Verify that the specific database supports this syntax, and adapt as necessary
based on the documentation for the specific connected database and database
version.</p>
</section>
</section>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="singlestore.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements, detailed in the
following sections.</p>
<section id="pushdown">
<span id="singlestore-pushdown"></span><h3 id="pushdown">Pushdown<a class="headerlink" href="singlestore.html#pushdown" title="Link to this heading">#</a></h3>
<p>The connector supports pushdown for a number of operations:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../optimizer/pushdown.html#join-pushdown"><span class="std std-ref">Join pushdown</span></a></p></li>
<li><p><a class="reference internal" href="../optimizer/pushdown.html#limit-pushdown"><span class="std std-ref">Limit pushdown</span></a></p></li>
<li><p><a class="reference internal" href="../optimizer/pushdown.html#topn-pushdown"><span class="std std-ref">Top-N pushdown</span></a></p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The connector performs pushdown where performance may be improved, but in
order to preserve correctness an operation may not be pushed down. When
pushdown of an operation may result in better performance but risks
correctness, the connector prioritizes correctness.</p>
</div>
<section id="join-pushdown">
<h4 id="join-pushdown">Join pushdown<a class="headerlink" href="singlestore.html#join-pushdown" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">join-pushdown.enabled</span></code> catalog configuration property or
<code class="docutils literal notranslate"><span class="pre">join_pushdown_enabled</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">catalog session property</span></a> control whether the connector pushes
down join operations. The property defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>, and enabling join
pushdowns may negatively impact performance for some queries.</p>
</section>
<section id="predicate-pushdown-support">
<h4 id="predicate-pushdown-support">Predicate pushdown support<a class="headerlink" href="singlestore.html#predicate-pushdown-support" title="Link to this heading">#</a></h4>
<p>The connector does not support pushdown of any predicates on columns with
<a class="reference internal" href="../language/types.html#string-data-types"><span class="std std-ref">textual types</span></a> like <code class="docutils literal notranslate"><span class="pre">CHAR</span></code> or <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>.
This ensures correctness of results since the data source may compare strings
case-insensitively.</p>
<p>In the following example, the predicate is not pushed down for either query
since <code class="docutils literal notranslate"><span class="pre">name</span></code> is a column of type <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="s1">'CANADA'</span><span class="p">;</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'CANADA'</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="redshift.html" title="Redshift connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Redshift connector </span>
              </div>
            </a>
          
          
            <a href="snowflake.html" title="Snowflake connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Snowflake connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>