<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Hudi connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="hudi.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Iceberg connector" href="iceberg.html" />
    <link rel="prev" title="Hive connector" href="hive.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="hudi.html#connector/hudi" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Hudi connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Hudi </label>
    
      <a href="hudi.html#" class="md-nav__link md-nav__link--active">Hudi</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="hudi.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#basic-usage-examples" class="md-nav__link">Basic usage examples</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#timeline-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$timeline</span></code> table</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="hudi.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#basic-usage-examples" class="md-nav__link">Basic usage examples</a>
        </li>
        <li class="md-nav__item"><a href="hudi.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="hudi.html#timeline-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$timeline</span></code> table</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="hudi-connector">
<h1 id="connector-hudi--page-root">Hudi connector<a class="headerlink" href="hudi.html#connector-hudi--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/hudi.png"/><p>The Hudi connector enables querying <a class="reference external" href="https://hudi.apache.org/docs/overview/">Hudi</a> tables.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="hudi.html#requirements" title="Link to this heading">#</a></h2>
<p>To use the Hudi connector, you need:</p>
<ul class="simple">
<li><p>Hudi version 0.12.3 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to the Hudi storage.</p></li>
<li><p>Access to a Hive metastore service (HMS).</p></li>
<li><p>Network access from the Trino coordinator to the HMS.</p></li>
<li><p>Data files stored in the <a class="reference internal" href="../object-storage/file-formats.html#parquet-format-configuration"><span class="std std-ref">Parquet file format</span></a>
on a <a class="reference internal" href="hudi.html#hudi-file-system-configuration"><span class="std std-ref">supported file system</span></a>.</p></li>
</ul>
</section>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="hudi.html#general-configuration" title="Link to this heading">#</a></h2>
<p>To configure the Hudi connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> that references the <code class="docutils literal notranslate"><span class="pre">hudi</span></code> connector.</p>
<p>You must configure a <a class="reference internal" href="../object-storage/metastores.html"><span class="doc std std-doc">metastore for table metadata</span></a>.</p>
<p>You must select and configure one of the <a class="reference internal" href="hudi.html#hudi-file-system-configuration"><span class="std std-ref">supported file
systems</span></a>.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">hudi</span>
<span class="na">hive.metastore.uri</span><span class="o">=</span><span class="s">thrift://example.net:9083</span>
<span class="na">fs.x.enabled</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
<p>Replace the <code class="docutils literal notranslate"><span class="pre">fs.x.enabled</span></code> configuration property with the desired file system.</p>
<p>There are <a class="reference internal" href="../object-storage/metastores.html#general-metastore-properties"><span class="std std-ref">HMS configuration properties</span></a>
available for use with the Hudi connector. The connector recognizes Hudi tables
synced to the metastore by the <a class="reference external" href="https://hudi.apache.org/docs/syncing_metastore">Hudi sync tool</a>.</p>
<p>Additionally, following configuration properties can be set depending on the use-case:</p>
<table id="id1">
<caption><span class="caption-text">Hudi configuration properties</span><a class="headerlink" href="hudi.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 55%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.columns-to-hide</span></code></p></td>
<td><p>List of column names that are hidden from the query output. It can be used
to hide Hudi meta fields. By default, no fields are hidden.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.parquet.use-column-names</span></code></p></td>
<td><p>Access Parquet columns using names from the file. If disabled, then columns
are accessed using the index. Only applicable to Parquet file format.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.split-generator-parallelism</span></code></p></td>
<td><p>Number of threads to generate splits from partitions.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.split-loader-parallelism</span></code></p></td>
<td><p>Number of threads to run background split loader. A single background split
loader is needed per query.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.size-based-split-weights-enabled</span></code></p></td>
<td><p>Unlike uniform splitting, size-based splitting ensures that each batch of
splits has enough data to process. By default, it is enabled to improve
performance.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.standard-split-weight-size</span></code></p></td>
<td><p>The split size corresponding to the standard weight (1.0) when size-based
split weights are enabled.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">128MB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.minimum-assigned-split-weight</span></code></p></td>
<td><p>Minimum weight that a split can be assigned when size-based split weights
are enabled.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0.05</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.max-splits-per-second</span></code></p></td>
<td><p>Rate at which splits are queued for processing. The queue is throttled if
this rate limit is breached.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Integer.MAX_VALUE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.max-outstanding-splits</span></code></p></td>
<td><p>Maximum outstanding splits in a batch enqueued for processing.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.per-transaction-metastore-cache-maximum-size</span></code></p></td>
<td><p>Maximum number of metastore data objects per transaction in the Hive
metastore cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.query-partition-filter-required</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to force a query to use a partition column in the filter condition.
The equivalent catalog session property is <code class="docutils literal notranslate"><span class="pre">query_partition_filter_required</span></code>.
Enabling this property causes query failures if the partition column used
in the filter condition doesn’t effectively reduce the number of data files read.
Example: Complex filter expressions such as <code class="docutils literal notranslate"><span class="pre">id</span> <span class="pre">=</span> <span class="pre">1</span> <span class="pre">OR</span> <span class="pre">part_key</span> <span class="pre">=</span> <span class="pre">'100'</span></code>
or <code class="docutils literal notranslate"><span class="pre">CAST(part_key</span> <span class="pre">AS</span> <span class="pre">INTEGER)</span> <span class="pre">%</span> <span class="pre">2</span> <span class="pre">=</span> <span class="pre">0</span></code> are not recognized as partition filters,
and queries using such expressions fail if the property is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hudi.ignore-absent-partitions</span></code></p></td>
<td><p>Ignore partitions when the file system location does not exist rather than
failing the query. This skips data that may be expected to be part of the
table.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="file-system-access-configuration">
<span id="hudi-file-system-configuration"></span><h2 id="file-system-access-configuration">File system access configuration<a class="headerlink" href="hudi.html#file-system-access-configuration" title="Link to this heading">#</a></h2>
<p>The connector supports accessing the following file systems:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-hdfs.html"><span class="doc std std-doc">HDFS file system support</span></a></p></li>
</ul>
<p>You must enable and configure the specific file system access. <a class="reference internal" href="../object-storage.html#file-system-legacy"><span class="std std-ref">Legacy
support</span></a> is not recommended and will be removed.</p>
</section>
<section id="sql-support">
<h2 id="sql-support">SQL support<a class="headerlink" href="hudi.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read access to data in the Hudi table that has been synced to
Hive metastore. The <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a>
and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements are supported.</p>
<section id="basic-usage-examples">
<h3 id="basic-usage-examples">Basic usage examples<a class="headerlink" href="hudi.html#basic-usage-examples" title="Link to this heading">#</a></h3>
<p>In the following example queries, <code class="docutils literal notranslate"><span class="pre">stock_ticks_cow</span></code> is the Hudi copy-on-write
table referred to in the Hudi <a class="reference external" href="https://hudi.apache.org/docs/docker_demo/">quickstart guide</a>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>

<span class="k">SELECT</span><span class="w"> </span><span class="n">symbol</span><span class="p">,</span><span class="w"> </span><span class="k">max</span><span class="p">(</span><span class="n">ts</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">stock_ticks_cow</span>
<span class="k">GROUP</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">symbol</span>
<span class="k">HAVING</span><span class="w"> </span><span class="n">symbol</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'GOOG'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>  symbol   |        _col1         |
-----------+----------------------+
 GOOG      | 2018-08-31 10:59:00  |
(1 rows)
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">dt</span><span class="p">,</span><span class="w"> </span><span class="n">symbol</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">stock_ticks_cow</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">symbol</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'GOOG'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>    dt      | symbol |
------------+--------+
 2018-08-31 |  GOOG  |
(1 rows)
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">dt</span><span class="p">,</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">stock_ticks_cow</span>
<span class="k">GROUP</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">dt</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>    dt      | _col1 |
------------+--------+
 2018-08-31 |  99  |
(1 rows)
</pre></div>
</div>
</section>
<section id="schema-and-table-management">
<h3 id="schema-and-table-management">Schema and table management<a class="headerlink" href="hudi.html#schema-and-table-management" title="Link to this heading">#</a></h3>
<p>Hudi supports <a class="reference external" href="https://hudi.apache.org/docs/table_types">two types of tables</a>
depending on how the data is indexed and laid out on the file system. The following
table displays a support matrix of tables types and query types for the connector:</p>
<table id="id2">
<caption><span class="caption-text">Hudi configuration properties</span><a class="headerlink" href="hudi.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 45%"/>
<col style="width: 55%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Table type</p></th>
<th class="head"><p>Supported query type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Copy on write</p></td>
<td><p>Snapshot queries</p></td>
</tr>
<tr class="row-odd"><td><p>Merge on read</p></td>
<td><p>Read-optimized queries</p></td>
</tr>
</tbody>
</table>
<section id="metadata-tables">
<span id="hudi-metadata-tables"></span><h4 id="metadata-tables">Metadata tables<a class="headerlink" href="hudi.html#metadata-tables" title="Link to this heading">#</a></h4>
<p>The connector exposes a metadata table for each Hudi table.
The metadata table contains information about the internal structure
of the Hudi table. You can query each metadata table by appending the
metadata table name to the table name:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$timeline"</span>
</pre></div>
</div>
<section id="timeline-table">
<h5 id="timeline-table"><code class="docutils literal notranslate"><span class="pre">$timeline</span></code> table<a class="headerlink" href="hudi.html#timeline-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$timeline</span></code> table provides a detailed view of meta-data instants
in the Hudi table. Instants are specific points in time.</p>
<p>You can retrieve the information about the timeline of the Hudi table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$timeline"</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> timestamp          | action  | state
--------------------+---------+-----------
8667764846443717831 | commit  | COMPLETED
7860805980949777961 | commit  | COMPLETED
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id3">
<caption><span class="caption-text">Timeline columns</span><a class="headerlink" href="hudi.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 20%"/>
<col style="width: 30%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>Instant time is typically a timestamp when the actions performed.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">action</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><a class="reference external" href="https://hudi.apache.org/docs/concepts/#timeline">Type of action</a> performed
on the table.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">state</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>Current state of the instant.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="hive.html" title="Hive connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Hive connector </span>
              </div>
            </a>
          
          
            <a href="iceberg.html" title="Iceberg connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Iceberg connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>