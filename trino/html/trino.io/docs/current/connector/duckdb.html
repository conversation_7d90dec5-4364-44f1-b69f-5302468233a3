<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>DuckDB connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="duckdb.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Elasticsearch connector" href="elasticsearch.html" />
    <link rel="prev" title="Druid connector" href="druid.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="duckdb.html#connector/duckdb" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> DuckDB connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> DuckDB </label>
    
      <a href="duckdb.html#" class="md-nav__link md-nav__link--active">DuckDB</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="duckdb.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#multiple-duckdb-servers" class="md-nav__link">Multiple DuckDB servers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#duckdb-type-to-trino-type-mapping" class="md-nav__link">DuckDB type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#trino-type-to-duckdb-type-mapping" class="md-nav__link">Trino type to DuckDB type mapping</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="duckdb.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#multiple-duckdb-servers" class="md-nav__link">Multiple DuckDB servers</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#duckdb-type-to-trino-type-mapping" class="md-nav__link">DuckDB type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#trino-type-to-duckdb-type-mapping" class="md-nav__link">Trino type to DuckDB type mapping</a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="duckdb.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="duckdb.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="duckdb-connector">
<h1 id="connector-duckdb--page-root">DuckDB connector<a class="headerlink" href="duckdb.html#connector-duckdb--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/duckdb.png"/><p>The DuckDB connector allows querying and creating tables in an external
<a class="reference external" href="https://duckdb.org/">DuckDB</a> instance. This can be used to join data between
different systems like DuckDB and Hive, or between two different
DuckDB instances.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="duckdb.html#requirements" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>All cluster nodes must include <code class="docutils literal notranslate"><span class="pre">libstdc++</span></code> as required by the <a class="reference external" href="https://duckdb.org/docs/clients/java.html">DuckDB JDBC
driver</a>.</p></li>
<li><p>The path to the persistent DuckDB database must be identical and available on
all cluster nodes and point to the same storage location.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="duckdb.html#configuration" title="Link to this heading">#</a></h2>
<p>The connector can query a DuckDB database. Create a catalog properties file that
specifies the DuckDb connector by setting the <code class="docutils literal notranslate"><span class="pre">connector.name</span></code> to <code class="docutils literal notranslate"><span class="pre">duckdb</span></code>.</p>
<p>For example, to access a database as the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog, create the file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code>. Replace the connection properties as
appropriate for your setup:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>connector.name=duckdb
connection-url=***********************;
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">connection-url</span></code> defines the connection information and parameters to pass
to the DuckDB JDBC driver. The parameters for the URL are available in the
<a class="reference external" href="https://duckdb.org/docs/clients/java.html">DuckDB JDBC driver documentation</a>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code> must point to an existing, persistent DuckDB database file. For
example, use <code class="docutils literal notranslate"><span class="pre">************************************</span></code> for a database created with
the command <code class="docutils literal notranslate"><span class="pre">duckdb</span> <span class="pre">/opt/duckdb/trino.duckdb</span></code>. The database automatically
contains the <code class="docutils literal notranslate"><span class="pre">main</span></code> schema  and the <code class="docutils literal notranslate"><span class="pre">information_schema</span></code> schema. Use the <code class="docutils literal notranslate"><span class="pre">main</span></code>
schema for your new tables or create a new schema.</p>
<p>When using the connector on a Trino cluster the path must be consistent on all
nodes and point to a shared storage to ensure that all nodes operate on the same
database.</p>
<p>Using an in-memory DuckDB database <code class="docutils literal notranslate"><span class="pre">jdbc:duckdb:</span></code> is not supported.</p>
<p>Refer to the DuckDB documentation for tips on <a class="reference external" href="https://duckdb.org/docs/operations_manual/securing_duckdb/overview">securing DuckDB</a>. Note that
Trino connects to the database using the JDBC driver and does not use the DuckDB
CLI.</p>
<section id="multiple-duckdb-servers">
<h3 id="multiple-duckdb-servers">Multiple DuckDB servers<a class="headerlink" href="duckdb.html#multiple-duckdb-servers" title="Link to this heading">#</a></h3>
<p>The DuckDB connector can only access a single database within
a DuckDB instance. Thus, if you have multiple DuckDB servers,
or want to connect to multiple DuckDB servers, you must configure
multiple instances of the DuckDB connector.</p>
</section>
</section>
<section id="type-mapping">
<span id="duckdb-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="duckdb.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and DuckDB each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<p>List of <a class="reference external" href="https://duckdb.org/docs/sql/data_types/overview.html">DuckDB data types</a>.</p>
<section id="duckdb-type-to-trino-type-mapping">
<h3 id="duckdb-type-to-trino-type-mapping">DuckDB type to Trino type mapping<a class="headerlink" href="duckdb.html#duckdb-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps DuckDB types to the corresponding Trino types following
this table:</p>
<table id="id1">
<caption><span class="caption-text">DuckDB type to Trino type mapping</span><a class="headerlink" href="duckdb.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>DuckDB type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></p></td>
<td><p>Default precision and scale are (18,3).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-type-to-duckdb-type-mapping">
<h3 id="trino-type-to-duckdb-type-mapping">Trino type to DuckDB type mapping<a class="headerlink" href="duckdb.html#trino-type-to-duckdb-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding DuckDB types following
this table:</p>
<table id="id2">
<caption><span class="caption-text">Trino type to DuckDB type mapping</span><a class="headerlink" href="duckdb.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>DuckDB type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">CHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="type-mapping-configuration-properties">
<h3 id="type-mapping-configuration-properties">Type mapping configuration properties<a class="headerlink" href="duckdb.html#type-mapping-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following properties can be used to configure how data types from the
connected data source are mapped to Trino data types and how the metadata is
cached in Trino.</p>
<table>
<colgroup>
<col style="width: 30%"/>
<col style="width: 40%"/>
<col style="width: 30%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">unsupported-type-handling</span></code></p></td>
<td><p>Configure how unsupported column data types are handled:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code>, column is not accessible.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CONVERT_TO_VARCHAR</span></code>, column is converted to unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>.</p></li>
</ul>
<p>The respective catalog session property is <code class="docutils literal notranslate"><span class="pre">unsupported_type_handling</span></code>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">jdbc-types-mapped-to-varchar</span></code></p></td>
<td><p>Allow forced mapping of comma separated lists of data types to convert to
unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sql-support">
<span id="duckdb-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="duckdb.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read access and write access to data and metadata in
a DuckDB database.  In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a>
statements, the connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/truncate.html"><span class="doc">TRUNCATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
</ul>
<section id="procedures">
<h3 id="procedures">Procedures<a class="headerlink" href="duckdb.html#procedures" title="Link to this heading">#</a></h3>
<section id="system-flush-metadata-cache">
<h4 id="system-flush-metadata-cache"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code><a class="headerlink" href="duckdb.html#system-flush-metadata-cache" title="Link to this heading">#</a></h4>
<p>Flush JDBC metadata caches. For example, the following system call
flushes the metadata caches for all schemas in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">flush_metadata_cache</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="system-execute-query">
<h4 id="system-execute-query"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code><a class="headerlink" href="duckdb.html#system-execute-query" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">execute</span></code> procedure allows you to execute a query in the underlying data
source directly. The query must use supported syntax of the connected data
source. Use the procedure to access features which are not available in Trino
or to execute queries that return no result set and therefore can not be used
with the <code class="docutils literal notranslate"><span class="pre">query</span></code> or <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> pass-through table function. Typical use cases
are statements that create or alter objects, and require native feature such
as constraints, default values, automatic identifier creation, or indexes.
Queries can also invoke statements that insert, update, or delete data, and do
not return any data as a result.</p>
<p>The query text is not parsed by Trino, only passed through, and therefore only
subject to any security or access control of the underlying data source.</p>
<p>The following example sets the current database to the <code class="docutils literal notranslate"><span class="pre">example_schema</span></code> of the
<code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Then it calls the procedure in that schema to drop the
default value from <code class="docutils literal notranslate"><span class="pre">your_column</span></code> on <code class="docutils literal notranslate"><span class="pre">your_table</span></code> table using the standard SQL
syntax in the parameter value assigned for <code class="docutils literal notranslate"><span class="pre">query</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="k">execute</span><span class="p">(</span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ALTER TABLE your_table ALTER COLUMN your_column DROP DEFAULT'</span><span class="p">);</span>
</pre></div>
</div>
<p>Verify that the specific database supports this syntax, and adapt as necessary
based on the documentation for the specific connected database and database
version.</p>
</section>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="duckdb.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc std std-doc">table functions</span></a> to
access DuckDB.</p>
<section id="query-varchar-table">
<span id="duckdb-query-function"></span><h4 id="query-varchar-table"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="duckdb.html#query-varchar-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">query</span></code> function allows you to query the underlying database directly. It
requires syntax native to DuckDB, because the full query is pushed down and
processed in DuckDB. This can be useful for accessing native features which
are not available in Trino or for improving query performance in situations
where running a query natively may be faster.</p>
<p>Find details about the SQL support of DuckDB that you can use in the query in
the <a class="reference external" href="https://duckdb.org/docs/sql/query_syntax/select">DuckDB SQL Command
Reference</a> and
other statements and functions.</p>
<p>The native query passed to the underlying data source is required to return a
table as a result set. Only the data source performs validation or security
checks for these queries using its own configuration. Trino does not perform
these tasks. Only use passthrough queries to read data.</p>
<p>As a simple example, query the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog and select an entire table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">query</span><span class="p">(</span>
<span class="w">      </span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'SELECT</span>
<span class="s1">        *</span>
<span class="s1">      FROM</span>
<span class="s1">        tpch.nation'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The query engine does not preserve the order of the results of this
function. If the passed query contains an <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause, the
function result may not be ordered as expected.</p>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="druid.html" title="Druid connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Druid connector </span>
              </div>
            </a>
          
          
            <a href="elasticsearch.html" title="Elasticsearch connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Elasticsearch connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>