<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Cassandra connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="cassandra.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="ClickHouse connector" href="clickhouse.html" />
    <link rel="prev" title="Black Hole connector" href="blackhole.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="cassandra.html#connector/cassandra" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Cassandra connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Cassandra </label>
    
      <a href="cassandra.html#" class="md-nav__link md-nav__link--active">Cassandra</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cassandra.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#multiple-cassandra-clusters" class="md-nav__link">Multiple Cassandra clusters</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#querying-cassandra-tables" class="md-nav__link">Querying Cassandra tables</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#cassandra-type-to-trino-type-mapping" class="md-nav__link">Cassandra type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#trino-type-to-cassandra-type-mapping" class="md-nav__link">Trino type to Cassandra type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#partition-key-types" class="md-nav__link">Partition key types</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#limitations" class="md-nav__link">Limitations</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#drop-table" class="md-nav__link">DROP TABLE</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#sql-delete-limitation" class="md-nav__link">SQL delete limitation</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cassandra.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#multiple-cassandra-clusters" class="md-nav__link">Multiple Cassandra clusters</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#querying-cassandra-tables" class="md-nav__link">Querying Cassandra tables</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#cassandra-type-to-trino-type-mapping" class="md-nav__link">Cassandra type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#trino-type-to-cassandra-type-mapping" class="md-nav__link">Trino type to Cassandra type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#partition-key-types" class="md-nav__link">Partition key types</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#limitations" class="md-nav__link">Limitations</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cassandra.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#drop-table" class="md-nav__link">DROP TABLE</a>
        </li>
        <li class="md-nav__item"><a href="cassandra.html#sql-delete-limitation" class="md-nav__link">SQL delete limitation</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="cassandra-connector">
<h1 id="connector-cassandra--page-root">Cassandra connector<a class="headerlink" href="cassandra.html#connector-cassandra--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/cassandra.png"/><p>The Cassandra connector allows querying data stored in
<a class="reference external" href="https://cassandra.apache.org/">Apache Cassandra</a>.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="cassandra.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to Cassandra, you need:</p>
<ul class="simple">
<li><p>Cassandra version 3.0 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to Cassandra.
Port 9042 is the default port.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="cassandra.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Cassandra connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following contents, replacing
<code class="docutils literal notranslate"><span class="pre">host1,host2</span></code> with a comma-separated list of the Cassandra nodes, used to
discovery the cluster topology:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=cassandra
cassandra.contact-points=host1,host2
cassandra.load-policy.dc-aware.local-dc=datacenter1
</pre></div>
</div>
<p>You also need to set <code class="docutils literal notranslate"><span class="pre">cassandra.native-protocol-port</span></code>, if your
Cassandra nodes are not using the default port 9042.</p>
<section id="multiple-cassandra-clusters">
<h3 id="multiple-cassandra-clusters">Multiple Cassandra clusters<a class="headerlink" href="cassandra.html#multiple-cassandra-clusters" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need, so if you have additional
Cassandra clusters, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name, making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>. For
example, if you name the property file <code class="docutils literal notranslate"><span class="pre">sales.properties</span></code>, Trino
creates a catalog named <code class="docutils literal notranslate"><span class="pre">sales</span></code> using the configured connector.</p>
</section>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="cassandra.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.contact-points</span></code></p></td>
<td><p>Comma-separated list of hosts in a Cassandra cluster. The Cassandra driver uses these contact points to discover cluster topology. At least one Cassandra host is required.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.native-protocol-port</span></code></p></td>
<td><p>The Cassandra server port running the native client protocol, defaults to <code class="docutils literal notranslate"><span class="pre">9042</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.consistency-level</span></code></p></td>
<td><p>Consistency levels in Cassandra refer to the level of consistency to be used for both read and write operations.  More information about consistency levels can be found in the <a class="reference external" href="https://docs.datastax.com/en/cassandra-oss/2.2/cassandra/dml/dmlConfigConsistency.html">Cassandra consistency</a> documentation. This property defaults to a consistency level of <code class="docutils literal notranslate"><span class="pre">ONE</span></code>. Possible values include <code class="docutils literal notranslate"><span class="pre">ALL</span></code>, <code class="docutils literal notranslate"><span class="pre">EACH_QUORUM</span></code>, <code class="docutils literal notranslate"><span class="pre">QUORUM</span></code>, <code class="docutils literal notranslate"><span class="pre">LOCAL_QUORUM</span></code>, <code class="docutils literal notranslate"><span class="pre">ONE</span></code>, <code class="docutils literal notranslate"><span class="pre">TWO</span></code>, <code class="docutils literal notranslate"><span class="pre">THREE</span></code>, <code class="docutils literal notranslate"><span class="pre">LOCAL_ONE</span></code>, <code class="docutils literal notranslate"><span class="pre">ANY</span></code>, <code class="docutils literal notranslate"><span class="pre">SERIAL</span></code>, <code class="docutils literal notranslate"><span class="pre">LOCAL_SERIAL</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.allow-drop-table</span></code></p></td>
<td><p>Enables <a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a> operations. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.security</span></code></p></td>
<td><p>Configure authentication to Cassandra. Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>. Set to <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> for basic authentication, and configure <code class="docutils literal notranslate"><span class="pre">cassandra.username</span></code> and <code class="docutils literal notranslate"><span class="pre">cassandra.password</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.username</span></code></p></td>
<td><p>Username used for authentication to the Cassandra cluster. Requires <code class="docutils literal notranslate"><span class="pre">cassandra.security=PASSWORD</span></code>. This is a global setting used for all connections, regardless of the user connected to Trino.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.password</span></code></p></td>
<td><p>Password used for authentication to the Cassandra cluster. Requires <code class="docutils literal notranslate"><span class="pre">cassandra.security=PASSWORD</span></code>. This is a global setting used for all connections, regardless of the user connected to Trino.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.protocol-version</span></code></p></td>
<td><p>It is possible to override the protocol version for older Cassandra clusters. By default, the value corresponds to the default protocol version used in the underlying Cassandra java driver. Possible values include <code class="docutils literal notranslate"><span class="pre">V3</span></code>, <code class="docutils literal notranslate"><span class="pre">V4</span></code>, <code class="docutils literal notranslate"><span class="pre">V5</span></code>, <code class="docutils literal notranslate"><span class="pre">V6</span></code>.</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If authorization is enabled, <code class="docutils literal notranslate"><span class="pre">cassandra.username</span></code> must have enough permissions to perform <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries on
the <code class="docutils literal notranslate"><span class="pre">system.size_estimates</span></code> table.</p>
</div>
<p>The following advanced configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.fetch-size</span></code></p></td>
<td><p>Number of rows fetched at a time in a Cassandra query.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.partition-size-for-batch-select</span></code></p></td>
<td><p>Number of partitions batched together into a single select for a single partition key column table.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.split-size</span></code></p></td>
<td><p>Number of keys per split when querying Cassandra.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.splits-per-node</span></code></p></td>
<td><p>Number of splits per node. By default, the values from the <code class="docutils literal notranslate"><span class="pre">system.size_estimates</span></code> table are used. Only override when connecting to Cassandra versions &lt; 2.1.5, which lacks the <code class="docutils literal notranslate"><span class="pre">system.size_estimates</span></code> table.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.batch-size</span></code></p></td>
<td><p>Maximum number of statements to execute in one batch.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.client.read-timeout</span></code></p></td>
<td><p>Maximum time the Cassandra driver waits for an answer to a query from one Cassandra node. Note that the underlying Cassandra driver may retry a query against more than one node in the event of a read timeout. Increasing this may help with queries that use an index.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.client.connect-timeout</span></code></p></td>
<td><p>Maximum time the Cassandra driver waits to establish a connection to a Cassandra node. Increasing this may help with heavily loaded Cassandra clusters.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.client.so-linger</span></code></p></td>
<td><p>Number of seconds to linger on close if unsent data is queued. If set to zero, the socket will be closed immediately. When this option is non-zero, a socket lingers that many seconds for an acknowledgement that all data was written to a peer. This option can be used to avoid consuming sockets on a Cassandra server by immediately closing connections when they are no longer needed.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.retry-policy</span></code></p></td>
<td><p>Policy used to retry failed requests to Cassandra. This property defaults to <code class="docutils literal notranslate"><span class="pre">DEFAULT</span></code>. Using <code class="docutils literal notranslate"><span class="pre">BACKOFF</span></code> may help when queries fail with <em>“not enough replicas”</em>. The other possible values are <code class="docutils literal notranslate"><span class="pre">DOWNGRADING_CONSISTENCY</span></code> and <code class="docutils literal notranslate"><span class="pre">FALLTHROUGH</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.load-policy.use-dc-aware</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> if the load balancing policy requires a local datacenter, defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.load-policy.dc-aware.local-dc</span></code></p></td>
<td><p>The name of the datacenter considered “local”.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.load-policy.dc-aware.used-hosts-per-remote-dc</span></code></p></td>
<td><p>Uses the provided number of host per remote datacenter as failover for the local hosts for <code class="docutils literal notranslate"><span class="pre">DefaultLoadBalancingPolicy</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.load-policy.dc-aware.allow-remote-dc-for-local</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to allow to use hosts of remote datacenter for local consistency level.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.no-host-available-retry-timeout</span></code></p></td>
<td><p>Retry timeout for <code class="docutils literal notranslate"><span class="pre">AllNodesFailedException</span></code>, defaults to <code class="docutils literal notranslate"><span class="pre">1m</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.speculative-execution.limit</span></code></p></td>
<td><p>The number of speculative executions. This is disabled by default.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.speculative-execution.delay</span></code></p></td>
<td><p>The delay between each speculative execution, defaults to <code class="docutils literal notranslate"><span class="pre">500ms</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.tls.enabled</span></code></p></td>
<td><p>Whether TLS security is enabled, defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.tls.keystore-path</span></code></p></td>
<td><p>Path to the <a class="reference internal" href="../security/inspect-pem.html"><span class="doc">PEM</span></a> or <a class="reference internal" href="../security/inspect-jks.html"><span class="doc">JKS</span></a> key store file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.tls.truststore-path</span></code></p></td>
<td><p>Path to the <a class="reference internal" href="../security/inspect-pem.html"><span class="doc">PEM</span></a> or <a class="reference internal" href="../security/inspect-jks.html"><span class="doc">JKS</span></a> trust store file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.tls.keystore-password</span></code></p></td>
<td><p>Password for the key store.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cassandra.tls.truststore-password</span></code></p></td>
<td><p>Password for the trust store.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="querying-cassandra-tables">
<h2 id="querying-cassandra-tables">Querying Cassandra tables<a class="headerlink" href="cassandra.html#querying-cassandra-tables" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">users</span></code> table is an example Cassandra table from the Cassandra
<a class="reference external" href="https://cassandra.apache.org/doc/latest/cassandra/getting_started/index.html">Getting Started</a> guide. It can be created along with the <code class="docutils literal notranslate"><span class="pre">example_keyspace</span></code>
keyspace using Cassandra’s cqlsh (CQL interactive terminal):</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>cqlsh&gt; CREATE KEYSPACE example_keyspace
   ... WITH REPLICATION = { 'class' : 'SimpleStrategy', 'replication_factor' : 1 };
cqlsh&gt; USE example_keyspace;
cqlsh:example_keyspace&gt; CREATE TABLE users (
              ...   user_id int PRIMARY KEY,
              ...   fname text,
              ...   lname text
              ... );
</pre></div>
</div>
<p>This table can be described in Trino:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">DESCRIBE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_keyspace</span><span class="p">.</span><span class="n">users</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> Column  |  Type   | Extra | Comment
---------+---------+-------+---------
 user_id | bigint  |       |
 fname   | varchar |       |
 lname   | varchar |       |
(3 rows)
</pre></div>
</div>
<p>This table can then be queried in Trino:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_keyspace</span><span class="p">.</span><span class="n">users</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="type-mapping">
<span id="cassandra-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="cassandra.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Cassandra each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<section id="cassandra-type-to-trino-type-mapping">
<h3 id="cassandra-type-to-trino-type-mapping">Cassandra type to Trino type mapping<a class="headerlink" href="cassandra.html#cassandra-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Cassandra types to the corresponding Trino types according to
the following table:</p>
<table id="id2">
<caption><span class="caption-text">Cassandra type to Trino type mapping</span><a class="headerlink" href="cassandra.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 29%"/>
<col style="width: 24%"/>
<col style="width: 48%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Cassandra type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ASCII</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>US-ASCII character string</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>UTF-8 encoded string</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>UTF-8 encoded string</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>Arbitrary-precision integer</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BLOB</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(9)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LIST&lt;?&gt;</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAP&lt;?,</span> <span class="pre">?&gt;</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SET&lt;?&gt;</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TUPLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code> with anonymous fields</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">UDT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code> with field names</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INET</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMEUUID</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-type-to-cassandra-type-mapping">
<h3 id="trino-type-to-cassandra-type-mapping">Trino type to Cassandra type mapping<a class="headerlink" href="cassandra.html#trino-type-to-cassandra-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding Cassandra types according to
the following table:</p>
<table id="id3">
<caption><span class="caption-text">Trino type to Cassandra type mapping</span><a class="headerlink" href="cassandra.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 29%"/>
<col style="width: 24%"/>
<col style="width: 48%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>Cassandra type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INET</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="partition-key-types">
<h2 id="partition-key-types">Partition key types<a class="headerlink" href="cassandra.html#partition-key-types" title="Link to this heading">#</a></h2>
<p>Partition keys can only be of the following types:</p>
<ul class="simple">
<li><p>ASCII</p></li>
<li><p>TEXT</p></li>
<li><p>VARCHAR</p></li>
<li><p>BIGINT</p></li>
<li><p>BOOLEAN</p></li>
<li><p>DOUBLE</p></li>
<li><p>INET</p></li>
<li><p>INT</p></li>
<li><p>FLOAT</p></li>
<li><p>DECIMAL</p></li>
<li><p>TIMESTAMP</p></li>
<li><p>UUID</p></li>
<li><p>TIMEUUID</p></li>
</ul>
</section>
<section id="limitations">
<h2 id="limitations">Limitations<a class="headerlink" href="cassandra.html#limitations" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>Queries without filters containing the partition key result in fetching all partitions.
This causes a full scan of the entire data set, and is therefore much slower compared to a similar
query with a partition key as a filter.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">IN</span></code> list filters are only allowed on index (that is, partition key or clustering key) columns.</p></li>
<li><p>Range (<code class="docutils literal notranslate"><span class="pre">&lt;</span></code> or <code class="docutils literal notranslate"><span class="pre">&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">BETWEEN</span></code>) filters can be applied only to the partition keys.</p></li>
</ul>
</section>
<section id="sql-support">
<span id="cassandra-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="cassandra.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read and write access to data and metadata in
the Cassandra database. In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a>
statements, the connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a> see <a class="reference internal" href="cassandra.html#sql-delete-limitation"><span class="std std-ref">SQL delete limitation</span></a></p></li>
<li><p><a class="reference internal" href="../sql/truncate.html"><span class="doc">TRUNCATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
</ul>
<section id="procedures">
<h3 id="procedures">Procedures<a class="headerlink" href="cassandra.html#procedures" title="Link to this heading">#</a></h3>
<section id="system-execute-query">
<h4 id="system-execute-query"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code><a class="headerlink" href="cassandra.html#system-execute-query" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">execute</span></code> procedure allows you to execute a query in the underlying data
source directly. The query must use supported syntax of the connected data
source. Use the procedure to access features which are not available in Trino
or to execute queries that return no result set and therefore can not be used
with the <code class="docutils literal notranslate"><span class="pre">query</span></code> or <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> pass-through table function. Typical use cases
are statements that create or alter objects, and require native feature such
as constraints, default values, automatic identifier creation, or indexes.
Queries can also invoke statements that insert, update, or delete data, and do
not return any data as a result.</p>
<p>The query text is not parsed by Trino, only passed through, and therefore only
subject to any security or access control of the underlying data source.</p>
<p>The following example sets the current database to the <code class="docutils literal notranslate"><span class="pre">example_schema</span></code> of the
<code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Then it calls the procedure in that schema to drop the
default value from <code class="docutils literal notranslate"><span class="pre">your_column</span></code> on <code class="docutils literal notranslate"><span class="pre">your_table</span></code> table using the standard SQL
syntax in the parameter value assigned for <code class="docutils literal notranslate"><span class="pre">query</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="k">execute</span><span class="p">(</span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ALTER TABLE your_table ALTER COLUMN your_column DROP DEFAULT'</span><span class="p">);</span>
</pre></div>
</div>
<p>Verify that the specific database supports this syntax, and adapt as necessary
based on the documentation for the specific connected database and database
version.</p>
</section>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="cassandra.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc">table functions</span></a> to
access Cassandra.
.. _cassandra-query-function:</p>
<section id="query-varchar-table">
<h4 id="query-varchar-table"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="cassandra.html#query-varchar-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">query</span></code> function allows you to query the underlying Cassandra directly. It
requires syntax native to Cassandra, because the full query is pushed down and
processed by Cassandra. This can be useful for accessing native features which are
not available in Trino or for improving query performance in situations where
running a query natively may be faster.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The query engine does not preserve the order of the results of this
function. If the passed query contains an <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause, the
function result may not be ordered as expected.</p>
</div>
<p>As a simple example, to select an entire table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">query</span><span class="p">(</span>
<span class="w">      </span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'SELECT</span>
<span class="s1">        *</span>
<span class="s1">      FROM</span>
<span class="s1">        tpch.nation'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
<section id="drop-table">
<h3 id="drop-table">DROP TABLE<a class="headerlink" href="cassandra.html#drop-table" title="Link to this heading">#</a></h3>
<p>By default, <code class="docutils literal notranslate"><span class="pre">DROP</span> <span class="pre">TABLE</span></code> operations are disabled on Cassandra catalogs. To
enable <code class="docutils literal notranslate"><span class="pre">DROP</span> <span class="pre">TABLE</span></code>, set the <code class="docutils literal notranslate"><span class="pre">cassandra.allow-drop-table</span></code> catalog
configuration property to <code class="docutils literal notranslate"><span class="pre">true</span></code>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">cassandra.allow-drop-table</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
</section>
<section id="sql-delete-limitation">
<span id="id1"></span><h3 id="sql-delete-limitation">SQL delete limitation<a class="headerlink" href="cassandra.html#sql-delete-limitation" title="Link to this heading">#</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">DELETE</span></code> is only supported if the <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause matches entire partitions.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="blackhole.html" title="Black Hole connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Black Hole connector </span>
              </div>
            </a>
          
          
            <a href="clickhouse.html" title="ClickHouse connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> ClickHouse connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>