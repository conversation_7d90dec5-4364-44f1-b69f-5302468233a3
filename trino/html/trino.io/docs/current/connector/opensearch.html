<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>OpenSearch connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="opensearch.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Oracle connector" href="oracle.html" />
    <link rel="prev" title="MySQL connector" href="mysql.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="opensearch.html#connector/opensearch" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> OpenSearch connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> OpenSearch </label>
    
      <a href="opensearch.html#" class="md-nav__link md-nav__link--active">OpenSearch</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="opensearch.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#authentication" class="md-nav__link">Authentication</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#connection-security-with-tls" class="md-nav__link">Connection security with TLS</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#opensearch-type-to-trino-type-mapping" class="md-nav__link">OpenSearch type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#array-types" class="md-nav__link">Array types</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#date-types" class="md-nav__link">Date types</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#raw-json-transform" class="md-nav__link">Raw JSON transform</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#special-columns" class="md-nav__link">Special columns</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#wildcard-table" class="md-nav__link">Wildcard table</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#raw-query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">raw_query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#parallel-data-access" class="md-nav__link">Parallel data access</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#predicate-push-down" class="md-nav__link">Predicate push down</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="opensearch.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#authentication" class="md-nav__link">Authentication</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#connection-security-with-tls" class="md-nav__link">Connection security with TLS</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#opensearch-type-to-trino-type-mapping" class="md-nav__link">OpenSearch type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#array-types" class="md-nav__link">Array types</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#date-types" class="md-nav__link">Date types</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#raw-json-transform" class="md-nav__link">Raw JSON transform</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#special-columns" class="md-nav__link">Special columns</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#wildcard-table" class="md-nav__link">Wildcard table</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#raw-query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">raw_query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="opensearch.html#parallel-data-access" class="md-nav__link">Parallel data access</a>
        </li>
        <li class="md-nav__item"><a href="opensearch.html#predicate-push-down" class="md-nav__link">Predicate push down</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="opensearch-connector">
<h1 id="connector-opensearch--page-root">OpenSearch connector<a class="headerlink" href="opensearch.html#connector-opensearch--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/opensearch.png"/><p>The OpenSearch connector allows access to <a class="reference external" href="https://opensearch.org/">OpenSearch</a>
data from Trino. This document describes how to configure a catalog with the
OpenSearch connector to run SQL queries against OpenSearch.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="opensearch.html#requirements" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>OpenSearch 1.1.0 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to the OpenSearch nodes.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="opensearch.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the OpenSearch connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following content, replacing the
properties as appropriate for your setup:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=opensearch
opensearch.host=search.example.com
opensearch.port=9200
opensearch.default-schema-name=default
</pre></div>
</div>
<p>The following table details all general configuration properties:</p>
<table id="id1">
<caption><span class="caption-text">OpenSearch configuration properties</span><a class="headerlink" href="opensearch.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 35%"/>
<col style="width: 55%"/>
<col style="width: 10%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.host</span></code></p></td>
<td><p>The comma-separated list of host names of the OpenSearch cluster. This
property is required.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.port</span></code></p></td>
<td><p>Port to use to connect to OpenSearch.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">9200</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.default-schema-name</span></code></p></td>
<td><p>The schema that contains all tables defined without a qualifying schema
name.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">default</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.scroll-size</span></code></p></td>
<td><p>Sets the maximum number of hits that can be returned with each <a class="reference external" href="https://opensearch.org/docs/latest/api-reference/scroll/">OpenSearch
scroll request</a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.scroll-timeout</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for OpenSearch to keep the search context
alive for scroll requests.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1m</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.request-timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for all OpenSearch requests.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.connect-timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for all OpenSearch connection
attempts.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.backoff-init-delay</span></code></p></td>
<td><p>The minimum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> between backpressure retry
attempts for a single request to OpenSearch. Setting it too low can
overwhelm an already struggling cluster.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">500ms</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.backoff-max-delay</span></code></p></td>
<td><p>The maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> between backpressure retry
attempts for a single request.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">20s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.max-retry-time</span></code></p></td>
<td><p>The maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> across all retry attempts for a
single request.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">30s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.node-refresh-interval</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> between requests to refresh the list of
available OpenSearch nodes.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1m</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.ignore-publish-address</span></code></p></td>
<td><p>Disable using the address published by the OpenSearch API to connect for
queries. Some deployments map OpenSearch ports to a random public port and
enabling this property can help in these cases.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.projection-pushdown-enabled</span></code></p></td>
<td><p>Read only projected fields from row columns while performing <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
<section id="authentication">
<h3 id="authentication">Authentication<a class="headerlink" href="opensearch.html#authentication" title="Link to this heading">#</a></h3>
<p>The connection to OpenSearch can use AWS or password authentication.</p>
<p>To enable AWS authentication and authorization using IAM policies, the
<code class="docutils literal notranslate"><span class="pre">opensearch.security</span></code> option must be set to <code class="docutils literal notranslate"><span class="pre">AWS</span></code>. Additionally, the
following options must be configured:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.region</span></code></p></td>
<td><p>AWS region of the OpenSearch endpoint. This option is required.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.access-key</span></code></p></td>
<td><p>AWS access key to use to connect to the OpenSearch domain. If not set, the
default AWS credentials provider chain is used.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.secret-key</span></code></p></td>
<td><p>AWS secret key to use to connect to the OpenSearch domain. If not set, the
default AWS credentials provider chain is used.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.iam-role</span></code></p></td>
<td><p>Optional ARN of an IAM role to assume to connect to OpenSearch. Note that
the configured IAM user must be able to assume this role.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.external-id</span></code></p></td>
<td><p>Optional external ID to pass while assuming an AWS IAM role.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.aws.deployment-type</span></code></p></td>
<td><p>AWS OpenSearch deployment type. Possible values are <code class="docutils literal notranslate"><span class="pre">PROVISIONED</span></code> &amp; <code class="docutils literal notranslate"><span class="pre">SERVERLESS</span></code>. This option is required.</p></td>
</tr>
</tbody>
</table>
<p>To enable password authentication, the <code class="docutils literal notranslate"><span class="pre">opensearch.security</span></code> option must be set
to <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code>. Additionally the following options must be configured:</p>
<table>
<colgroup>
<col style="width: 45%"/>
<col style="width: 55%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.auth.user</span></code></p></td>
<td><p>Username to use to connect to OpenSearch.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.auth.password</span></code></p></td>
<td><p>Password to use to connect to OpenSearch.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="connection-security-with-tls">
<h3 id="connection-security-with-tls">Connection security with TLS<a class="headerlink" href="opensearch.html#connection-security-with-tls" title="Link to this heading">#</a></h3>
<p>The connector provides additional security options to connect to OpenSearch
clusters with TLS enabled.</p>
<p>If your cluster uses globally-trusted certificates, you only need to
enable TLS. If you require custom configuration for certificates, the connector
supports key stores and trust stores in P12 (PKCS) or Java Key Store (JKS) format.</p>
<p>The available configuration values are listed in the following table:</p>
<table id="id2">
<caption><span class="caption-text">TLS configuration properties</span><a class="headerlink" href="opensearch.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.enabled</span></code></p></td>
<td><p>Enable TLS security. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.keystore-path</span></code></p></td>
<td><p>The path to the P12 (PKCS) or <a class="reference internal" href="../security/inspect-jks.html"><span class="doc std std-doc">JKS</span></a>
key store.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.truststore-path</span></code></p></td>
<td><p>The path to P12 (PKCS) or <a class="reference internal" href="../security/inspect-jks.html"><span class="doc std std-doc">JKS</span></a>
trust store.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.keystore-password</span></code></p></td>
<td><p>The password for the key store specified by
<code class="docutils literal notranslate"><span class="pre">opensearch.tls.keystore-path</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.truststore-password</span></code></p></td>
<td><p>The password for the trust store specified by
<code class="docutils literal notranslate"><span class="pre">opensearch.tls.truststore-path</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">opensearch.tls.verify-hostnames</span></code></p></td>
<td><p>Flag to determine if the hostnames in the certificates must be verified.
Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="type-mapping">
<span id="opensearch-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="opensearch.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and OpenSearch each support types that the other does not, the
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">maps some types</span></a> when reading data.</p>
<section id="opensearch-type-to-trino-type-mapping">
<h3 id="opensearch-type-to-trino-type-mapping">OpenSearch type to Trino type mapping<a class="headerlink" href="opensearch.html#opensearch-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps OpenSearch types to the corresponding Trino types
according to the following table:</p>
<table id="id3">
<caption><span class="caption-text">OpenSearch type to Trino type mapping</span><a class="headerlink" href="opensearch.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 27%"/>
<col style="width: 27%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>OpenSearch type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">KEYWORD</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p>For more information, see <a class="reference internal" href="opensearch.html#opensearch-date-types"><span class="std std-ref">Date types</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">IPADDRESS</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">IP</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="array-types">
<span id="opensearch-array-types"></span><h3 id="array-types">Array types<a class="headerlink" href="opensearch.html#array-types" title="Link to this heading">#</a></h3>
<p>Fields in OpenSearch can contain <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/date/#custom-formats">zero or more
values</a>,
but there is no dedicated array type. To indicate a field contains an array, it
can be annotated in a Trino-specific structure in the
<a class="reference external" href="https://opensearch.org/docs/latest/field-types/index/#get-a-mapping">_meta</a>
section of the index mapping in OpenSearch.</p>
<p>For example, you can have an OpenSearch index that contains documents with the
following structure:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"array_string_field"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"trino"</span><span class="p">,</span><span class="s2">"the"</span><span class="p">,</span><span class="s2">"lean"</span><span class="p">,</span><span class="s2">"machine-ohs"</span><span class="p">],</span>
<span class="w">    </span><span class="nt">"long_field"</span><span class="p">:</span><span class="w"> </span><span class="mi">314159265359</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"id_field"</span><span class="p">:</span><span class="w"> </span><span class="s2">"564e6982-88ee-4498-aa98-df9e3f6b6109"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"timestamp_field"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1987-09-17T06:22:48.000Z"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"object_field"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"array_int_field"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">86</span><span class="p">,</span><span class="mi">75</span><span class="p">,</span><span class="mi">309</span><span class="p">],</span>
<span class="w">        </span><span class="nt">"int_field"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The array fields of this structure can be defined by using the following command
to add the field property definition to the <code class="docutils literal notranslate"><span class="pre">_meta.trino</span></code> property of the target
index mapping with OpenSearch available at <code class="docutils literal notranslate"><span class="pre">search.example.com:9200</span></code>:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>--request<span class="w"> </span>PUT<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--url<span class="w"> </span>search.example.com:9200/doc/_mapping<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--header<span class="w"> </span><span class="s1">'content-type: application/json'</span><span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--data<span class="w"> </span><span class="s1">'</span>
<span class="s1">{</span>
<span class="s1">    "_meta": {</span>
<span class="s1">        "trino":{</span>
<span class="s1">            "array_string_field":{</span>
<span class="s1">                "isArray":true</span>
<span class="s1">            },</span>
<span class="s1">            "object_field":{</span>
<span class="s1">                "array_int_field":{</span>
<span class="s1">                    "isArray":true</span>
<span class="s1">                }</span>
<span class="s1">            },</span>
<span class="s1">        }</span>
<span class="s1">    }</span>
<span class="s1">}'</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It is not allowed to use <code class="docutils literal notranslate"><span class="pre">asRawJson</span></code> and <code class="docutils literal notranslate"><span class="pre">isArray</span></code> flags simultaneously for the same column.</p>
</div>
</section>
<section id="date-types">
<span id="opensearch-date-types"></span><h3 id="date-types">Date types<a class="headerlink" href="opensearch.html#date-types" title="Link to this heading">#</a></h3>
<p>The OpenSearch connector supports only the default <code class="docutils literal notranslate"><span class="pre">date</span></code> type. All other
OpenSearch <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/date/">date</a> formats including <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/date/#custom-formats">built-in date formats</a> and <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/date/#custom-formats">custom date
formats</a> are not supported. Dates with the <a class="reference external" href="https://opensearch.org/docs/latest/query-dsl/term/range/#format">format</a> property are ignored.</p>
</section>
<section id="raw-json-transform">
<h3 id="raw-json-transform">Raw JSON transform<a class="headerlink" href="opensearch.html#raw-json-transform" title="Link to this heading">#</a></h3>
<p>Documents in OpenSearch can include more complex structures that are not
represented in the mapping. For example, a single <code class="docutils literal notranslate"><span class="pre">keyword</span></code> field can have
widely different content including a single <code class="docutils literal notranslate"><span class="pre">keyword</span></code> value, an array, or a
multidimensional <code class="docutils literal notranslate"><span class="pre">keyword</span></code> array with any level of nesting.</p>
<p>The following command configures <code class="docutils literal notranslate"><span class="pre">array_string_field</span></code> mapping with OpenSearch
available at <code class="docutils literal notranslate"><span class="pre">search.example.com:9200</span></code>:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>--request<span class="w"> </span>PUT<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--url<span class="w"> </span>search.example.com:9200/doc/_mapping<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--header<span class="w"> </span><span class="s1">'content-type: application/json'</span><span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--data<span class="w"> </span><span class="s1">'</span>
<span class="s1">{</span>
<span class="s1">    "properties": {</span>
<span class="s1">        "array_string_field":{</span>
<span class="s1">            "type": "keyword"</span>
<span class="s1">        }</span>
<span class="s1">    }</span>
<span class="s1">}'</span>
</pre></div>
</div>
<p>All the following documents are legal for OpenSearch with <code class="docutils literal notranslate"><span class="pre">array_string_field</span></code>
mapping:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"array_string_field"</span><span class="p">:</span><span class="w"> </span><span class="s2">"trino"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"array_string_field"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"trino"</span><span class="p">,</span><span class="s2">"is"</span><span class="p">,</span><span class="s2">"the"</span><span class="p">,</span><span class="s2">"best"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"array_string_field"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"trino"</span><span class="p">,[</span><span class="s2">"is"</span><span class="p">,</span><span class="s2">"the"</span><span class="p">,</span><span class="s2">"best"</span><span class="p">]]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"array_string_field"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"trino"</span><span class="p">,[</span><span class="s2">"is"</span><span class="p">,[</span><span class="s2">"the"</span><span class="p">,</span><span class="s2">"best"</span><span class="p">]]]</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
<p>See the <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/index/#arrays">OpenSearch array
documentation</a>
for more details.</p>
<p>Further, OpenSearch supports types, such as <a class="reference external" href="https://opensearch.org/docs/latest/field-types/supported-field-types/knn-vector/">k-NN
vector</a>,
that are not supported in Trino. These and other types can cause parsing
exceptions for users that use of these types in OpenSearch. To manage all of
these scenarios, you can transform fields to raw JSON by annotating it in a
Trino-specific structure in the
<a class="reference external" href="https://opensearch.org/docs/latest/field-types/index/">_meta</a> section of the
OpenSearch index mapping. This indicates to Trino that the field, and all nested
fields beneath, must be cast to a <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> field that contains the raw JSON
content. These fields can be defined by using the following command to add the
field property definition to the <code class="docutils literal notranslate"><span class="pre">_meta.trino</span></code> property of the target index
mapping.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>--request<span class="w"> </span>PUT<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--url<span class="w"> </span>search.example.com:9200/doc/_mapping<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--header<span class="w"> </span><span class="s1">'content-type: application/json'</span><span class="w"> </span><span class="se">\</span>
<span class="w">    </span>--data<span class="w"> </span><span class="s1">'</span>
<span class="s1">{</span>
<span class="s1">    "_meta": {</span>
<span class="s1">      "trino":{</span>
<span class="s1">        "array_string_field":{</span>
<span class="s1">            "asRawJson":true</span>
<span class="s1">        }</span>
<span class="s1">      }</span>
<span class="s1">    }</span>
<span class="s1">}'</span>
</pre></div>
</div>
<p>The preceding configuration causes Trino to return the <code class="docutils literal notranslate"><span class="pre">array_string_field</span></code>
field as a <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> containing raw JSON. You can parse these fields with the
<a class="reference internal" href="../functions/json.html"><span class="doc std std-doc">built-in JSON functions</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It is not allowed to use <code class="docutils literal notranslate"><span class="pre">asRawJson</span></code> and <code class="docutils literal notranslate"><span class="pre">isArray</span></code> flags simultaneously for the same column.</p>
</div>
</section>
</section>
<section id="special-columns">
<h2 id="special-columns">Special columns<a class="headerlink" href="opensearch.html#special-columns" title="Link to this heading">#</a></h2>
<p>The following hidden columns are available:</p>
<table>
<colgroup>
<col style="width: 25%"/>
<col style="width: 75%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_id</span></code></p></td>
<td><p>The OpenSearch document ID.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_score</span></code></p></td>
<td><p>The document score returned by the OpenSearch query.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_source</span></code></p></td>
<td><p>The source of the original document.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="sql-support">
<span id="opensearch-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="opensearch.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements to access data and
metadata in the OpenSearch catalog.</p>
<section id="wildcard-table">
<h3 id="wildcard-table">Wildcard table<a class="headerlink" href="opensearch.html#wildcard-table" title="Link to this heading">#</a></h3>
<p>The connector provides support to query multiple tables using a concise
<a class="reference external" href="https://opensearch.org/docs/latest/api-reference/multi-search/#metadata-only-options">wildcard table</a>
notation.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="ss">"page_views_*"</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="opensearch.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc std std-doc">table functions</span></a> to
access OpenSearch.</p>
<section id="raw-query-varchar-table">
<span id="opensearch-raw-query-function"></span><h4 id="raw-query-varchar-table"><code class="docutils literal notranslate"><span class="pre">raw_query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="opensearch.html#raw-query-varchar-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> function allows you to query the underlying database directly
using the <a class="reference external" href="https://opensearch.org/docs/latest/query-dsl/index/">OpenSearch Query
DSL</a> syntax. The full DSL
query is pushed down and processed in OpenSearch. This can be useful for
accessing native features which are not available in Trino, or for improving
query performance in situations where running a query natively may be faster.</p>
<p>The native query passed to the underlying data source is required to return a
table as a result set. Only the data source performs validation or security
checks for these queries using its own configuration. Trino does not perform
these tasks. Only use passthrough queries to read data.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> function requires three parameters:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code>: The schema in the catalog that the query is to be executed on.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">index</span></code>: The index in OpenSearch to search.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">query</span></code>: The query to execute, written in <a class="reference external" href="https://opensearch.org/docs/latest/query-dsl">OpenSearch Query DSL</a>.</p></li>
</ul>
<p>Once executed, the query returns a single row containing the resulting JSON
payload returned by OpenSearch.</p>
<p>For example, query the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog and use the <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> table function
to search for documents in the <code class="docutils literal notranslate"><span class="pre">orders</span></code> index where the country name is
<code class="docutils literal notranslate"><span class="pre">ALGERIA</span></code> as defined as a JSON-formatted query matcher and passed to the
<code class="docutils literal notranslate"><span class="pre">raw_query</span></code> table function in the <code class="docutils literal notranslate"><span class="pre">query</span></code> parameter:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">raw_query</span><span class="p">(</span>
<span class="w">      </span><span class="k">schema</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'sales'</span><span class="p">,</span>
<span class="w">      </span><span class="k">index</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'orders'</span><span class="p">,</span>
<span class="w">      </span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'{</span>
<span class="s1">        "query": {</span>
<span class="s1">          "match": {</span>
<span class="s1">            "name": "ALGERIA"</span>
<span class="s1">          }</span>
<span class="s1">        }</span>
<span class="s1">      }'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The query engine does not preserve the order of the results of this
function. If the passed query contains an <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause, the
function result may not be ordered as expected.</p>
</div>
</section>
</section>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="opensearch.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements, detailed in the
following sections.</p>
<section id="parallel-data-access">
<h3 id="parallel-data-access">Parallel data access<a class="headerlink" href="opensearch.html#parallel-data-access" title="Link to this heading">#</a></h3>
<p>The connector requests data from multiple nodes of the OpenSearch cluster for
query processing in parallel.</p>
</section>
<section id="predicate-push-down">
<h3 id="predicate-push-down">Predicate push down<a class="headerlink" href="opensearch.html#predicate-push-down" title="Link to this heading">#</a></h3>
<p>The connector supports <a class="reference internal" href="../optimizer/pushdown.html#predicate-pushdown"><span class="std std-ref">predicate push down</span></a> for the
following data types:</p>
<table>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>OpenSearch</p></th>
<th class="head"><p>Trino</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">boolean</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">double</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">float</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">byte</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">short</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">integer</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">long</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">keyword</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">date</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other data types are supported for predicate push down.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="mysql.html" title="MySQL connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> MySQL connector </span>
              </div>
            </a>
          
          
            <a href="oracle.html" title="Oracle connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Oracle connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>