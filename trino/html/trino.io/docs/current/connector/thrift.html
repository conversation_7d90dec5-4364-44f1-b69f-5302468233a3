<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Thrift connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="thrift.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="TPC-DS connector" href="tpcds.html" />
    <link rel="prev" title="System connector" href="system.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="thrift.html#connector/thrift" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Thrift connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Thrift </label>
    
      <a href="thrift.html#" class="md-nav__link md-nav__link--active">Thrift</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="thrift.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="thrift.html#multiple-thrift-systems" class="md-nav__link">Multiple Thrift systems</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="thrift.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-client-addresses" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.addresses</span></code></a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-max-response-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino-thrift.max-response-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-metadata-refresh-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino-thrift.metadata-refresh-threads</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trinothriftservice-implementation" class="md-nav__link">TrinoThriftService implementation</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#type-mapping" class="md-nav__link">Type mapping</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="thrift.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="thrift.html#multiple-thrift-systems" class="md-nav__link">Multiple Thrift systems</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="thrift.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-client-addresses" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.addresses</span></code></a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-max-response-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino-thrift.max-response-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trino-thrift-metadata-refresh-threads" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">trino-thrift.metadata-refresh-threads</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="thrift.html#trinothriftservice-implementation" class="md-nav__link">TrinoThriftService implementation</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#type-mapping" class="md-nav__link">Type mapping</a>
        </li>
        <li class="md-nav__item"><a href="thrift.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="thrift-connector">
<h1 id="connector-thrift--page-root">Thrift connector<a class="headerlink" href="thrift.html#connector-thrift--page-root" title="Link to this heading">#</a></h1>
<p>The Thrift connector makes it possible to integrate with external storage systems
without a custom Trino connector implementation by using
<a class="reference external" href="https://thrift.apache.org/">Apache Thrift</a> on these servers. It is therefore
generic and can provide access to any backend, as long as it exposes the expected
API by using Thrift.</p>
<p>In order to use the Thrift connector with an external system, you need to implement
the <code class="docutils literal notranslate"><span class="pre">TrinoThriftService</span></code> interface, found below. Next, you configure the Thrift connector
to point to a set of machines, called Thrift servers, that implement the interface.
As part of the interface implementation, the Thrift servers provide metadata,
splits and data. The connector randomly chooses a server to talk to from the available
instances for metadata calls, or for data calls unless the splits include a list of addresses.
All requests are assumed to be idempotent and can be retried freely among any server.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="thrift.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to your custom servers with the Thrift protocol, you need:</p>
<ul class="simple">
<li><p>Network access from the Trino coordinator and workers to the Thrift servers.</p></li>
<li><p>A <a class="reference internal" href="thrift.html#trino-thrift-service"><span class="std std-ref">TrinoThriftService implementation</span></a> for your system.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="thrift.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Thrift connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following content, replacing the
properties as appropriate:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=trino_thrift
trino.thrift.client.addresses=host:port,host:port
</pre></div>
</div>
<section id="multiple-thrift-systems">
<h3 id="multiple-thrift-systems">Multiple Thrift systems<a class="headerlink" href="thrift.html#multiple-thrift-systems" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need, so if you have additional
Thrift systems to connect to, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name, making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>.</p>
</section>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="thrift.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.addresses</span></code></p></td>
<td><p>Location of Thrift servers</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino-thrift.max-response-size</span></code></p></td>
<td><p>Maximum size of data returned from Thrift server</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino-thrift.metadata-refresh-threads</span></code></p></td>
<td><p>Number of refresh threads for metadata cache</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.max-retries</span></code></p></td>
<td><p>Maximum number of retries for failed Thrift requests</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.max-backoff-delay</span></code></p></td>
<td><p>Maximum interval between retry attempts</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.min-backoff-delay</span></code></p></td>
<td><p>Minimum interval between retry attempts</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.max-retry-time</span></code></p></td>
<td><p>Maximum duration across all attempts of a Thrift request</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.backoff-scale-factor</span></code></p></td>
<td><p>Scale factor for exponential back off</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.connect-timeout</span></code></p></td>
<td><p>Connect timeout</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.request-timeout</span></code></p></td>
<td><p>Request timeout</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.socks-proxy</span></code></p></td>
<td><p>SOCKS proxy address</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.max-frame-size</span></code></p></td>
<td><p>Maximum size of a raw Thrift response</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.transport</span></code></p></td>
<td><p>Thrift transport type (<code class="docutils literal notranslate"><span class="pre">UNFRAMED</span></code>, <code class="docutils literal notranslate"><span class="pre">FRAMED</span></code>, <code class="docutils literal notranslate"><span class="pre">HEADER</span></code>)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.protocol</span></code></p></td>
<td><p>Thrift protocol type (<code class="docutils literal notranslate"><span class="pre">BINARY</span></code>, <code class="docutils literal notranslate"><span class="pre">COMPACT</span></code>, <code class="docutils literal notranslate"><span class="pre">FB_COMPACT</span></code>)</p></td>
</tr>
</tbody>
</table>
<section id="trino-thrift-client-addresses">
<h3 id="trino-thrift-client-addresses"><code class="docutils literal notranslate"><span class="pre">trino.thrift.client.addresses</span></code><a class="headerlink" href="thrift.html#trino-thrift-client-addresses" title="Link to this heading">#</a></h3>
<p>Comma-separated list of thrift servers in the form of <code class="docutils literal notranslate"><span class="pre">host:port</span></code>. For example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino.thrift.client.addresses=*********:7777,*********:7779
</pre></div>
</div>
<p>This property is required; there is no default.</p>
</section>
<section id="trino-thrift-max-response-size">
<h3 id="trino-thrift-max-response-size"><code class="docutils literal notranslate"><span class="pre">trino-thrift.max-response-size</span></code><a class="headerlink" href="thrift.html#trino-thrift-max-response-size" title="Link to this heading">#</a></h3>
<p>Maximum size of a data response that the connector accepts. This value is sent
by the connector to the Thrift server when requesting data, allowing it to size
the response appropriately.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">16MB</span></code>.</p>
</section>
<section id="trino-thrift-metadata-refresh-threads">
<h3 id="trino-thrift-metadata-refresh-threads"><code class="docutils literal notranslate"><span class="pre">trino-thrift.metadata-refresh-threads</span></code><a class="headerlink" href="thrift.html#trino-thrift-metadata-refresh-threads" title="Link to this heading">#</a></h3>
<p>Number of refresh threads for metadata cache.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</section>
</section>
<section id="trinothriftservice-implementation">
<span id="trino-thrift-service"></span><h2 id="trinothriftservice-implementation">TrinoThriftService implementation<a class="headerlink" href="thrift.html#trinothriftservice-implementation" title="Link to this heading">#</a></h2>
<p>The following IDL describes the <code class="docutils literal notranslate"><span class="pre">TrinoThriftService</span></code> that must be implemented:</p>
<div class="highlight-thrift notranslate"><div class="highlight"><pre><span></span><span class="kd">enum</span><span class="w"> </span><span class="nc">TrinoThriftBound</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="n">BELOW</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">  </span><span class="n">EXACTLY</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>
<span class="w">  </span><span class="n">ABOVE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">exception</span><span class="w"> </span><span class="nc">TrinoThriftServiceException</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">message</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="n">retryable</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftNullableSchemaName</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">schemaName</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftSchemaTableName</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">schemaName</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">tableName</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftTableMetadata</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftSchemaTableName</span><span class="w"> </span><span class="n">schemaTableName</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftColumnMetadata</span><span class="p">&gt;</span><span class="w"> </span><span class="n">columns</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">comment</span><span class="p">;</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns a list of key sets which can be used for index lookups.</span>
<span class="cm">   * The list is expected to have only unique key sets.</span>
<span class="cm">   * {@code set&lt;set&lt;string&gt;&gt;} is not used here because some languages (like php) don't support it.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">set</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;&gt;</span><span class="w"> </span><span class="n">indexableKeys</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftColumnMetadata</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">name</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">type</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">comment</span><span class="p">;</span>
<span class="w">  </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="n">hidden</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftNullableColumnSet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">set</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;</span><span class="w"> </span><span class="n">columns</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftTupleDomain</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Return a map of column names to constraints.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">map</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="n">TrinoThriftDomain</span><span class="p">&gt;</span><span class="w"> </span><span class="n">domains</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Set that either includes all values, or excludes all values.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftAllOrNoneValueSet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="n">all</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * A set containing values that are uniquely identifiable.</span>
<span class="cm"> * Assumes an infinite number of possible values. The values may be collectively included</span>
<span class="cm"> * or collectively excluded.</span>
<span class="cm"> * This structure is used with comparable, but not orderable types like "json", "map".</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftEquatableValueSet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="n">inclusive</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftBlock</span><span class="p">&gt;</span><span class="w"> </span><span class="n">values</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code ints} array are values for each row. If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftInteger</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">ints</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code longs} array are values for each row. If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftBigint</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i64</span><span class="p">&gt;</span><span class="w"> </span><span class="n">longs</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code doubles} array are values for each row. If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftDouble</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">double</span><span class="p">&gt;</span><span class="w"> </span><span class="n">doubles</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Each elements of {@code sizes} array contains the length in bytes for the corresponding element.</span>
<span class="cm"> * If row is null then the corresponding element in {@code sizes} is ignored.</span>
<span class="cm"> * {@code bytes} array contains UTF-8 encoded byte values.</span>
<span class="cm"> * Values for all rows are written to {@code bytes} array one after another.</span>
<span class="cm"> * The total number of bytes must be equal to the sum of all sizes.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftVarchar</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">sizes</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">binary</span><span class="w"> </span><span class="n">bytes</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code booleans} array are values for each row. If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftBoolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">booleans</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code dates} array are date values for each row represented as the number</span>
<span class="cm"> * of days passed since 1970-01-01.</span>
<span class="cm"> * If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftDate</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">dates</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Elements of {@code timestamps} array are values for each row represented as the number</span>
<span class="cm"> * of milliseconds passed since 1970-01-01T00:00:00 UTC.</span>
<span class="cm"> * If row is null then value is ignored.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftTimestamp</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i64</span><span class="p">&gt;</span><span class="w"> </span><span class="n">timestamps</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Each elements of {@code sizes} array contains the length in bytes for the corresponding element.</span>
<span class="cm"> * If row is null then the corresponding element in {@code sizes} is ignored.</span>
<span class="cm"> * {@code bytes} array contains UTF-8 encoded byte values for string representation of json.</span>
<span class="cm"> * Values for all rows are written to {@code bytes} array one after another.</span>
<span class="cm"> * The total number of bytes must be equal to the sum of all sizes.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftJson</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">sizes</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">binary</span><span class="w"> </span><span class="n">bytes</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Each elements of {@code sizes} array contains the length in bytes for the corresponding element.</span>
<span class="cm"> * If row is null then the corresponding element in {@code sizes} is ignored.</span>
<span class="cm"> * {@code bytes} array contains encoded byte values for HyperLogLog representation as defined in</span>
<span class="cm"> * Airlift specification: href="https://github.com/airlift/airlift/blob/master/stats/docs/hll.md</span>
<span class="cm"> * Values for all rows are written to {@code bytes} array one after another.</span>
<span class="cm"> * The total number of bytes must be equal to the sum of all sizes.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftHyperLogLog</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">sizes</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">binary</span><span class="w"> </span><span class="n">bytes</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Elements of {@code nulls} array determine if a value for a corresponding row is null.</span>
<span class="cm"> * Each elements of {@code sizes} array contains the number of elements in the corresponding values array.</span>
<span class="cm"> * If row is null then the corresponding element in {@code sizes} is ignored.</span>
<span class="cm"> * {@code values} is a bigint block containing array elements one after another for all rows.</span>
<span class="cm"> * The total number of elements in bigint block must be equal to the sum of all sizes.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftBigintArray</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">bool</span><span class="p">&gt;</span><span class="w"> </span><span class="n">nulls</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">i32</span><span class="p">&gt;</span><span class="w"> </span><span class="n">sizes</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftBigint</span><span class="w"> </span><span class="n">values</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * A set containing zero or more Ranges of the same type over a continuous space of possible values.</span>
<span class="cm"> * Ranges are coalesced into the most compact representation of non-overlapping Ranges.</span>
<span class="cm"> * This structure is used with comparable and orderable types like bigint, integer, double, varchar, etc.</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftRangeValueSet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftRange</span><span class="p">&gt;</span><span class="w"> </span><span class="n">ranges</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftId</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">binary</span><span class="w"> </span><span class="n">id</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftSplitBatch</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftSplit</span><span class="p">&gt;</span><span class="w"> </span><span class="n">splits</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftId</span><span class="w"> </span><span class="n">nextToken</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftSplit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Encodes all the information needed to identify a batch of rows to return to Trino.</span>
<span class="cm">   * For a basic scan, includes schema name, table name, and output constraint.</span>
<span class="cm">   * For an index scan, includes schema name, table name, set of keys to lookup and output constraint.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftId</span><span class="w"> </span><span class="n">splitId</span><span class="p">;</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Identifies the set of hosts on which the rows are available. If empty, then the rows</span>
<span class="cm">   * are expected to be available on any host. The hosts in this list may be independent</span>
<span class="cm">   * from the hosts used to serve metadata requests.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftHostAddress</span><span class="p">&gt;</span><span class="w"> </span><span class="n">hosts</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftHostAddress</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="n">host</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">i32</span><span class="w"> </span><span class="n">port</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftPageResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns data in a columnar format.</span>
<span class="cm">   * Columns in this list must be in the order they were requested by the engine.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftBlock</span><span class="p">&gt;</span><span class="w"> </span><span class="n">columnBlocks</span><span class="p">;</span>

<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">i32</span><span class="w"> </span><span class="n">rowCount</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftId</span><span class="w"> </span><span class="n">nextToken</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftNullableTableMetadata</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftTableMetadata</span><span class="w"> </span><span class="n">tableMetadata</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftValueSet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftAllOrNoneValueSet</span><span class="w"> </span><span class="n">allOrNoneValueSet</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftEquatableValueSet</span><span class="w"> </span><span class="n">equatableValueSet</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftRangeValueSet</span><span class="w"> </span><span class="n">rangeValueSet</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftBlock</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftInteger</span><span class="w"> </span><span class="n">integerData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftBigint</span><span class="w"> </span><span class="n">bigintData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftDouble</span><span class="w"> </span><span class="n">doubleData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftVarchar</span><span class="w"> </span><span class="n">varcharData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">5</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftBoolean</span><span class="w"> </span><span class="n">booleanData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">6</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftDate</span><span class="w"> </span><span class="n">dateData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">7</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftTimestamp</span><span class="w"> </span><span class="n">timestampData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">8</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftJson</span><span class="w"> </span><span class="n">jsonData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">9</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftHyperLogLog</span><span class="w"> </span><span class="n">hyperLogLogData</span><span class="p">;</span>
<span class="w">  </span><span class="mi">10</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftBigintArray</span><span class="w"> </span><span class="n">bigintArrayData</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * LOWER UNBOUNDED is specified with an empty value and an ABOVE bound</span>
<span class="cm"> * UPPER UNBOUNDED is specified with an empty value and a BELOW bound</span>
<span class="cm"> */</span>
<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftMarker</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftBlock</span><span class="w"> </span><span class="n">value</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftBound</span><span class="w"> </span><span class="n">bound</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftNullableToken</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="k">optional</span><span class="w"> </span><span class="n">TrinoThriftId</span><span class="w"> </span><span class="n">token</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftDomain</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftValueSet</span><span class="w"> </span><span class="n">valueSet</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="n">nullAllowed</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">struct</span><span class="w"> </span><span class="nc">TrinoThriftRange</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftMarker</span><span class="w"> </span><span class="n">low</span><span class="p">;</span>
<span class="w">  </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftMarker</span><span class="w"> </span><span class="n">high</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Trino Thrift service definition.</span>
<span class="cm"> * This thrift service needs to be implemented in order to be used with Thrift Connector.</span>
<span class="cm"> */</span>
<span class="kd">service</span><span class="w"> </span><span class="nc">TrinoThriftService</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns available schema names.</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;</span><span class="w"> </span><span class="nf">trinoListSchemaNames</span><span class="o">(</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns tables for the given schema name.</span>
<span class="cm">   *</span>
<span class="cm">   * @param schemaNameOrNull a structure containing schema name or {@literal null}</span>
<span class="cm">   * @return a list of table names with corresponding schemas. If schema name is null then returns</span>
<span class="cm">   * a list of tables for all schemas. Returns an empty list if a schema does not exist</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="kt">list</span><span class="p">&lt;</span><span class="n">TrinoThriftSchemaTableName</span><span class="p">&gt;</span><span class="w"> </span><span class="nf">trinoListTables</span><span class="o">(</span>
<span class="w">      </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftNullableSchemaName</span><span class="w"> </span><span class="n">schemaNameOrNull</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns metadata for a given table.</span>
<span class="cm">   *</span>
<span class="cm">   * @param schemaTableName schema and table name</span>
<span class="cm">   * @return metadata for a given table, or a {@literal null} value inside if it does not exist</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="n">TrinoThriftNullableTableMetadata</span><span class="w"> </span><span class="nf">trinoGetTableMetadata</span><span class="o">(</span>
<span class="w">      </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftSchemaTableName</span><span class="w"> </span><span class="n">schemaTableName</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns a batch of splits.</span>
<span class="cm">   *</span>
<span class="cm">   * @param schemaTableName schema and table name</span>
<span class="cm">   * @param desiredColumns a superset of columns to return; empty set means "no columns", {@literal null} set means "all columns"</span>
<span class="cm">   * @param outputConstraint constraint on the returned data</span>
<span class="cm">   * @param maxSplitCount maximum number of splits to return</span>
<span class="cm">   * @param nextToken token from a previous split batch or {@literal null} if it is the first call</span>
<span class="cm">   * @return a batch of splits</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="n">TrinoThriftSplitBatch</span><span class="w"> </span><span class="nf">trinoGetSplits</span><span class="o">(</span>
<span class="w">      </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftSchemaTableName</span><span class="w"> </span><span class="n">schemaTableName</span><span class="p">,</span>
<span class="w">      </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftNullableColumnSet</span><span class="w"> </span><span class="n">desiredColumns</span><span class="p">,</span>
<span class="w">      </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftTupleDomain</span><span class="w"> </span><span class="n">outputConstraint</span><span class="p">,</span>
<span class="w">      </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="kt">i32</span><span class="w"> </span><span class="n">maxSplitCount</span><span class="p">,</span>
<span class="w">      </span><span class="mi">5</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftNullableToken</span><span class="w"> </span><span class="n">nextToken</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns a batch of index splits for the given batch of keys.</span>
<span class="cm">   * This method is called if index join strategy is chosen for a query.</span>
<span class="cm">   *</span>
<span class="cm">   * @param schemaTableName schema and table name</span>
<span class="cm">   * @param indexColumnNames specifies columns and their order for keys</span>
<span class="cm">   * @param outputColumnNames a list of column names to return</span>
<span class="cm">   * @param keys keys for which records need to be returned; includes only unique and non-null values</span>
<span class="cm">   * @param outputConstraint constraint on the returned data</span>
<span class="cm">   * @param maxSplitCount maximum number of splits to return</span>
<span class="cm">   * @param nextToken token from a previous split batch or {@literal null} if it is the first call</span>
<span class="cm">   * @return a batch of splits</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="n">TrinoThriftSplitBatch</span><span class="w"> </span><span class="nf">trinoGetIndexSplits</span><span class="o">(</span>
<span class="w">      </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftSchemaTableName</span><span class="w"> </span><span class="n">schemaTableName</span><span class="p">,</span>
<span class="w">      </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;</span><span class="w"> </span><span class="n">indexColumnNames</span><span class="p">,</span>
<span class="w">      </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;</span><span class="w"> </span><span class="n">outputColumnNames</span><span class="p">,</span>
<span class="w">      </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftPageResult</span><span class="w"> </span><span class="n">keys</span><span class="p">,</span>
<span class="w">      </span><span class="mi">5</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftTupleDomain</span><span class="w"> </span><span class="n">outputConstraint</span><span class="p">,</span>
<span class="w">      </span><span class="mi">6</span><span class="p">:</span><span class="w"> </span><span class="kt">i32</span><span class="w"> </span><span class="n">maxSplitCount</span><span class="p">,</span>
<span class="w">      </span><span class="mi">7</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftNullableToken</span><span class="w"> </span><span class="n">nextToken</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>

<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Returns a batch of rows for the given split.</span>
<span class="cm">   *</span>
<span class="cm">   * @param splitId split id as returned in split batch</span>
<span class="cm">   * @param columns a list of column names to return</span>
<span class="cm">   * @param maxBytes maximum size of returned data in bytes</span>
<span class="cm">   * @param nextToken token from a previous batch or {@literal null} if it is the first call</span>
<span class="cm">   * @return a batch of table data</span>
<span class="cm">   */</span>
<span class="w">  </span><span class="n">TrinoThriftPageResult</span><span class="w"> </span><span class="nf">trinoGetRows</span><span class="o">(</span>
<span class="w">      </span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftId</span><span class="w"> </span><span class="n">splitId</span><span class="p">,</span>
<span class="w">      </span><span class="mi">2</span><span class="p">:</span><span class="w"> </span><span class="kt">list</span><span class="p">&lt;</span><span class="kt">string</span><span class="p">&gt;</span><span class="w"> </span><span class="n">columns</span><span class="p">,</span>
<span class="w">      </span><span class="mi">3</span><span class="p">:</span><span class="w"> </span><span class="kt">i64</span><span class="w"> </span><span class="n">maxBytes</span><span class="p">,</span>
<span class="w">      </span><span class="mi">4</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftNullableToken</span><span class="w"> </span><span class="n">nextToken</span><span class="p">)</span>
<span class="w">    </span><span class="k">throws</span><span class="w"> </span><span class="p">(</span><span class="mi">1</span><span class="p">:</span><span class="w"> </span><span class="n">TrinoThriftServiceException</span><span class="w"> </span><span class="n">ex1</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="type-mapping">
<span id="thrift-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="thrift.html#type-mapping" title="Link to this heading">#</a></h2>
<p>The Thrift service defines data type support and mappings to Trino data types.</p>
</section>
<section id="sql-support">
<span id="thrift-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="thrift.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements to access data and
metadata in your Thrift service.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="system.html" title="System connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> System connector </span>
              </div>
            </a>
          
          
            <a href="tpcds.html" title="TPC-DS connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> TPC-DS connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>