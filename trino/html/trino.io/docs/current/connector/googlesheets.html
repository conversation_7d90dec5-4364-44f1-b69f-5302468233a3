<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Google Sheets connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="googlesheets.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Hive connector" href="hive.html" />
    <link rel="prev" title="Faker connector" href="faker.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="googlesheets.html#connector/googlesheets" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Google Sheets connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Google Sheets </label>
    
      <a href="googlesheets.html#" class="md-nav__link md-nav__link--active">Google Sheets</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="googlesheets.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#credentials" class="md-nav__link">Credentials</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#metadata-sheet" class="md-nav__link">Metadata sheet</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#querying-sheets" class="md-nav__link">Querying sheets</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#writing-to-sheets" class="md-nav__link">Writing to sheets</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#api-usage-limits" class="md-nav__link">API usage limits</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#google-sheets-type-to-trino-type-mapping" class="md-nav__link">Google Sheets type to Trino type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#sheet-id-range-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sheet(id,</span> <span class="pre">range)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="googlesheets.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#credentials" class="md-nav__link">Credentials</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#metadata-sheet" class="md-nav__link">Metadata sheet</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#querying-sheets" class="md-nav__link">Querying sheets</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#writing-to-sheets" class="md-nav__link">Writing to sheets</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#api-usage-limits" class="md-nav__link">API usage limits</a>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#google-sheets-type-to-trino-type-mapping" class="md-nav__link">Google Sheets type to Trino type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="googlesheets.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="googlesheets.html#sheet-id-range-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">sheet(id,</span> <span class="pre">range)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="google-sheets-connector">
<h1 id="connector-googlesheets--page-root">Google Sheets connector<a class="headerlink" href="googlesheets.html#connector-googlesheets--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/google-sheets.png"/><p>The Google Sheets connector allows reading and writing <a class="reference external" href="https://www.google.com/sheets/about/">Google Sheets</a> spreadsheets as tables in Trino.</p>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="googlesheets.html#configuration" title="Link to this heading">#</a></h2>
<p>Create <code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> to mount the Google Sheets connector
as the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog, with the following contents:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=gsheets
gsheets.credentials-path=/path/to/google-sheets-credentials.json
gsheets.metadata-sheet-id=exampleId
</pre></div>
</div>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="googlesheets.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.credentials-path</span></code></p></td>
<td><p>Path to the Google API JSON key file</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.credentials-key</span></code></p></td>
<td><p>The base64 encoded credentials key</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.delegated-user-email</span></code></p></td>
<td><p>User email to impersonate the service account with domain-wide delegation enabled</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.metadata-sheet-id</span></code></p></td>
<td><p>Sheet ID of the spreadsheet, that contains the table mapping</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.max-data-cache-size</span></code></p></td>
<td><p>Maximum number of spreadsheets to cache, defaults to <code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.data-cache-ttl</span></code></p></td>
<td><p>How long to cache spreadsheet data or metadata, defaults to <code class="docutils literal notranslate"><span class="pre">5m</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.connection-timeout</span></code></p></td>
<td><p>Timeout when connection to Google Sheets API, defaults to <code class="docutils literal notranslate"><span class="pre">20s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.read-timeout</span></code></p></td>
<td><p>Timeout when reading from Google Sheets API, defaults to <code class="docutils literal notranslate"><span class="pre">20s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">gsheets.write-timeout</span></code></p></td>
<td><p>Timeout when writing to Google Sheets API, defaults to <code class="docutils literal notranslate"><span class="pre">20s</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="credentials">
<h2 id="credentials">Credentials<a class="headerlink" href="googlesheets.html#credentials" title="Link to this heading">#</a></h2>
<p>The connector requires credentials in order to access the Google Sheets API.</p>
<ol class="arabic simple">
<li><p>Open the <a class="reference external" href="https://console.developers.google.com/apis/library/sheets.googleapis.com">Google Sheets API</a>
page and click the <em>Enable</em> button. This takes you to the API manager page.</p></li>
<li><p>Select a project using the drop-down menu at the top of the page.
Create a new project, if you do not already have one.</p></li>
<li><p>Choose <em>Credentials</em> in the left panel.</p></li>
<li><p>Click <em>Manage service accounts</em>, then create a service account for the connector.
On the <em>Create key</em> step, create and download a key in JSON format.</p></li>
</ol>
<p>The key file needs to be available on the Trino coordinator and workers.
Set the <code class="docutils literal notranslate"><span class="pre">gsheets.credentials-path</span></code> configuration property to point to this file.
The exact name of the file does not matter – it can be named anything.</p>
<p>Alternatively, set the <code class="docutils literal notranslate"><span class="pre">gsheets.credentials-key</span></code> configuration property.
It should contain the contents of the JSON file, encoded using base64.</p>
<p>Optionally, set the <code class="docutils literal notranslate"><span class="pre">gsheets.delegated-user-email</span></code> property to impersonate a user.
This allows you to share Google Sheets with this email instead of the service account.</p>
</section>
<section id="metadata-sheet">
<h2 id="metadata-sheet">Metadata sheet<a class="headerlink" href="googlesheets.html#metadata-sheet" title="Link to this heading">#</a></h2>
<p>The metadata sheet is used to map table names to sheet IDs.
Create a new metadata sheet. The first row must be a header row
containing the following columns in this order:</p>
<ul class="simple">
<li><p>Table Name</p></li>
<li><p>Sheet ID</p></li>
<li><p>Owner (optional)</p></li>
<li><p>Notes (optional)</p></li>
</ul>
<p>See this <a class="reference external" href="https://docs.google.com/spreadsheets/d/1Es4HhWALUQjoa-bQh4a8B5HROz7dpGMfq_HbfoaW5LM">example sheet</a>
as a reference.</p>
<p>The metadata sheet must be shared with the service account user,
the one for which the key credentials file was created. Click the <em>Share</em>
button to share the sheet with the email address of the service account.</p>
<p>Set the <code class="docutils literal notranslate"><span class="pre">gsheets.metadata-sheet-id</span></code> configuration property to the ID of this sheet.</p>
</section>
<section id="querying-sheets">
<h2 id="querying-sheets">Querying sheets<a class="headerlink" href="googlesheets.html#querying-sheets" title="Link to this heading">#</a></h2>
<p>The service account user must have access to the sheet in order for Trino
to query it. Click the <em>Share</em> button to share the sheet with the email
address of the service account.</p>
<p>The sheet needs to be mapped to a Trino table name. Specify a table name
(column A) and the sheet ID (column B) in the metadata sheet. To refer
to a specific range in the sheet, add the range after the sheet ID, separated
with <code class="docutils literal notranslate"><span class="pre">#</span></code>. If a range is not provided, the connector loads only 10,000 rows by default from
the first tab in the sheet.</p>
<p>The first row of the provided sheet range is used as the header and will determine the column
names of the Trino table.
For more details on sheet range syntax see the <a class="reference external" href="https://developers.google.com/sheets/api/guides/concepts">google sheets docs</a>.</p>
</section>
<section id="writing-to-sheets">
<h2 id="writing-to-sheets">Writing to sheets<a class="headerlink" href="googlesheets.html#writing-to-sheets" title="Link to this heading">#</a></h2>
<p>The same way sheets can be queried, they can also be written by appending data to existing sheets.
In this case the service account user must also have <strong>Editor</strong> permissions on the sheet.</p>
<p>After data is written to a table, the table contents are removed from the cache
described in <a class="reference internal" href="googlesheets.html#gsheets-api-usage"><span class="std std-ref">API usage limits</span></a>. If the table is accessed
immediately after the write, querying the Google Sheets API may not reflect the
change yet. In that case the old version of the table is read and cached for the
configured amount of time, and it might take some time for the written changes
to propagate properly.</p>
<p>Keep in mind that the Google Sheets API has <a class="reference external" href="https://developers.google.com/sheets/api/limits">usage limits</a>, that limit the speed of inserting data.
If you run into timeouts you can increase timeout times to avoid <code class="docutils literal notranslate"><span class="pre">503:</span> <span class="pre">The</span> <span class="pre">service</span> <span class="pre">is</span> <span class="pre">currently</span> <span class="pre">unavailable</span></code> errors.</p>
</section>
<section id="api-usage-limits">
<span id="gsheets-api-usage"></span><h2 id="api-usage-limits">API usage limits<a class="headerlink" href="googlesheets.html#api-usage-limits" title="Link to this heading">#</a></h2>
<p>The Google Sheets API has <a class="reference external" href="https://developers.google.com/sheets/api/limits">usage limits</a>,
that may impact the usage of this connector. Increasing the cache duration and/or size
may prevent the limit from being reached. Running queries on the <code class="docutils literal notranslate"><span class="pre">information_schema.columns</span></code>
table without a schema and table name filter may lead to hitting the limit, as this requires
fetching the sheet data for every table, unless it is already cached.</p>
</section>
<section id="type-mapping">
<h2 id="type-mapping">Type mapping<a class="headerlink" href="googlesheets.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Google Sheets each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading data.</p>
<section id="google-sheets-type-to-trino-type-mapping">
<h3 id="google-sheets-type-to-trino-type-mapping">Google Sheets type to Trino type mapping<a class="headerlink" href="googlesheets.html#google-sheets-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Google Sheets types to the corresponding Trino types
following this table:</p>
<table id="id1">
<caption><span class="caption-text">Google Sheets type to Trino type mapping</span><a class="headerlink" href="googlesheets.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 60%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Google Sheets type</p></th>
<th class="head"><p>Trino type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="sql-support">
<span id="google-sheets-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="googlesheets.html#sql-support" title="Link to this heading">#</a></h2>
<p>In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements,
this connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
</ul>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="googlesheets.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc">Table functions</span></a> to access Google Sheets.</p>
<section id="sheet-id-range-table">
<span id="google-sheets-sheet-function"></span><h4 id="sheet-id-range-table"><code class="docutils literal notranslate"><span class="pre">sheet(id,</span> <span class="pre">range)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="googlesheets.html#sheet-id-range-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">sheet</span></code> function allows you to query a Google Sheet directly without
specifying it as a named table in the metadata sheet.</p>
<p>For example, for a catalog named ‘example’:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">sheet</span><span class="p">(</span>
<span class="w">      </span><span class="n">id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'googleSheetIdHere'</span><span class="p">));</span>
</pre></div>
</div>
<p>A sheet range or named range can be provided as an optional <code class="docutils literal notranslate"><span class="pre">range</span></code> argument.
The default sheet range is <code class="docutils literal notranslate"><span class="pre">$1:$10000</span></code> if one is not provided:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">sheet</span><span class="p">(</span>
<span class="w">      </span><span class="n">id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'googleSheetIdHere'</span><span class="p">,</span>
<span class="w">      </span><span class="n">range</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'TabName!A1:B4'</span><span class="p">));</span>
</pre></div>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="faker.html" title="Faker connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Faker connector </span>
              </div>
            </a>
          
          
            <a href="hive.html" title="Hive connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Hive connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>