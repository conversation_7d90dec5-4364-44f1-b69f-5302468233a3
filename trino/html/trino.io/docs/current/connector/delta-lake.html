<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Delta Lake connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="delta-lake.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Druid connector" href="druid.html" />
    <link rel="prev" title="ClickHouse connector" href="clickhouse.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="delta-lake.html#connector/delta-lake" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Delta Lake connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Delta Lake </label>
    
      <a href="delta-lake.html#" class="md-nav__link md-nav__link--active">Delta Lake</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="delta-lake.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-general-configuration-properties" class="md-nav__link">Delta Lake general configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#catalog-session-properties" class="md-nav__link">Catalog session properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-to-trino-type-mapping" class="md-nav__link">Delta Lake to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#trino-to-delta-lake-type-mapping" class="md-nav__link">Trino to Delta Lake type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-table-features" class="md-nav__link">Delta Lake table features</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#authorization-checks" class="md-nav__link">Authorization checks</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#time-travel-queries" class="md-nav__link">Time travel queries</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#register-table" class="md-nav__link">Register table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#unregister-table" class="md-nav__link">Unregister table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#flush-metadata-cache" class="md-nav__link">Flush metadata cache</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#vacuum" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VACUUM</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#data-management" class="md-nav__link">Data management</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#replace-tables" class="md-nav__link">Replace tables</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#alter-table-execute" class="md-nav__link">ALTER TABLE EXECUTE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#optimize" class="md-nav__link">optimize</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#alter-table-rename-to" class="md-nav__link">ALTER TABLE RENAME TO</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-properties" class="md-nav__link">Table properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#shallow-cloned-tables" class="md-nav__link">Shallow cloned tables</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#history-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#partitions-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#properties-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#metadata-columns" class="md-nav__link">Metadata columns</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#table-changes" class="md-nav__link">table_changes</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#table-statistics" class="md-nav__link">Table statistics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#fine-tuning" class="md-nav__link">Fine-tuning</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#disable-and-drop-extended-statistics" class="md-nav__link">Disable and drop extended statistics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#memory-usage" class="md-nav__link">Memory usage</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#memory-monitoring" class="md-nav__link">Memory monitoring</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-redirection" class="md-nav__link">Table redirection</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#performance-tuning-configuration-properties" class="md-nav__link">Performance tuning configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#file-system-cache" class="md-nav__link">File system cache</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="delta-lake.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#general-configuration" class="md-nav__link">General configuration</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-general-configuration-properties" class="md-nav__link">Delta Lake general configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#catalog-session-properties" class="md-nav__link">Catalog session properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-to-trino-type-mapping" class="md-nav__link">Delta Lake to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#trino-to-delta-lake-type-mapping" class="md-nav__link">Trino to Delta Lake type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#delta-lake-table-features" class="md-nav__link">Delta Lake table features</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#authorization-checks" class="md-nav__link">Authorization checks</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#time-travel-queries" class="md-nav__link">Time travel queries</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#register-table" class="md-nav__link">Register table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#unregister-table" class="md-nav__link">Unregister table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#flush-metadata-cache" class="md-nav__link">Flush metadata cache</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#vacuum" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">VACUUM</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#data-management" class="md-nav__link">Data management</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#replace-tables" class="md-nav__link">Replace tables</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#alter-table-execute" class="md-nav__link">ALTER TABLE EXECUTE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#optimize" class="md-nav__link">optimize</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#alter-table-rename-to" class="md-nav__link">ALTER TABLE RENAME TO</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-properties" class="md-nav__link">Table properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#shallow-cloned-tables" class="md-nav__link">Shallow cloned tables</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#history-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#partitions-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#properties-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#metadata-columns" class="md-nav__link">Metadata columns</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#table-changes" class="md-nav__link">table_changes</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#table-statistics" class="md-nav__link">Table statistics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#fine-tuning" class="md-nav__link">Fine-tuning</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#disable-and-drop-extended-statistics" class="md-nav__link">Disable and drop extended statistics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#memory-usage" class="md-nav__link">Memory usage</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="delta-lake.html#memory-monitoring" class="md-nav__link">Memory monitoring</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#table-redirection" class="md-nav__link">Table redirection</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#performance-tuning-configuration-properties" class="md-nav__link">Performance tuning configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="delta-lake.html#file-system-cache" class="md-nav__link">File system cache</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="delta-lake-connector">
<h1 id="connector-delta-lake--page-root">Delta Lake connector<a class="headerlink" href="delta-lake.html#connector-delta-lake--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/delta-lake.png"/><p>The Delta Lake connector allows querying data stored in the <a class="reference external" href="https://delta.io">Delta Lake</a> format, including <a class="reference external" href="https://docs.databricks.com/delta/index.html">Databricks Delta Lake</a>. The connector can natively
read the Delta Lake transaction log and thus detect when external systems change
data.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="delta-lake.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to Databricks Delta Lake, you need:</p>
<ul class="simple">
<li><p>Tables written by Databricks Runtime 7.3 LTS, 9.1 LTS, 10.4 LTS, 11.3 LTS,
12.2 LTS, 13.3 LTS, 14.3 LTS and 15.4 LTS are supported.</p></li>
<li><p>Deployments using AWS, HDFS, Azure Storage, and Google Cloud Storage (GCS) are
fully supported.</p></li>
<li><p>Network access from the coordinator and workers to the Delta Lake storage.</p></li>
<li><p>Access to the Hive metastore service (HMS) of Delta Lake or a separate HMS,
or a Glue metastore.</p></li>
<li><p>Network access to the HMS from the coordinator and workers. Port 9083 is the
default port for the Thrift protocol used by the HMS.</p></li>
<li><p>Data files stored in the <a class="reference internal" href="../object-storage/file-formats.html#parquet-format-configuration"><span class="std std-ref">Parquet file format</span></a>
on a <a class="reference internal" href="delta-lake.html#delta-lake-file-system-configuration"><span class="std std-ref">supported file system</span></a>.</p></li>
</ul>
</section>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="delta-lake.html#general-configuration" title="Link to this heading">#</a></h2>
<p>To configure the Delta Lake connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> that references the <code class="docutils literal notranslate"><span class="pre">delta_lake</span></code> connector.</p>
<p>You must configure a <a class="reference internal" href="../object-storage/metastores.html"><span class="doc std std-doc">metastore for metadata</span></a>.</p>
<p>You must select and configure one of the <a class="reference internal" href="delta-lake.html#delta-lake-file-system-configuration"><span class="std std-ref">supported file
systems</span></a>.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">delta_lake</span>
<span class="na">hive.metastore.uri</span><span class="o">=</span><span class="s">thrift://example.net:9083</span>
<span class="na">fs.x.enabled</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
<p>Replace the <code class="docutils literal notranslate"><span class="pre">fs.x.enabled</span></code> configuration property with the desired file system.</p>
<p>If you are using <a class="reference internal" href="../object-storage/metastores.html#hive-glue-metastore"><span class="std std-ref">AWS Glue</span></a> as your metastore, you
must instead set <code class="docutils literal notranslate"><span class="pre">hive.metastore</span></code> to <code class="docutils literal notranslate"><span class="pre">glue</span></code>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">delta_lake</span>
<span class="na">hive.metastore</span><span class="o">=</span><span class="s">glue</span>
</pre></div>
</div>
<p>Each metastore type has specific configuration properties along with
<a class="reference internal" href="../object-storage/metastores.html#general-metastore-properties"><span class="std std-ref">general metastore configuration properties</span></a>.</p>
<p>The connector recognizes Delta Lake tables created in the metastore by the Databricks
runtime. If non-Delta Lake tables are present in the metastore as well, they are not
visible to the connector.</p>
</section>
<section id="file-system-access-configuration">
<span id="delta-lake-file-system-configuration"></span><h2 id="file-system-access-configuration">File system access configuration<a class="headerlink" href="delta-lake.html#file-system-access-configuration" title="Link to this heading">#</a></h2>
<p>The connector supports accessing the following file systems:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-hdfs.html"><span class="doc std std-doc">HDFS file system support</span></a></p></li>
</ul>
<p>You must enable and configure the specific file system access. <a class="reference internal" href="../object-storage.html#file-system-legacy"><span class="std std-ref">Legacy
support</span></a> is not recommended and will be removed.</p>
<section id="delta-lake-general-configuration-properties">
<h3 id="delta-lake-general-configuration-properties">Delta Lake general configuration properties<a class="headerlink" href="delta-lake.html#delta-lake-general-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following configuration properties are all using reasonable, tested default
values. Typical usage does not require you to configure them.</p>
<table id="id1">
<caption><span class="caption-text">Delta Lake configuration properties</span><a class="headerlink" href="delta-lake.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 55%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metadata.cache-ttl</span></code></p></td>
<td><p>Caching duration for Delta Lake tables metadata.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">30m</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metadata.cache-max-retained-size</span></code></p></td>
<td><p>Maximum retained size of Delta table metadata stored in cache. Must be
specified in <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> values such as <code class="docutils literal notranslate"><span class="pre">64MB</span></code>. Default is
calculated to 5% of the maximum memory allocated to the JVM.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metadata.live-files.cache-size</span></code></p></td>
<td><p>Amount of memory allocated for caching information about files. Must be
specified in <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> values such as <code class="docutils literal notranslate"><span class="pre">64MB</span></code>. Default is
calculated to 10% of the maximum memory allocated to the JVM.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metadata.live-files.cache-ttl</span></code></p></td>
<td><p>Caching duration for active files that correspond to the Delta Lake tables.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">30m</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.compression-codec</span></code></p></td>
<td><p>The compression codec to be used when writing new data files. Possible
values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SNAPPY</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ZSTD</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GZIP</span></code></p></li>
</ul>
<p>The equivalent catalog session property is <code class="docutils literal notranslate"><span class="pre">compression_codec</span></code>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">ZSTD</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.max-partitions-per-writer</span></code></p></td>
<td><p>Maximum number of partitions per writer.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">100</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.hide-non-delta-lake-tables</span></code></p></td>
<td><p>Hide information about tables that are not managed by Delta Lake. Hiding
only applies to tables with the metadata managed in a Glue catalog, and does
not apply to usage with a Hive metastore service.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.enable-non-concurrent-writes</span></code></p></td>
<td><p>Enable <a class="reference internal" href="delta-lake.html#delta-lake-data-management"><span class="std std-ref">write support</span></a> for all supported file
systems. Specifically, take note of the warning about concurrency and
checkpoints.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.default-checkpoint-writing-interval</span></code></p></td>
<td><p>Default integer count to write transaction log checkpoint entries. If the
value is set to N, then checkpoints are written after every Nth statement
performing table writes. The value can be overridden for a specific table
with the <code class="docutils literal notranslate"><span class="pre">checkpoint_interval</span></code> table property.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.hive-catalog-name</span></code></p></td>
<td><p>Name of the catalog to which <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries are redirected when a
Hive table is detected.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.checkpoint-row-statistics-writing.enabled</span></code></p></td>
<td><p>Enable writing row statistics to checkpoint files.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.checkpoint-filtering.enabled</span></code></p></td>
<td><p>Enable pruning of data file entries as well as data file statistics columns
which are irrelevant for the query when reading Delta Lake checkpoint files.
Reading only the relevant active file data from the checkpoint, directly
from the storage, instead of relying on the active files caching, likely
results in decreased memory pressure on the coordinator. The equivalent
catalog session property is <code class="docutils literal notranslate"><span class="pre">checkpoint_filtering_enabled</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.dynamic-filtering.wait-timeout</span></code></p></td>
<td><p>Duration to wait for completion of <a class="reference internal" href="../admin/dynamic-filtering.html"><span class="doc std std-doc">dynamic
filtering</span></a> during split generation. The equivalent
catalog session property is <code class="docutils literal notranslate"><span class="pre">dynamic_filtering_wait_timeout</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.table-statistics-enabled</span></code></p></td>
<td><p>Enables <a class="reference internal" href="delta-lake.html#delta-lake-table-statistics"><span class="std std-ref">Table statistics</span></a> for performance
improvements. The equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">statistics_enabled</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.extended-statistics.enabled</span></code></p></td>
<td><p>Enable statistics collection with <a class="reference internal" href="../sql/analyze.html"><span class="doc std std-doc">ANALYZE</span></a> and use of extended
statistics. The equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">extended_statistics_enabled</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.extended-statistics.collect-on-write</span></code></p></td>
<td><p>Enable collection of extended statistics for write operations. The
equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">extended_statistics_collect_on_write</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.per-transaction-metastore-cache-maximum-size</span></code></p></td>
<td><p>Maximum number of metastore data objects per transaction in the Hive
metastore cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metastore.store-table-metadata</span></code></p></td>
<td><p>Store table comments and colum definitions in the metastore. The write
permission is required to update the metastore.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metastore.store-table-metadata-threads</span></code></p></td>
<td><p>Number of threads used for storing table metadata in metastore.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">5</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.delete-schema-locations-fallback</span></code></p></td>
<td><p>Whether schema locations are deleted when Trino can’t determine whether they
contain external files.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.parquet.time-zone</span></code></p></td>
<td><p>Time zone for Parquet read and write.</p></td>
<td><p>JVM default</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.target-max-file-size</span></code></p></td>
<td><p>Target maximum size of written files; the actual size could be larger. The
equivalent catalog session property is <code class="docutils literal notranslate"><span class="pre">target_max_file_size</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1GB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.unique-table-location</span></code></p></td>
<td><p>Use randomized, unique table locations.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.register-table-procedure.enabled</span></code></p></td>
<td><p>Enable to allow users to call the <a class="reference internal" href="delta-lake.html#delta-lake-register-table"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">register_table</span></code> procedure</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.vacuum.min-retention</span></code></p></td>
<td><p>Minimum retention threshold for the files taken into account for removal by
the <a class="reference internal" href="delta-lake.html#delta-lake-vacuum"><span class="std std-ref">VACUUM</span></a> procedure. The equivalent catalog session
property is <code class="docutils literal notranslate"><span class="pre">vacuum_min_retention</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">7</span> <span class="pre">DAYS</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.deletion-vectors-enabled</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> for enabling deletion vectors by default when creating new tables.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.metadata.parallelism</span></code></p></td>
<td><p>Number of threads used for retrieving metadata. Currently, only table loading
is parallelized.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">8</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.checkpoint-processing.parallelism</span></code></p></td>
<td><p>Number of threads used for retrieving checkpoint files of each table. Currently, only
retrievals of V2 Checkpoint’s sidecar files are parallelized.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="catalog-session-properties">
<h3 id="catalog-session-properties">Catalog session properties<a class="headerlink" href="delta-lake.html#catalog-session-properties" title="Link to this heading">#</a></h3>
<p>The following table describes <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">catalog session properties</span></a> supported by the Delta Lake connector:</p>
<table id="id2">
<caption><span class="caption-text">Catalog session properties</span><a class="headerlink" href="delta-lake.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 33%"/>
<col style="width: 50%"/>
<col style="width: 17%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_max_read_block_size</span></code></p></td>
<td><p>The maximum block size used when reading Parquet files.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">16MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_writer_block_size</span></code></p></td>
<td><p>The maximum block size created by the Parquet writer.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">128MB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_writer_page_size</span></code></p></td>
<td><p>The maximum page size created by the Parquet writer.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_writer_page_value_count</span></code></p></td>
<td><p>The maximum value count of pages created by the Parquet writer.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">60000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_writer_batch_size</span></code></p></td>
<td><p>Maximum number of rows processed by the Parquet writer in a batch.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">10000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">projection_pushdown_enabled</span></code></p></td>
<td><p>Read only projected fields from row columns while performing <code class="docutils literal notranslate"><span class="pre">SELECT</span></code>
queries.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="fault-tolerant-execution-support">
<span id="delta-lake-fte-support"></span><h3 id="fault-tolerant-execution-support">Fault-tolerant execution support<a class="headerlink" href="delta-lake.html#fault-tolerant-execution-support" title="Link to this heading">#</a></h3>
<p>The connector supports <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a> of query
processing. Read and write operations are both supported with any retry policy.</p>
</section>
</section>
<section id="type-mapping">
<span id="delta-lake-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="delta-lake.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Delta Lake each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types might not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<p>See the <a class="reference external" href="https://github.com/delta-io/delta/blob/master/PROTOCOL.md#primitive-types">Delta Transaction Log specification</a>
for more information about supported data types in the Delta Lake table format
specification.</p>
<section id="delta-lake-to-trino-type-mapping">
<h3 id="delta-lake-to-trino-type-mapping">Delta Lake to Trino type mapping<a class="headerlink" href="delta-lake.html#delta-lake-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Delta Lake types to the corresponding Trino types following
this table:</p>
<table id="id3">
<caption><span class="caption-text">Delta Lake to Trino type mapping</span><a class="headerlink" href="delta-lake.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Delta Lake type</p></th>
<th class="head"><p>Trino type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMPNTZ</span></code> (<code class="docutils literal notranslate"><span class="pre">TIMESTAMP_NTZ</span></code>)</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">STRUCT(...)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-to-delta-lake-type-mapping">
<h3 id="trino-to-delta-lake-type-mapping">Trino to Delta Lake type mapping<a class="headerlink" href="delta-lake.html#trino-to-delta-lake-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding Delta Lake types following
this table:</p>
<table id="id4">
<caption><span class="caption-text">Trino to Delta Lake type mapping</span><a class="headerlink" href="delta-lake.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 60%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>Delta Lake type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BINARY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMPNTZ</span></code> (<code class="docutils literal notranslate"><span class="pre">TIMESTAMP_NTZ</span></code>)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRUCT(...)</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="delta-lake-table-features">
<h2 id="delta-lake-table-features">Delta Lake table features<a class="headerlink" href="delta-lake.html#delta-lake-table-features" title="Link to this heading">#</a></h2>
<p>The connector supports the following <a class="reference external" href="https://github.com/delta-io/delta/blob/master/PROTOCOL.md#table-features">Delta Lake table
features</a>:</p>
<table id="id5">
<caption><span class="caption-text">Table features</span><a class="headerlink" href="delta-lake.html#id5" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 70%"/>
<col style="width: 30%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Feature</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Append-only tables</p></td>
<td><p>Writers only</p></td>
</tr>
<tr class="row-odd"><td><p>Column invariants</p></td>
<td><p>Writers only</p></td>
</tr>
<tr class="row-even"><td><p>CHECK constraints</p></td>
<td><p>Writers only</p></td>
</tr>
<tr class="row-odd"><td><p>Change data feed</p></td>
<td><p>Writers only</p></td>
</tr>
<tr class="row-even"><td><p>Column mapping</p></td>
<td><p>Readers and writers</p></td>
</tr>
<tr class="row-odd"><td><p>Deletion vectors</p></td>
<td><p>Readers and writers</p></td>
</tr>
<tr class="row-even"><td><p>Iceberg compatibility V1 &amp; V2</p></td>
<td><p>Readers only</p></td>
</tr>
<tr class="row-odd"><td><p>Invariants</p></td>
<td><p>Writers only</p></td>
</tr>
<tr class="row-even"><td><p>Timestamp without time zone</p></td>
<td><p>Readers and writers</p></td>
</tr>
<tr class="row-odd"><td><p>Type widening</p></td>
<td><p>Readers only</p></td>
</tr>
<tr class="row-even"><td><p>Vacuum protocol check</p></td>
<td><p>Readers and writers</p></td>
</tr>
<tr class="row-odd"><td><p>V2 checkpoint</p></td>
<td><p>Readers only</p></td>
</tr>
</tbody>
</table>
<p>No other features are supported.</p>
</section>
<section id="security">
<h2 id="security">Security<a class="headerlink" href="delta-lake.html#security" title="Link to this heading">#</a></h2>
<p>The Delta Lake connector allows you to choose one of several means of providing
authorization at the catalog level. You can select a different type of
authorization check in different Delta Lake catalog files.</p>
<section id="authorization-checks">
<span id="delta-lake-authorization"></span><h3 id="authorization-checks">Authorization checks<a class="headerlink" href="delta-lake.html#authorization-checks" title="Link to this heading">#</a></h3>
<p>Enable authorization checks for the connector by setting the <code class="docutils literal notranslate"><span class="pre">delta.security</span></code>
property in the catalog properties file. This property must be one of the
security values in the following table:</p>
<table id="id6">
<caption><span class="caption-text">Delta Lake security values</span><a class="headerlink" href="delta-lake.html#id6" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 33%"/>
<col style="width: 67%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ALLOW_ALL</span></code> (default value)</p></td>
<td><p>No authorization checks are enforced.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SYSTEM</span></code></p></td>
<td><p>The connector relies on system-level access control.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">READ_ONLY</span></code></p></td>
<td><p>Operations that read data or metadata, such as <a class="reference internal" href="../sql/select.html"><span class="doc std std-doc">SELECT</span></a> are
permitted. No operations that write data or metadata, such as
<a class="reference internal" href="../sql/create-table.html"><span class="doc std std-doc">CREATE TABLE</span></a>, <a class="reference internal" href="../sql/insert.html"><span class="doc std std-doc">INSERT</span></a>, or <a class="reference internal" href="../sql/delete.html"><span class="doc std std-doc">DELETE</span></a> are allowed.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FILE</span></code></p></td>
<td><p>Authorization checks are enforced using a catalog-level access control
configuration file whose path is specified in the <code class="docutils literal notranslate"><span class="pre">security.config-file</span></code>
catalog configuration property. See <a class="reference internal" href="../security/file-system-access-control.html#catalog-file-based-access-control"><span class="std std-ref">Catalog-level access control files</span></a>
for information on the authorization configuration file.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sql-support">
<span id="delta-lake-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="delta-lake.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read and write access to data and metadata in
Delta Lake. In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a>
statements, the connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../language/sql-support.html#sql-write-operations"><span class="std std-ref">Write operations</span></a>:</p>
<ul>
<li><p><a class="reference internal" href="../language/sql-support.html#sql-data-management"><span class="std std-ref">Data management</span></a>, see details for  <a class="reference internal" href="delta-lake.html#delta-lake-data-management"><span class="std std-ref">Delta Lake data management</span></a></p></li>
<li><p><a class="reference internal" href="../language/sql-support.html#sql-schema-table-management"><span class="std std-ref">Schema and table management</span></a>, see details for  <a class="reference internal" href="delta-lake.html#delta-lake-schema-table-management"><span class="std std-ref">Delta Lake schema and table management</span></a></p></li>
<li><p><a class="reference internal" href="../language/sql-support.html#sql-view-management"><span class="std std-ref">View management</span></a></p></li>
</ul>
</li>
</ul>
<section id="time-travel-queries">
<span id="delta-time-travel"></span><h3 id="time-travel-queries">Time travel queries<a class="headerlink" href="delta-lake.html#time-travel-queries" title="Link to this heading">#</a></h3>
<p>The connector offers the ability to query historical data. This allows to
query the table as it was when a previous snapshot of the table was taken, even
if the data has since been modified or deleted.</p>
<p>The historical data of the table can be retrieved by specifying the version
number corresponding to the version of the table to be retrieved:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">VERSION</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="mi">3</span>
</pre></div>
</div>
<p>A different approach of retrieving historical data is to specify a point in time
in the past, such as a day or week ago. The latest snapshot of the table taken
before or at the specified timestamp in the query is internally used for
providing the previous state of the table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 09:59:29.803 America/Los_Angeles'</span><span class="p">;</span>
</pre></div>
</div>
<p>The connector allows to create a new snapshot through Delta Lake’s <a class="reference internal" href="delta-lake.html#delta-lake-create-or-replace"><span class="std std-ref">replace table</span></a>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">AS</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 09:59:29.803 America/Los_Angeles'</span><span class="p">;</span>
</pre></div>
</div>
<p>You can use a date to specify a point a time in the past for using a snapshot of a table in a query.
Assuming that the session time zone is <code class="docutils literal notranslate"><span class="pre">America/Los_Angeles</span></code> the following queries are equivalent:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2022-03-23'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 00:00:00'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 00:00:00.000 America/Los_Angeles'</span><span class="p">;</span>
</pre></div>
</div>
<p>Use the <code class="docutils literal notranslate"><span class="pre">$history</span></code> metadata table to determine the snapshot ID of the
table like in the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">version</span><span class="p">,</span><span class="w"> </span><span class="k">operation</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="ss">"customer_orders$history"</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="k">version</span><span class="w"> </span><span class="k">DESC</span>
</pre></div>
</div>
</section>
<section id="procedures">
<h3 id="procedures">Procedures<a class="headerlink" href="delta-lake.html#procedures" title="Link to this heading">#</a></h3>
<p>Use the <a class="reference internal" href="../sql/call.html"><span class="doc">CALL</span></a> statement to perform data manipulation or
administrative tasks. Procedures are available in the system schema of each
catalog. The following code snippet displays how to call the
<code class="docutils literal notranslate"><span class="pre">example_procedure</span></code> in the <code class="docutils literal notranslate"><span class="pre">examplecatalog</span></code> catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">examplecatalog</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">example_procedure</span><span class="p">()</span>
</pre></div>
</div>
<section id="register-table">
<span id="delta-lake-register-table"></span><h4 id="register-table">Register table<a class="headerlink" href="delta-lake.html#register-table" title="Link to this heading">#</a></h4>
<p>The connector can register existing Delta Lake tables into the metastore if
<code class="docutils literal notranslate"><span class="pre">delta.register-table-procedure.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code> for the catalog.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">system.register_table</span></code> procedure allows the caller to register an
existing Delta Lake table in the metastore, using its existing transaction logs
and data files:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">register_table</span><span class="p">(</span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span><span class="w"> </span><span class="n">table_location</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path'</span><span class="p">)</span>
</pre></div>
</div>
<p>To prevent unauthorized users from accessing data, this procedure is disabled by
default. The procedure is enabled only when
<code class="docutils literal notranslate"><span class="pre">delta.register-table-procedure.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
</section>
<section id="unregister-table">
<span id="delta-lake-unregister-table"></span><h4 id="unregister-table">Unregister table<a class="headerlink" href="delta-lake.html#unregister-table" title="Link to this heading">#</a></h4>
<p>The connector can remove existing Delta Lake tables from the metastore. Once
unregistered, you can no longer query the table from Trino.</p>
<p>The procedure <code class="docutils literal notranslate"><span class="pre">system.unregister_table</span></code> allows the caller to unregister an
existing Delta Lake table from the metastores without deleting the data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">unregister_table</span><span class="p">(</span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="flush-metadata-cache">
<span id="delta-lake-flush-metadata-cache"></span><h4 id="flush-metadata-cache">Flush metadata cache<a class="headerlink" href="delta-lake.html#flush-metadata-cache" title="Link to this heading">#</a></h4>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></p>
<p>Flushes all metadata caches.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache(schema_name</span> <span class="pre">=&gt;</span> <span class="pre">...,</span> <span class="pre">table_name</span> <span class="pre">=&gt;</span> <span class="pre">...)</span></code></p>
<p>Flushes metadata cache entries of a specific table.
Procedure requires passing named parameters.</p>
</li>
</ul>
</section>
<section id="vacuum">
<span id="delta-lake-vacuum"></span><h4 id="vacuum"><code class="docutils literal notranslate"><span class="pre">VACUUM</span></code><a class="headerlink" href="delta-lake.html#vacuum" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">VACUUM</span></code> procedure removes all old files that are not in the transaction
log, as well as files that are not needed to read table snapshots newer than the
current time minus the retention period defined by the <code class="docutils literal notranslate"><span class="pre">retention</span> <span class="pre">period</span></code>
parameter.</p>
<p>Users with <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> permissions on a table can run <code class="docutils literal notranslate"><span class="pre">VACUUM</span></code>
as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="k">vacuum</span><span class="p">(</span><span class="s1">'exampleschemaname'</span><span class="p">,</span><span class="w"> </span><span class="s1">'exampletablename'</span><span class="p">,</span><span class="w"> </span><span class="s1">'7d'</span><span class="p">);</span>
</pre></div>
</div>
<p>All parameters are required and must be presented in the following order:</p>
<ul class="simple">
<li><p>Schema name</p></li>
<li><p>Table name</p></li>
<li><p>Retention period</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">delta.vacuum.min-retention</span></code> configuration property provides a safety
measure to ensure that files are retained as expected. The minimum value for
this property is <code class="docutils literal notranslate"><span class="pre">0s</span></code>. There is a minimum retention session property as well,
<code class="docutils literal notranslate"><span class="pre">vacuum_min_retention</span></code>.</p>
</section>
</section>
<section id="data-management">
<span id="delta-lake-data-management"></span><h3 id="data-management">Data management<a class="headerlink" href="delta-lake.html#data-management" title="Link to this heading">#</a></h3>
<p>You can use the connector to <a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a>, <a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a>,
<a class="reference internal" href="../sql/update.html"><span class="doc">UPDATE</span></a>, and <a class="reference internal" href="../sql/merge.html"><span class="doc">MERGE</span></a> data in Delta Lake tables.</p>
<p>Write operations are supported for tables stored on the following systems:</p>
<ul>
<li><p>Azure ADLS Gen2, Google Cloud Storage</p>
<p>Writes to the Azure ADLS Gen2 and Google Cloud Storage are
enabled by default. Trino detects write collisions on these storage systems
when writing from multiple Trino clusters, or from other query engines.</p>
</li>
<li><p>S3 and S3-compatible storage</p>
<p>Writes to Amazon S3 and S3-compatible storage must be enabled
with the <code class="docutils literal notranslate"><span class="pre">delta.enable-non-concurrent-writes</span></code> property. Writes to S3 can
safely be made from multiple Trino clusters; however, write collisions are not
detected when writing concurrently from other Delta Lake engines. You must
make sure that no concurrent data modifications are run to avoid data
corruption.</p>
</li>
</ul>
</section>
<section id="schema-and-table-management">
<span id="delta-lake-schema-table-management"></span><h3 id="schema-and-table-management">Schema and table management<a class="headerlink" href="delta-lake.html#schema-and-table-management" title="Link to this heading">#</a></h3>
<p>The <a class="reference internal" href="../language/sql-support.html#sql-schema-table-management"><span class="std std-ref">Schema and table management</span></a> functionality includes support for:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a>, see details for <a class="reference internal" href="delta-lake.html#delta-lake-alter-table"><span class="std std-ref">Delta Lake ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-schema.html"><span class="doc">ALTER SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
<p>The connector supports creating schemas. You can create a schema with or without
a specified location.</p>
<p>You can create a schema with the <a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a> statement and the
<code class="docutils literal notranslate"><span class="pre">location</span></code> schema property. Tables in this schema are located in a
subdirectory under the schema location. Data files for tables in this schema
using the default location are cleaned up if the table is dropped:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path'</span><span class="p">);</span>
</pre></div>
</div>
<p>Optionally, the location can be omitted. Tables in this schema must have a
location included when you create them. The data files for these tables are not
removed if the table is dropped:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
</pre></div>
</div>
<p>When Delta Lake tables exist in storage but not in the metastore, Trino can be
used to register the tables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">register_table</span><span class="p">(</span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'example_table'</span><span class="p">,</span><span class="w"> </span><span class="n">table_location</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path'</span><span class="p">)</span>
</pre></div>
</div>
<p>The table schema is read from the transaction log instead. If the
schema is changed by an external system, Trino automatically uses the new
schema.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Using <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span></code> with an existing table content is disallowed,
use the <code class="docutils literal notranslate"><span class="pre">system.register_table</span></code> procedure instead.</p>
</div>
<p>If the specified location does not already contain a Delta table, the connector
automatically writes the initial transaction log entries and registers the table
in the metastore. As a result, any Databricks engine can write to the table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">new_table</span><span class="w"> </span><span class="p">(</span><span class="n">id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span>
</pre></div>
</div>
<p>The Delta Lake connector also supports creating tables using the <a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a> syntax.</p>
<p id="delta-lake-alter-table">The connector supports the following <a class="reference internal" href="../sql/alter-table.html"><span class="doc std std-doc">ALTER TABLE</span></a> statements.</p>
<section id="replace-tables">
<span id="delta-lake-create-or-replace"></span><h4 id="replace-tables">Replace tables<a class="headerlink" href="delta-lake.html#replace-tables" title="Link to this heading">#</a></h4>
<p>The connector supports replacing an existing table as an atomic operation.
Atomic table replacement creates a new snapshot with the new table definition as
part of the <a class="reference internal" href="delta-lake.html#delta-lake-history-table">table history</a>.</p>
<p>To replace a table, use <a class="reference internal" href="../sql/create-table.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">OR</span> <span class="pre">REPLACE</span> <span class="pre">TABLE</span></code></span></a> or
<a class="reference internal" href="../sql/create-table-as.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">OR</span> <span class="pre">REPLACE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></span></a>.</p>
<p>In this example, a table <code class="docutils literal notranslate"><span class="pre">example_table</span></code> is replaced by a completely new
definition and data from the source table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example_table</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">partitioned_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'a'</span><span class="p">])</span>
<span class="k">AS</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">another_table</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="alter-table-execute">
<span id="delta-lake-alter-table-execute"></span><h4 id="alter-table-execute">ALTER TABLE EXECUTE<a class="headerlink" href="delta-lake.html#alter-table-execute" title="Link to this heading">#</a></h4>
<p>The connector supports the following commands for use with <a class="reference internal" href="../sql/alter-table.html#alter-table-execute"><span class="std std-ref">ALTER TABLE EXECUTE</span></a>.</p>
<section id="optimize">
<h5 id="optimize">optimize<a class="headerlink" href="delta-lake.html#optimize" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">optimize</span></code> command is used for rewriting the content of the specified
table so that it is merged into fewer but larger files. If the table is
partitioned, the data compaction acts separately on each partition selected for
optimization. This operation improves read performance.</p>
<p>All files with a size below the optional <code class="docutils literal notranslate"><span class="pre">file_size_threshold</span></code> parameter
(default value for the threshold is <code class="docutils literal notranslate"><span class="pre">100MB</span></code>) are merged:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
</pre></div>
</div>
<p>The following statement merges files in a table that are
under 128 megabytes in size:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span><span class="p">(</span><span class="n">file_size_threshold</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'128MB'</span><span class="p">)</span>
</pre></div>
</div>
<p>You can use a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause with the columns used to partition the table
to filter which partitions are optimized:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_partitioned_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">partition_key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span>
</pre></div>
</div>
<p>You can use a more complex <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause to narrow down the scope of the
<code class="docutils literal notranslate"><span class="pre">optimize</span></code> procedure. The following example casts the timestamp values to
dates, and uses a comparison to only optimize partitions with data from the year
2022 or newer:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">timestamp_tz</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">DATE</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2021-12-31'</span>
</pre></div>
</div>
<p>Use a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause with <a class="reference internal" href="delta-lake.html#delta-lake-special-columns"><span class="std std-ref">metadata columns</span></a> to filter
which files are optimized.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$file_modified_time"</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">date_trunc</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">);</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$path"</span><span class="w"> </span><span class="o">&lt;&gt;</span><span class="w"> </span><span class="s1">'skipping-file-path'</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- optimze files smaller than 1MB</span>
<span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$file_size"</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1024</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">1024</span>
</pre></div>
</div>
</section>
</section>
<section id="alter-table-rename-to">
<span id="delta-lake-alter-table-rename-to"></span><h4 id="alter-table-rename-to">ALTER TABLE RENAME TO<a class="headerlink" href="delta-lake.html#alter-table-rename-to" title="Link to this heading">#</a></h4>
<p>The connector only supports the <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">RENAME</span> <span class="pre">TO</span></code> statement when met with
one of the following conditions:</p>
<ul class="simple">
<li><p>The table type is external.</p></li>
<li><p>The table is backed by a metastore that does not perform object storage
operations, for example, AWS Glue.</p></li>
</ul>
</section>
<section id="table-properties">
<h4 id="table-properties">Table properties<a class="headerlink" href="delta-lake.html#table-properties" title="Link to this heading">#</a></h4>
<p>The following table properties are available for use:</p>
<table id="id7">
<caption><span class="caption-text">Delta Lake table properties</span><a class="headerlink" href="delta-lake.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">location</span></code></p></td>
<td><p>File system location URI for the table.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">partitioned_by</span></code></p></td>
<td><p>Set partition columns.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">checkpoint_interval</span></code></p></td>
<td><p>Set the checkpoint interval in number of table writes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">change_data_feed_enabled</span></code></p></td>
<td><p>Enables storing change data feed entries.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">column_mapping_mode</span></code></p></td>
<td><p>Column mapping mode. Possible values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ID</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NAME</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></li>
</ul>
<p>Defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code>.</p>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">deletion_vectors_enabled</span></code></p></td>
<td><p>Enables deletion vectors.</p></td>
</tr>
</tbody>
</table>
<p>The following example uses all available table properties:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">example_partitioned_table</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">  </span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path'</span><span class="p">,</span>
<span class="w">  </span><span class="n">partitioned_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'regionkey'</span><span class="p">],</span>
<span class="w">  </span><span class="n">checkpoint_interval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">  </span><span class="n">change_data_feed_enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="p">,</span>
<span class="w">  </span><span class="n">column_mapping_mode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'name'</span><span class="p">,</span>
<span class="w">  </span><span class="n">deletion_vectors_enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span>
<span class="p">)</span>
<span class="k">AS</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="k">comment</span><span class="p">,</span><span class="w"> </span><span class="n">regionkey</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">tpch</span><span class="p">.</span><span class="n">tiny</span><span class="p">.</span><span class="n">nation</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="shallow-cloned-tables">
<span id="delta-lake-shallow-clone"></span><h4 id="shallow-cloned-tables">Shallow cloned tables<a class="headerlink" href="delta-lake.html#shallow-cloned-tables" title="Link to this heading">#</a></h4>
<p>The connector supports read and write operations on shallow cloned tables. Trino
does not support creating shallow clone tables. More information about shallow
cloning is available in the <a class="reference external" href="https://docs.delta.io/latest/delta-utility.html#shallow-clone-a-delta-table">Delta Lake
documentation</a>.</p>
<p>Shallow cloned tables let you test queries or experiment with changes to a table
without duplicating data.</p>
</section>
<section id="metadata-tables">
<h4 id="metadata-tables">Metadata tables<a class="headerlink" href="delta-lake.html#metadata-tables" title="Link to this heading">#</a></h4>
<p>The connector exposes several metadata tables for each Delta Lake table.
These metadata tables contain information about the internal structure
of the Delta Lake table. You can query each metadata table by appending the
metadata table name to the table name:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$history"</span>
</pre></div>
</div>
<section id="history-table">
<span id="delta-lake-history-table"></span><h5 id="history-table"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table<a class="headerlink" href="delta-lake.html#history-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$history</span></code> table provides a log of the metadata changes performed on
the Delta Lake table.</p>
<p>You can retrieve the changelog of the Delta Lake table <code class="docutils literal notranslate"><span class="pre">test_table</span></code>
by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$history"</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> version |               timestamp               | user_id | user_name |  operation   |         operation_parameters          |                 cluster_id      | read_version |  isolation_level  | is_blind_append | operation_metrics              
---------+---------------------------------------+---------+-----------+--------------+---------------------------------------+---------------------------------+--------------+-------------------+-----------------+-------------------
       2 | 2023-01-19 07:40:54.684 Europe/Vienna | trino   | trino     | WRITE        | {queryId=20230119_064054_00008_4vq5t} | trino-406-trino-coordinator     |            2 | WriteSerializable | true            | {}
       1 | 2023-01-19 07:40:41.373 Europe/Vienna | trino   | trino     | ADD COLUMNS  | {queryId=20230119_064041_00007_4vq5t} | trino-406-trino-coordinator     |            0 | WriteSerializable | true            | {}
       0 | 2023-01-19 07:40:10.497 Europe/Vienna | trino   | trino     | CREATE TABLE | {queryId=20230119_064010_00005_4vq5t} | trino-406-trino-coordinator     |            0 | WriteSerializable | true            | {}
</pre></div>
</div>
<p>The output of the query has the following history columns:</p>
<table id="id8">
<caption><span class="caption-text">History columns</span><a class="headerlink" href="delta-lake.html#id8" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">version</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The version of the table corresponding to the operation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p>The time when the table version became active
For tables with in-Commit timestamps enabled, this field returns value of
<a class="reference external" href="https://github.com/delta-io/delta/blob/master/PROTOCOL.md#in-commit-timestamps">inCommitTimestamp</a>,
Otherwise returns value of <code class="docutils literal notranslate"><span class="pre">timestamp</span></code> field that in the
<a class="reference external" href="https://github.com/delta-io/delta/blob/master/PROTOCOL.md#commit-provenance-information">commitInfo</a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">user_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The identifier for the user which performed the operation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">user_name</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The username for the user which performed the operation</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">operation</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The name of the operation performed on the table</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">operation_parameters</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(VARCHAR,</span> <span class="pre">VARCHAR)</span></code></p></td>
<td><p>Parameters of the operation</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">cluster_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The ID of the cluster which ran the operation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">read_version</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The version of the table which was read in order to perform the operation</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">isolation_level</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The level of isolation used to perform the operation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">is_blind_append</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p>Whether or not the operation appended data</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">operation_metrics</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(VARCHAR,</span> <span class="pre">VARCHAR)</span></code></p></td>
<td><p>Metrics of the operation</p></td>
</tr>
</tbody>
</table>
</section>
<section id="partitions-table">
<span id="delta-lake-partitions-table"></span><h5 id="partitions-table"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table<a class="headerlink" href="delta-lake.html#partitions-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table provides a detailed overview of the partitions of the
Delta Lake table.</p>
<p>You can retrieve the information about the partitions of the Delta Lake table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$partitions"</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>           partition           | file_count | total_size |                     data                     |
-------------------------------+------------+------------+----------------------------------------------+
{_bigint=1, _date=2021-01-12}  |          2 |        884 | {_decimal={min=1.0, max=2.0, null_count=0}}  |
{_bigint=1, _date=2021-01-13}  |          1 |        442 | {_decimal={min=1.0, max=1.0, null_count=0}}  |
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id9">
<caption><span class="caption-text">Partitions columns</span><a class="headerlink" href="delta-lake.html#id9" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 20%"/>
<col style="width: 30%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">partition</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
<td><p>A row that contains the mapping of the partition column names to the
partition column values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">file_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The number of files mapped in the partition.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">total_size</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The size of all the files in the partition.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">data</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...</span> <span class="pre">ROW</span> <span class="pre">(min</span> <span class="pre">...,</span> <span class="pre">max</span> <span class="pre">...</span> <span class="pre">,</span> <span class="pre">null_count</span> <span class="pre">BIGINT))</span></code></p></td>
<td><p>Partition range and null counts.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="properties-table">
<h5 id="properties-table"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table<a class="headerlink" href="delta-lake.html#properties-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$properties</span></code> table provides access to Delta Lake table configuration,
table features and table properties. The table rows are key/value pairs.</p>
<p>You can retrieve the properties of the Delta
table <code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$properties"</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> key                        | value           |
----------------------------+-----------------+
delta.minReaderVersion      | 1               |
delta.minWriterVersion      | 4               |
delta.columnMapping.mode    | name            |
delta.feature.columnMapping | supported       |
</pre></div>
</div>
</section>
</section>
<section id="metadata-columns">
<span id="delta-lake-special-columns"></span><h4 id="metadata-columns">Metadata columns<a class="headerlink" href="delta-lake.html#metadata-columns" title="Link to this heading">#</a></h4>
<p>In addition to the defined columns, the Delta Lake connector automatically
exposes metadata in a number of hidden columns in each table. You can use these
columns in your SQL statements like any other column, e.g., they can be selected
directly or used in conditional statements.</p>
<ul class="simple">
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">$path</span></code></dt><dd><p>Full file system path name of the file for this row.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">$file_modified_time</span></code></dt><dd><p>Date and time of the last modification of the file for this row.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">$file_size</span></code></dt><dd><p>Size of the file for this row.</p>
</dd>
</dl>
</li>
</ul>
</section>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="delta-lake.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides the following table functions:</p>
<section id="table-changes">
<h4 id="table-changes">table_changes<a class="headerlink" href="delta-lake.html#table-changes" title="Link to this heading">#</a></h4>
<p>Allows reading Change Data Feed (CDF) entries to expose row-level changes
between two versions of a Delta Lake table. When the <code class="docutils literal notranslate"><span class="pre">change_data_feed_enabled</span></code>
table property is set to <code class="docutils literal notranslate"><span class="pre">true</span></code> on a specific Delta Lake table,
the connector records change events for all data changes on the table.
This is how these changes can be read:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="k">system</span><span class="p">.</span><span class="n">table_changes</span><span class="p">(</span>
<span class="w">      </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'test_schema'</span><span class="p">,</span>
<span class="w">      </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'tableName'</span><span class="p">,</span>
<span class="w">      </span><span class="n">since_version</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">0</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">schema_name</span></code> - type <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, required, name of the schema for which the function is called</p>
<p><code class="docutils literal notranslate"><span class="pre">table_name</span></code> - type <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, required, name of the table for which the function is called</p>
<p><code class="docutils literal notranslate"><span class="pre">since_version</span></code> - type <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, optional, version from which changes are shown, exclusive</p>
<p>In addition to returning the columns present in the table, the function
returns the following values for each change event:</p>
<ul class="simple">
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_change_type</span></code></dt><dd><p>Gives the type of change that occurred. Possible values are <code class="docutils literal notranslate"><span class="pre">insert</span></code>,
<code class="docutils literal notranslate"><span class="pre">delete</span></code>, <code class="docutils literal notranslate"><span class="pre">update_preimage</span></code> and <code class="docutils literal notranslate"><span class="pre">update_postimage</span></code>.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_commit_version</span></code></dt><dd><p>Shows the table version for which the change occurred.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_commit_timestamp</span></code></dt><dd><p>Represents the timestamp for the commit in which the specified change happened.</p>
</dd>
</dl>
</li>
</ul>
<p>This is how it would be normally used:</p>
<p>Create table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span><span class="w"> </span><span class="p">(</span><span class="n">page_url</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="k">domain</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">views</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">)</span>
<span class="w">    </span><span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">change_data_feed_enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">true</span><span class="p">);</span>
</pre></div>
</div>
<p>Insert data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span>
<span class="w">    </span><span class="k">VALUES</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url2'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain2'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url3'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span>
<span class="w">    </span><span class="k">VALUES</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url4'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">400</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url5'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain2'</span><span class="p">,</span><span class="w"> </span><span class="mi">500</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url6'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain3'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span>
</pre></div>
</div>
<p>Update data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">UPDATE</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span>
<span class="w">    </span><span class="k">SET</span><span class="w"> </span><span class="k">domain</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'domain4'</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">views</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>
</pre></div>
</div>
<p>Select changes:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="k">system</span><span class="p">.</span><span class="n">table_changes</span><span class="p">(</span>
<span class="w">      </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'test_schema'</span><span class="p">,</span>
<span class="w">      </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'pages'</span><span class="p">,</span>
<span class="w">      </span><span class="n">since_version</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">1</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">)</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">_commit_version</span><span class="w"> </span><span class="k">ASC</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding sequence of SQL statements returns the following result:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>page_url    |     domain     |    views    |    _change_type     |    _commit_version    |    _commit_timestamp
url4        |     domain1    |    400      |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url5        |     domain2    |    500      |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url6        |     domain3    |    2        |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url2        |     domain2    |    2        |    update_preimage  |     3                 |    2023-03-10T22:23:24.000+0000
url2        |     domain4    |    2        |    update_postimage |     3                 |    2023-03-10T22:23:24.000+0000
url6        |     domain3    |    2        |    update_preimage  |     3                 |    2023-03-10T22:23:24.000+0000
url6        |     domain4    |    2        |    update_postimage |     3                 |    2023-03-10T22:23:24.000+0000
</pre></div>
</div>
<p>The output shows what changes happen in which version.
For example in version 3 two rows were modified, first one changed from
<code class="docutils literal notranslate"><span class="pre">('url2',</span> <span class="pre">'domain2',</span> <span class="pre">2)</span></code> into <code class="docutils literal notranslate"><span class="pre">('url2',</span> <span class="pre">'domain4',</span> <span class="pre">2)</span></code> and the second from
<code class="docutils literal notranslate"><span class="pre">('url6',</span> <span class="pre">'domain2',</span> <span class="pre">2)</span></code> into <code class="docutils literal notranslate"><span class="pre">('url6',</span> <span class="pre">'domain4',</span> <span class="pre">2)</span></code>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">since_version</span></code> is not provided the function produces change events
starting from when the table was created.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="k">system</span><span class="p">.</span><span class="n">table_changes</span><span class="p">(</span>
<span class="w">      </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'test_schema'</span><span class="p">,</span>
<span class="w">      </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'pages'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">)</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">_commit_version</span><span class="w"> </span><span class="k">ASC</span><span class="p">;</span>
</pre></div>
</div>
<p>The preceding SQL statement returns the following result:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>page_url    |     domain     |    views    |    _change_type     |    _commit_version    |    _commit_timestamp
url1        |     domain1    |    1        |    insert           |     1                 |    2023-03-10T20:21:22.000+0000
url2        |     domain2    |    2        |    insert           |     1                 |    2023-03-10T20:21:22.000+0000
url3        |     domain1    |    3        |    insert           |     1                 |    2023-03-10T20:21:22.000+0000
url4        |     domain1    |    400      |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url5        |     domain2    |    500      |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url6        |     domain3    |    2        |    insert           |     2                 |    2023-03-10T21:22:23.000+0000
url2        |     domain2    |    2        |    update_preimage  |     3                 |    2023-03-10T22:23:24.000+0000
url2        |     domain4    |    2        |    update_postimage |     3                 |    2023-03-10T22:23:24.000+0000
url6        |     domain3    |    2        |    update_preimage  |     3                 |    2023-03-10T22:23:24.000+0000
url6        |     domain4    |    2        |    update_postimage |     3                 |    2023-03-10T22:23:24.000+0000
</pre></div>
</div>
<p>You can see changes that occurred at version 1 as three inserts. They are
not visible in the previous statement when <code class="docutils literal notranslate"><span class="pre">since_version</span></code> value was set to 1.</p>
</section>
</section>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="delta-lake.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements detailed in the
following sections:</p>
<ul class="simple">
<li><p>Support for <a class="reference internal" href="../admin/properties-write-partitioning.html"><span class="doc">write partitioning</span></a>.</p></li>
</ul>
<section id="table-statistics">
<span id="delta-lake-table-statistics"></span><h3 id="table-statistics">Table statistics<a class="headerlink" href="delta-lake.html#table-statistics" title="Link to this heading">#</a></h3>
<p>Use <a class="reference internal" href="../sql/analyze.html"><span class="doc">ANALYZE</span></a> statements in Trino to populate data size and
number of distinct values (NDV) extended table statistics in Delta Lake.
The minimum value, maximum value, value count, and null value count
statistics are computed on the fly out of the transaction log of the
Delta Lake table. The <a class="reference internal" href="../optimizer/cost-based-optimizations.html"><span class="doc">cost-based optimizer</span></a> then uses these statistics to improve
query performance.</p>
<p>Extended statistics enable a broader set of optimizations, including join
reordering. The controlling catalog property <code class="docutils literal notranslate"><span class="pre">delta.table-statistics-enabled</span></code>
is enabled by default. The equivalent <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">catalog session property</span></a> is <code class="docutils literal notranslate"><span class="pre">statistics_enabled</span></code>.</p>
<p>Each <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> statement updates the table statistics incrementally, so only
the data changed since the last <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> is counted. The table statistics are
not automatically updated by write operations such as <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>,
and <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>. You must manually run <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> again to update the table
statistics.</p>
<p>To collect statistics for a table, execute the following statement:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">table_schema</span><span class="p">.</span><span class="k">table_name</span><span class="p">;</span>
</pre></div>
</div>
<p>To recalculate from scratch the statistics for the table use additional parameter <code class="docutils literal notranslate"><span class="pre">mode</span></code>:</p>
<blockquote>
<div><p>ANALYZE table_schema.table_name WITH(mode = ‘full_refresh’);</p>
</div></blockquote>
<p>There are two modes available <code class="docutils literal notranslate"><span class="pre">full_refresh</span></code> and <code class="docutils literal notranslate"><span class="pre">incremental</span></code>.
The procedure use <code class="docutils literal notranslate"><span class="pre">incremental</span></code> by default.</p>
<p>To gain the most benefit from cost-based optimizations, run periodic <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code>
statements on every large table that is frequently queried.</p>
<section id="fine-tuning">
<h4 id="fine-tuning">Fine-tuning<a class="headerlink" href="delta-lake.html#fine-tuning" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">files_modified_after</span></code> property is useful if you want to run the
<code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> statement on a table that was previously analyzed. You can use it to
limit the amount of data used to generate the table statistics:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">example_table</span><span class="w"> </span><span class="k">WITH</span><span class="p">(</span><span class="n">files_modified_after</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2021-08-23</span>
<span class="s1">16:43:01.321 Z'</span><span class="p">)</span>
</pre></div>
</div>
<p>As a result, only files newer than the specified time stamp are used in the
analysis.</p>
<p>You can also specify a set or subset of columns to analyze using the <code class="docutils literal notranslate"><span class="pre">columns</span></code>
property:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">example_table</span><span class="w"> </span><span class="k">WITH</span><span class="p">(</span><span class="n">columns</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'nationkey'</span><span class="p">,</span><span class="w"> </span><span class="s1">'regionkey'</span><span class="p">])</span>
</pre></div>
</div>
<p>To run <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> with <code class="docutils literal notranslate"><span class="pre">columns</span></code> more than once, the next <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code> must
run on the same set or a subset of the original columns used.</p>
<p>To broaden the set of <code class="docutils literal notranslate"><span class="pre">columns</span></code>, drop the statistics and reanalyze the table.</p>
</section>
<section id="disable-and-drop-extended-statistics">
<h4 id="disable-and-drop-extended-statistics">Disable and drop extended statistics<a class="headerlink" href="delta-lake.html#disable-and-drop-extended-statistics" title="Link to this heading">#</a></h4>
<p>You can disable extended statistics with the catalog configuration property
<code class="docutils literal notranslate"><span class="pre">delta.extended-statistics.enabled</span></code> set to <code class="docutils literal notranslate"><span class="pre">false</span></code>. Alternatively, you can
disable it for a session, with the <a class="reference internal" href="../sql/set-session.html"><span class="doc">catalog session property</span></a> <code class="docutils literal notranslate"><span class="pre">extended_statistics_enabled</span></code> set to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<p>If a table is changed with many delete and update operation, calling <code class="docutils literal notranslate"><span class="pre">ANALYZE</span></code>
does not result in accurate statistics. To correct the statistics, you have to
drop the extended statistics and analyze the table again.</p>
<p>Use the <code class="docutils literal notranslate"><span class="pre">system.drop_extended_stats</span></code> procedure in the catalog to drop the
extended statistics for a specified table in a specified schema:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">drop_extended_stats</span><span class="p">(</span><span class="s1">'example_schema'</span><span class="p">,</span><span class="w"> </span><span class="s1">'example_table'</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="memory-usage">
<h3 id="memory-usage">Memory usage<a class="headerlink" href="delta-lake.html#memory-usage" title="Link to this heading">#</a></h3>
<p>The Delta Lake connector is memory intensive and the amount of required memory
grows with the size of Delta Lake transaction logs of any accessed tables. It is
important to take that into account when provisioning the coordinator.</p>
<p>You must decrease memory usage by keeping the number of active data files in
the table low by regularly running <code class="docutils literal notranslate"><span class="pre">OPTIMIZE</span></code> and <code class="docutils literal notranslate"><span class="pre">VACUUM</span></code> in Delta Lake.</p>
<section id="memory-monitoring">
<h4 id="memory-monitoring">Memory monitoring<a class="headerlink" href="delta-lake.html#memory-monitoring" title="Link to this heading">#</a></h4>
<p>When using the Delta Lake connector, you must monitor memory usage on the
coordinator. Specifically, monitor JVM heap utilization using standard tools as
part of routine operation of the cluster.</p>
<p>A good proxy for memory usage is the cache utilization of Delta Lake caches. It
is exposed by the connector with the
<code class="docutils literal notranslate"><span class="pre">plugin.deltalake.transactionlog:name=&lt;catalog-name&gt;,type=transactionlogaccess</span></code>
JMX bean.</p>
<p>You can access it with any standard monitoring software with JMX support, or use
the <a class="reference internal" href="jmx.html"><span class="doc">JMX connector</span></a> with the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">jmx</span><span class="p">.</span><span class="k">current</span><span class="p">.</span><span class="ss">"*.plugin.deltalake.transactionlog:name=&lt;catalog-name&gt;,type=transactionlogaccess"</span>
</pre></div>
</div>
<p>Following is an example result:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>datafilemetadatacachestats.hitrate      | 0.97
datafilemetadatacachestats.missrate     | 0.03
datafilemetadatacachestats.requestcount | 3232
metadatacachestats.hitrate              | 0.98
metadatacachestats.missrate             | 0.02
metadatacachestats.requestcount         | 6783
node                                    | trino-master
object_name                             | io.trino.plugin.deltalake.transactionlog:type=TransactionLogAccess,name=delta
</pre></div>
</div>
<p>In a healthy system, both <code class="docutils literal notranslate"><span class="pre">datafilemetadatacachestats.hitrate</span></code> and
<code class="docutils literal notranslate"><span class="pre">metadatacachestats.hitrate</span></code> are close to <code class="docutils literal notranslate"><span class="pre">1.0</span></code>.</p>
</section>
</section>
<section id="table-redirection">
<span id="delta-lake-table-redirection"></span><h3 id="table-redirection">Table redirection<a class="headerlink" href="delta-lake.html#table-redirection" title="Link to this heading">#</a></h3>
<p>Trino offers the possibility to transparently redirect operations on an existing
table to the appropriate catalog based on the format of the table and catalog configuration.</p>
<p>In the context of connectors which depend on a metastore service
(for example, <a class="reference internal" href="hive.html"><span class="doc">Hive connector</span></a>, <a class="reference internal" href="iceberg.html"><span class="doc">Iceberg connector</span></a> and <a class="reference internal" href="delta-lake.html#"><span class="doc">Delta Lake connector</span></a>),
the metastore (Hive metastore service, <a class="reference external" href="https://aws.amazon.com/glue/">AWS Glue Data Catalog</a>)
can be used to accustom tables with different table formats.
Therefore, a metastore database can hold a variety of tables with different table formats.</p>
<p>As a concrete example, let’s use the following
simple scenario which makes use of table redirection:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>

<span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example_table</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                               Query Plan
-------------------------------------------------------------------------
Fragment 0 [SOURCE]
     ...
     Output[columnNames = [...]]
     │   ...
     └─ TableScan[table = another_catalog:example_schema:example_table]
            ...
</pre></div>
</div>
<p>The output of the <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code> statement points out the actual
catalog which is handling the <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> query over the table <code class="docutils literal notranslate"><span class="pre">example_table</span></code>.</p>
<p>The table redirection functionality works also when using
fully qualified names for the tables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">.</span><span class="n">example_table</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                               Query Plan
-------------------------------------------------------------------------
Fragment 0 [SOURCE]
     ...
     Output[columnNames = [...]]
     │   ...
     └─ TableScan[table = another_catalog:example_schema:example_table]
            ...
</pre></div>
</div>
<p>Trino offers table redirection support for the following operations:</p>
<ul class="simple">
<li><p>Table read operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/describe.html"><span class="doc">DESCRIBE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-stats.html"><span class="doc">SHOW STATS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-table.html"><span class="doc">SHOW CREATE TABLE</span></a></p></li>
</ul>
</li>
<li><p>Table write operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/update.html"><span class="doc">UPDATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/merge.html"><span class="doc">MERGE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
</ul>
</li>
<li><p>Table management operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
</li>
</ul>
<p>Trino does not offer view redirection support.</p>
<p>The connector supports redirection from Delta Lake tables to Hive tables
with the <code class="docutils literal notranslate"><span class="pre">delta.hive-catalog-name</span></code> catalog configuration property.</p>
</section>
<section id="performance-tuning-configuration-properties">
<h3 id="performance-tuning-configuration-properties">Performance tuning configuration properties<a class="headerlink" href="delta-lake.html#performance-tuning-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following table describes performance tuning catalog properties specific to
the Delta Lake connector.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Performance tuning configuration properties are considered expert-level
features. Altering these properties from their default values is likely to
cause instability and performance degradation. It is strongly suggested that
you use them only to address non-trivial performance issues, and that you
keep a backup of the original values if you change them.</p>
</div>
<table id="id10">
<caption><span class="caption-text">Delta Lake performance tuning configuration properties</span><a class="headerlink" href="delta-lake.html#id10" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 50%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.domain-compaction-threshold</span></code></p></td>
<td><p>Minimum size of query predicates above which Trino compacts the predicates.
Pushing a large list of predicates down to the data source can compromise
performance. For optimization in that situation, Trino can compact the large
predicates. If necessary, adjust the threshold to ensure a balance between
performance and predicate pushdown.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.max-outstanding-splits</span></code></p></td>
<td><p>The target number of buffered splits for each table scan in a query, before
the scheduler tries to pause.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.max-splits-per-second</span></code></p></td>
<td><p>Sets the maximum number of splits used per second to access underlying
storage. Reduce this number if your limit is routinely exceeded, based on
your filesystem limits. This is set to the absolute maximum value, which
results in Trino maximizing the parallelization of data access by default.
Attempting to set it higher results in Trino not being able to start.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Integer.MAX_VALUE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.max-split-size</span></code></p></td>
<td><p>Sets the largest <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> for a single read section
assigned to a worker after <code class="docutils literal notranslate"><span class="pre">max-initial-splits</span></code> have been processed. You can
also use the corresponding catalog session property
<code class="docutils literal notranslate"><span class="pre">&lt;catalog-name&gt;.max_split_size</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">128MB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.minimum-assigned-split-weight</span></code></p></td>
<td><p>A decimal value in the range (0, 1] used as a minimum for weights assigned
to each split. A low value might improve performance on tables with small
files. A higher value might improve performance for queries with highly
skewed aggregations or joins.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0.05</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">delta.projection-pushdown-enabled</span></code></p></td>
<td><p>Read only projected fields from row columns while performing <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.query-partition-filter-required</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to force a query to use a partition filter. You can use the
<code class="docutils literal notranslate"><span class="pre">query_partition_filter_required</span></code> catalog session property for temporary,
catalog specific use.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="file-system-cache">
<h3 id="file-system-cache">File system cache<a class="headerlink" href="delta-lake.html#file-system-cache" title="Link to this heading">#</a></h3>
<p>The connector supports configuring and using <a class="reference internal" href="../object-storage/file-system-cache.html"><span class="doc std std-doc">file system
caching</span></a>.</p>
<p>The following table describes file system cache properties specific to
the Delta Lake connector.</p>
<table id="id11">
<caption><span class="caption-text">Delta Lake file system cache configuration properties</span><a class="headerlink" href="delta-lake.html#id11" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 50%"/>
<col style="width: 20%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">delta.fs.cache.disable-transaction-log-caching</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to disable caching of the <code class="docutils literal notranslate"><span class="pre">_delta_log</span></code> directory of
Delta Tables. This is useful in those cases when Delta Tables are
destroyed and recreated, and the files inside the transaction log
directory get overwritten and cannot be safely cached. Effective
only when <code class="docutils literal notranslate"><span class="pre">fs.cache.enabled=true</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="clickhouse.html" title="ClickHouse connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> ClickHouse connector </span>
              </div>
            </a>
          
          
            <a href="druid.html" title="Druid connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Druid connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>