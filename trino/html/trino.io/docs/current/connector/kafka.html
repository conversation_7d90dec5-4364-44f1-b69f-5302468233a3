<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Kafka connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="kafka.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Kafka connector tutorial" href="kafka-tutorial.html" />
    <link rel="prev" title="JMX connector" href="jmx.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="kafka.html#connector/kafka" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Kafka connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Kafka </label>
    
      <a href="kafka.html#" class="md-nav__link md-nav__link--active">Kafka</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kafka.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#multiple-kafka-clusters" class="md-nav__link">Multiple Kafka clusters</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#log-levels" class="md-nav__link">Log levels</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#kafka-default-schema" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.default-schema</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-buffer-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.buffer-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-timestamp-upper-bound-force-push-down-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.timestamp-upper-bound-force-push-down-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-hide-internal-columns" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.hide-internal-columns</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-security-protocol" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.security-protocol</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-key-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.key.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-endpoint-identification-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.endpoint-identification-algorithm</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#internal-columns" class="md-nav__link">Internal columns</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#table-schema-and-schema-registry-usage" class="md-nav__link">Table schema and schema registry usage</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#file-table-description-supplier" class="md-nav__link">File table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#kafka-table-names" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.table-names</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-table-description-dir" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.table-description-dir</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#table-definition-files" class="md-nav__link">Table definition files</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#key-and-message-in-kafka" class="md-nav__link">Key and message in Kafka</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#confluent-table-description-supplier" class="md-nav__link">Confluent table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#confluent-subject-to-table-name-mapping" class="md-nav__link">Confluent subject to table name mapping</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-specific-type-handling-in-confluent-table-description-supplier" class="md-nav__link">Protobuf-specific type handling in Confluent table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#oneof" class="md-nav__link">oneof</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-inserts" class="md-nav__link">Kafka inserts</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#row-encoding" class="md-nav__link">Row encoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#raw-encoder" class="md-nav__link">Raw encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#csv-encoder" class="md-nav__link">CSV encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#json-encoder" class="md-nav__link">JSON encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#avro-encoder" class="md-nav__link">Avro encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-encoder" class="md-nav__link">Protobuf encoder</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#row-decoding" class="md-nav__link">Row decoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#raw-decoder" class="md-nav__link">Raw decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#csv-decoder" class="md-nav__link">CSV decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#json-decoder" class="md-nav__link">JSON decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#default-field-decoder" class="md-nav__link">Default field decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#date-and-time-decoders" class="md-nav__link">Date and time decoders</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#avro-decoder" class="md-nav__link">Avro decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#avro-schema-evolution" class="md-nav__link">Avro schema evolution</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-decoder" class="md-nav__link">Protobuf decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#any" class="md-nav__link">any</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-schema-evolution" class="md-nav__link">Protobuf schema evolution</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-limitations" class="md-nav__link">Protobuf limitations</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="kafka-tutorial.html" class="md-nav__link">Tutorial</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kafka.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#multiple-kafka-clusters" class="md-nav__link">Multiple Kafka clusters</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#log-levels" class="md-nav__link">Log levels</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#kafka-default-schema" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.default-schema</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-buffer-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.buffer-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-timestamp-upper-bound-force-push-down-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.timestamp-upper-bound-force-push-down-enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-hide-internal-columns" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.hide-internal-columns</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-security-protocol" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.security-protocol</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-keystore-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-location" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.location</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-truststore-type" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.type</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-key-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.key.password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-ssl-endpoint-identification-algorithm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.endpoint-identification-algorithm</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#internal-columns" class="md-nav__link">Internal columns</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#table-schema-and-schema-registry-usage" class="md-nav__link">Table schema and schema registry usage</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#file-table-description-supplier" class="md-nav__link">File table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#kafka-table-names" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.table-names</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-table-description-dir" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">kafka.table-description-dir</span></code></a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#table-definition-files" class="md-nav__link">Table definition files</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#key-and-message-in-kafka" class="md-nav__link">Key and message in Kafka</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#confluent-table-description-supplier" class="md-nav__link">Confluent table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#confluent-subject-to-table-name-mapping" class="md-nav__link">Confluent subject to table name mapping</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-specific-type-handling-in-confluent-table-description-supplier" class="md-nav__link">Protobuf-specific type handling in Confluent table description supplier</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#oneof" class="md-nav__link">oneof</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#kafka-inserts" class="md-nav__link">Kafka inserts</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#row-encoding" class="md-nav__link">Row encoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#raw-encoder" class="md-nav__link">Raw encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#csv-encoder" class="md-nav__link">CSV encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#json-encoder" class="md-nav__link">JSON encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#avro-encoder" class="md-nav__link">Avro encoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-encoder" class="md-nav__link">Protobuf encoder</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#row-decoding" class="md-nav__link">Row decoding</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#raw-decoder" class="md-nav__link">Raw decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#csv-decoder" class="md-nav__link">CSV decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#json-decoder" class="md-nav__link">JSON decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#default-field-decoder" class="md-nav__link">Default field decoder</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#date-and-time-decoders" class="md-nav__link">Date and time decoders</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#avro-decoder" class="md-nav__link">Avro decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#avro-schema-evolution" class="md-nav__link">Avro schema evolution</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-decoder" class="md-nav__link">Protobuf decoder</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka.html#any" class="md-nav__link">any</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-schema-evolution" class="md-nav__link">Protobuf schema evolution</a>
        </li>
        <li class="md-nav__item"><a href="kafka.html#protobuf-limitations" class="md-nav__link">Protobuf limitations</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="kafka-connector">
<h1 id="connector-kafka--page-root">Kafka connector<a class="headerlink" href="kafka.html#connector-kafka--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/kafka.png"/><div class="toctree-wrapper compound">
</div>
<p>This connector allows the use of <a class="reference external" href="https://kafka.apache.org/">Apache Kafka</a>
topics as tables in Trino. Each message is presented as a row in Trino.</p>
<p>Topics can be live. Rows appear as data arrives, and disappear as
segments get dropped. This can result in strange behavior if accessing the
same table multiple times in a single query (e.g., performing a self join).</p>
<p>The connector reads and writes message data from Kafka topics in parallel across
workers to achieve a significant performance gain. The size of data sets for this
parallelization is configurable and can therefore be adapted to your specific
needs.</p>
<p>See the <a class="reference internal" href="kafka-tutorial.html"><span class="doc">Kafka connector tutorial</span></a>.</p>
<section id="requirements">
<span id="kafka-requirements"></span><h2 id="requirements">Requirements<a class="headerlink" href="kafka.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to Kafka, you need:</p>
<ul class="simple">
<li><p>Kafka broker version 3.3 or higher (with KRaft enabled).</p></li>
<li><p>Network access from the Trino coordinator and workers to the Kafka nodes.
Port 9092 is the default port.</p></li>
</ul>
<p>When using Protobuf decoder with the <a class="reference internal" href="kafka.html#confluent-table-description-supplier"><span class="std std-ref">Confluent table description supplier</span></a>, the following additional steps
must be taken:</p>
<ul class="simple">
<li><p>Copy the <code class="docutils literal notranslate"><span class="pre">kafka-protobuf-provider</span></code> and <code class="docutils literal notranslate"><span class="pre">kafka-protobuf-types</span></code> JAR files
from <a class="reference external" href="https://packages.confluent.io/maven/io/confluent/">Confluent</a> for
Confluent version 7.9.0 to the Kafka connector plugin directory (<code class="docutils literal notranslate"><span class="pre">&lt;install</span> <span class="pre">directory&gt;/plugin/kafka</span></code>) on all nodes in the cluster.
The plugin directory depends on the <a class="reference internal" href="../installation.html"><span class="doc">Installation</span></a> method.</p></li>
<li><p>By copying those JARs and using them, you agree to the terms of the <a class="reference external" href="https://github.com/confluentinc/schema-registry/blob/master/LICENSE-ConfluentCommunity">Confluent
Community License Agreement</a>
under which Confluent makes them available.</p></li>
</ul>
<p>These steps are not required if you are not using Protobuf and Confluent table
description supplier.</p>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="kafka.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Kafka connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following content, replacing the
properties as appropriate.</p>
<p>In some cases, such as when using specialized authentication methods, it is necessary to specify
additional Kafka client properties in order to access your Kafka cluster. To do so,
add the <code class="docutils literal notranslate"><span class="pre">kafka.config.resources</span></code> property to reference your Kafka config files. Note that configs
can be overwritten if defined explicitly in <code class="docutils literal notranslate"><span class="pre">kafka.properties</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=kafka
kafka.table-names=table1,table2
kafka.nodes=host1:port,host2:port
kafka.config.resources=/etc/kafka-configuration.properties
</pre></div>
</div>
<section id="multiple-kafka-clusters">
<h3 id="multiple-kafka-clusters">Multiple Kafka clusters<a class="headerlink" href="kafka.html#multiple-kafka-clusters" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need, so if you have additional
Kafka clusters, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name (making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>). For
example, if you name the property file <code class="docutils literal notranslate"><span class="pre">sales.properties</span></code>, Trino
creates a catalog named <code class="docutils literal notranslate"><span class="pre">sales</span></code> using the configured connector.</p>
</section>
<section id="log-levels">
<h3 id="log-levels">Log levels<a class="headerlink" href="kafka.html#log-levels" title="Link to this heading">#</a></h3>
<p>Kafka consumer logging can be verbose and pollute Trino logs. To lower the
<a class="reference internal" href="../admin/logging.html#logging-configuration"><span class="std std-ref">log level</span></a>, simply add the following to <code class="docutils literal notranslate"><span class="pre">etc/log.properties</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>org.apache.kafka=WARN
</pre></div>
</div>
</section>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="kafka.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.default-schema</span></code></p></td>
<td><p>Default schema name for tables.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.nodes</span></code></p></td>
<td><p>List of nodes in the Kafka cluster.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.buffer-size</span></code></p></td>
<td><p>Kafka read buffer size.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.hide-internal-columns</span></code></p></td>
<td><p>Controls whether internal columns are part of the table schema or not.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.internal-column-prefix</span></code></p></td>
<td><p>Prefix for internal columns, defaults to <code class="docutils literal notranslate"><span class="pre">_</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.messages-per-split</span></code></p></td>
<td><p>Number of messages that are processed by each Trino split; defaults to <code class="docutils literal notranslate"><span class="pre">100000</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.protobuf-any-support-enabled</span></code></p></td>
<td><p>Enable support for encoding Protobuf <code class="docutils literal notranslate"><span class="pre">any</span></code> types to <code class="docutils literal notranslate"><span class="pre">JSON</span></code> by setting the property to <code class="docutils literal notranslate"><span class="pre">true</span></code>, defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.timestamp-upper-bound-force-push-down-enabled</span></code></p></td>
<td><p>Controls if upper bound timestamp pushdown is enabled for topics using <code class="docutils literal notranslate"><span class="pre">CreateTime</span></code> mode.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.security-protocol</span></code></p></td>
<td><p>Security protocol for connection to Kafka cluster; defaults to <code class="docutils literal notranslate"><span class="pre">PLAINTEXT</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.location</span></code></p></td>
<td><p>Location of the keystore file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.password</span></code></p></td>
<td><p>Password for the keystore file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.type</span></code></p></td>
<td><p>File format of the keystore file; defaults to <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.location</span></code></p></td>
<td><p>Location of the truststore file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.password</span></code></p></td>
<td><p>Password for the truststore file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.type</span></code></p></td>
<td><p>File format of the truststore file; defaults to <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.key.password</span></code></p></td>
<td><p>Password for the private key in the keystore file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.ssl.endpoint-identification-algorithm</span></code></p></td>
<td><p>Endpoint identification algorithm used by clients to validate server host name; defaults to <code class="docutils literal notranslate"><span class="pre">https</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.config.resources</span></code></p></td>
<td><p>A comma-separated list of Kafka client configuration files. These files must exist on the machines running Trino. Only specify this if absolutely necessary to access Kafka. Example: <code class="docutils literal notranslate"><span class="pre">/etc/kafka-configuration.properties</span></code></p></td>
</tr>
</tbody>
</table>
<p>In addition, you must configure <a class="reference internal" href="kafka.html#kafka-table-schema-registry"><span class="std std-ref">table schema and schema registry usage</span></a> with the relevant properties.</p>
<section id="kafka-default-schema">
<h3 id="kafka-default-schema"><code class="docutils literal notranslate"><span class="pre">kafka.default-schema</span></code><a class="headerlink" href="kafka.html#kafka-default-schema" title="Link to this heading">#</a></h3>
<p>Defines the schema which contains all tables that were defined without
a qualifying schema name.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">default</span></code>.</p>
</section>
<section id="kafka-nodes">
<h3 id="kafka-nodes"><code class="docutils literal notranslate"><span class="pre">kafka.nodes</span></code><a class="headerlink" href="kafka.html#kafka-nodes" title="Link to this heading">#</a></h3>
<p>A comma separated list of <code class="docutils literal notranslate"><span class="pre">hostname:port</span></code> pairs for the Kafka data nodes.</p>
<p>This property is required; there is no default and at least one node must be defined.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Trino must still be able to connect to all nodes of the cluster
even if only a subset is specified here, as segment files may be
located only on a specific node.</p>
</div>
</section>
<section id="kafka-buffer-size">
<h3 id="kafka-buffer-size"><code class="docutils literal notranslate"><span class="pre">kafka.buffer-size</span></code><a class="headerlink" href="kafka.html#kafka-buffer-size" title="Link to this heading">#</a></h3>
<p>Size of the internal data buffer for reading data from Kafka. The data
buffer must be able to hold at least one message and ideally can hold many
messages. There is one data buffer allocated per worker and data node.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">64kb</span></code>.</p>
</section>
<section id="kafka-timestamp-upper-bound-force-push-down-enabled">
<h3 id="kafka-timestamp-upper-bound-force-push-down-enabled"><code class="docutils literal notranslate"><span class="pre">kafka.timestamp-upper-bound-force-push-down-enabled</span></code><a class="headerlink" href="kafka.html#kafka-timestamp-upper-bound-force-push-down-enabled" title="Link to this heading">#</a></h3>
<p>The upper bound predicate on <code class="docutils literal notranslate"><span class="pre">_timestamp</span></code> column
is pushed down only for topics using <code class="docutils literal notranslate"><span class="pre">LogAppendTime</span></code> mode.</p>
<p>For topics using <code class="docutils literal notranslate"><span class="pre">CreateTime</span></code> mode, upper bound pushdown must be explicitly
enabled via <code class="docutils literal notranslate"><span class="pre">kafka.timestamp-upper-bound-force-push-down-enabled</span></code> config property
or <code class="docutils literal notranslate"><span class="pre">timestamp_upper_bound_force_push_down_enabled</span></code> session property.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="kafka-hide-internal-columns">
<h3 id="kafka-hide-internal-columns"><code class="docutils literal notranslate"><span class="pre">kafka.hide-internal-columns</span></code><a class="headerlink" href="kafka.html#kafka-hide-internal-columns" title="Link to this heading">#</a></h3>
<p>In addition to the data columns defined in a table description file, the
connector maintains a number of additional columns for each table. If
these columns are hidden, they can still be used in queries but do not
show up in <code class="docutils literal notranslate"><span class="pre">DESCRIBE</span> <span class="pre">&lt;table-name&gt;</span></code> or <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span></code>.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
</section>
<section id="kafka-security-protocol">
<h3 id="kafka-security-protocol"><code class="docutils literal notranslate"><span class="pre">kafka.security-protocol</span></code><a class="headerlink" href="kafka.html#kafka-security-protocol" title="Link to this heading">#</a></h3>
<p>Protocol used to communicate with brokers.
Valid values are: <code class="docutils literal notranslate"><span class="pre">PLAINTEXT</span></code>, <code class="docutils literal notranslate"><span class="pre">SSL</span></code>.</p>
<p>This property is optional; default is <code class="docutils literal notranslate"><span class="pre">PLAINTEXT</span></code>.</p>
</section>
<section id="kafka-ssl-keystore-location">
<h3 id="kafka-ssl-keystore-location"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.location</span></code><a class="headerlink" href="kafka.html#kafka-ssl-keystore-location" title="Link to this heading">#</a></h3>
<p>Location of the keystore file used for connection to Kafka cluster.</p>
<p>This property is optional.</p>
</section>
<section id="kafka-ssl-keystore-password">
<h3 id="kafka-ssl-keystore-password"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.password</span></code><a class="headerlink" href="kafka.html#kafka-ssl-keystore-password" title="Link to this heading">#</a></h3>
<p>Password for the keystore file used for connection to Kafka cluster.</p>
<p>This property is optional, but required when <code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.location</span></code> is given.</p>
</section>
<section id="kafka-ssl-keystore-type">
<h3 id="kafka-ssl-keystore-type"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.keystore.type</span></code><a class="headerlink" href="kafka.html#kafka-ssl-keystore-type" title="Link to this heading">#</a></h3>
<p>File format of the keystore file.
Valid values are: <code class="docutils literal notranslate"><span class="pre">JKS</span></code>, <code class="docutils literal notranslate"><span class="pre">PKCS12</span></code>.</p>
<p>This property is optional; default is <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p>
</section>
<section id="kafka-ssl-truststore-location">
<h3 id="kafka-ssl-truststore-location"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.location</span></code><a class="headerlink" href="kafka.html#kafka-ssl-truststore-location" title="Link to this heading">#</a></h3>
<p>Location of the truststore file used for connection to Kafka cluster.</p>
<p>This property is optional.</p>
</section>
<section id="kafka-ssl-truststore-password">
<h3 id="kafka-ssl-truststore-password"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.password</span></code><a class="headerlink" href="kafka.html#kafka-ssl-truststore-password" title="Link to this heading">#</a></h3>
<p>Password for the truststore file used for connection to Kafka cluster.</p>
<p>This property is optional, but required when <code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.location</span></code> is given.</p>
</section>
<section id="kafka-ssl-truststore-type">
<h3 id="kafka-ssl-truststore-type"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.truststore.type</span></code><a class="headerlink" href="kafka.html#kafka-ssl-truststore-type" title="Link to this heading">#</a></h3>
<p>File format of the truststore file.
Valid values are: JKS, PKCS12.</p>
<p>This property is optional; default is <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p>
</section>
<section id="kafka-ssl-key-password">
<h3 id="kafka-ssl-key-password"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.key.password</span></code><a class="headerlink" href="kafka.html#kafka-ssl-key-password" title="Link to this heading">#</a></h3>
<p>Password for the private key in the keystore file used for connection to Kafka cluster.</p>
<p>This property is optional. This is required for clients only if two-way authentication is configured, i.e. <code class="docutils literal notranslate"><span class="pre">ssl.client.auth=required</span></code>.</p>
</section>
<section id="kafka-ssl-endpoint-identification-algorithm">
<h3 id="kafka-ssl-endpoint-identification-algorithm"><code class="docutils literal notranslate"><span class="pre">kafka.ssl.endpoint-identification-algorithm</span></code><a class="headerlink" href="kafka.html#kafka-ssl-endpoint-identification-algorithm" title="Link to this heading">#</a></h3>
<p>The endpoint identification algorithm used by clients to validate server host name for connection to Kafka cluster.
Kafka uses <code class="docutils literal notranslate"><span class="pre">https</span></code> as default. Use <code class="docutils literal notranslate"><span class="pre">disabled</span></code> to disable server host name validation.</p>
<p>This property is optional; default is <code class="docutils literal notranslate"><span class="pre">https</span></code>.</p>
</section>
</section>
<section id="internal-columns">
<h2 id="internal-columns">Internal columns<a class="headerlink" href="kafka.html#internal-columns" title="Link to this heading">#</a></h2>
<p>The internal column prefix is configurable by <code class="docutils literal notranslate"><span class="pre">kafka.internal-column-prefix</span></code>
configuration property and defaults to <code class="docutils literal notranslate"><span class="pre">_</span></code>. A different prefix affects the
internal column names in the following sections. For example, a value of
<code class="docutils literal notranslate"><span class="pre">internal_</span></code> changes the partition ID column name from <code class="docutils literal notranslate"><span class="pre">_partition_id</span></code>
to <code class="docutils literal notranslate"><span class="pre">internal_partition_id</span></code>.</p>
<p>For each defined table, the connector maintains the following columns:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Column name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_partition_id</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>ID of the Kafka partition which contains this row.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_partition_offset</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Offset within the Kafka partition for this row.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_segment_start</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Lowest offset in the segment (inclusive) which contains this row. This offset is partition specific.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_segment_end</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Highest offset in the segment (exclusive) which contains this row. The offset is partition specific. This is the same value as <code class="docutils literal notranslate"><span class="pre">_segment_start</span></code> of the next segment (if it exists).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_segment_count</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Running count for the current row within the segment. For an uncompacted topic, <code class="docutils literal notranslate"><span class="pre">_segment_start</span> <span class="pre">+</span> <span class="pre">_segment_count</span></code> is equal to <code class="docutils literal notranslate"><span class="pre">_partition_offset</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_message_corrupt</span></code></p></td>
<td><p>BOOLEAN</p></td>
<td><p>True if the decoder could not decode the message for this row. When true, data columns mapped from the message should be treated as invalid.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_message</span></code></p></td>
<td><p>VARCHAR</p></td>
<td><p>Message bytes as a UTF-8 encoded string. This is only useful for a text topic.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_message_length</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Number of bytes in the message.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_headers</span></code></p></td>
<td><p>map(VARCHAR, array(VARBINARY))</p></td>
<td><p>Headers of the message where values with the same key are grouped as array.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_key_corrupt</span></code></p></td>
<td><p>BOOLEAN</p></td>
<td><p>True if the key decoder could not decode the key for this row. When true, data columns mapped from the key should be treated as invalid.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_key</span></code></p></td>
<td><p>VARCHAR</p></td>
<td><p>Key bytes as a UTF-8 encoded string. This is only useful for textual keys.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">_key_length</span></code></p></td>
<td><p>BIGINT</p></td>
<td><p>Number of bytes in the key.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">_timestamp</span></code></p></td>
<td><p>TIMESTAMP</p></td>
<td><p>Message timestamp.</p></td>
</tr>
</tbody>
</table>
<p>For tables without a table definition file, the <code class="docutils literal notranslate"><span class="pre">_key_corrupt</span></code> and
<code class="docutils literal notranslate"><span class="pre">_message_corrupt</span></code> columns will always be <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="table-schema-and-schema-registry-usage">
<span id="kafka-table-schema-registry"></span><h2 id="table-schema-and-schema-registry-usage">Table schema and schema registry usage<a class="headerlink" href="kafka.html#table-schema-and-schema-registry-usage" title="Link to this heading">#</a></h2>
<p>The table schema for the messages can be supplied to the connector with a
configuration file or a schema registry. It also provides a mechanism for the
connector to discover tables.</p>
<p>You must configure the supplier with the <code class="docutils literal notranslate"><span class="pre">kafka.table-description-supplier</span></code>
property, setting it to <code class="docutils literal notranslate"><span class="pre">FILE</span></code> or <code class="docutils literal notranslate"><span class="pre">CONFLUENT</span></code>. Each table description
supplier has a separate set of configuration properties.</p>
<p>Refer to the following subsections for more detail. The <code class="docutils literal notranslate"><span class="pre">FILE</span></code> table
description supplier is the default, and the value is case-insensitive.</p>
<section id="file-table-description-supplier">
<h3 id="file-table-description-supplier">File table description supplier<a class="headerlink" href="kafka.html#file-table-description-supplier" title="Link to this heading">#</a></h3>
<p>In order to use the file-based table description supplier, the
<code class="docutils literal notranslate"><span class="pre">kafka.table-description-supplier</span></code> must be set to <code class="docutils literal notranslate"><span class="pre">FILE</span></code>, which is the
default.</p>
<p>In addition, you must set <code class="docutils literal notranslate"><span class="pre">kafka.table-names</span></code> and
<code class="docutils literal notranslate"><span class="pre">kafka.table-description-dir</span></code> as described in the following sections:</p>
<section id="kafka-table-names">
<h4 id="kafka-table-names"><code class="docutils literal notranslate"><span class="pre">kafka.table-names</span></code><a class="headerlink" href="kafka.html#kafka-table-names" title="Link to this heading">#</a></h4>
<p>Comma-separated list of all tables provided by this catalog. A table name can be
unqualified (simple name), and is placed into the default schema (see
below), or it can be qualified with a schema name
(<code class="docutils literal notranslate"><span class="pre">&lt;schema-name&gt;.&lt;table-name&gt;</span></code>).</p>
<p>For each table defined here, a table description file (see below) may exist. If
no table description file exists, the table name is used as the topic name on
Kafka, and no data columns are mapped into the table. The table still contains
all internal columns (see below).</p>
<p>This property is required; there is no default and at least one table must be
defined.</p>
</section>
<section id="kafka-table-description-dir">
<h4 id="kafka-table-description-dir"><code class="docutils literal notranslate"><span class="pre">kafka.table-description-dir</span></code><a class="headerlink" href="kafka.html#kafka-table-description-dir" title="Link to this heading">#</a></h4>
<p>References a folder within Trino deployment that holds one or more JSON files
(must end with <code class="docutils literal notranslate"><span class="pre">.json</span></code>) which contain table description files.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">etc/kafka</span></code>.</p>
</section>
<section id="table-definition-files">
<span id="id1"></span><h4 id="table-definition-files">Table definition files<a class="headerlink" href="kafka.html#table-definition-files" title="Link to this heading">#</a></h4>
<p>Kafka maintains topics only as byte messages and leaves it to producers
and consumers to define how a message should be interpreted. For Trino,
this data must be mapped into columns to allow queries against the data.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For textual topics that contain JSON data, it is entirely possible to not
use any table definition files, but instead use the Trino
<a class="reference internal" href="../functions/json.html"><span class="doc">JSON functions and operators</span></a> to parse the <code class="docutils literal notranslate"><span class="pre">_message</span></code> column which contains
the bytes mapped into a UTF-8 string. This is cumbersome and makes it
difficult to write SQL queries. This only works when reading data.</p>
</div>
<p>A table definition file consists of a JSON definition for a table. The
name of the file can be arbitrary but must end in <code class="docutils literal notranslate"><span class="pre">.json</span></code>. Place the
file in the directory configured with the <code class="docutils literal notranslate"><span class="pre">kafka.table-description-dir</span></code>
property. The table definition file must be accessible from all Trino nodes.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "tableName": ...,
    "schemaName": ...,
    "topicName": ...,
    "key": {
        "dataFormat": ...,
        "fields": [
            ...
        ]
    },
    "message": {
        "dataFormat": ...,
        "fields": [
            ...
       ]
    }
}
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">tableName</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Trino table name defined by this file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">schemaName</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Schema containing the table. If omitted, the default schema name is used.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">topicName</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Kafka topic that is mapped.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">key</span></code></p></td>
<td><p>optional</p></td>
<td><p>JSON object</p></td>
<td><p>Field definitions for data columns mapped to the message key.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">message</span></code></p></td>
<td><p>optional</p></td>
<td><p>JSON object</p></td>
<td><p>Field definitions for data columns mapped to the message itself.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="key-and-message-in-kafka">
<h4 id="key-and-message-in-kafka">Key and message in Kafka<a class="headerlink" href="kafka.html#key-and-message-in-kafka" title="Link to this heading">#</a></h4>
<p>Starting with Kafka 0.8, each message in a topic can have an optional key.
A table definition file contains sections for both key and message to map
the data onto table columns.</p>
<p>Each of the <code class="docutils literal notranslate"><span class="pre">key</span></code> and <code class="docutils literal notranslate"><span class="pre">message</span></code> fields in the table definition is a
JSON object that must contain two fields:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Selects the decoder for this group of fields.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fields</span></code></p></td>
<td><p>required</p></td>
<td><p>JSON array</p></td>
<td><p>A list of field definitions. Each field definition creates a new column in the Trino table.</p></td>
</tr>
</tbody>
</table>
<p>Each field definition is a JSON object:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "name": ...,
    "type": ...,
    "dataFormat": ...,
    "mapping": ...,
    "formatHint": ...,
    "hidden": ...,
    "comment": ...
}
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">name</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Name of the column in the Trino table.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">type</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Trino type of the column.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Selects the column decoder for this field. Defaults to the default decoder for this row data format and column type.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">dataSchema</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>The path or URL where the Avro schema resides. Used only for Avro decoder.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Mapping information for the column. This is decoder specific, see below.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">formatHint</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Sets a column-specific format hint to the column decoder.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hidden</span></code></p></td>
<td><p>optional</p></td>
<td><p>boolean</p></td>
<td><p>Hides the column from <code class="docutils literal notranslate"><span class="pre">DESCRIBE</span> <span class="pre">&lt;table</span> <span class="pre">name&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span></code>. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">comment</span></code></p></td>
<td><p>optional</p></td>
<td><p>string</p></td>
<td><p>Adds a column comment, which is shown with <code class="docutils literal notranslate"><span class="pre">DESCRIBE</span> <span class="pre">&lt;table</span> <span class="pre">name&gt;</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>There is no limit on field descriptions for either key or message.</p>
</section>
</section>
<section id="confluent-table-description-supplier">
<span id="id2"></span><h3 id="confluent-table-description-supplier">Confluent table description supplier<a class="headerlink" href="kafka.html#confluent-table-description-supplier" title="Link to this heading">#</a></h3>
<p>The Confluent table description supplier uses the <a class="reference external" href="https://docs.confluent.io/1.0/schema-registry/docs/intro.html">Confluent Schema Registry</a> to discover
table definitions. It is only tested to work with the Confluent Schema
Registry.</p>
<p>The benefits of using the Confluent table description supplier over the file
table description supplier are:</p>
<ul class="simple">
<li><p>New tables can be defined without a cluster restart.</p></li>
<li><p>Schema updates are detected automatically.</p></li>
<li><p>There is no need to define tables manually.</p></li>
<li><p>Some Protobuf specific types like <code class="docutils literal notranslate"><span class="pre">oneof</span></code> and <code class="docutils literal notranslate"><span class="pre">any</span></code> are supported and mapped to JSON.</p></li>
</ul>
<p>When using Protobuf decoder with the Confluent table description supplier, some
additional steps are necessary. For details, refer to <a class="reference internal" href="kafka.html#kafka-requirements"><span class="std std-ref">Requirements</span></a>.</p>
<p>Set <code class="docutils literal notranslate"><span class="pre">kafka.table-description-supplier</span></code> to <code class="docutils literal notranslate"><span class="pre">CONFLUENT</span></code> to use the
schema registry. You must also configure the additional properties in the following table:</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Inserts are not supported, and the only data format supported is AVRO.</p>
</div>
<table id="id7">
<caption><span class="caption-text">Confluent table description supplier properties</span><a class="headerlink" href="kafka.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 55%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.confluent-schema-registry-url</span></code></p></td>
<td><p>Comma-separated list of URL addresses for the Confluent schema registry.
For example, <code class="docutils literal notranslate"><span class="pre">http://schema-registry-1.example.org:8081,http://schema-registry-2.example.org:8081</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.confluent-schema-registry-client-cache-size</span></code></p></td>
<td><p>The maximum number of subjects that can be stored in the local cache. The
cache stores the schemas locally by subjectId, and is provided by the
Confluent <code class="docutils literal notranslate"><span class="pre">CachingSchemaRegistry</span></code> client.</p></td>
<td><p>1000</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.empty-field-strategy</span></code></p></td>
<td><p>Avro allows empty struct fields, but this is not allowed in Trino. There are
three strategies for handling empty struct fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code> - Ignore structs with no fields. This propagates to parents.
For example, an array of structs with no fields is ignored.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FAIL</span></code> - Fail the query if a struct with no fields is defined.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MARK</span></code> - Add a marker field named <code class="docutils literal notranslate"><span class="pre">$empty_field_marker</span></code>, which of type
boolean with a null value. This may be desired if the struct represents
a marker field.</p></li>
</ul>
<p>This can also be modified via the <code class="docutils literal notranslate"><span class="pre">empty_field_strategy</span></code> session property.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">kafka.confluent-subjects-cache-refresh-interval</span></code></p></td>
<td><p>The interval used for refreshing the list of subjects and the definition
of the schema for the subject in the subject’s cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
</tbody>
</table>
<section id="confluent-subject-to-table-name-mapping">
<h4 id="confluent-subject-to-table-name-mapping">Confluent subject to table name mapping<a class="headerlink" href="kafka.html#confluent-subject-to-table-name-mapping" title="Link to this heading">#</a></h4>
<p>The <a class="reference external" href="https://docs.confluent.io/platform/current/schema-registry/serdes-develop/index.html#sr-schemas-subject-name-strategy">subject naming strategy</a>
determines how a subject is resolved from the table name.</p>
<p>The default strategy is the <code class="docutils literal notranslate"><span class="pre">TopicNameStrategy</span></code>, where the key subject is
defined as <code class="docutils literal notranslate"><span class="pre">&lt;topic-name&gt;-key</span></code> and the value subject is defined as
<code class="docutils literal notranslate"><span class="pre">&lt;topic-name&gt;-value</span></code>. If other strategies are used there is no way to
determine the subject name beforehand, so it must be specified manually in the
table name.</p>
<p>To manually specify the key and value subjects, append to the topic name,
for example: <code class="docutils literal notranslate"><span class="pre">&lt;topic</span> <span class="pre">name&gt;&amp;key-subject=&lt;key</span> <span class="pre">subject&gt;&amp;value-subject=&lt;value</span> <span class="pre">subject&gt;</span></code>. Both the <code class="docutils literal notranslate"><span class="pre">key-subject</span></code> and <code class="docutils literal notranslate"><span class="pre">value-subject</span></code> parameters are
optional. If neither is specified, then the default <code class="docutils literal notranslate"><span class="pre">TopicNameStrategy</span></code> is
used to resolve the subject name via the topic name. Note that a
case-insensitive match must be done, as identifiers cannot contain upper case
characters.</p>
</section>
<section id="protobuf-specific-type-handling-in-confluent-table-description-supplier">
<h4 id="protobuf-specific-type-handling-in-confluent-table-description-supplier">Protobuf-specific type handling in Confluent table description supplier<a class="headerlink" href="kafka.html#protobuf-specific-type-handling-in-confluent-table-description-supplier" title="Link to this heading">#</a></h4>
<p>When using the Confluent table description supplier, the following Protobuf
specific types are supported in addition to the <a class="reference internal" href="kafka.html#kafka-protobuf-decoding"><span class="std std-ref">normally supported types</span></a>:</p>
<section id="oneof">
<h5 id="oneof">oneof<a class="headerlink" href="kafka.html#oneof" title="Link to this heading">#</a></h5>
<p>Protobuf schemas containing <code class="docutils literal notranslate"><span class="pre">oneof</span></code> fields are mapped to a <code class="docutils literal notranslate"><span class="pre">JSON</span></code> field in
Trino.</p>
<p>For example, given the following Protobuf schema:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>syntax = "proto3";

message schema {
    oneof test_oneof_column {
        string string_column = 1;
        uint32 integer_column = 2;
        uint64 long_column = 3;
        double double_column = 4;
        float float_column = 5;
        bool boolean_column = 6;
    }
}
</pre></div>
</div>
<p>The corresponding Trino row is a <code class="docutils literal notranslate"><span class="pre">JSON</span></code> field <code class="docutils literal notranslate"><span class="pre">test_oneof_column</span></code>
containing a JSON object with a single key. The value of the key matches
the name of the <code class="docutils literal notranslate"><span class="pre">oneof</span></code> type that is present.</p>
<p>In the above example, if the Protobuf message has the
<code class="docutils literal notranslate"><span class="pre">test_oneof_column</span></code> containing <code class="docutils literal notranslate"><span class="pre">string_column</span></code> set to a value <code class="docutils literal notranslate"><span class="pre">Trino</span></code>
then the corresponding Trino row includes a column named
<code class="docutils literal notranslate"><span class="pre">test_oneof_column</span></code> with the value <code class="docutils literal notranslate"><span class="pre">JSON</span> <span class="pre">'{"string_column":</span> <span class="pre">"Trino"}'</span></code>.</p>
</section>
</section>
</section>
</section>
<section id="kafka-inserts">
<span id="kafka-sql-inserts"></span><h2 id="kafka-inserts">Kafka inserts<a class="headerlink" href="kafka.html#kafka-inserts" title="Link to this heading">#</a></h2>
<p>The Kafka connector supports the use of <a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a> statements to write
data to a Kafka topic. Table column data is mapped to Kafka messages as defined
in the <a class="reference internal" href="kafka.html#table-definition-files">table definition file</a>. There are
five supported data formats for key and message encoding:</p>
<ul class="simple">
<li><p><a class="reference internal" href="kafka.html#raw-encoder"><span class="std std-ref">raw format</span></a></p></li>
<li><p><a class="reference internal" href="kafka.html#csv-encoder"><span class="std std-ref">CSV format</span></a></p></li>
<li><p><a class="reference internal" href="kafka.html#json-encoder"><span class="std std-ref">JSON format</span></a></p></li>
<li><p><a class="reference internal" href="kafka.html#avro-encoder"><span class="std std-ref">Avro format</span></a></p></li>
<li><p><a class="reference internal" href="kafka.html#kafka-protobuf-encoding"><span class="std std-ref">Protobuf format</span></a></p></li>
</ul>
<p>These data formats each have an encoder that maps column values into bytes to be
sent to a Kafka topic.</p>
<p>Trino supports at-least-once delivery for Kafka producers. This means that
messages are guaranteed to be sent to Kafka topics at least once. If a producer
acknowledgement times out, or if the producer receives an error, it might retry
sending the message. This could result in a duplicate message being sent to the
Kafka topic.</p>
<p>The Kafka connector does not allow the user to define which partition will be
used as the target for a message. If a message includes a key, the producer will
use a hash algorithm to choose the target partition for the message. The same
key will always be assigned the same partition.</p>
</section>
<section id="type-mapping">
<span id="kafka-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="kafka.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Kafka each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">maps some types</span></a> when reading
(<a class="reference internal" href="kafka.html#kafka-row-decoding"><span class="std std-ref">decoding</span></a>) or writing (<a class="reference internal" href="kafka.html#kafka-row-encoding"><span class="std std-ref">encoding</span></a>) data. Type mapping depends on the format (Raw, Avro,
JSON, CSV).</p>
<section id="row-encoding">
<span id="kafka-row-encoding"></span><h3 id="row-encoding">Row encoding<a class="headerlink" href="kafka.html#row-encoding" title="Link to this heading">#</a></h3>
<p>Encoding is required to allow writing data; it defines how table columns in
Trino map to Kafka keys and message data.</p>
<p>The Kafka connector contains the following encoders:</p>
<ul class="simple">
<li><p><a class="reference internal" href="kafka.html#raw-encoder"><span class="std std-ref">raw encoder</span></a> - Table columns are mapped to a Kafka
message as raw bytes.</p></li>
<li><p><a class="reference internal" href="kafka.html#csv-encoder"><span class="std std-ref">CSV encoder</span></a> - Kafka message is formatted as a
comma-separated value.</p></li>
<li><p><a class="reference internal" href="kafka.html#json-encoder"><span class="std std-ref">JSON encoder</span></a> - Table columns are mapped to JSON
fields.</p></li>
<li><p><a class="reference internal" href="kafka.html#avro-encoder"><span class="std std-ref">Avro encoder</span></a> - Table columns are mapped to Avro
fields based on an Avro schema.</p></li>
<li><p><a class="reference internal" href="kafka.html#kafka-protobuf-encoding"><span class="std std-ref">Protobuf encoder</span></a> - Table columns are mapped to
Protobuf fields based on a Protobuf schema.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A <a class="reference internal" href="kafka.html#table-definition-files">table definition file</a> must be defined
for the encoder to work.</p>
</div>
<section id="raw-encoder">
<span id="id3"></span><h4 id="raw-encoder">Raw encoder<a class="headerlink" href="kafka.html#raw-encoder" title="Link to this heading">#</a></h4>
<p>The raw encoder formats the table columns as raw bytes using the mapping
information specified in the
<a class="reference internal" href="kafka.html#table-definition-files">table definition file</a>.</p>
<p>The following field attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Specifies the width of the column data type.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - start and optional end position of bytes to convert
(specified as <code class="docutils literal notranslate"><span class="pre">start</span></code> or <code class="docutils literal notranslate"><span class="pre">start:end</span></code>).</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute selects the number of bytes converted.
If absent, <code class="docutils literal notranslate"><span class="pre">BYTE</span></code> is assumed. All values are signed.</p>
<p>Supported values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code> - one byte</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code> - two bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INT</span></code> - four bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code> - eight bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code> - four bytes (IEEE 754 format, big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> - eight bytes (IEEE 754 format, big-endian)</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">type</span></code> attribute defines the Trino data type.</p>
<p>Different values of <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> are supported, depending on the Trino data
type:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> values</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attribute specifies the range of bytes in a key or
message used for encoding.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Both a start and end position must be defined for <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> types.
Otherwise, there is no way to know how many bytes the message contains. The
raw format mapping information is static and cannot be dynamically changed
to fit the variable width of some Trino data types.</p>
</div>
<p>If only a start position is given:</p>
<ul class="simple">
<li><p>For fixed width types, the appropriate number of bytes are used for the
specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> (see above).</p></li>
</ul>
<p>If both a start and end position are given, then:</p>
<ul class="simple">
<li><p>For fixed width types, the size must be equal to number of bytes used by
specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code>.</p></li>
<li><p>All bytes between start (inclusive) and end (exclusive) are used.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>All mappings must include a start position for encoding to work.</p>
</div>
<p>The encoding for numeric data types (<code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>,
<code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">REAL</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>) is straightforward. All numeric types use
big-endian. Floating point types use IEEE 754 format.</p>
<p>Example raw field definition in a <a class="reference internal" href="kafka.html#table-definition-files">table definition file</a>
for a Kafka message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_table_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_schema_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_topic_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"..."</span><span class="w"> </span><span class="p">},</span>
<span class="w">  </span><span class="nt">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"raw"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LONG"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"INTEGER"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"INT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"8"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SMALLINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LONG"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"12"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field4"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR(6)"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BYTE"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"20:26"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Columns should be defined in the same order they are mapped. There can be no
gaps or overlaps between column mappings. The width of the column as defined by
the column mapping must be equivalent to the width of the <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> for all
types except for variable width types.</p>
<p>Example insert query for the above table definition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">example_raw_table</span><span class="w"> </span><span class="p">(</span><span class="n">field1</span><span class="p">,</span><span class="w"> </span><span class="n">field2</span><span class="p">,</span><span class="w"> </span><span class="n">field3</span><span class="p">,</span><span class="w"> </span><span class="n">field4</span><span class="p">)</span>
<span class="w">  </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">*********</span><span class="p">,</span><span class="w"> </span><span class="mi">123456</span><span class="p">,</span><span class="w"> </span><span class="mi">1234</span><span class="p">,</span><span class="w"> </span><span class="s1">'abcdef'</span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The raw encoder requires the field size to be known ahead of time, including
for variable width data types like <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>. It also disallows inserting
values that do not match the width defined in the table definition
file. This is done to ensure correctness, as otherwise longer values are
truncated, and shorter values are read back incorrectly due to an undefined
padding character.</p>
</div>
</section>
<section id="csv-encoder">
<span id="id4"></span><h4 id="csv-encoder">CSV encoder<a class="headerlink" href="kafka.html#csv-encoder" title="Link to this heading">#</a></h4>
<p>The CSV encoder formats the values for each row as a line of
comma-separated-values (CSV) using UTF-8 encoding. The CSV line is formatted
with a comma <code class="docutils literal notranslate"><span class="pre">,</span></code> as the column delimiter.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">type</span></code> and <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attributes must be defined for each field:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - The integer index of the column in the CSV line (the first
column is 0, the second is 1, and so on)</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> and <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> are not supported and must be omitted.</p>
<p>The following Trino data types are supported by the CSV encoder:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></li>
</ul>
<p>No other types are supported.</p>
<p>Column values are converted to strings before they are formatted as a CSV line.</p>
<p>The following is an example CSV field definition in a <a class="reference internal" href="kafka.html#table-definition-files">table definition file</a> for a Kafka message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_table_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_schema_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_topic_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"..."</span><span class="w"> </span><span class="p">},</span>
<span class="w">  </span><span class="nt">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"csv"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BOOLEAN"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Example insert query for the above table definition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">example_csv_table</span><span class="w"> </span><span class="p">(</span><span class="n">field1</span><span class="p">,</span><span class="w"> </span><span class="n">field2</span><span class="p">,</span><span class="w"> </span><span class="n">field3</span><span class="p">)</span>
<span class="w">  </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">*********</span><span class="p">,</span><span class="w"> </span><span class="s1">'example text'</span><span class="p">,</span><span class="w"> </span><span class="k">TRUE</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="json-encoder">
<span id="id5"></span><h4 id="json-encoder">JSON encoder<a class="headerlink" href="kafka.html#json-encoder" title="Link to this heading">#</a></h4>
<p>The JSON encoder maps table columns to JSON fields defined in the
<a class="reference internal" href="kafka.html#table-definition-files">table definition file</a> according to
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4627.html"><strong>RFC 4627</strong></a>.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - A slash-separated list of field names to select a field from the
JSON object.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Name of formatter. Required for temporal types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">formatHint</span></code> - Pattern to format temporal data. Only use with
<code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code> formatter.</p></li>
</ul>
<p>The following Trino data types are supported by the JSON encoder:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></li>
</ul>
<p>No other types are supported.</p>
<p>The following <code class="docutils literal notranslate"><span class="pre">dataFormats</span></code> are available for temporal data:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rfc2822</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code> - Formats temporal data according to
<a class="reference external" href="https://www.joda.org/joda-time/key_format.html">Joda Time</a>
pattern given by <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> field.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></li>
</ul>
<p>All temporal data in Kafka supports milliseconds precision.</p>
<p>The following table defines which temporal data types are supported by
<code class="docutils literal notranslate"><span class="pre">dataFormats</span></code>:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Decoding rules</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
</tbody>
</table>
<p>The following is an example JSON field definition in a <a class="reference internal" href="kafka.html#table-definition-files">table definition file</a> for a Kafka message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_table_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_schema_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_topic_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"..."</span><span class="w"> </span><span class="p">},</span>
<span class="w">  </span><span class="nt">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"json"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TIMESTAMP"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"custom-date-time"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"formatHint"</span><span class="p">:</span><span class="w"> </span><span class="s2">"yyyy-dd-MM HH:mm:ss.SSS"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The following shows an example insert query for the preceding table definition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">example_json_table</span><span class="w"> </span><span class="p">(</span><span class="n">field1</span><span class="p">,</span><span class="w"> </span><span class="n">field2</span><span class="p">,</span><span class="w"> </span><span class="n">field3</span><span class="p">)</span>
<span class="w">  </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">*********</span><span class="p">,</span><span class="w"> </span><span class="s1">'example text'</span><span class="p">,</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2020-07-15 01:02:03.456'</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="avro-encoder">
<span id="id6"></span><h4 id="avro-encoder">Avro encoder<a class="headerlink" href="kafka.html#avro-encoder" title="Link to this heading">#</a></h4>
<p>The Avro encoder serializes rows to Avro records as defined by the
<a class="reference external" href="https://avro.apache.org/docs/current/">Avro schema</a>.
Trino does not support schemaless Avro encoding.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The Avro schema is encoded with the table column values in each Kafka message.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">dataSchema</span></code> must be defined in the table definition file to use the Avro
encoder. It points to the location of the Avro schema file for the key or message.</p>
<p>Avro schema files can be retrieved via HTTP or HTTPS from remote server with the
syntax:</p>
<p><code class="docutils literal notranslate"><span class="pre">"dataSchema":</span> <span class="pre">"http://example.org/schema/avro_data.avsc"</span></code></p>
<p>Local files need to be available on all Trino nodes and use an absolute path in
the syntax, for example:</p>
<p><code class="docutils literal notranslate"><span class="pre">"dataSchema":</span> <span class="pre">"/usr/local/schema/avro_data.avsc"</span></code></p>
<p>The following field attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> - Name of the column in the Trino table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - A slash-separated list of field names to select a field from the
Avro schema. If the field specified in <code class="docutils literal notranslate"><span class="pre">mapping</span></code> does not exist
in the original Avro schema, then a write operation fails.</p></li>
</ul>
<p>The following table lists supported Trino data types, which can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code>
for the equivalent Avro field type.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Avro data type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<p>The following example shows an Avro field definition in a <a class="reference internal" href="kafka.html#table-definition-files">table definition
file</a> for a Kafka message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_table_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_schema_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_topic_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"..."</span><span class="w"> </span><span class="p">},</span>
<span class="w">  </span><span class="nt">"message"</span><span class="p">:</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"avro"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"dataSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"/avro_message_schema.avsc"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"fields"</span><span class="p">:</span>
<span class="w">    </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BOOLEAN"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>In the following example, an Avro schema definition for the preceding table
definition is shown:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"type"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"record"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"name"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"example_avro_message"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"namespace"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"io.trino.plugin.kafka"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"fields"</span><span class="w"> </span><span class="p">:</span>
<span class="w">  </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"name"</span><span class="p">:</span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"type"</span><span class="p">:[</span><span class="s2">"null"</span><span class="p">,</span><span class="w"> </span><span class="s2">"long"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"type"</span><span class="p">:[</span><span class="s2">"null"</span><span class="p">,</span><span class="w"> </span><span class="s2">"string"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"name"</span><span class="p">:</span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"type"</span><span class="p">:[</span><span class="s2">"null"</span><span class="p">,</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"doc:"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"A basic avro schema"</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The following is an example insert query for the preceding table definition:</p>
<blockquote>
<div><dl class="simple myst">
<dt>INSERT INTO example_avro_table (field1, field2, field3)</dt><dd><p>VALUES (*********, ‘example text’, FALSE);</p>
</dd>
</dl>
</div></blockquote>
</section>
<section id="protobuf-encoder">
<span id="kafka-protobuf-encoding"></span><h4 id="protobuf-encoder">Protobuf encoder<a class="headerlink" href="kafka.html#protobuf-encoder" title="Link to this heading">#</a></h4>
<p>The Protobuf encoder serializes rows to Protobuf DynamicMessages as defined by
the <a class="reference external" href="https://developers.google.com/protocol-buffers/docs/overview">Protobuf schema</a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The Protobuf schema is encoded with the table column values in each Kafka message.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">dataSchema</span></code> must be defined in the table definition file to use the
Protobuf encoder. It points to the location of the <code class="docutils literal notranslate"><span class="pre">proto</span></code> file for the key
or message.</p>
<p>Protobuf schema files can be retrieved via HTTP or HTTPS from a remote server
with the syntax:</p>
<p><code class="docutils literal notranslate"><span class="pre">"dataSchema":</span> <span class="pre">"http://example.org/schema/schema.proto"</span></code></p>
<p>Local files need to be available on all Trino nodes and use an absolute path in
the syntax, for example:</p>
<p><code class="docutils literal notranslate"><span class="pre">"dataSchema":</span> <span class="pre">"/usr/local/schema/schema.proto"</span></code></p>
<p>The following field attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> - Name of the column in the Trino table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - slash-separated list of field names to select a field from the
Protobuf schema. If the field specified in <code class="docutils literal notranslate"><span class="pre">mapping</span></code> does not exist in the
original Protobuf schema, then a write operation fails.</p></li>
</ul>
<p>The following table lists supported Trino data types, which can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code>
for the equivalent Protobuf field type.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Protobuf data type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bool</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int32</span></code>, <code class="docutils literal notranslate"><span class="pre">uint32</span></code>, <code class="docutils literal notranslate"><span class="pre">sint32</span></code>, <code class="docutils literal notranslate"><span class="pre">fixed32</span></code>, <code class="docutils literal notranslate"><span class="pre">sfixed32</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int64</span></code>, <code class="docutils literal notranslate"><span class="pre">uint64</span></code>, <code class="docutils literal notranslate"><span class="pre">sint64</span></code>, <code class="docutils literal notranslate"><span class="pre">fixed64</span></code>, <code class="docutils literal notranslate"><span class="pre">sfixed64</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">double</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">float</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">string</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bytes</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Message</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p>Protobuf type with <code class="docutils literal notranslate"><span class="pre">repeated</span></code> field</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Map</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Timestamp</span></code>, predefined in <code class="docutils literal notranslate"><span class="pre">timestamp.proto</span></code></p></td>
</tr>
</tbody>
</table>
<p>The following example shows a Protobuf field definition in a <a class="reference internal" href="kafka.html#table-definition-files">table definition
file</a> for a Kafka message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_table_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_schema_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example_topic_name"</span><span class="p">,</span>
<span class="w">  </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"..."</span><span class="w"> </span><span class="p">},</span>
<span class="w">  </span><span class="nt">"message"</span><span class="p">:</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"protobuf"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"dataSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"/message_schema.proto"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"fields"</span><span class="p">:</span>
<span class="w">    </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field1"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field2"</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BOOLEAN"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"field3"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>In the following example, a Protobuf schema definition for the preceding table
definition is shown:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>syntax = "proto3";

message schema {
  uint64 field1 = 1 ;
  string field2 = 2;
  bool field3 = 3;
}
</pre></div>
</div>
<p>The following is an example insert query for the preceding table definition:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">example_protobuf_table</span><span class="w"> </span><span class="p">(</span><span class="n">field1</span><span class="p">,</span><span class="w"> </span><span class="n">field2</span><span class="p">,</span><span class="w"> </span><span class="n">field3</span><span class="p">)</span>
<span class="w">  </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="mi">*********</span><span class="p">,</span><span class="w"> </span><span class="s1">'example text'</span><span class="p">,</span><span class="w"> </span><span class="k">FALSE</span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
<section id="row-decoding">
<span id="kafka-row-decoding"></span><h3 id="row-decoding">Row decoding<a class="headerlink" href="kafka.html#row-decoding" title="Link to this heading">#</a></h3>
<p>For key and message, a decoder is used to map message and key data onto table columns.</p>
<p>The Kafka connector contains the following decoders:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">raw</span></code> - Kafka message is not interpreted; ranges of raw message bytes are mapped to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">csv</span></code> - Kafka message is interpreted as comma separated message, and fields are mapped to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">json</span></code> - Kafka message is parsed as JSON, and JSON fields are mapped to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">avro</span></code> - Kafka message is parsed based on an Avro schema, and Avro fields are mapped to table columns.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">protobuf</span></code> - Kafka message is parsed based on a Protobuf schema, and Protobuf fields are mapped to table columns.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If no table definition file exists for a table, the <code class="docutils literal notranslate"><span class="pre">dummy</span></code> decoder is used,
which does not expose any columns.</p>
</div>
<section id="raw-decoder">
<h4 id="raw-decoder">Raw decoder<a class="headerlink" href="kafka.html#raw-decoder" title="Link to this heading">#</a></h4>
<p>The raw decoder supports reading of raw byte-based values from Kafka message
or key, and converting it into Trino columns.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Selects the width of the data type converted.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type. See table later min this document for list of
supported data types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - <code class="docutils literal notranslate"><span class="pre">&lt;start&gt;[:&lt;end&gt;]</span></code> - Start and end position of bytes to convert (optional).</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute selects the number of bytes converted.
If absent, <code class="docutils literal notranslate"><span class="pre">BYTE</span></code> is assumed. All values are signed.</p>
<p>Supported values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code> - one byte</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SHORT</span></code> - two bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INT</span></code> - four bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code> - eight bytes (big-endian)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code> - four bytes (IEEE 754 format)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> - eight bytes (IEEE 754 format)</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">type</span></code> attribute defines the Trino data type on which the value is mapped.</p>
<p>Depending on the Trino type assigned to a column, different values of dataFormat can be used:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> values</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SHORT</span></code>, <code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTE</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attribute specifies the range of the bytes in a key or
message used for decoding. It can be one or two numbers separated by a colon (<code class="docutils literal notranslate"><span class="pre">&lt;start&gt;[:&lt;end&gt;]</span></code>).</p>
<p>If only a start position is given:</p>
<ul class="simple">
<li><p>For fixed width types, the column will use the appropriate number of bytes for the specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> (see above).</p></li>
<li><p>When <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> value is decoded, all bytes from start position till the end of the message will be used.</p></li>
</ul>
<p>If start and end position are given:</p>
<ul class="simple">
<li><p>For fixed width types, the size must be equal to number of bytes used by specified <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code>.</p></li>
<li><p>For <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> all bytes between start (inclusive) and end (exclusive) are used.</p></li>
</ul>
<p>If no <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attribute is specified, it is equivalent to setting start position to 0 and leaving end position undefined.</p>
<p>The decoding scheme of numeric data types (<code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>) is straightforward.
A sequence of bytes is read from input message and decoded according to either:</p>
<ul class="simple">
<li><p>big-endian encoding (for integer types)</p></li>
<li><p>IEEE 754 format for (for <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>).</p></li>
</ul>
<p>Length of decoded byte sequence is implied by the <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code>.</p>
<p>For <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> data type a sequence of bytes is interpreted according to UTF-8
encoding.</p>
</section>
<section id="csv-decoder">
<h4 id="csv-decoder">CSV decoder<a class="headerlink" href="kafka.html#csv-decoder" title="Link to this heading">#</a></h4>
<p>The CSV decoder converts the bytes representing a message or key into a
string using UTF-8 encoding and then interprets the result as a CSV
(comma-separated value) line.</p>
<p>For fields, the <code class="docutils literal notranslate"><span class="pre">type</span></code> and <code class="docutils literal notranslate"><span class="pre">mapping</span></code> attributes must be defined:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type. See the following table for a list of supported data types.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - The index of the field in the CSV record.</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> and <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> attributes are not supported and must be omitted.</p>
<p>Table below lists supported Trino types, which can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code> and decoding scheme:</p>
<table>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Decoding rules</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></td>
<td><p>Decoded using Java <code class="docutils literal notranslate"><span class="pre">Long.parseLong()</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p>Decoded using Java <code class="docutils literal notranslate"><span class="pre">Double.parseDouble()</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p>“true” character sequence maps to <code class="docutils literal notranslate"><span class="pre">true</span></code>; Other character sequences map to <code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p>Used as is</p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="json-decoder">
<h4 id="json-decoder">JSON decoder<a class="headerlink" href="kafka.html#json-decoder" title="Link to this heading">#</a></h4>
<p>The JSON decoder converts the bytes representing a message or key into a
JSON according to <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4627.html"><strong>RFC 4627</strong></a>. Note that the message or key <em>MUST</em> convert
into a JSON object, not an array or simple type.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> - Field decoder to be used for column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - slash-separated list of field names to select a field from the JSON object.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">formatHint</span></code> - Only for <code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>.</p></li>
</ul>
<p>The JSON decoder supports multiple field decoders, with <code class="docutils literal notranslate"><span class="pre">_default</span></code> being
used for standard table columns and a number of decoders for date- and
time-based types.</p>
<p>The following table lists Trino data types, which can be used as in <code class="docutils literal notranslate"><span class="pre">type</span></code>, and matching field decoders,
which can be specified via <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute.</p>
<table>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> values</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code>, <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p>Default field decoder (omitted <code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code>, <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code>, <code class="docutils literal notranslate"><span class="pre">iso8601</span></code>, <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code>, <code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code> <code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<section id="default-field-decoder">
<h5 id="default-field-decoder">Default field decoder<a class="headerlink" href="kafka.html#default-field-decoder" title="Link to this heading">#</a></h5>
<p>This is the standard field decoder, supporting all the Trino physical data
types. A field value is transformed under JSON conversion rules into
boolean, long, double or string values. For non-date/time based columns,
this decoder should be used.</p>
</section>
<section id="date-and-time-decoders">
<h5 id="date-and-time-decoders">Date and time decoders<a class="headerlink" href="kafka.html#date-and-time-decoders" title="Link to this heading">#</a></h5>
<p>To convert values from JSON objects into Trino <code class="docutils literal notranslate"><span class="pre">DATE</span></code>, <code class="docutils literal notranslate"><span class="pre">TIME</span></code>, <code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code>,
<code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> or <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> columns, special decoders must be selected using the
<code class="docutils literal notranslate"><span class="pre">dataFormat</span></code> attribute of a field definition.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">iso8601</span></code> - Text based, parses a text field as an ISO 8601 timestamp.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rfc2822</span></code> - Text based, parses a text field as an <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a> timestamp.</p></li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">custom-date-time</span></code> - Text based, parses a text field according to Joda format pattern</dt><dd><p>specified via <code class="docutils literal notranslate"><span class="pre">formatHint</span></code> attribute. Format pattern should conform
to <a class="reference external" href="https://www.joda.org/joda-time/apidocs/org/joda/time/format/DateTimeFormat.html">https://www.joda.org/joda-time/apidocs/org/joda/time/format/DateTimeFormat.html</a>.</p>
</dd>
</dl>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">milliseconds-since-epoch</span></code> - Number-based; interprets a text or number as number of milliseconds since the epoch.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seconds-since-epoch</span></code> - Number-based; interprets a text or number as number of milliseconds since the epoch.</p></li>
</ul>
<p>For <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> and <code class="docutils literal notranslate"><span class="pre">TIME</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code> data types, if timezone information is present in decoded value, it will
be used as Trino value. Otherwise result time zone will be set to <code class="docutils literal notranslate"><span class="pre">UTC</span></code>.</p>
</section>
</section>
<section id="avro-decoder">
<h4 id="avro-decoder">Avro decoder<a class="headerlink" href="kafka.html#avro-decoder" title="Link to this heading">#</a></h4>
<p>The Avro decoder converts the bytes representing a message or key in
Avro format based on a schema. The message must have the Avro schema embedded.
Trino does not support schemaless Avro decoding.</p>
<p>For key/message, using <code class="docutils literal notranslate"><span class="pre">avro</span></code> decoder, the <code class="docutils literal notranslate"><span class="pre">dataSchema</span></code> must be defined.
This should point to the location of a valid Avro schema file of the message which needs to be decoded. This location can be a remote web server
(e.g.: <code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'http://example.org/schema/avro_data.avsc'</span></code>) or local file system(e.g.: <code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'/usr/local/schema/avro_data.avsc'</span></code>).
The decoder fails if this location is not accessible from the Trino coordinator node.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> - Name of the column in the Trino table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - A slash-separated list of field names to select a field from the Avro schema. If field specified in <code class="docutils literal notranslate"><span class="pre">mapping</span></code> does not exist in the original Avro schema, then a read operation returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p></li>
</ul>
<p>The following table lists the supported Trino types which can be used in <code class="docutils literal notranslate"><span class="pre">type</span></code> for the equivalent Avro field types:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed Avro data type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code>, <code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FIXED</span></code>, <code class="docutils literal notranslate"><span class="pre">BYTES</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<section id="avro-schema-evolution">
<h5 id="avro-schema-evolution">Avro schema evolution<a class="headerlink" href="kafka.html#avro-schema-evolution" title="Link to this heading">#</a></h5>
<p>The Avro decoder supports schema evolution feature with backward compatibility. With backward compatibility,
a newer schema can be used to read Avro data created with an older schema. Any change in the Avro schema must also be
reflected in Trino’s topic definition file. Newly added/renamed fields <em>must</em> have a default value in the Avro schema file.</p>
<p>The schema evolution behavior is as follows:</p>
<ul class="simple">
<li><p>Column added in new schema:
Data created with an older schema produces a <em>default</em> value when the table is using the new schema.</p></li>
<li><p>Column removed in new schema:
Data created with an older schema no longer outputs the data from the column that was removed.</p></li>
<li><p>Column is renamed in the new schema:
This is equivalent to removing the column and adding a new one, and data created with an older schema
produces a <em>default</em> value when table is using the new schema.</p></li>
<li><p>Changing type of column in the new schema:
If the type coercion is supported by Avro, then the conversion happens. An
error is thrown for incompatible types.</p></li>
</ul>
</section>
</section>
<section id="protobuf-decoder">
<span id="kafka-protobuf-decoding"></span><h4 id="protobuf-decoder">Protobuf decoder<a class="headerlink" href="kafka.html#protobuf-decoder" title="Link to this heading">#</a></h4>
<p>The Protobuf decoder converts the bytes representing a message or key in
Protobuf formatted message based on a schema.</p>
<p>For key/message, using the <code class="docutils literal notranslate"><span class="pre">protobuf</span></code> decoder, the <code class="docutils literal notranslate"><span class="pre">dataSchema</span></code> must be
defined. It points to the location of a valid <code class="docutils literal notranslate"><span class="pre">proto</span></code> file of the message
which needs to be decoded. This location can be a remote web server,
<code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'http://example.org/schema/schema.proto'</span></code>,  or local file,
<code class="docutils literal notranslate"><span class="pre">dataSchema:</span> <span class="pre">'/usr/local/schema/schema.proto'</span></code>. The decoder fails if the
location is not accessible from the coordinator.</p>
<p>For fields, the following attributes are supported:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> - Name of the column in the Trino table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> - Trino data type of column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mapping</span></code> - slash-separated list of field names to select a field from the
Protobuf schema. If field specified in <code class="docutils literal notranslate"><span class="pre">mapping</span></code> does not exist in the
original <code class="docutils literal notranslate"><span class="pre">proto</span></code> file then a read operation returns NULL.</p></li>
</ul>
<p>The following table lists the supported Trino types which can be used in
<code class="docutils literal notranslate"><span class="pre">type</span></code> for the equivalent Protobuf field types:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Trino data type</p></th>
<th class="head"><p>Allowed Protobuf data type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bool</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int32</span></code>, <code class="docutils literal notranslate"><span class="pre">uint32</span></code>, <code class="docutils literal notranslate"><span class="pre">sint32</span></code>, <code class="docutils literal notranslate"><span class="pre">fixed32</span></code>, <code class="docutils literal notranslate"><span class="pre">sfixed32</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int64</span></code>, <code class="docutils literal notranslate"><span class="pre">uint64</span></code>, <code class="docutils literal notranslate"><span class="pre">sint64</span></code>, <code class="docutils literal notranslate"><span class="pre">fixed64</span></code>, <code class="docutils literal notranslate"><span class="pre">sfixed64</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">double</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">float</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> / <code class="docutils literal notranslate"><span class="pre">VARCHAR(x)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">string</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bytes</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Message</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p>Protobuf type with <code class="docutils literal notranslate"><span class="pre">repeated</span></code> field</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Map</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Timestamp</span></code>, predefined in <code class="docutils literal notranslate"><span class="pre">timestamp.proto</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">oneof</span></code> (Confluent table supplier only), <code class="docutils literal notranslate"><span class="pre">Any</span></code></p></td>
</tr>
</tbody>
</table>
<section id="any">
<h5 id="any">any<a class="headerlink" href="kafka.html#any" title="Link to this heading">#</a></h5>
<p>Message types with an <a class="reference external" href="https://protobuf.dev/programming-guides/proto3/#any">Any</a>
field contain an arbitrary serialized message as bytes and a type URL to resolve
that message’s type with a scheme of <code class="docutils literal notranslate"><span class="pre">file://</span></code>, <code class="docutils literal notranslate"><span class="pre">http://</span></code>, or <code class="docutils literal notranslate"><span class="pre">https://</span></code>.
The connector reads the contents of the URL to create the type descriptor
for the <code class="docutils literal notranslate"><span class="pre">Any</span></code> message and convert the message to JSON. This behavior is enabled
by setting <code class="docutils literal notranslate"><span class="pre">kafka.protobuf-any-support-enabled</span></code> to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
<p>The descriptors for each distinct URL are cached for performance reasons and
any modifications made to the type returned by the URL requires a restart of
Trino.</p>
<p>For example, given the following Protobuf schema which defines <code class="docutils literal notranslate"><span class="pre">MyMessage</span></code>
with three columns:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>syntax = "proto3";

message MyMessage {
  string stringColumn = 1;
  uint32 integerColumn = 2;
  uint64 longColumn = 3;
}
</pre></div>
</div>
<p>And a separate schema which uses an <code class="docutils literal notranslate"><span class="pre">Any</span></code> type which is a packed message
of the above type and a valid URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>syntax = "proto3";

import "google/protobuf/any.proto";

message schema {
    google.protobuf.Any any_message = 1;
}
</pre></div>
</div>
<p>The corresponding Trino column is named <code class="docutils literal notranslate"><span class="pre">any_message</span></code> of type <code class="docutils literal notranslate"><span class="pre">JSON</span></code>
containing a JSON-serialized representation of the Protobuf message:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
  "@type":"file:///path/to/schemas/MyMessage",
  "longColumn":"493857959588286460",
  "numberColumn":"ONE",
  "stringColumn":"Trino"
}
</pre></div>
</div>
</section>
<section id="protobuf-schema-evolution">
<h5 id="protobuf-schema-evolution">Protobuf schema evolution<a class="headerlink" href="kafka.html#protobuf-schema-evolution" title="Link to this heading">#</a></h5>
<p>The Protobuf decoder supports the schema evolution feature with backward
compatibility. With backward compatibility, a newer schema can be used to read
Protobuf data created with an older schema. Any change in the Protobuf schema
<em>must</em> also be reflected in the topic definition file.</p>
<p>The schema evolution behavior is as follows:</p>
<ul class="simple">
<li><p>Column added in new schema:
Data created with an older schema produces a <em>default</em> value when the table is using the new schema.</p></li>
<li><p>Column removed in new schema:
Data created with an older schema no longer outputs the data from the column that was removed.</p></li>
<li><p>Column is renamed in the new schema:
This is equivalent to removing the column and adding a new one, and data created with an older schema
produces a <em>default</em> value when table is using the new schema.</p></li>
<li><p>Changing type of column in the new schema:
If the type coercion is supported by Protobuf, then the conversion happens. An error is thrown for incompatible types.</p></li>
</ul>
</section>
<section id="protobuf-limitations">
<h5 id="protobuf-limitations">Protobuf limitations<a class="headerlink" href="kafka.html#protobuf-limitations" title="Link to this heading">#</a></h5>
<ul class="simple">
<li><p>Protobuf Timestamp has a nanosecond precision but Trino supports
decoding/encoding at microsecond precision.</p></li>
</ul>
</section>
</section>
</section>
</section>
<section id="sql-support">
<span id="kafka-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="kafka.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read and write access to data and metadata in Trino
tables populated by Kafka topics. See <a class="reference internal" href="kafka.html#kafka-row-decoding"><span class="std std-ref">Row decoding</span></a> for more
information.</p>
<p>In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a>
and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements, the connector
supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a>, encoded to a specified data format. See also
<a class="reference internal" href="kafka.html#kafka-sql-inserts"><span class="std std-ref">Kafka inserts</span></a>.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="jmx.html" title="JMX connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> JMX connector </span>
              </div>
            </a>
          
          
            <a href="kafka-tutorial.html" title="Kafka connector tutorial"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Kafka connector tutorial </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>