<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>BigQuery connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="bigquery.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Black Hole connector" href="blackhole.html" />
    <link rel="prev" title="Connectors" href="../connector.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="bigquery.html#connector/bigquery" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> BigQuery connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> BigQuery </label>
    
      <a href="bigquery.html#" class="md-nav__link md-nav__link--active">BigQuery</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="bigquery.html#bigquery-storage-api" class="md-nav__link">BigQuery Storage API</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#multiple-gcp-projects" class="md-nav__link">Multiple GCP projects</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#billing-and-data-projects" class="md-nav__link">Billing and data projects</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#arrow-serialization-support" class="md-nav__link">Arrow serialization support</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#reading-from-views" class="md-nav__link">Reading from views</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#bigquery-type-to-trino-type-mapping" class="md-nav__link">BigQuery type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#trino-type-to-bigquery-type-mapping" class="md-nav__link">Trino type to BigQuery type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#system-tables" class="md-nav__link">System tables</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#special-columns" class="md-nav__link">Special columns</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#delete-limitation" class="md-nav__link">DELETE limitation</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#wildcard-table" class="md-nav__link">Wildcard table</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#pushdown" class="md-nav__link">Pushdown</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#faq" class="md-nav__link">FAQ</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#what-is-the-pricing-for-the-storage-api" class="md-nav__link">What is the Pricing for the Storage API?</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="bigquery.html#bigquery-storage-api" class="md-nav__link">BigQuery Storage API</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#multiple-gcp-projects" class="md-nav__link">Multiple GCP projects</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#billing-and-data-projects" class="md-nav__link">Billing and data projects</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#arrow-serialization-support" class="md-nav__link">Arrow serialization support</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#reading-from-views" class="md-nav__link">Reading from views</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#configuration-properties" class="md-nav__link">Configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#bigquery-type-to-trino-type-mapping" class="md-nav__link">BigQuery type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#trino-type-to-bigquery-type-mapping" class="md-nav__link">Trino type to BigQuery type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#system-tables" class="md-nav__link">System tables</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#special-columns" class="md-nav__link">Special columns</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#delete-limitation" class="md-nav__link">DELETE limitation</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#wildcard-table" class="md-nav__link">Wildcard table</a>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#pushdown" class="md-nav__link">Pushdown</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="bigquery.html#faq" class="md-nav__link">FAQ</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="bigquery.html#what-is-the-pricing-for-the-storage-api" class="md-nav__link">What is the Pricing for the Storage API?</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="bigquery-connector">
<h1 id="connector-bigquery--page-root">BigQuery connector<a class="headerlink" href="bigquery.html#connector-bigquery--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/bigquery.png"/><p>The BigQuery connector allows querying the data stored in <a class="reference external" href="https://cloud.google.com/bigquery/">BigQuery</a>. This can be used to join data between
different systems like BigQuery and Hive. The connector uses the <a class="reference external" href="https://cloud.google.com/bigquery/docs/reference/storage/">BigQuery
Storage API</a> to
read the data from the tables.</p>
<section id="bigquery-storage-api">
<h2 id="bigquery-storage-api">BigQuery Storage API<a class="headerlink" href="bigquery.html#bigquery-storage-api" title="Link to this heading">#</a></h2>
<p>The Storage API streams data in parallel directly from BigQuery via gRPC without
using Google Cloud Storage as an intermediary.
It has a number of advantages over using the previous export-based read flow
that should generally lead to better read performance:</p>
<dl class="simple myst">
<dt><strong>Direct Streaming</strong></dt><dd><p>It does not leave any temporary files in Google Cloud Storage. Rows are read
directly from BigQuery servers using an Avro wire format.</p>
</dd>
<dt><strong>Column Filtering</strong></dt><dd><p>The new API allows column filtering to only read the data you are interested in.
<a class="reference external" href="https://cloud.google.com/blog/products/bigquery/inside-capacitor-bigquerys-next-generation-columnar-storage-format">Backed by a columnar datastore</a>,
it can efficiently stream data without reading all columns.</p>
</dd>
<dt><strong>Dynamic Sharding</strong></dt><dd><p>The API rebalances records between readers until they all complete. This means
that all Map phases will finish nearly concurrently. See this blog article on
<a class="reference external" href="https://cloud.google.com/blog/products/gcp/no-shard-left-behind-dynamic-work-rebalancing-in-google-cloud-dataflow">how dynamic sharding is similarly used in Google Cloud Dataflow</a>.</p>
</dd>
</dl>
</section>
<section id="requirements">
<span id="bigquery-requirements"></span><h2 id="requirements">Requirements<a class="headerlink" href="bigquery.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to BigQuery, you need:</p>
<ul>
<li><p>To enable the <a class="reference external" href="https://cloud.google.com/bigquery/docs/reference/storage/#enabling_the_api">BigQuery Storage Read API</a>.</p></li>
<li><p>Network access from your Trino coordinator and workers to the
Google Cloud API service endpoint. This endpoint uses HTTPS, or port 443.</p></li>
<li><p>To configure BigQuery so that the Trino coordinator and workers have <a class="reference external" href="https://cloud.google.com/bigquery/docs/reference/storage#permissions">permissions
in BigQuery</a>.</p></li>
<li><p>To set up authentication. Your authentication options differ depending on whether
you are using Dataproc/Google Compute Engine (GCE) or not.</p>
<p><strong>On Dataproc/GCE</strong> the authentication is done from the machine’s role.</p>
<p><strong>Outside Dataproc/GCE</strong> you have 3 options:</p>
<ul class="simple">
<li><p>Use a service account JSON key and <code class="docutils literal notranslate"><span class="pre">GOOGLE_APPLICATION_CREDENTIALS</span></code> as
described in the Google Cloud authentication <a class="reference external" href="https://cloud.google.com/docs/authentication/getting-started">getting started guide</a>.</p></li>
<li><p>Set <code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code> in the catalog properties file. It should
contain the contents of the JSON file, encoded using base64.</p></li>
<li><p>Set <code class="docutils literal notranslate"><span class="pre">bigquery.credentials-file</span></code> in the catalog properties file. It should
point to the location of the JSON file.</p></li>
</ul>
</li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="bigquery.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the BigQuery connector, create a catalog properties file in
<code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code> named <code class="docutils literal notranslate"><span class="pre">example.properties</span></code>, to mount the BigQuery connector as
the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Create the file with the following contents, replacing
the connection properties as appropriate for your setup:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=bigquery
bigquery.project-id=&lt;your Google Cloud Platform project id&gt;
</pre></div>
</div>
<section id="multiple-gcp-projects">
<h3 id="multiple-gcp-projects">Multiple GCP projects<a class="headerlink" href="bigquery.html#multiple-gcp-projects" title="Link to this heading">#</a></h3>
<p>The BigQuery connector can only access a single GCP project. If you have
data in multiple GCP projects, you must create several catalogs, each
pointing to a different GCP project. For example, if you have two GCP projects,
one for the sales and one for analytics, you can create two properties files in
<code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code> named <code class="docutils literal notranslate"><span class="pre">sales.properties</span></code> and <code class="docutils literal notranslate"><span class="pre">analytics.properties</span></code>, both
having <code class="docutils literal notranslate"><span class="pre">connector.name=bigquery</span></code> but with different <code class="docutils literal notranslate"><span class="pre">project-id</span></code>. This will
create the two catalogs, <code class="docutils literal notranslate"><span class="pre">sales</span></code> and <code class="docutils literal notranslate"><span class="pre">analytics</span></code> respectively.</p>
</section>
<section id="billing-and-data-projects">
<span id="bigquery-project-id-resolution"></span><h3 id="billing-and-data-projects">Billing and data projects<a class="headerlink" href="bigquery.html#billing-and-data-projects" title="Link to this heading">#</a></h3>
<p>The BigQuery connector determines the <a class="reference external" href="https://cloud.google.com/resource-manager/docs/creating-managing-projects">project
ID</a>
to use based on the configuration settings. This behavior provides users with
flexibility in selecting both the project to query and the project to bill for
BigQuery operations. The following table explains how project IDs are resolved
in different scenarios:</p>
<table id="id1">
<caption><span class="caption-text">Billing and data project ID resolution</span><a class="headerlink" href="bigquery.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 31%"/>
<col style="width: 34%"/>
<col style="width: 34%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Configured properties</p></th>
<th class="head"><p>Billing project</p></th>
<th class="head"><p>Data project</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Only <code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code></p></td>
<td><p>The project ID from the credentials key is used for billing.</p></td>
<td><p>The project ID from the credentials key is used for querying data.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code> and <code class="docutils literal notranslate"><span class="pre">bigquery.project-id</span></code></p></td>
<td><p>The project ID from the credentials key is used for billing.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bigquery.project-id</span></code> is used for querying data.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code> and <code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code> is used for billing.</p></td>
<td><p>The project ID from the credentials key is used for querying data.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code> and <code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code>
and <code class="docutils literal notranslate"><span class="pre">bigquery.project-id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code> is used for billing.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bigquery.project-id</span></code> is used for querying data.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="arrow-serialization-support">
<span id="bigquery-arrow-serialization-support"></span><h3 id="arrow-serialization-support">Arrow serialization support<a class="headerlink" href="bigquery.html#arrow-serialization-support" title="Link to this heading">#</a></h3>
<p>This is a feature which introduces support for using Apache Arrow
as the serialization format when reading from BigQuery. Add the following
required, additional JVM argument to the <a class="reference internal" href="../installation/deployment.html#jvm-config"><span class="std std-ref">JVM config</span></a>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>--add-opens=java.base/java.nio=ALL-UNNAMED
--sun-misc-unsafe-memory-access=allow
</pre></div>
</div>
</section>
<section id="reading-from-views">
<span id="bigquery-reading-from-views"></span><h3 id="reading-from-views">Reading from views<a class="headerlink" href="bigquery.html#reading-from-views" title="Link to this heading">#</a></h3>
<p>The connector has a preliminary support for reading from <a class="reference external" href="https://cloud.google.com/bigquery/docs/views-intro">BigQuery views</a>. Please note there are
a few caveats:</p>
<ul class="simple">
<li><p>Reading from views is disabled by default. In order to enable it, set the
<code class="docutils literal notranslate"><span class="pre">bigquery.views-enabled</span></code> configuration property to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></li>
<li><p>BigQuery views are not materialized by default, which means that the
connector needs to materialize them before it can read them. This process
affects the read performance.</p></li>
<li><p>The materialization process can also incur additional costs to your BigQuery bill.</p></li>
<li><p>By default, the materialized views are created in the same project and
dataset. Those can be configured by the optional <code class="docutils literal notranslate"><span class="pre">bigquery.view-materialization-project</span></code>
and <code class="docutils literal notranslate"><span class="pre">bigquery.view-materialization-dataset</span></code> properties, respectively. The
service account must have write permission to the project and the dataset in
order to materialize the view.</p></li>
</ul>
</section>
<section id="configuration-properties">
<h3 id="configuration-properties">Configuration properties<a class="headerlink" href="bigquery.html#configuration-properties" title="Link to this heading">#</a></h3>
<table id="id2">
<caption><span class="caption-text">BigQuery configuration properties</span><a class="headerlink" href="bigquery.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 55%"/>
<col style="width: 15%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.project-id</span></code></p></td>
<td><p>The project ID of the Google Cloud account used to store the data,
see also <a class="reference internal" href="bigquery.html#bigquery-project-id-resolution"><span class="std std-ref">Billing and data projects</span></a></p></td>
<td><p>Taken from the service account or from <code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code>, if set</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.parent-project-id</span></code></p></td>
<td><p>The project ID Google Cloud Project to bill for the export,
see also <a class="reference internal" href="bigquery.html#bigquery-project-id-resolution"><span class="std std-ref">Billing and data projects</span></a></p></td>
<td><p>Taken from the service account</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.views-enabled</span></code></p></td>
<td><p>Enables the connector to read from views and not only tables. Read
<a class="reference internal" href="bigquery.html#bigquery-reading-from-views"><span class="std std-ref">this section</span></a> before enabling this feature.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.view-expire-duration</span></code></p></td>
<td><p>Expire duration for the materialized view.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">24h</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.view-materialization-project</span></code></p></td>
<td><p>The project where the materialized view is going to be created.</p></td>
<td><p>The view’s project</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.view-materialization-dataset</span></code></p></td>
<td><p>The dataset where the materialized view is going to be created.</p></td>
<td><p>The view’s project</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.skip-view-materialization</span></code></p></td>
<td><p>Use REST API to access views instead of Storage API. BigQuery <code class="docutils literal notranslate"><span class="pre">BIGNUMERIC</span></code>
and <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code> types are unsupported.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.view-materialization-with-filter</span></code></p></td>
<td><p>Use filter conditions when materializing views.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.views-cache-ttl</span></code></p></td>
<td><p>Duration for which the materialization of a view will be cached and reused.
Set to <code class="docutils literal notranslate"><span class="pre">0ms</span></code> to disable the cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">15m</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.metadata.cache-ttl</span></code></p></td>
<td><p>Duration for which metadata retrieved from BigQuery is cached and reused.
Set to <code class="docutils literal notranslate"><span class="pre">0ms</span></code> to disable the cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0ms</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.max-read-rows-retries</span></code></p></td>
<td><p>The number of retries in case of retryable server issues.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">3</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.credentials-key</span></code></p></td>
<td><p>The base64 encoded credentials key.</p></td>
<td><p>None. See the <a class="reference internal" href="bigquery.html#bigquery-requirements"><span class="std std-ref">requirements</span></a> section</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.credentials-file</span></code></p></td>
<td><p>The path to the JSON credentials file.</p></td>
<td><p>None. See the <a class="reference internal" href="bigquery.html#bigquery-requirements"><span class="std std-ref">requirements</span></a> section</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.case-insensitive-name-matching</span></code></p></td>
<td><p>Match dataset and table names case-insensitively.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.case-insensitive-name-matching.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which case insensitive schema and table
names are cached. Set to <code class="docutils literal notranslate"><span class="pre">0ms</span></code> to disable the cache.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0ms</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.query-results-cache.enabled</span></code></p></td>
<td><p>Enable <a class="reference external" href="https://cloud.google.com/bigquery/docs/cached-results">query results cache</a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.arrow-serialization.enabled</span></code></p></td>
<td><p>Enable using Apache Arrow serialization when reading data from BigQuery.
Read this <a class="reference internal" href="bigquery.html#bigquery-arrow-serialization-support"><span class="std std-ref">section</span></a> before using this feature.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.max-parallelism</span></code></p></td>
<td><p>The max number of partitions to split the data into. Reduce this number if
the default parallelism (number of workers x 3) is too high.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.channel-pool.initial-size</span></code></p></td>
<td><p>The initial size of the connection pool, also known as a channel pool,
used for gRPC communication.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.channel-pool.min-size</span></code></p></td>
<td><p>The minimum number of connections in the connection pool, also known as a
channel pool, used for gRPC communication.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.channel-pool.max-size</span></code></p></td>
<td><p>The maximum number of connections in the connection pool, also known as a
channel pool, used for gRPC communication.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.channel-pool.min-rpc-per-channel</span></code></p></td>
<td><p>Threshold to start scaling down the channel pool.
When the average of outstanding RPCs in a single minute drop below this
threshold, channels are removed from the pool.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.channel-pool.max-rpc-per-channel</span></code></p></td>
<td><p>Threshold to start scaling up the channel pool.
When the average of outstanding RPCs in a single minute surpass this
threshold, channels are added to the pool.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">2147483647</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code></p></td>
<td><p>The maximum number of retry attempts to perform for the RPC calls.
If this value is set to <code class="docutils literal notranslate"><span class="pre">0</span></code>, the value from
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-timeout</span></code> is used.
Retry is deactivated when both <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code> and
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-timeout</span></code> are <code class="docutils literal notranslate"><span class="pre">0</span></code>.
If this value is positive, and the number of attempts exceeds
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code> limit, retries stop even if
the total retry time is still lower than <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-timeout</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-timeout</span></code></p></td>
<td><p>Timeout <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> on when the retries for the
RPC call should be given up completely. The higher the timeout, the
more retries can be attempted. If this value is <code class="docutils literal notranslate"><span class="pre">0s</span></code>, then
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code> is used to determine retries.
Retry is deactivated when <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code> and
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-timeout</span></code> are both <code class="docutils literal notranslate"><span class="pre">0</span></code>.
If this value is positive, and the retry duration has reached the timeout
value, retries stop even if the number of attempts is lower than
the <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retries</span></code> value.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0s</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retry-delay</span></code></p></td>
<td><p>The delay <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> before the first retry attempt
for RPC calls.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retry-delay-multiplier</span></code></p></td>
<td><p>Controls the change in delay before the next retry.
The retry delay of the previous call is multiplied by the
<code class="docutils literal notranslate"><span class="pre">bigquery.rpc-retry-delay-multiplier</span></code> to calculate the retry delay
for the next RPC call.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1.0</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.enabled</span></code></p></td>
<td><p>Use a proxy for communication with BigQuery.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.uri</span></code></p></td>
<td><p>Proxy URI to use if connecting through a proxy.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.username</span></code></p></td>
<td><p>Proxy username to use if connecting through a proxy.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.password</span></code></p></td>
<td><p>Proxy password to use if connecting through a proxy.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.keystore-path</span></code></p></td>
<td><p>Keystore containing client certificates to present to proxy if connecting
through a proxy. Only required if proxy uses mutual TLS.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.keystore-password</span></code></p></td>
<td><p>Password of the keystore specified by <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.keystore-path</span></code>.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.truststore-path</span></code></p></td>
<td><p>Truststore containing certificates of the proxy server if connecting
through a proxy.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.truststore-password</span></code></p></td>
<td><p>Password of the truststore specified by <code class="docutils literal notranslate"><span class="pre">bigquery.rpc-proxy.truststore-path</span></code>.</p></td>
<td></td>
</tr>
</tbody>
</table>
</section>
<section id="fault-tolerant-execution-support">
<span id="bigquery-fte-support"></span><h3 id="fault-tolerant-execution-support">Fault-tolerant execution support<a class="headerlink" href="bigquery.html#fault-tolerant-execution-support" title="Link to this heading">#</a></h3>
<p>The connector supports <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a> of query
processing. Read and write operations are both supported with any retry policy.</p>
</section>
</section>
<section id="type-mapping">
<span id="bigquery-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="bigquery.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and BigQuery each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<section id="bigquery-type-to-trino-type-mapping">
<h3 id="bigquery-type-to-trino-type-mapping">BigQuery type to Trino type mapping<a class="headerlink" href="bigquery.html#bigquery-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps BigQuery types to the corresponding Trino types according
to the following table:</p>
<table id="id3">
<caption><span class="caption-text">BigQuery type to Trino type mapping</span><a class="headerlink" href="bigquery.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 27%"/>
<col style="width: 27%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>BigQuery type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INT64</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, and <code class="docutils literal notranslate"><span class="pre">BYTEINT</span></code> are aliases
for <code class="docutils literal notranslate"><span class="pre">INT64</span></code> in BigQuery.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT64</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">NUMERIC</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(P,S)</span></code></p></td>
<td><p>The default precision and scale of <code class="docutils literal notranslate"><span class="pre">NUMERIC</span></code> is <code class="docutils literal notranslate"><span class="pre">(38,</span> <span class="pre">9)</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGNUMERIC</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(P,S)</span></code></p></td>
<td><p>Precision &gt; 38 is not supported. The default precision and scale of
<code class="docutils literal notranslate"><span class="pre">BIGNUMERIC</span></code> is <code class="docutils literal notranslate"><span class="pre">(77,</span> <span class="pre">38)</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATETIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BYTES</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p>Time zone is UTC</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">GEOGRAPHY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>In <a class="reference external" href="https://wikipedia.org/wiki/Well-known_text_representation_of_geometry">Well-known text
(WKT)</a>
format</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">RECORD</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-type-to-bigquery-type-mapping">
<h3 id="trino-type-to-bigquery-type-mapping">Trino type to BigQuery type mapping<a class="headerlink" href="bigquery.html#trino-type-to-bigquery-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding BigQuery types according
to the following table:</p>
<table id="id4">
<caption><span class="caption-text">Trino type to BigQuery type mapping</span><a class="headerlink" href="bigquery.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 27%"/>
<col style="width: 27%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>BigQuery type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BYTES</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT64</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code>, <code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>, <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code>, <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code>, and
<code class="docutils literal notranslate"><span class="pre">BYTEINT</span></code> are aliases for <code class="docutils literal notranslate"><span class="pre">INT64</span></code> in BigQuery.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(P,S)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NUMERIC</span></code></p></td>
<td><p>The default precision and scale of <code class="docutils literal notranslate"><span class="pre">NUMERIC</span></code> is <code class="docutils literal notranslate"><span class="pre">(38,</span> <span class="pre">9)</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATETIME</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="system-tables">
<h2 id="system-tables">System tables<a class="headerlink" href="bigquery.html#system-tables" title="Link to this heading">#</a></h2>
<p>For each Trino table which maps to BigQuery view there exists a system table
which exposes BigQuery view definition. Given a BigQuery view <code class="docutils literal notranslate"><span class="pre">example_view</span></code>
you can send query <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span> <span class="pre">example_view$view_definition</span></code> to see the SQL
which defines view in BigQuery.</p>
</section>
<section id="special-columns">
<span id="bigquery-special-columns"></span><h2 id="special-columns">Special columns<a class="headerlink" href="bigquery.html#special-columns" title="Link to this heading">#</a></h2>
<p>In addition to the defined columns, the BigQuery connector exposes
partition information in a number of hidden columns:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$partition_date</span></code>: Equivalent to <code class="docutils literal notranslate"><span class="pre">_PARTITIONDATE</span></code> pseudo-column in BigQuery</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$partition_time</span></code>: Equivalent to <code class="docutils literal notranslate"><span class="pre">_PARTITIONTIME</span></code> pseudo-column in BigQuery</p></li>
</ul>
<p>You can use these columns in your SQL statements like any other column. They
can be selected directly, or used in conditional statements. For example, you
can inspect the partition date and time for each record:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="p">,</span><span class="w"> </span><span class="ss">"$partition_date"</span><span class="p">,</span><span class="w"> </span><span class="ss">"$partition_time"</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">page_views</span><span class="p">;</span>
</pre></div>
</div>
<p>Retrieve all records stored in the partition <code class="docutils literal notranslate"><span class="pre">_PARTITIONDATE</span> <span class="pre">=</span> <span class="pre">'2022-04-07'</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">page_views</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$partition_date"</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">date</span><span class="w"> </span><span class="s1">'2022-04-07'</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Two special partitions <code class="docutils literal notranslate"><span class="pre">__NULL__</span></code> and <code class="docutils literal notranslate"><span class="pre">__UNPARTITIONED__</span></code> are not supported.</p>
</div>
</section>
<section id="sql-support">
<span id="bigquery-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="bigquery.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read and write access to data and metadata in the
BigQuery database. In addition to the
<a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements, the connector supports
the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/truncate.html"><span class="doc">TRUNCATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
<section id="delete-limitation">
<h3 id="delete-limitation">DELETE limitation<a class="headerlink" href="bigquery.html#delete-limitation" title="Link to this heading">#</a></h3>
<p>If a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause is specified, the <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> operation only works if the
predicate in the clause can be fully pushed down to the data source.</p>
</section>
<section id="wildcard-table">
<h3 id="wildcard-table">Wildcard table<a class="headerlink" href="bigquery.html#wildcard-table" title="Link to this heading">#</a></h3>
<p>The connector provides support to query multiple tables using a concise
<a class="reference external" href="https://cloud.google.com/bigquery/docs/querying-wildcard-tables">wildcard table</a>
notation.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="ss">"page_views_*"</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="procedures">
<h3 id="procedures">Procedures<a class="headerlink" href="bigquery.html#procedures" title="Link to this heading">#</a></h3>
<section id="system-execute-query">
<h4 id="system-execute-query"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code><a class="headerlink" href="bigquery.html#system-execute-query" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">execute</span></code> procedure allows you to execute a query in the underlying data
source directly. The query must use supported syntax of the connected data
source. Use the procedure to access features which are not available in Trino
or to execute queries that return no result set and therefore can not be used
with the <code class="docutils literal notranslate"><span class="pre">query</span></code> or <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> pass-through table function. Typical use cases
are statements that create or alter objects, and require native feature such
as constraints, default values, automatic identifier creation, or indexes.
Queries can also invoke statements that insert, update, or delete data, and do
not return any data as a result.</p>
<p>The query text is not parsed by Trino, only passed through, and therefore only
subject to any security or access control of the underlying data source.</p>
<p>The following example sets the current database to the <code class="docutils literal notranslate"><span class="pre">example_schema</span></code> of the
<code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Then it calls the procedure in that schema to drop the
default value from <code class="docutils literal notranslate"><span class="pre">your_column</span></code> on <code class="docutils literal notranslate"><span class="pre">your_table</span></code> table using the standard SQL
syntax in the parameter value assigned for <code class="docutils literal notranslate"><span class="pre">query</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="k">execute</span><span class="p">(</span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ALTER TABLE your_table ALTER COLUMN your_column DROP DEFAULT'</span><span class="p">);</span>
</pre></div>
</div>
<p>Verify that the specific database supports this syntax, and adapt as necessary
based on the documentation for the specific connected database and database
version.</p>
</section>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="bigquery.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc">table functions</span></a> to
access BigQuery.</p>
<section id="query-varchar-table">
<span id="bigquery-query-function"></span><h4 id="query-varchar-table"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="bigquery.html#query-varchar-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">query</span></code> function allows you to query the underlying BigQuery directly. It
requires syntax native to BigQuery, because the full query is pushed down and
processed by BigQuery. This can be useful for accessing native features which are
not available in Trino or for improving query performance in situations where
running a query natively may be faster.</p>
<p>The native query passed to the underlying data source is required to return a
table as a result set. Only the data source performs validation or security
checks for these queries using its own configuration. Trino does not perform
these tasks. Only use passthrough queries to read data.</p>
<p>For example, query the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog and group and concatenate all
employee IDs by manager ID:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">query</span><span class="p">(</span>
<span class="w">      </span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'SELECT</span>
<span class="s1">        manager_id, STRING_AGG(employee_id)</span>
<span class="s1">      FROM</span>
<span class="s1">        company.employees</span>
<span class="s1">      GROUP BY</span>
<span class="s1">        manager_id'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The query engine does not preserve the order of the results of this
function. If the passed query contains an <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause, the
function result may not be ordered as expected.</p>
</div>
</section>
</section>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="bigquery.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements, detailed in the
following sections.</p>
<section id="pushdown">
<span id="bigquery-pushdown"></span><h3 id="pushdown">Pushdown<a class="headerlink" href="bigquery.html#pushdown" title="Link to this heading">#</a></h3>
<p>The connector supports pushdown for a number of operations:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../optimizer/pushdown.html#limit-pushdown"><span class="std std-ref">Limit pushdown</span></a> for access to tables and other objects when using the REST
API to reduce CPU consumption in BigQuery and performance overall. Pushdown is
not supported by the Storage API, used for the more common Trino-managed
tables, and therefore not used for access with it.</p></li>
</ul>
</section>
</section>
<section id="faq">
<h2 id="faq">FAQ<a class="headerlink" href="bigquery.html#faq" title="Link to this heading">#</a></h2>
<section id="what-is-the-pricing-for-the-storage-api">
<h3 id="what-is-the-pricing-for-the-storage-api">What is the Pricing for the Storage API?<a class="headerlink" href="bigquery.html#what-is-the-pricing-for-the-storage-api" title="Link to this heading">#</a></h3>
<p>See the <a class="reference external" href="https://cloud.google.com/bigquery/pricing#storage-api">BigQuery pricing documentation</a>.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../connector.html" title="Connectors"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Connectors </span>
              </div>
            </a>
          
          
            <a href="blackhole.html" title="Black Hole connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Black Hole connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>