<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Pinot connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="pinot.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="PostgreSQL connector" href="postgresql.html" />
    <link rel="prev" title="Oracle connector" href="oracle.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="pinot.html#connector/pinot" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Pinot connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Pinot </label>
    
      <a href="pinot.html#" class="md-nav__link md-nav__link--active">Pinot</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pinot.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#grpc-configuration-properties" class="md-nav__link">gRPC configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pinot.html#querying-pinot-tables" class="md-nav__link">Querying Pinot tables</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#dynamic-tables" class="md-nav__link">Dynamic tables</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#pinot-type-to-trino-type-mapping" class="md-nav__link">Pinot type to Trino type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#date-type" class="md-nav__link">Date Type</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#null-handling" class="md-nav__link">Null Handling</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pinot.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#pushdown" class="md-nav__link">Pushdown</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pinot.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#grpc-configuration-properties" class="md-nav__link">gRPC configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pinot.html#querying-pinot-tables" class="md-nav__link">Querying Pinot tables</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#dynamic-tables" class="md-nav__link">Dynamic tables</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#pinot-type-to-trino-type-mapping" class="md-nav__link">Pinot type to Trino type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pinot.html#date-type" class="md-nav__link">Date Type</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#null-handling" class="md-nav__link">Null Handling</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pinot.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
        <li class="md-nav__item"><a href="pinot.html#pushdown" class="md-nav__link">Pushdown</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="pinot-connector">
<h1 id="connector-pinot--page-root">Pinot connector<a class="headerlink" href="pinot.html#connector-pinot--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/pinot.png"/><p>The Pinot connector allows Trino to query data stored in
<a class="reference external" href="https://pinot.apache.org/">Apache Pinot™</a>.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="pinot.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to Pinot, you need:</p>
<ul class="simple">
<li><p>Pinot 1.1.0 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to the Pinot controller
nodes. Port 8098 is the default port.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="pinot.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the Pinot connector, create a catalog properties file
e.g. <code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with at least the following contents:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=pinot
pinot.controller-urls=host1:8098,host2:8098
</pre></div>
</div>
<p>Replace <code class="docutils literal notranslate"><span class="pre">host1:8098,host2:8098</span></code> with a comma-separated list of Pinot controller nodes.
This can be the ip or the FQDN, the url scheme (<code class="docutils literal notranslate"><span class="pre">http://</span></code>) is optional.</p>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="pinot.html#configuration-properties" title="Link to this heading">#</a></h2>
<section id="general-configuration-properties">
<h3 id="general-configuration-properties">General configuration properties<a class="headerlink" href="pinot.html#general-configuration-properties" title="Link to this heading">#</a></h3>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.controller-urls</span></code></p></td>
<td><p>Yes</p></td>
<td><p>A comma separated list of controller hosts. If Pinot is deployed via <a class="reference external" href="https://kubernetes.io/">Kubernetes</a> this needs to point to the controller service endpoint. The Pinot broker and server must be accessible via DNS as Pinot returns hostnames and not IP addresses.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.broker-url</span></code></p></td>
<td><p>No</p></td>
<td><p>A host and port of broker. If broker URL exposed by Pinot controller API is not accessible, this property can be used to specify the broker endpoint. Enabling this property will disable broker discovery.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.connection-timeout</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot connection timeout, default is <code class="docutils literal notranslate"><span class="pre">15s</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.metadata-expiry</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot metadata expiration time, default is <code class="docutils literal notranslate"><span class="pre">2m</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.type</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot authentication method for controller requests. Allowed values are <code class="docutils literal notranslate"><span class="pre">NONE</span></code> and <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> - defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code> which is no authentication.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.user</span></code></p></td>
<td><p>No</p></td>
<td><p>Controller username for basic authentication method.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.password</span></code></p></td>
<td><p>No</p></td>
<td><p>Controller password for basic authentication method.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.type</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot authentication method for broker requests. Allowed values are <code class="docutils literal notranslate"><span class="pre">NONE</span></code> and <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> - defaults to <code class="docutils literal notranslate"><span class="pre">NONE</span></code> which is no authentication.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.user</span></code></p></td>
<td><p>No</p></td>
<td><p>Broker username for basic authentication method.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.password</span></code></p></td>
<td><p>No</p></td>
<td><p>Broker password for basic authentication method.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.max-rows-per-split-for-segment-queries</span></code></p></td>
<td><p>No</p></td>
<td><p>Fail query if Pinot server split returns more rows than configured, default to <code class="docutils literal notranslate"><span class="pre">2,147,483,647</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.prefer-broker-queries</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot query plan prefers to query Pinot broker, default is <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.forbid-segment-queries</span></code></p></td>
<td><p>No</p></td>
<td><p>Forbid parallel querying and force all querying to happen via the broker, default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.segments-per-split</span></code></p></td>
<td><p>No</p></td>
<td><p>The number of segments processed in a split. Setting this higher reduces the number of requests made to Pinot. This is useful for smaller Pinot clusters, default is <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.fetch-retry-count</span></code></p></td>
<td><p>No</p></td>
<td><p>Retry count for retriable Pinot data fetch calls, default is <code class="docutils literal notranslate"><span class="pre">2</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.non-aggregate-limit-for-broker-queries</span></code></p></td>
<td><p>No</p></td>
<td><p>Max limit for non aggregate queries to the Pinot broker, default is <code class="docutils literal notranslate"><span class="pre">25,000</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.max-rows-for-broker-queries</span></code></p></td>
<td><p>No</p></td>
<td><p>Max rows for a broker query can return, default is <code class="docutils literal notranslate"><span class="pre">50,000</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.aggregation-pushdown.enabled</span></code></p></td>
<td><p>No</p></td>
<td><p>Push down aggregation queries, default is <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.count-distinct-pushdown.enabled</span></code></p></td>
<td><p>No</p></td>
<td><p>Push down count distinct queries to Pinot, default is <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.target-segment-page-size</span></code></p></td>
<td><p>No</p></td>
<td><p>Max allowed page size for segment query, default is <code class="docutils literal notranslate"><span class="pre">1MB</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.proxy.enabled</span></code></p></td>
<td><p>No</p></td>
<td><p>Use Pinot Proxy for controller and broker requests, default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>If <code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.type</span></code> is set to <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> then both <code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.user</span></code> and
<code class="docutils literal notranslate"><span class="pre">pinot.controller.authentication.password</span></code> are required.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.type</span></code> is set to <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> then both <code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.user</span></code> and
<code class="docutils literal notranslate"><span class="pre">pinot.broker.authentication.password</span></code> are required.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">pinot.controller-urls</span></code> uses <code class="docutils literal notranslate"><span class="pre">https</span></code> scheme then TLS is enabled for all connections including brokers.</p>
</section>
<section id="grpc-configuration-properties">
<h3 id="grpc-configuration-properties">gRPC configuration properties<a class="headerlink" href="pinot.html#grpc-configuration-properties" title="Link to this heading">#</a></h3>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.port</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot gRPC port, default to <code class="docutils literal notranslate"><span class="pre">8090</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.max-inbound-message-size</span></code></p></td>
<td><p>No</p></td>
<td><p>Max inbound message bytes when init gRPC client, default is <code class="docutils literal notranslate"><span class="pre">128MB</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.use-plain-text</span></code></p></td>
<td><p>No</p></td>
<td><p>Use plain text for gRPC communication, default to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.keystore-type</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS keystore type for gRPC connection, default is <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.keystore-path</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS keystore file location for gRPC connection, default is empty.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.keystore-password</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS keystore password, default is empty.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.truststore-type</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS truststore type for gRPC connection, default is <code class="docutils literal notranslate"><span class="pre">JKS</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.truststore-path</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS truststore file location for gRPC connection, default is empty.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.truststore-password</span></code></p></td>
<td><p>No</p></td>
<td><p>TLS truststore password, default is empty.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.tls.ssl-provider</span></code></p></td>
<td><p>No</p></td>
<td><p>SSL provider, default is <code class="docutils literal notranslate"><span class="pre">JDK</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">pinot.grpc.proxy-uri</span></code></p></td>
<td><p>No</p></td>
<td><p>Pinot Rest Proxy gRPC endpoint URI, default is null.</p></td>
</tr>
</tbody>
</table>
<p>For more Apache Pinot TLS configurations, please also refer to <a class="reference external" href="https://docs.pinot.apache.org/operators/tutorials/configuring-tls-ssl">Configuring TLS/SSL</a>.</p>
<p>You can use <a class="reference internal" href="../security/secrets.html"><span class="doc">secrets</span></a> to avoid actual values in the catalog properties files.</p>
</section>
</section>
<section id="querying-pinot-tables">
<h2 id="querying-pinot-tables">Querying Pinot tables<a class="headerlink" href="pinot.html#querying-pinot-tables" title="Link to this heading">#</a></h2>
<p>The Pinot connector automatically exposes all tables in the default schema of the catalog.
You can list all tables in the pinot catalog with the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span><span class="w"> </span><span class="n">TABLES</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">;</span>
</pre></div>
</div>
<p>You can list columns in the flight_status table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">DESCRIBE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">flight_status</span><span class="p">;</span>
<span class="k">SHOW</span><span class="w"> </span><span class="n">COLUMNS</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="n">flight_status</span><span class="p">;</span>
</pre></div>
</div>
<p>Queries written with SQL are fully supported and can include filters and limits:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">foo</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">pinot_table</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">bar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">baz</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'ONE'</span><span class="p">,</span><span class="w"> </span><span class="s1">'TWO'</span><span class="p">,</span><span class="w"> </span><span class="s1">'THREE'</span><span class="p">)</span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">25000</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="dynamic-tables">
<span id="pinot-dynamic-tables"></span><h2 id="dynamic-tables">Dynamic tables<a class="headerlink" href="pinot.html#dynamic-tables" title="Link to this heading">#</a></h2>
<p>To leverage Pinot’s fast aggregation, a Pinot query written in PQL can be used as the table name.
Filters and limits in the outer query are pushed down to Pinot.
Let’s look at an example query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">default</span><span class="p">.</span><span class="ss">"SELECT MAX(col1), COUNT(col2) FROM pinot_table GROUP BY col3, col4"</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span><span class="s1">'FOO'</span><span class="p">,</span><span class="w"> </span><span class="s1">'BAR'</span><span class="p">)</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">col4</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">50</span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">30000</span>
</pre></div>
</div>
<p>Filtering and limit processing is pushed down to Pinot.</p>
<p>The queries are routed to the broker and are more suitable to aggregate queries.</p>
<p>For <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> queries without aggregates it is more performant to issue a regular SQL query.
Processing is routed directly to the servers that store the data.</p>
<p>The above query is translated to the following Pinot PQL query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">MAX</span><span class="p">(</span><span class="n">col1</span><span class="p">),</span><span class="w"> </span><span class="k">COUNT</span><span class="p">(</span><span class="n">col2</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">pinot_table</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">col3</span><span class="w"> </span><span class="k">IN</span><span class="p">(</span><span class="s1">'FOO'</span><span class="p">,</span><span class="w"> </span><span class="s1">'BAR'</span><span class="p">)</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">col4</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">50</span>
<span class="n">TOP</span><span class="w"> </span><span class="mi">30000</span>
</pre></div>
</div>
</section>
<section id="type-mapping">
<span id="pinot-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="pinot.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Pinot each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">maps some types</span></a> when reading data.</p>
<section id="pinot-type-to-trino-type-mapping">
<h3 id="pinot-type-to-trino-type-mapping">Pinot type to Trino type mapping<a class="headerlink" href="pinot.html#pinot-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Pinot types to the corresponding Trino types
according to the following table:</p>
<table id="id1">
<caption><span class="caption-text">Pinot type to Trino type mapping</span><a class="headerlink" href="pinot.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 56%"/>
<col style="width: 44%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Pinot type</p></th>
<th class="head"><p>Trino type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BYTES</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">INT_ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LONG_ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT_ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE_ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRING_ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
<section id="date-type">
<h4 id="date-type">Date Type<a class="headerlink" href="pinot.html#date-type" title="Link to this heading">#</a></h4>
<p>For Pinot <code class="docutils literal notranslate"><span class="pre">DateTimeFields</span></code>, if the <code class="docutils literal notranslate"><span class="pre">FormatSpec</span></code> is in days,
then it is converted to a Trino <code class="docutils literal notranslate"><span class="pre">DATE</span></code> type.
Pinot allows for <code class="docutils literal notranslate"><span class="pre">LONG</span></code> fields to have a <code class="docutils literal notranslate"><span class="pre">FormatSpec</span></code> of days as well, if the
value is larger than <code class="docutils literal notranslate"><span class="pre">Integer.MAX_VALUE</span></code> then the conversion to Trino <code class="docutils literal notranslate"><span class="pre">DATE</span></code> fails.</p>
</section>
<section id="null-handling">
<h4 id="null-handling">Null Handling<a class="headerlink" href="pinot.html#null-handling" title="Link to this heading">#</a></h4>
<p>If a Pinot TableSpec has <code class="docutils literal notranslate"><span class="pre">nullHandlingEnabled</span></code> set to true, then for numeric
types the null value is encoded as <code class="docutils literal notranslate"><span class="pre">MIN_VALUE</span></code> for that type.
For Pinot <code class="docutils literal notranslate"><span class="pre">STRING</span></code> type, the value <code class="docutils literal notranslate"><span class="pre">null</span></code> is interpreted as a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value.</p>
</section>
</section>
</section>
<section id="sql-support">
<span id="pinot-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="pinot.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements to access data and
metadata in Pinot.</p>
</section>
<section id="pushdown">
<span id="pinot-pushdown"></span><h2 id="pushdown">Pushdown<a class="headerlink" href="pinot.html#pushdown" title="Link to this heading">#</a></h2>
<p>The connector supports pushdown for a number of operations:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../optimizer/pushdown.html#limit-pushdown"><span class="std std-ref">Limit pushdown</span></a></p></li>
</ul>
<p><a class="reference internal" href="../optimizer/pushdown.html#aggregation-pushdown"><span class="std std-ref">Aggregate pushdown</span></a> for the following functions:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../functions/aggregate.html#avg" title="avg"><code class="xref py py-func docutils literal notranslate"><span class="pre">avg()</span></code></a></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#approx_distinct" title="approx_distinct"><code class="xref py py-func docutils literal notranslate"><span class="pre">approx_distinct()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">count(*)</span></code> and <code class="docutils literal notranslate"><span class="pre">count(distinct)</span></code> variations of <a class="reference internal" href="../functions/aggregate.html#count" title="count"><code class="xref py py-func docutils literal notranslate"><span class="pre">count()</span></code></a></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#max" title="max"><code class="xref py py-func docutils literal notranslate"><span class="pre">max()</span></code></a></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#min" title="min"><code class="xref py py-func docutils literal notranslate"><span class="pre">min()</span></code></a></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#sum" title="sum"><code class="xref py py-func docutils literal notranslate"><span class="pre">sum()</span></code></a></p></li>
</ul>
<p>Aggregate function pushdown is enabled by default, but can be disabled with the
catalog property <code class="docutils literal notranslate"><span class="pre">pinot.aggregation-pushdown.enabled</span></code> or the catalog session
property <code class="docutils literal notranslate"><span class="pre">aggregation_pushdown_enabled</span></code>.</p>
<p>A <code class="docutils literal notranslate"><span class="pre">count(distint)</span></code> pushdown may cause Pinot to run a full table scan with
significant performance impact. If you encounter this problem, you can disable
it with the catalog property <code class="docutils literal notranslate"><span class="pre">pinot.count-distinct-pushdown.enabled</span></code> or the
catalog session property <code class="docutils literal notranslate"><span class="pre">count_distinct_pushdown_enabled</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The connector performs pushdown where performance may be improved, but in
order to preserve correctness an operation may not be pushed down. When
pushdown of an operation may result in better performance but risks
correctness, the connector prioritizes correctness.</p>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="oracle.html" title="Oracle connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Oracle connector </span>
              </div>
            </a>
          
          
            <a href="postgresql.html" title="PostgreSQL connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> PostgreSQL connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>