<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Kafka connector tutorial &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="kafka-tutorial.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Loki connector" href="loki.html" />
    <link rel="prev" title="Kafka connector" href="kafka.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="kafka-tutorial.html#connector/kafka-tutorial" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Kafka connector tutorial </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Tutorial </label>
    
      <a href="kafka-tutorial.html#" class="md-nav__link md-nav__link--active">Tutorial</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kafka-tutorial.html#introduction" class="md-nav__link">Introduction</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#installation" class="md-nav__link">Installation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-1-install-apache-kafka" class="md-nav__link">Step 1: Install Apache Kafka</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-2-load-data" class="md-nav__link">Step 2: Load data</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-3-make-the-kafka-topics-known-to-trino" class="md-nav__link">Step 3: Make the Kafka topics known to Trino</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-4-basic-data-querying" class="md-nav__link">Step 4: Basic data querying</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-5-add-a-topic-description-file" class="md-nav__link">Step 5: Add a topic description file</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-6-map-all-the-values-from-the-topic-message-onto-columns" class="md-nav__link">Step 6: Map all the values from the topic message onto columns</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-7-use-live-data" class="md-nav__link">Step 7: Use live data</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka-tutorial.html#setup-a-live-twitter-feed" class="md-nav__link">Setup a live Twitter feed</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#create-a-tweets-table-on-trino" class="md-nav__link">Create a tweets table on Trino</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#feed-live-data" class="md-nav__link">Feed live data</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#epilogue-time-stamps" class="md-nav__link">Epilogue: Time stamps</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kafka-tutorial.html#introduction" class="md-nav__link">Introduction</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#installation" class="md-nav__link">Installation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-1-install-apache-kafka" class="md-nav__link">Step 1: Install Apache Kafka</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-2-load-data" class="md-nav__link">Step 2: Load data</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-3-make-the-kafka-topics-known-to-trino" class="md-nav__link">Step 3: Make the Kafka topics known to Trino</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-4-basic-data-querying" class="md-nav__link">Step 4: Basic data querying</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-5-add-a-topic-description-file" class="md-nav__link">Step 5: Add a topic description file</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-6-map-all-the-values-from-the-topic-message-onto-columns" class="md-nav__link">Step 6: Map all the values from the topic message onto columns</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#step-7-use-live-data" class="md-nav__link">Step 7: Use live data</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kafka-tutorial.html#setup-a-live-twitter-feed" class="md-nav__link">Setup a live Twitter feed</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#create-a-tweets-table-on-trino" class="md-nav__link">Create a tweets table on Trino</a>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#feed-live-data" class="md-nav__link">Feed live data</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kafka-tutorial.html#epilogue-time-stamps" class="md-nav__link">Epilogue: Time stamps</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="kafka-connector-tutorial">
<h1 id="connector-kafka-tutorial--page-root">Kafka connector tutorial<a class="headerlink" href="kafka-tutorial.html#connector-kafka-tutorial--page-root" title="Link to this heading">#</a></h1>
<section id="introduction">
<h2 id="introduction">Introduction<a class="headerlink" href="kafka-tutorial.html#introduction" title="Link to this heading">#</a></h2>
<p>The <a class="reference internal" href="kafka.html"><span class="doc">Kafka connector</span></a> for Trino allows access to live topic data from
Apache Kafka using Trino. This tutorial shows how to set up topics, and
how to create the topic description files that back Trino tables.</p>
</section>
<section id="installation">
<h2 id="installation">Installation<a class="headerlink" href="kafka-tutorial.html#installation" title="Link to this heading">#</a></h2>
<p>This tutorial assumes familiarity with Trino and a working local Trino
installation (see <a class="reference internal" href="../installation/deployment.html"><span class="doc">Deploying Trino</span></a>). It focuses on
setting up Apache Kafka and integrating it with Trino.</p>
<section id="step-1-install-apache-kafka">
<h3 id="step-1-install-apache-kafka">Step 1: Install Apache Kafka<a class="headerlink" href="kafka-tutorial.html#step-1-install-apache-kafka" title="Link to this heading">#</a></h3>
<p>Download and extract <a class="reference external" href="https://kafka.apache.org/">Apache Kafka</a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This tutorial was tested with Apache Kafka 0.8.1.
It should work with any 0.8.x version of Apache Kafka.</p>
</div>
<p>Start ZooKeeper and the Kafka server:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ bin/zookeeper-server-start.sh config/zookeeper.properties
[2013-04-22 15:01:37,495] INFO Reading configuration from: config/zookeeper.properties (org.apache.zookeeper.server.quorum.QuorumPeerConfig)
...
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ bin/kafka-server-start.sh config/server.properties
[2013-04-22 15:01:47,028] INFO Verifying properties (kafka.utils.VerifiableProperties)
[2013-04-22 15:01:47,051] INFO Property socket.send.buffer.bytes is overridden to 1048576 (kafka.utils.VerifiableProperties)
...
</pre></div>
</div>
<p>This starts Zookeeper on port <code class="docutils literal notranslate"><span class="pre">2181</span></code> and Kafka on port <code class="docutils literal notranslate"><span class="pre">9092</span></code>.</p>
</section>
<section id="step-2-load-data">
<h3 id="step-2-load-data">Step 2: Load data<a class="headerlink" href="kafka-tutorial.html#step-2-load-data" title="Link to this heading">#</a></h3>
<p>Download the tpch-kafka loader from Maven Central:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ curl -o kafka-tpch https://repo1.maven.org/maven2/de/softwareforge/kafka_tpch_0811/1.0/kafka_tpch_0811-1.0.sh
$ chmod 755 kafka-tpch
</pre></div>
</div>
<p>Now run the <code class="docutils literal notranslate"><span class="pre">kafka-tpch</span></code> program to preload a number of topics with tpch data:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ ./kafka-tpch load --brokers localhost:9092 --prefix tpch. --tpch-type tiny
2014-07-28T17:17:07.594-0700     INFO    main    io.airlift.log.Logging    Logging to stderr
2014-07-28T17:17:07.623-0700     INFO    main    de.softwareforge.kafka.LoadCommand    Processing tables: [customer, orders, lineitem, part, partsupp, supplier, nation, region]
2014-07-28T17:17:07.981-0700     INFO    pool-1-thread-1    de.softwareforge.kafka.LoadCommand    Loading table 'customer' into topic 'tpch.customer'...
2014-07-28T17:17:07.981-0700     INFO    pool-1-thread-2    de.softwareforge.kafka.LoadCommand    Loading table 'orders' into topic 'tpch.orders'...
2014-07-28T17:17:07.981-0700     INFO    pool-1-thread-3    de.softwareforge.kafka.LoadCommand    Loading table 'lineitem' into topic 'tpch.lineitem'...
2014-07-28T17:17:07.982-0700     INFO    pool-1-thread-4    de.softwareforge.kafka.LoadCommand    Loading table 'part' into topic 'tpch.part'...
2014-07-28T17:17:07.982-0700     INFO    pool-1-thread-5    de.softwareforge.kafka.LoadCommand    Loading table 'partsupp' into topic 'tpch.partsupp'...
2014-07-28T17:17:07.982-0700     INFO    pool-1-thread-6    de.softwareforge.kafka.LoadCommand    Loading table 'supplier' into topic 'tpch.supplier'...
2014-07-28T17:17:07.982-0700     INFO    pool-1-thread-7    de.softwareforge.kafka.LoadCommand    Loading table 'nation' into topic 'tpch.nation'...
2014-07-28T17:17:07.982-0700     INFO    pool-1-thread-8    de.softwareforge.kafka.LoadCommand    Loading table 'region' into topic 'tpch.region'...
2014-07-28T17:17:10.612-0700    ERROR    pool-1-thread-8    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.region
2014-07-28T17:17:10.781-0700     INFO    pool-1-thread-8    de.softwareforge.kafka.LoadCommand    Generated 5 rows for table 'region'.
2014-07-28T17:17:10.797-0700    ERROR    pool-1-thread-3    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.lineitem
2014-07-28T17:17:10.932-0700    ERROR    pool-1-thread-1    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.customer
2014-07-28T17:17:11.068-0700    ERROR    pool-1-thread-2    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.orders
2014-07-28T17:17:11.200-0700    ERROR    pool-1-thread-6    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.supplier
2014-07-28T17:17:11.319-0700     INFO    pool-1-thread-6    de.softwareforge.kafka.LoadCommand    Generated 100 rows for table 'supplier'.
2014-07-28T17:17:11.333-0700    ERROR    pool-1-thread-4    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.part
2014-07-28T17:17:11.466-0700    ERROR    pool-1-thread-5    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.partsupp
2014-07-28T17:17:11.597-0700    ERROR    pool-1-thread-7    kafka.producer.async.DefaultEventHandler    Failed to collate messages by topic, partition due to: Failed to fetch topic metadata for topic: tpch.nation
2014-07-28T17:17:11.706-0700     INFO    pool-1-thread-7    de.softwareforge.kafka.LoadCommand    Generated 25 rows for table 'nation'.
2014-07-28T17:17:12.180-0700     INFO    pool-1-thread-1    de.softwareforge.kafka.LoadCommand    Generated 1500 rows for table 'customer'.
2014-07-28T17:17:12.251-0700     INFO    pool-1-thread-4    de.softwareforge.kafka.LoadCommand    Generated 2000 rows for table 'part'.
2014-07-28T17:17:12.905-0700     INFO    pool-1-thread-2    de.softwareforge.kafka.LoadCommand    Generated 15000 rows for table 'orders'.
2014-07-28T17:17:12.919-0700     INFO    pool-1-thread-5    de.softwareforge.kafka.LoadCommand    Generated 8000 rows for table 'partsupp'.
2014-07-28T17:17:13.877-0700     INFO    pool-1-thread-3    de.softwareforge.kafka.LoadCommand    Generated 60175 rows for table 'lineitem'.
</pre></div>
</div>
<p>Kafka now has a number of topics that are preloaded with data to query.</p>
</section>
<section id="step-3-make-the-kafka-topics-known-to-trino">
<h3 id="step-3-make-the-kafka-topics-known-to-trino">Step 3: Make the Kafka topics known to Trino<a class="headerlink" href="kafka-tutorial.html#step-3-make-the-kafka-topics-known-to-trino" title="Link to this heading">#</a></h3>
<p>In your Trino installation, add a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/kafka.properties</span></code> for the Kafka connector.
This file lists the Kafka nodes and topics:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=kafka
kafka.nodes=localhost:9092
kafka.table-names=tpch.customer,tpch.orders,tpch.lineitem,tpch.part,tpch.partsupp,tpch.supplier,tpch.nation,tpch.region
kafka.hide-internal-columns=false
</pre></div>
</div>
<p>Now start Trino:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ bin/launcher start
</pre></div>
</div>
<p>Because the Kafka tables all have the <code class="docutils literal notranslate"><span class="pre">tpch.</span></code> prefix in the configuration,
the tables are in the <code class="docutils literal notranslate"><span class="pre">tpch</span></code> schema. The connector is mounted into the
<code class="docutils literal notranslate"><span class="pre">kafka</span></code> catalog, because the properties file is named <code class="docutils literal notranslate"><span class="pre">kafka.properties</span></code>.</p>
<p>Start the <a class="reference internal" href="../client/cli.html"><span class="doc">Trino CLI</span></a>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ ./trino --catalog kafka --schema tpch
</pre></div>
</div>
<p>List the tables to verify that things are working:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:tpch&gt; SHOW TABLES;
  Table
----------
 customer
 lineitem
 nation
 orders
 part
 partsupp
 region
 supplier
(8 rows)
</pre></div>
</div>
</section>
<section id="step-4-basic-data-querying">
<h3 id="step-4-basic-data-querying">Step 4: Basic data querying<a class="headerlink" href="kafka-tutorial.html#step-4-basic-data-querying" title="Link to this heading">#</a></h3>
<p>Kafka data is unstructured, and it has no metadata to describe the format of
the messages. Without further configuration, the Kafka connector can access
the data, and map it in raw form. However there are no actual columns besides the
built-in ones:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:tpch&gt; DESCRIBE customer;
      Column       |  Type      | Extra |                   Comment
-------------------+------------+-------+---------------------------------------------
 _partition_id     | bigint     |       | Partition Id
 _partition_offset | bigint     |       | Offset for the message within the partition
 _key              | varchar    |       | Key text
 _key_corrupt      | boolean    |       | Key data is corrupt
 _key_length       | bigint     |       | Total number of key bytes
 _message          | varchar    |       | Message text
 _message_corrupt  | boolean    |       | Message data is corrupt
 _message_length   | bigint     |       | Total number of message bytes
 _timestamp        | timestamp  |       | Message timestamp
(11 rows)

trino:tpch&gt; SELECT count(*) FROM customer;
 _col0
-------
  1500

trino:tpch&gt; SELECT _message FROM customer LIMIT 5;
                                                                                                                                                 _message
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 {"rowNumber":1,"customerKey":1,"name":"Customer#*********","address":"IVhzIApeRb ot,c,E","nationKey":15,"phone":"25-************","accountBalance":711.56,"marketSegment":"BUILDING","comment":"to the even, regular platelets. regular, ironic epitaphs nag e"}
 {"rowNumber":3,"customerKey":3,"name":"Customer#*********","address":"MG9kdTD2WBHm","nationKey":1,"phone":"11-************","accountBalance":7498.12,"marketSegment":"AUTOMOBILE","comment":" deposits eat slyly ironic, even instructions. express foxes detect slyly. blithel
 {"rowNumber":5,"customerKey":5,"name":"Customer#*********","address":"KvpyuHCplrB84WgAiGV6sYpZq7Tj","nationKey":3,"phone":"13-************","accountBalance":794.47,"marketSegment":"HOUSEHOLD","comment":"n accounts will have to unwind. foxes cajole accor"}
 {"rowNumber":7,"customerKey":7,"name":"Customer#*********","address":"TcGe5gaZNgVePxU5kRrvXBfkasDTea","nationKey":18,"phone":"28-************","accountBalance":9561.95,"marketSegment":"AUTOMOBILE","comment":"ainst the ironic, express theodolites. express, even pinto bean
 {"rowNumber":9,"customerKey":9,"name":"Customer#*********","address":"xKiAFTjUsCuxfeleNqefumTrjS","nationKey":8,"phone":"18-************","accountBalance":8324.07,"marketSegment":"FURNITURE","comment":"r theodolites according to the requests wake thinly excuses: pending
(5 rows)

trino:tpch&gt; SELECT sum(cast(json_extract_scalar(_message, '$.accountBalance') AS DOUBLE)) FROM customer LIMIT 10;
   _col0
------------
 6681865.59
(1 row)
</pre></div>
</div>
<p>The data from Kafka can be queried using Trino, but it is not yet in
actual table shape. The raw data is available through the <code class="docutils literal notranslate"><span class="pre">_message</span></code> and
<code class="docutils literal notranslate"><span class="pre">_key</span></code> columns, but it is not decoded into columns. As the sample data is
in JSON format, the <a class="reference internal" href="../functions/json.html"><span class="doc">JSON functions and operators</span></a> built into Trino can be used
to slice the data.</p>
</section>
<section id="step-5-add-a-topic-description-file">
<h3 id="step-5-add-a-topic-description-file">Step 5: Add a topic description file<a class="headerlink" href="kafka-tutorial.html#step-5-add-a-topic-description-file" title="Link to this heading">#</a></h3>
<p>The Kafka connector supports topic description files to turn raw data into
table format. These files are located in the <code class="docutils literal notranslate"><span class="pre">etc/kafka</span></code> folder in the
Trino installation and must end with <code class="docutils literal notranslate"><span class="pre">.json</span></code>. It is recommended that
the file name matches the table name, but this is not necessary.</p>
<p>Add the following file as <code class="docutils literal notranslate"><span class="pre">etc/kafka/tpch.customer.json</span></code> and restart Trino:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"customer"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tpch"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tpch.customer"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"raw"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"kafka_key"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LONG"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"hidden"</span><span class="p">:</span><span class="w"> </span><span class="s2">"false"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The customer table now has an additional column: <code class="docutils literal notranslate"><span class="pre">kafka_key</span></code>.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:tpch&gt; DESCRIBE customer;
      Column       |  Type      | Extra |                   Comment
-------------------+------------+-------+---------------------------------------------
 kafka_key         | bigint     |       |
 _partition_id     | bigint     |       | Partition Id
 _partition_offset | bigint     |       | Offset for the message within the partition
 _key              | varchar    |       | Key text
 _key_corrupt      | boolean    |       | Key data is corrupt
 _key_length       | bigint     |       | Total number of key bytes
 _message          | varchar    |       | Message text
 _message_corrupt  | boolean    |       | Message data is corrupt
 _message_length   | bigint     |       | Total number of message bytes
 _timestamp        | timestamp  |       | Message timestamp
(12 rows)

trino:tpch&gt; SELECT kafka_key FROM customer ORDER BY kafka_key LIMIT 10;
 kafka_key
-----------
         0
         <USER>
         <GROUP>
         3
         4
         5
         6
         7
         8
         9
(10 rows)
</pre></div>
</div>
<p>The topic definition file maps the internal Kafka key, which is a raw long
in eight bytes, onto a Trino <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code> column.</p>
</section>
<section id="step-6-map-all-the-values-from-the-topic-message-onto-columns">
<h3 id="step-6-map-all-the-values-from-the-topic-message-onto-columns">Step 6: Map all the values from the topic message onto columns<a class="headerlink" href="kafka-tutorial.html#step-6-map-all-the-values-from-the-topic-message-onto-columns" title="Link to this heading">#</a></h3>
<p>Update the <code class="docutils literal notranslate"><span class="pre">etc/kafka/tpch.customer.json</span></code> file to add fields for the
message, and restart Trino. As the fields in the message are JSON, it uses
the <code class="docutils literal notranslate"><span class="pre">JSON</span></code> data format. This is an example, where different data formats
are used for the key and the message.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"customer"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"schemaName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tpch"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tpch.customer"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"raw"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"kafka_key"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LONG"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"hidden"</span><span class="p">:</span><span class="w"> </span><span class="s2">"false"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"json"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"row_number"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rowNumber"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"customer_key"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"customerKey"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"address"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"address"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"nation_key"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"nationKey"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"phone"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"phone"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"account_balance"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"accountBalance"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"DOUBLE"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"market_segment"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"marketSegment"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"comment"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"comment"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Now for all the fields in the JSON of the message, columns are defined and
the sum query from earlier can operate on the <code class="docutils literal notranslate"><span class="pre">account_balance</span></code> column directly:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:tpch&gt; DESCRIBE customer;
      Column       |  Type      | Extra |                   Comment
-------------------+------------+-------+---------------------------------------------
 kafka_key         | bigint     |       |
 row_number        | bigint     |       |
 customer_key      | bigint     |       |
 name              | varchar    |       |
 address           | varchar    |       |
 nation_key        | bigint     |       |
 phone             | varchar    |       |
 account_balance   | double     |       |
 market_segment    | varchar    |       |
 comment           | varchar    |       |
 _partition_id     | bigint     |       | Partition Id
 _partition_offset | bigint     |       | Offset for the message within the partition
 _key              | varchar    |       | Key text
 _key_corrupt      | boolean    |       | Key data is corrupt
 _key_length       | bigint     |       | Total number of key bytes
 _message          | varchar    |       | Message text
 _message_corrupt  | boolean    |       | Message data is corrupt
 _message_length   | bigint     |       | Total number of message bytes
 _timestamp        | timestamp  |       | Message timestamp
(21 rows)

trino:tpch&gt; SELECT * FROM customer LIMIT 5;
 kafka_key | row_number | customer_key |        name        |                address                | nation_key |      phone      | account_balance | market_segment |                                                      comment
-----------+------------+--------------+--------------------+---------------------------------------+------------+-----------------+-----------------+----------------+---------------------------------------------------------------------------------------------------------
         1 |          2 |            2 | Customer#********* | XSTf4,NCwDVaWNe6tEgvwfmRchLXak        |         13 | 23-************ |          121.65 | AUTOMOBILE     | l accounts. blithely ironic theodolites integrate boldly: caref
         3 |          4 |            4 | Customer#********* | XxVSJsLAGtn                           |          4 | 14-************ |         2866.83 | MACHINERY      |  requests. final, regular ideas sleep final accou
         5 |          6 |            6 | Customer#********* | sKZz0CsnMD7mp4Xd0YrBvx,LREYKUWAh yVn  |         20 | 30-************ |         7638.57 | AUTOMOBILE     | tions. even deposits boost according to the slyly bold packages. final accounts cajole requests. furious
         7 |          8 |            8 | Customer#********* | I0B10bB0AymmC, 0PrRYBCP1yGJ8xcBPmWhl5 |         17 | 27-************ |         6819.74 | BUILDING       | among the slyly regular theodolites kindle blithely courts. carefully even theodolites haggle slyly alon
         9 |         10 |           10 | Customer#********* | 6LrEaV6KR6PLVcgl2ArL Q3rqzLzcT1 v2    |          5 | 15-************ |         2753.54 | HOUSEHOLD      | es regular deposits haggle. fur
(5 rows)

trino:tpch&gt; SELECT sum(account_balance) FROM customer LIMIT 10;
   _col0
------------
 6681865.59
(1 row)
</pre></div>
</div>
<p>Now all the fields from the <code class="docutils literal notranslate"><span class="pre">customer</span></code> topic messages are available as
Trino table columns.</p>
</section>
<section id="step-7-use-live-data">
<h3 id="step-7-use-live-data">Step 7: Use live data<a class="headerlink" href="kafka-tutorial.html#step-7-use-live-data" title="Link to this heading">#</a></h3>
<p>Trino can query live data in Kafka as it arrives. To simulate a live feed
of data, this tutorial sets up a feed of live tweets into Kafka.</p>
<section id="setup-a-live-twitter-feed">
<h4 id="setup-a-live-twitter-feed">Setup a live Twitter feed<a class="headerlink" href="kafka-tutorial.html#setup-a-live-twitter-feed" title="Link to this heading">#</a></h4>
<ul class="simple">
<li><p>Download the twistr tool</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ curl -o twistr https://repo1.maven.org/maven2/de/softwareforge/twistr_kafka_0811/1.2/twistr_kafka_0811-1.2.sh
$ chmod 755 twistr
</pre></div>
</div>
<ul class="simple">
<li><p>Create a developer account at <a class="reference external" href="https://dev.twitter.com/">https://dev.twitter.com/</a> and set up an
access and consumer token.</p></li>
<li><p>Create a <code class="docutils literal notranslate"><span class="pre">twistr.properties</span></code> file and put the access and consumer key
and secrets into it:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>twistr.access-token-key=...
twistr.access-token-secret=...
twistr.consumer-key=...
twistr.consumer-secret=...
twistr.kafka.brokers=localhost:9092
</pre></div>
</div>
</section>
<section id="create-a-tweets-table-on-trino">
<h4 id="create-a-tweets-table-on-trino">Create a tweets table on Trino<a class="headerlink" href="kafka-tutorial.html#create-a-tweets-table-on-trino" title="Link to this heading">#</a></h4>
<p>Add the tweets table to the <code class="docutils literal notranslate"><span class="pre">etc/catalog/kafka.properties</span></code> file:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=kafka
kafka.nodes=localhost:9092
kafka.table-names=tpch.customer,tpch.orders,tpch.lineitem,tpch.part,tpch.partsupp,tpch.supplier,tpch.nation,tpch.region,tweets
kafka.hide-internal-columns=false
</pre></div>
</div>
<p>Add a topic definition file for the Twitter feed as <code class="docutils literal notranslate"><span class="pre">etc/kafka/tweets.json</span></code>:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"tableName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tweets"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"topicName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"twitter_feed"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"json"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"raw"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"kafka_key"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LONG"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"hidden"</span><span class="p">:</span><span class="w"> </span><span class="s2">"false"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="s2">"json"</span><span class="p">,</span>
<span class="w">        </span><span class="nt">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"text"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"text"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"user_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"user/screen_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"lang"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"lang"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"created_at"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"created_at"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TIMESTAMP"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"dataFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rfc2822"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"favorite_count"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"favorite_count"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"retweet_count"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"retweet_count"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BIGINT"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"favorited"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"favorited"</span><span class="p">,</span>
<span class="w">                    </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BOOLEAN"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"id"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"id_str"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"in_reply_to_screen_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"in_reply_to_screen_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="p">{</span>
<span class="w">                </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"place_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"place/full_name"</span><span class="p">,</span>
<span class="w">                </span><span class="nt">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VARCHAR"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>As this table does not have an explicit schema name, it is placed
into the <code class="docutils literal notranslate"><span class="pre">default</span></code> schema.</p>
</section>
<section id="feed-live-data">
<h4 id="feed-live-data">Feed live data<a class="headerlink" href="kafka-tutorial.html#feed-live-data" title="Link to this heading">#</a></h4>
<p>Start the twistr tool:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ java -Dness.config.location=file:$(pwd) -Dness.config=twistr -jar ./twistr
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">twistr</span></code> connects to the Twitter API and feeds the “sample tweet” feed
into a Kafka topic called <code class="docutils literal notranslate"><span class="pre">twitter_feed</span></code>.</p>
<p>Now run queries against live data:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ ./trino --catalog kafka --schema default

trino:default&gt; SELECT count(*) FROM tweets;
 _col0
-------
  4467
(1 row)

trino:default&gt; SELECT count(*) FROM tweets;
 _col0
-------
  4517
(1 row)

trino:default&gt; SELECT count(*) FROM tweets;
 _col0
-------
  4572
(1 row)

trino:default&gt; SELECT kafka_key, user_name, lang, created_at FROM tweets LIMIT 10;
     kafka_key      |    user_name    | lang |       created_at
--------------------+-----------------+------+-------------------------
 494227746231685121 | burncaniff      | en   | 2014-07-29 14:07:31.000
 494227746214535169 | gu8tn           | ja   | 2014-07-29 14:07:31.000
 494227746219126785 | pequitamedicen  | es   | 2014-07-29 14:07:31.000
 494227746201931777 | josnyS          | ht   | 2014-07-29 14:07:31.000
 494227746219110401 | Cafe510         | en   | 2014-07-29 14:07:31.000
 494227746210332673 | Da_JuanAnd_Only | en   | 2014-07-29 14:07:31.000
 494227746193956865 | Smile_Kidrauhl6 | pt   | 2014-07-29 14:07:31.000
 494227750426017793 | CashforeverCD   | en   | 2014-07-29 14:07:32.000
 494227750396653569 | FilmArsivimiz   | tr   | 2014-07-29 14:07:32.000
 494227750388256769 | jmolas          | es   | 2014-07-29 14:07:32.000
(10 rows)
</pre></div>
</div>
<p>There is now a live feed into Kafka, which can be queried using Trino.</p>
</section>
</section>
<section id="epilogue-time-stamps">
<h3 id="epilogue-time-stamps">Epilogue: Time stamps<a class="headerlink" href="kafka-tutorial.html#epilogue-time-stamps" title="Link to this heading">#</a></h3>
<p>The tweets feed, that was set up in the last step, contains a timestamp in
RFC 2822 format as <code class="docutils literal notranslate"><span class="pre">created_at</span></code> attribute in each tweet.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:default&gt; SELECT DISTINCT json_extract_scalar(_message, '$.created_at')) AS raw_date
             -&gt; FROM tweets LIMIT 5;
            raw_date
--------------------------------
 Tue Jul 29 21:07:31 +0000 2014
 Tue Jul 29 21:07:32 +0000 2014
 Tue Jul 29 21:07:33 +0000 2014
 Tue Jul 29 21:07:34 +0000 2014
 Tue Jul 29 21:07:35 +0000 2014
(5 rows)
</pre></div>
</div>
<p>The topic definition file for the tweets table contains a mapping onto a
timestamp using the <code class="docutils literal notranslate"><span class="pre">rfc2822</span></code> converter:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>...
{
    "name": "created_at",
    "mapping": "created_at",
    "type": "TIMESTAMP",
    "dataFormat": "rfc2822"
},
...
</pre></div>
</div>
<p>This allows the raw data to be mapped onto a Trino TIMESTAMP column:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino:default&gt; SELECT created_at, raw_date FROM (
             -&gt;   SELECT created_at, json_extract_scalar(_message, '$.created_at') AS raw_date
             -&gt;   FROM tweets)
             -&gt; GROUP BY 1, 2 LIMIT 5;
       created_at        |            raw_date
-------------------------+--------------------------------
 2014-07-29 14:07:20.000 | Tue Jul 29 21:07:20 +0000 2014
 2014-07-29 14:07:21.000 | Tue Jul 29 21:07:21 +0000 2014
 2014-07-29 14:07:22.000 | Tue Jul 29 21:07:22 +0000 2014
 2014-07-29 14:07:23.000 | Tue Jul 29 21:07:23 +0000 2014
 2014-07-29 14:07:24.000 | Tue Jul 29 21:07:24 +0000 2014
(5 rows)
</pre></div>
</div>
<p>The Kafka connector contains converters for ISO 8601, RFC 2822 text
formats and for number-based timestamps using seconds or milliseconds
since the epoch. There is also a generic, text-based formatter, which uses
Joda-Time format strings to parse text columns.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="kafka.html" title="Kafka connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Kafka connector </span>
              </div>
            </a>
          
          
            <a href="loki.html" title="Loki connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Loki connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>