<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>MongoDB connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="mongodb.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="MySQL connector" href="mysql.html" />
    <link rel="prev" title="Memory connector" href="memory.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="mongodb.html#connector/mongodb" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> MongoDB connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> MongoDB </label>
    
      <a href="mongodb.html#" class="md-nav__link md-nav__link--active">MongoDB</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="mongodb.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#multiple-mongodb-clusters" class="md-nav__link">Multiple MongoDB clusters</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connection-url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-url</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-schema-collection" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.schema-collection</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-case-insensitive-name-matching" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.case-insensitive-name-matching</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-min-connections-per-host" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.min-connections-per-host</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connections-per-host" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connections-per-host</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-max-wait-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.max-wait-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-max-connection-idle-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.max-connection-idle-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connection-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-socket-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.socket-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-keystore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-truststore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-keystore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-truststore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-read-preference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.read-preference</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-write-concern" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.write-concern</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-required-replica-set" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.required-replica-set</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-cursor-batch-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.cursor-batch-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-allow-local-scheduling" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.allow-local-scheduling</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-dynamic-filtering-wait-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.dynamic-filtering.wait-timeout</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#table-definition" class="md-nav__link">Table definition</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#objectid" class="md-nav__link">ObjectId</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#objectid-timestamp-functions" class="md-nav__link">ObjectId timestamp functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#objectid_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">objectid_timestamp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#timestamp_objectid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timestamp_objectid()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#mongodb-to-trino-type-mapping" class="md-nav__link">MongoDB to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#trino-to-mongodb-type-mapping" class="md-nav__link">Trino to MongoDB type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#alter-table" class="md-nav__link">ALTER TABLE</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#query-database-collection-filter-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(database,</span> <span class="pre">collection,</span> <span class="pre">filter)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="mongodb.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#multiple-mongodb-clusters" class="md-nav__link">Multiple MongoDB clusters</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#configuration-properties" class="md-nav__link">Configuration properties</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connection-url" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-url</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-schema-collection" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.schema-collection</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-case-insensitive-name-matching" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.case-insensitive-name-matching</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-min-connections-per-host" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.min-connections-per-host</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connections-per-host" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connections-per-host</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-max-wait-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.max-wait-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-max-connection-idle-time" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.max-connection-idle-time</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-connection-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-socket-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.socket-timeout</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-enabled" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.enabled</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-keystore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-truststore-path" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-path</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-keystore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-tls-truststore-password" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-password</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-read-preference" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.read-preference</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-write-concern" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.write-concern</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-required-replica-set" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.required-replica-set</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-cursor-batch-size" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.cursor-batch-size</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-allow-local-scheduling" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.allow-local-scheduling</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#mongodb-dynamic-filtering-wait-timeout" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">mongodb.dynamic-filtering.wait-timeout</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#table-definition" class="md-nav__link">Table definition</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#objectid" class="md-nav__link">ObjectId</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#objectid-timestamp-functions" class="md-nav__link">ObjectId timestamp functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#objectid_timestamp" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">objectid_timestamp()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#timestamp_objectid" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">timestamp_objectid()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#mongodb-to-trino-type-mapping" class="md-nav__link">MongoDB to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#trino-to-mongodb-type-mapping" class="md-nav__link">Trino to MongoDB type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#alter-table" class="md-nav__link">ALTER TABLE</a>
        </li>
        <li class="md-nav__item"><a href="mongodb.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="mongodb.html#query-database-collection-filter-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(database,</span> <span class="pre">collection,</span> <span class="pre">filter)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="mongodb-connector">
<h1 id="connector-mongodb--page-root">MongoDB connector<a class="headerlink" href="mongodb.html#connector-mongodb--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/mongodb.png"/><p>The <code class="docutils literal notranslate"><span class="pre">mongodb</span></code> connector allows the use of <a class="reference external" href="https://www.mongodb.com/">MongoDB</a> collections as tables in Trino.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="mongodb.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to MongoDB, you need:</p>
<ul class="simple">
<li><p>MongoDB 4.2 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to MongoDB.
Port 27017 is the default port.</p></li>
<li><p>Write access to the <a class="reference internal" href="mongodb.html#table-definition-label"><span class="std std-ref">schema information collection</span></a>
in MongoDB.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="mongodb.html#configuration" title="Link to this heading">#</a></h2>
<p>To configure the MongoDB connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> with the following contents,
replacing the properties as appropriate:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>connector.name=mongodb
mongodb.connection-url=mongodb://user:<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="1464756767546775796478713a7c7b6760">[email&#160;protected]</a>:27017/
</pre></div>
</div>
<section id="multiple-mongodb-clusters">
<h3 id="multiple-mongodb-clusters">Multiple MongoDB clusters<a class="headerlink" href="mongodb.html#multiple-mongodb-clusters" title="Link to this heading">#</a></h3>
<p>You can have as many catalogs as you need, so if you have additional
MongoDB clusters, simply add another properties file to <code class="docutils literal notranslate"><span class="pre">etc/catalog</span></code>
with a different name, making sure it ends in <code class="docutils literal notranslate"><span class="pre">.properties</span></code>). For
example, if you name the property file <code class="docutils literal notranslate"><span class="pre">sales.properties</span></code>, Trino
will create a catalog named <code class="docutils literal notranslate"><span class="pre">sales</span></code> using the configured connector.</p>
</section>
</section>
<section id="configuration-properties">
<h2 id="configuration-properties">Configuration properties<a class="headerlink" href="mongodb.html#configuration-properties" title="Link to this heading">#</a></h2>
<p>The following configuration properties are available:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.connection-url</span></code></p></td>
<td><p>The connection url that the driver uses to connect to a MongoDB deployment</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.schema-collection</span></code></p></td>
<td><p>A collection which contains schema information</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.case-insensitive-name-matching</span></code></p></td>
<td><p>Match database and collection names case insensitively</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.min-connections-per-host</span></code></p></td>
<td><p>The minimum size of the connection pool per host</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.connections-per-host</span></code></p></td>
<td><p>The maximum size of the connection pool per host</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.max-wait-time</span></code></p></td>
<td><p>The maximum wait time</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.max-connection-idle-time</span></code></p></td>
<td><p>The maximum idle time of a pooled connection</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.connection-timeout</span></code></p></td>
<td><p>The socket connect timeout</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.socket-timeout</span></code></p></td>
<td><p>The socket timeout</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.tls.enabled</span></code></p></td>
<td><p>Use TLS/SSL for connections to mongod/mongos</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-path</span></code></p></td>
<td><p>Path to the  or JKS key store</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-path</span></code></p></td>
<td><p>Path to the  or JKS trust store</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-password</span></code></p></td>
<td><p>Password for the key store</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-password</span></code></p></td>
<td><p>Password for the trust store</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.read-preference</span></code></p></td>
<td><p>The read preference</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.write-concern</span></code></p></td>
<td><p>The write concern</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.required-replica-set</span></code></p></td>
<td><p>The required replica set name</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.cursor-batch-size</span></code></p></td>
<td><p>The number of elements to return in a batch</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.allow-local-scheduling</span></code></p></td>
<td><p>Assign MongoDB splits to a specific worker</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">mongodb.dynamic-filtering.wait-timeout</span></code></p></td>
<td><p>Duration to wait for completion of dynamic filters during split generation</p></td>
</tr>
</tbody>
</table>
<section id="mongodb-connection-url">
<h3 id="mongodb-connection-url"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-url</span></code><a class="headerlink" href="mongodb.html#mongodb-connection-url" title="Link to this heading">#</a></h3>
<p>A connection string containing the protocol, credential, and host info for use
in connecting to your MongoDB deployment.</p>
<p>For example, the connection string may use the format
<code class="docutils literal notranslate"><span class="pre">mongodb://&lt;user&gt;:&lt;pass&gt;@&lt;host&gt;:&lt;port&gt;/?&lt;options&gt;</span></code> or
<code class="docutils literal notranslate"><span class="pre">mongodb+srv://&lt;user&gt;:&lt;pass&gt;@&lt;host&gt;/?&lt;options&gt;</span></code>, depending on the protocol
used. The user/pass credentials must be for a user with write access to the
<a class="reference internal" href="mongodb.html#table-definition-label"><span class="std std-ref">schema information collection</span></a>.</p>
<p>See the <a class="reference external" href="https://docs.mongodb.com/drivers/java/sync/current/fundamentals/connection/#connection-uri">MongoDB Connection URI</a> for more information.</p>
<p>This property is required; there is no default. A connection URL must be
provided to connect to a MongoDB deployment.</p>
</section>
<section id="mongodb-schema-collection">
<h3 id="mongodb-schema-collection"><code class="docutils literal notranslate"><span class="pre">mongodb.schema-collection</span></code><a class="headerlink" href="mongodb.html#mongodb-schema-collection" title="Link to this heading">#</a></h3>
<p>As MongoDB is a document database, there is no fixed schema information in the system. So a special collection in each MongoDB database should define the schema of all tables. Please refer the <a class="reference internal" href="mongodb.html#table-definition-label"><span class="std std-ref">Table definition</span></a> section for the details.</p>
<p>At startup, the connector tries to guess the data type of fields based on the <a class="reference internal" href="mongodb.html#mongodb-type-mapping"><span class="std std-ref">type mapping</span></a>.</p>
<p>The initial guess can be incorrect for your specific collection. In that case, you need to modify it manually. Please refer the <a class="reference internal" href="mongodb.html#table-definition-label"><span class="std std-ref">Table definition</span></a> section for the details.</p>
<p>Creating new tables using <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span> <span class="pre">SELECT</span></code> automatically create an entry for you.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">_schema</span></code>.</p>
</section>
<section id="mongodb-case-insensitive-name-matching">
<h3 id="mongodb-case-insensitive-name-matching"><code class="docutils literal notranslate"><span class="pre">mongodb.case-insensitive-name-matching</span></code><a class="headerlink" href="mongodb.html#mongodb-case-insensitive-name-matching" title="Link to this heading">#</a></h3>
<p>Match database and collection names case insensitively.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="mongodb-min-connections-per-host">
<h3 id="mongodb-min-connections-per-host"><code class="docutils literal notranslate"><span class="pre">mongodb.min-connections-per-host</span></code><a class="headerlink" href="mongodb.html#mongodb-min-connections-per-host" title="Link to this heading">#</a></h3>
<p>The minimum number of connections per host for this MongoClient instance. Those connections are kept in a pool when idle, and the pool ensures over time that it contains at least this minimum number.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</section>
<section id="mongodb-connections-per-host">
<h3 id="mongodb-connections-per-host"><code class="docutils literal notranslate"><span class="pre">mongodb.connections-per-host</span></code><a class="headerlink" href="mongodb.html#mongodb-connections-per-host" title="Link to this heading">#</a></h3>
<p>The maximum number of connections allowed per host for this MongoClient instance. Those connections are kept in a pool when idle. Once the pool is exhausted, any operation requiring a connection blocks waiting for an available connection.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">100</span></code>.</p>
</section>
<section id="mongodb-max-wait-time">
<h3 id="mongodb-max-wait-time"><code class="docutils literal notranslate"><span class="pre">mongodb.max-wait-time</span></code><a class="headerlink" href="mongodb.html#mongodb-max-wait-time" title="Link to this heading">#</a></h3>
<p>The maximum wait time in milliseconds, that a thread may wait for a connection to become available.
A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> means that it does not wait. A negative value means to wait indefinitely for a connection to become available.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">120000</span></code>.</p>
</section>
<section id="mongodb-max-connection-idle-time">
<h3 id="mongodb-max-connection-idle-time"><code class="docutils literal notranslate"><span class="pre">mongodb.max-connection-idle-time</span></code><a class="headerlink" href="mongodb.html#mongodb-max-connection-idle-time" title="Link to this heading">#</a></h3>
<p>The maximum idle time of a pooled connection in milliseconds. A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> indicates no limit to the idle time.
A pooled connection that has exceeded its idle time will be closed and replaced when necessary by a new connection.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</section>
<section id="mongodb-connection-timeout">
<h3 id="mongodb-connection-timeout"><code class="docutils literal notranslate"><span class="pre">mongodb.connection-timeout</span></code><a class="headerlink" href="mongodb.html#mongodb-connection-timeout" title="Link to this heading">#</a></h3>
<p>The connection timeout in milliseconds. A value of <code class="docutils literal notranslate"><span class="pre">0</span></code> means no timeout. It is used solely when establishing a new connection.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">10000</span></code>.</p>
</section>
<section id="mongodb-socket-timeout">
<h3 id="mongodb-socket-timeout"><code class="docutils literal notranslate"><span class="pre">mongodb.socket-timeout</span></code><a class="headerlink" href="mongodb.html#mongodb-socket-timeout" title="Link to this heading">#</a></h3>
<p>The socket timeout in milliseconds. It is used for I/O socket read and write operations.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">0</span></code> and means no timeout.</p>
</section>
<section id="mongodb-tls-enabled">
<h3 id="mongodb-tls-enabled"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.enabled</span></code><a class="headerlink" href="mongodb.html#mongodb-tls-enabled" title="Link to this heading">#</a></h3>
<p>This flag enables TLS connections to MongoDB servers.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="mongodb-tls-keystore-path">
<h3 id="mongodb-tls-keystore-path"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-path</span></code><a class="headerlink" href="mongodb.html#mongodb-tls-keystore-path" title="Link to this heading">#</a></h3>
<p>The path to the <a class="reference internal" href="../security/inspect-pem.html"><span class="doc">PEM</span></a> or
<a class="reference internal" href="../security/inspect-jks.html"><span class="doc">JKS</span></a> key store.</p>
<p>This property is optional.</p>
</section>
<section id="mongodb-tls-truststore-path">
<h3 id="mongodb-tls-truststore-path"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-path</span></code><a class="headerlink" href="mongodb.html#mongodb-tls-truststore-path" title="Link to this heading">#</a></h3>
<p>The path to <a class="reference internal" href="../security/inspect-pem.html"><span class="doc">PEM</span></a> or
<a class="reference internal" href="../security/inspect-jks.html"><span class="doc">JKS</span></a> trust store.</p>
<p>This property is optional.</p>
</section>
<section id="mongodb-tls-keystore-password">
<h3 id="mongodb-tls-keystore-password"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-password</span></code><a class="headerlink" href="mongodb.html#mongodb-tls-keystore-password" title="Link to this heading">#</a></h3>
<p>The key password for the key store specified by <code class="docutils literal notranslate"><span class="pre">mongodb.tls.keystore-path</span></code>.</p>
<p>This property is optional.</p>
</section>
<section id="mongodb-tls-truststore-password">
<h3 id="mongodb-tls-truststore-password"><code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-password</span></code><a class="headerlink" href="mongodb.html#mongodb-tls-truststore-password" title="Link to this heading">#</a></h3>
<p>The key password for the trust store specified by <code class="docutils literal notranslate"><span class="pre">mongodb.tls.truststore-path</span></code>.</p>
<p>This property is optional.</p>
</section>
<section id="mongodb-read-preference">
<h3 id="mongodb-read-preference"><code class="docutils literal notranslate"><span class="pre">mongodb.read-preference</span></code><a class="headerlink" href="mongodb.html#mongodb-read-preference" title="Link to this heading">#</a></h3>
<p>The read preference to use for queries, map-reduce, aggregation, and count.
The available values are <code class="docutils literal notranslate"><span class="pre">PRIMARY</span></code>, <code class="docutils literal notranslate"><span class="pre">PRIMARY_PREFERRED</span></code>, <code class="docutils literal notranslate"><span class="pre">SECONDARY</span></code>, <code class="docutils literal notranslate"><span class="pre">SECONDARY_PREFERRED</span></code> and <code class="docutils literal notranslate"><span class="pre">NEAREST</span></code>.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">PRIMARY</span></code>.</p>
</section>
<section id="mongodb-write-concern">
<h3 id="mongodb-write-concern"><code class="docutils literal notranslate"><span class="pre">mongodb.write-concern</span></code><a class="headerlink" href="mongodb.html#mongodb-write-concern" title="Link to this heading">#</a></h3>
<p>The write concern to use. The available values are
<code class="docutils literal notranslate"><span class="pre">ACKNOWLEDGED</span></code>, <code class="docutils literal notranslate"><span class="pre">JOURNALED</span></code>, <code class="docutils literal notranslate"><span class="pre">MAJORITY</span></code> and <code class="docutils literal notranslate"><span class="pre">UNACKNOWLEDGED</span></code>.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">ACKNOWLEDGED</span></code>.</p>
</section>
<section id="mongodb-required-replica-set">
<h3 id="mongodb-required-replica-set"><code class="docutils literal notranslate"><span class="pre">mongodb.required-replica-set</span></code><a class="headerlink" href="mongodb.html#mongodb-required-replica-set" title="Link to this heading">#</a></h3>
<p>The required replica set name. With this option set, the MongoClient instance performs the following actions:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="o">#</span><span class="p">.</span><span class="w"> </span><span class="k">Connect</span><span class="w"> </span><span class="k">in</span><span class="w"> </span><span class="n">replica</span><span class="w"> </span><span class="k">set</span><span class="w"> </span><span class="k">mode</span><span class="p">,</span><span class="w"> </span><span class="k">and</span><span class="w"> </span><span class="n">discover</span><span class="w"> </span><span class="k">all</span><span class="w"> </span><span class="n">members</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">set</span><span class="w"> </span><span class="n">based</span><span class="w"> </span><span class="k">on</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">given</span><span class="w"> </span><span class="n">servers</span>
<span class="o">#</span><span class="p">.</span><span class="w"> </span><span class="n">Make</span><span class="w"> </span><span class="n">sure</span><span class="w"> </span><span class="n">that</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">set</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="n">reported</span><span class="w"> </span><span class="k">by</span><span class="w"> </span><span class="k">all</span><span class="w"> </span><span class="n">members</span><span class="w"> </span><span class="n">matches</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">required</span><span class="w"> </span><span class="k">set</span><span class="w"> </span><span class="n">name</span><span class="p">.</span>
<span class="o">#</span><span class="p">.</span><span class="w"> </span><span class="n">Refuse</span><span class="w"> </span><span class="k">to</span><span class="w"> </span><span class="n">service</span><span class="w"> </span><span class="k">any</span><span class="w"> </span><span class="n">requests</span><span class="p">,</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="n">authenticated</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="k">is</span><span class="w"> </span><span class="k">not</span><span class="w"> </span><span class="n">part</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="n">replica</span><span class="w"> </span><span class="k">set</span><span class="w"> </span><span class="k">with</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">required</span><span class="w"> </span><span class="n">name</span><span class="p">.</span>
</pre></div>
</div>
<p>This property is optional; no default value.</p>
</section>
<section id="mongodb-cursor-batch-size">
<h3 id="mongodb-cursor-batch-size"><code class="docutils literal notranslate"><span class="pre">mongodb.cursor-batch-size</span></code><a class="headerlink" href="mongodb.html#mongodb-cursor-batch-size" title="Link to this heading">#</a></h3>
<p>Limits the number of elements returned in one batch. A cursor typically fetches a batch of result objects and stores them locally.
If batchSize is 0, Driver’s default are used.
If batchSize is positive, it represents the size of each batch of objects retrieved. It can be adjusted to optimize performance and limit data transfer.
If batchSize is negative, it limits the number of objects returned, that fit within the max batch size limit (usually 4MB), and the cursor is closed. For example if batchSize is -10, then the server returns a maximum of 10 documents, and as many as can fit in 4MB, then closes the cursor.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Do not use a batch size of <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</section>
<section id="mongodb-allow-local-scheduling">
<h3 id="mongodb-allow-local-scheduling"><code class="docutils literal notranslate"><span class="pre">mongodb.allow-local-scheduling</span></code><a class="headerlink" href="mongodb.html#mongodb-allow-local-scheduling" title="Link to this heading">#</a></h3>
<p>Set the value of this property to <code class="docutils literal notranslate"><span class="pre">true</span></code> if Trino and MongoDB share the same
cluster, and specific MongoDB splits should be processed on the same worker and
MongoDB node. Note that a shared deployment is not recommended, and enabling
this property can lead to resource contention.</p>
<p>This property is optional, and defaults to false.</p>
</section>
<section id="mongodb-dynamic-filtering-wait-timeout">
<h3 id="mongodb-dynamic-filtering-wait-timeout"><code class="docutils literal notranslate"><span class="pre">mongodb.dynamic-filtering.wait-timeout</span></code><a class="headerlink" href="mongodb.html#mongodb-dynamic-filtering-wait-timeout" title="Link to this heading">#</a></h3>
<p>Duration to wait for completion of dynamic filters during split generation.</p>
<p>This property is optional; the default is <code class="docutils literal notranslate"><span class="pre">5s</span></code>.</p>
</section>
</section>
<section id="table-definition">
<span id="table-definition-label"></span><h2 id="table-definition">Table definition<a class="headerlink" href="mongodb.html#table-definition" title="Link to this heading">#</a></h2>
<p>MongoDB maintains table definitions on the special collection where <code class="docutils literal notranslate"><span class="pre">mongodb.schema-collection</span></code> configuration value specifies.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The plugin cannot detect that a collection has been deleted. You must
delete the entry by executing <code class="docutils literal notranslate"><span class="pre">db.getCollection("_schema").remove(</span> <span class="pre">{</span> <span class="pre">table:</span> <span class="pre">deleted_table_name</span> <span class="pre">})</span></code> in the MongoDB Shell. You can also drop a collection in
Trino by running <code class="docutils literal notranslate"><span class="pre">DROP</span> <span class="pre">TABLE</span> <span class="pre">table_name</span></code>.</p>
</div>
<p>A schema collection consists of a MongoDB document for a table.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "table": ...,
    "fields": [
          { "name" : ...,
            "type" : "varchar|bigint|boolean|double|date|array(bigint)|...",
            "hidden" : false },
            ...
        ]
    }
}
</pre></div>
</div>
<p>The connector quotes the fields for a row type when auto-generating the schema;
however, the auto-generated schema must be corrected manually in the collection
to match the information in the tables.</p>
<p>Manually altered fields must be explicitly quoted, for example, <code class="docutils literal notranslate"><span class="pre">row("UpperCase"</span> <span class="pre">varchar)</span></code>.</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">table</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Trino table name</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fields</span></code></p></td>
<td><p>required</p></td>
<td><p>array</p></td>
<td><p>A list of field definitions. Each field definition creates a new column in the Trino table.</p></td>
</tr>
</tbody>
</table>
<p>Each field definition:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>{
    "name": ...,
    "type": ...,
    "hidden": ...
}
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Required</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">name</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Name of the column in the Trino table.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">type</span></code></p></td>
<td><p>required</p></td>
<td><p>string</p></td>
<td><p>Trino type of the column.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">hidden</span></code></p></td>
<td><p>optional</p></td>
<td><p>boolean</p></td>
<td><p>Hides the column from <code class="docutils literal notranslate"><span class="pre">DESCRIBE</span> <span class="pre">&lt;table</span> <span class="pre">name&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span></code>. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>There is no limit on field descriptions for either key or message.</p>
</section>
<section id="objectid">
<h2 id="objectid">ObjectId<a class="headerlink" href="mongodb.html#objectid" title="Link to this heading">#</a></h2>
<p>MongoDB collection has the special field <code class="docutils literal notranslate"><span class="pre">_id</span></code>. The connector tries to follow the same rules for this special field, so there will be hidden field <code class="docutils literal notranslate"><span class="pre">_id</span></code>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">IF</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">EXISTS</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">orderkey</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">orderstatus</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">totalprice</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">,</span>
<span class="w">    </span><span class="n">orderdate</span><span class="w"> </span><span class="nb">DATE</span>
<span class="p">);</span>

<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="k">VALUES</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="s1">'bad'</span><span class="p">,</span><span class="w"> </span><span class="mi">50</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="k">current_date</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="k">VALUES</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="s1">'good'</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="k">current_date</span><span class="p">);</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">_id</span><span class="p">,</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">orders</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                 _id                 | orderkey | orderstatus | totalprice | orderdate
-------------------------------------+----------+-------------+------------+------------
 55 <USER> <GROUP> 63 38 64 d6 43 8c 61 a9 ce |        1 | bad         |       50.0 | 2015-07-23
 55 b1 51 67 38 64 d6 43 8c 61 a9 cf |        2 | good        |      100.0 | 2015-07-23
(2 rows)
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">_id</span><span class="p">,</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ObjectId</span><span class="p">(</span><span class="s1">'55b151633864d6438c61a9ce'</span><span class="p">);</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                 _id                 | orderkey | orderstatus | totalprice | orderdate
-------------------------------------+----------+-------------+------------+------------
 55 <USER> <GROUP> 63 38 64 d6 43 8c 61 a9 ce |        1 | bad         |       50.0 | 2015-07-23
(1 row)
</pre></div>
</div>
<p>You can render the <code class="docutils literal notranslate"><span class="pre">_id</span></code> field to readable values with a cast to <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">_id</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">),</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ObjectId</span><span class="p">(</span><span class="s1">'55b151633864d6438c61a9ce'</span><span class="p">);</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>           _id             | orderkey | orderstatus | totalprice | orderdate
---------------------------+----------+-------------+------------+------------
 55b151633864d6438c61a9ce  |        1 | bad         |       50.0 | 2015-07-23
(1 row)
</pre></div>
</div>
<section id="objectid-timestamp-functions">
<h3 id="objectid-timestamp-functions">ObjectId timestamp functions<a class="headerlink" href="mongodb.html#objectid-timestamp-functions" title="Link to this heading">#</a></h3>
<p>The first four bytes of each <a class="reference external" href="https://docs.mongodb.com/manual/reference/method/ObjectId">ObjectId</a> represent
an embedded timestamp of its creation time. Trino provides a couple of functions to take advantage of this MongoDB feature.</p>
<dl class="py function">
<dt class="sig sig-object py" id="objectid_timestamp">
<span class="sig-name descname"><span class="pre">objectid_timestamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ObjectId</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">timestamp</span></span></span><a class="headerlink" href="mongodb.html#objectid_timestamp" title="Link to this definition">#</a></dt>
<dd><p>Extracts the TIMESTAMP WITH TIME ZONE from a given ObjectId:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">objectid_timestamp</span><span class="p">(</span><span class="n">ObjectId</span><span class="p">(</span><span class="s1">'507f191e810c19729de860ea'</span><span class="p">));</span>
<span class="c1">-- 2012-10-17 20:46:22.000 UTC</span>
</pre></div>
</div>
</dd></dl>
<dl class="py function">
<dt class="sig sig-object py" id="timestamp_objectid">
<span class="sig-name descname"><span class="pre">timestamp_objectid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">→</span> <span class="sig-return-typehint"><span class="pre">ObjectId</span></span></span><a class="headerlink" href="mongodb.html#timestamp_objectid" title="Link to this definition">#</a></dt>
<dd><p>Creates an ObjectId from a TIMESTAMP WITH TIME ZONE:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">timestamp_objectid</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2021-08-07 17:51:36 +00:00'</span><span class="p">);</span>
<span class="c1">-- 61 0e c8 28 00 00 00 00 00 00 00 00</span>
</pre></div>
</div>
</dd></dl>
<p>In MongoDB, you can filter all the documents created after <code class="docutils literal notranslate"><span class="pre">2021-08-07</span> <span class="pre">17:51:36</span></code>
with a query like this:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>db.collection.find({"_id": {"$gt": ObjectId("610ec8280000000000000000")}})
</pre></div>
</div>
<p>In Trino, the same can be achieved with this query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">collection</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">_id</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">timestamp_objectid</span><span class="p">(</span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2021-08-07 17:51:36 +00:00'</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="fault-tolerant-execution-support">
<span id="mongodb-fte-support"></span><h3 id="fault-tolerant-execution-support">Fault-tolerant execution support<a class="headerlink" href="mongodb.html#fault-tolerant-execution-support" title="Link to this heading">#</a></h3>
<p>The connector supports <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a> of query
processing. Read and write operations are both supported with any retry policy.</p>
</section>
</section>
<section id="type-mapping">
<span id="mongodb-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="mongodb.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and MongoDB each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<section id="mongodb-to-trino-type-mapping">
<h3 id="mongodb-to-trino-type-mapping">MongoDB to Trino type mapping<a class="headerlink" href="mongodb.html#mongodb-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps MongoDB types to the corresponding Trino types following
this table:</p>
<table id="id1">
<caption><span class="caption-text">MongoDB to Trino type mapping</span><a class="headerlink" href="mongodb.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 20%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>MongoDB type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Boolean</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">Int32</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Int64</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">Double</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Decimal128</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">Date</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">String</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">Binary</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ObjectId</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ObjectId</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">Object</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Array</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p>Map to <code class="docutils literal notranslate"><span class="pre">ROW</span></code> if the element type is not unique.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DBRef</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-to-mongodb-type-mapping">
<h3 id="trino-to-mongodb-type-mapping">Trino to MongoDB type mapping<a class="headerlink" href="mongodb.html#trino-to-mongodb-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding MongoDB types following
this table:</p>
<table id="id2">
<caption><span class="caption-text">Trino to MongoDB type mapping</span><a class="headerlink" href="mongodb.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 60%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>MongoDB type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Boolean</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Int64</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Double</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,</span> <span class="pre">s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Decimal128</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Date</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">String</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Binary</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ObjectId</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ObjectId</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Object</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">Array</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="sql-support">
<span id="mongodb-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="mongodb.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read and write access to data and metadata in
MongoDB. In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a>
statements, the connector supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
<section id="alter-table">
<h3 id="alter-table">ALTER TABLE<a class="headerlink" href="mongodb.html#alter-table" title="Link to this heading">#</a></h3>
<p>The connector supports <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">RENAME</span> <span class="pre">TO</span></code>, <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">ADD</span> <span class="pre">COLUMN</span></code>
and <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span> <span class="pre">DROP</span> <span class="pre">COLUMN</span></code> operations.
Other uses of <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span></code> are not supported.</p>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="mongodb.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc">table functions</span></a> to
access MongoDB.</p>
<section id="query-database-collection-filter-table">
<span id="mongodb-query-function"></span><h4 id="query-database-collection-filter-table"><code class="docutils literal notranslate"><span class="pre">query(database,</span> <span class="pre">collection,</span> <span class="pre">filter)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="mongodb.html#query-database-collection-filter-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">query</span></code> function allows you to query the underlying MongoDB directly. It
requires syntax native to MongoDB, because the full query is pushed down and
processed by MongoDB. This can be useful for accessing native features which are
not available in Trino or for improving query performance in situations where
running a query natively may be faster.</p>
<p>For example, get all rows where <code class="docutils literal notranslate"><span class="pre">regionkey</span></code> field is 0:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">query</span><span class="p">(</span>
<span class="w">      </span><span class="k">database</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'tpch'</span><span class="p">,</span>
<span class="w">      </span><span class="n">collection</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'region'</span><span class="p">,</span>
<span class="w">      </span><span class="n">filter</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'{ regionkey: 0 }'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="memory.html" title="Memory connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Memory connector </span>
              </div>
            </a>
          
          
            <a href="mysql.html" title="MySQL connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> MySQL connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>