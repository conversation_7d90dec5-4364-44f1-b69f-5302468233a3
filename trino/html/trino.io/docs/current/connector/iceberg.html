<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Iceberg connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="iceberg.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Ignite connector" href="ignite.html" />
    <link rel="prev" title="Hudi connector" href="hudi.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="iceberg.html#connector/iceberg" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Iceberg connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Iceberg </label>
    
      <a href="iceberg.html#" class="md-nav__link md-nav__link--active">Iceberg</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="iceberg.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#general-configuration" class="md-nav__link">General configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#iceberg-to-trino-type-mapping" class="md-nav__link">Iceberg to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#trino-to-iceberg-type-mapping" class="md-nav__link">Trino to Iceberg type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#kerberos-authentication" class="md-nav__link">Kerberos authentication</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#authorization" class="md-nav__link">Authorization</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#basic-usage-examples" class="md-nav__link">Basic usage examples</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#register-table" class="md-nav__link">Register table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#unregister-table" class="md-nav__link">Unregister table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#migrate-table" class="md-nav__link">Migrate table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#add-files" class="md-nav__link">Add files</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#functions" class="md-nav__link">Functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#bucket" class="md-nav__link">bucket</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#data-management" class="md-nav__link">Data management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#deletion-by-partition" class="md-nav__link">Deletion by partition</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#row-level-deletion" class="md-nav__link">Row level deletion</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#schema-evolution" class="md-nav__link">Schema evolution</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#alter-table-execute" class="md-nav__link">ALTER TABLE EXECUTE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#optimize" class="md-nav__link">optimize</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#optimize-manifests" class="md-nav__link">optimize_manifests</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#expire-snapshots" class="md-nav__link">expire_snapshots</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#remove-orphan-files" class="md-nav__link">remove_orphan_files</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#drop-extended-stats" class="md-nav__link">drop_extended_stats</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#alter-table-set-properties" class="md-nav__link">ALTER TABLE SET PROPERTIES</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-properties" class="md-nav__link">Table properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#properties-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#history-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-log-entries-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$metadata_log_entries</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#snapshots-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#manifests-and-all-manifests-tables" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$manifests</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_manifests</span></code> tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#partitions-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#files-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$files</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#entries-and-all-entries-tables" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$entries</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_entries</span></code> tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#refs-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$refs</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-columns" class="md-nav__link">Metadata columns</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#system-tables" class="md-nav__link">System tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#iceberg-tables-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">iceberg_tables</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#drop-table" class="md-nav__link">DROP TABLE</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#comment" class="md-nav__link">COMMENT</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#partitioned-tables" class="md-nav__link">Partitioned tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#sorted-tables" class="md-nav__link">Sorted tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#using-snapshots" class="md-nav__link">Using snapshots</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#replace-tables" class="md-nav__link">Replace tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#time-travel-queries" class="md-nav__link">Time travel queries</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#rolling-back-to-a-previous-snapshot" class="md-nav__link">Rolling back to a previous snapshot</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#not-null-column-constraint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> column constraint</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#materialized-views" class="md-nav__link">Materialized views</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-changes" class="md-nav__link">table_changes</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-statistics" class="md-nav__link">Table statistics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#updating-table-statistics" class="md-nav__link">Updating table statistics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#table-redirection" class="md-nav__link">Table redirection</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#file-system-cache" class="md-nav__link">File system cache</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#iceberg-metadata-caching" class="md-nav__link">Iceberg metadata caching</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="iceberg.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#general-configuration" class="md-nav__link">General configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#fault-tolerant-execution-support" class="md-nav__link">Fault-tolerant execution support</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#file-system-access-configuration" class="md-nav__link">File system access configuration</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#iceberg-to-trino-type-mapping" class="md-nav__link">Iceberg to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#trino-to-iceberg-type-mapping" class="md-nav__link">Trino to Iceberg type mapping</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#security" class="md-nav__link">Security</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#kerberos-authentication" class="md-nav__link">Kerberos authentication</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#authorization" class="md-nav__link">Authorization</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#basic-usage-examples" class="md-nav__link">Basic usage examples</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#register-table" class="md-nav__link">Register table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#unregister-table" class="md-nav__link">Unregister table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#migrate-table" class="md-nav__link">Migrate table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#add-files" class="md-nav__link">Add files</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#functions" class="md-nav__link">Functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#bucket" class="md-nav__link">bucket</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#data-management" class="md-nav__link">Data management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#deletion-by-partition" class="md-nav__link">Deletion by partition</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#row-level-deletion" class="md-nav__link">Row level deletion</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#schema-and-table-management" class="md-nav__link">Schema and table management</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#schema-evolution" class="md-nav__link">Schema evolution</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#alter-table-execute" class="md-nav__link">ALTER TABLE EXECUTE</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#optimize" class="md-nav__link">optimize</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#optimize-manifests" class="md-nav__link">optimize_manifests</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#expire-snapshots" class="md-nav__link">expire_snapshots</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#remove-orphan-files" class="md-nav__link">remove_orphan_files</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#drop-extended-stats" class="md-nav__link">drop_extended_stats</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#alter-table-set-properties" class="md-nav__link">ALTER TABLE SET PROPERTIES</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-properties" class="md-nav__link">Table properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-tables" class="md-nav__link">Metadata tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#properties-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#history-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-log-entries-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$metadata_log_entries</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#snapshots-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#manifests-and-all-manifests-tables" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$manifests</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_manifests</span></code> tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#partitions-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#files-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$files</span></code> table</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#entries-and-all-entries-tables" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$entries</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_entries</span></code> tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#refs-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">$refs</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#metadata-columns" class="md-nav__link">Metadata columns</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#system-tables" class="md-nav__link">System tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#iceberg-tables-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">iceberg_tables</span></code> table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#drop-table" class="md-nav__link">DROP TABLE</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#comment" class="md-nav__link">COMMENT</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#partitioned-tables" class="md-nav__link">Partitioned tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#sorted-tables" class="md-nav__link">Sorted tables</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#using-snapshots" class="md-nav__link">Using snapshots</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#replace-tables" class="md-nav__link">Replace tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#time-travel-queries" class="md-nav__link">Time travel queries</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#rolling-back-to-a-previous-snapshot" class="md-nav__link">Rolling back to a previous snapshot</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#not-null-column-constraint" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> column constraint</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#materialized-views" class="md-nav__link">Materialized views</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-changes" class="md-nav__link">table_changes</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#performance" class="md-nav__link">Performance</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#table-statistics" class="md-nav__link">Table statistics</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="iceberg.html#updating-table-statistics" class="md-nav__link">Updating table statistics</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#table-redirection" class="md-nav__link">Table redirection</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#file-system-cache" class="md-nav__link">File system cache</a>
        </li>
        <li class="md-nav__item"><a href="iceberg.html#iceberg-metadata-caching" class="md-nav__link">Iceberg metadata caching</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="iceberg-connector">
<h1 id="connector-iceberg--page-root">Iceberg connector<a class="headerlink" href="iceberg.html#connector-iceberg--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/iceberg.png"/><p>Apache Iceberg is an open table format for huge analytic datasets. The Iceberg
connector allows querying data stored in files written in Iceberg format, as
defined in the <a class="reference external" href="https://iceberg.apache.org/spec/">Iceberg Table Spec</a>. The
connector supports Apache Iceberg table spec versions 1 and 2.</p>
<p>The table state is maintained in metadata files. All changes to table
state create a new metadata file and replace the old metadata with an atomic
swap. The table metadata file tracks the table schema, partitioning
configuration, custom properties, and snapshots of the table contents.</p>
<p>Iceberg data files are stored in either Parquet, ORC, or Avro format, as
determined by the <code class="docutils literal notranslate"><span class="pre">format</span></code> property in the table definition.</p>
<p>Iceberg is designed to improve on the known scalability limitations of Hive,
which stores table metadata in a metastore that is backed by a relational
database such as MySQL.  It tracks partition locations in the metastore, but not
individual data files.  Trino queries using the <a class="reference internal" href="hive.html"><span class="doc">Hive connector</span></a> must
first call the metastore to get partition locations, then call the underlying
file system to list all data files inside each partition, and then read metadata
from each data file.</p>
<p>Since Iceberg stores the paths to data files in the metadata files, it only
consults the underlying file system for files that must be read.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="iceberg.html#requirements" title="Link to this heading">#</a></h2>
<p>To use Iceberg, you need:</p>
<ul class="simple">
<li><p>Network access from the Trino coordinator and workers to the distributed
object storage.</p></li>
<li><p>Access to a <a class="reference internal" href="../object-storage/metastores.html#hive-thrift-metastore"><span class="std std-ref">Hive metastore service (HMS)</span></a>, an
<a class="reference internal" href="../object-storage/metastores.html#iceberg-glue-catalog"><span class="std std-ref">AWS Glue catalog</span></a>, a <a class="reference internal" href="../object-storage/metastores.html#iceberg-jdbc-catalog"><span class="std std-ref">JDBC catalog</span></a>, a <a class="reference internal" href="../object-storage/metastores.html#iceberg-rest-catalog"><span class="std std-ref">REST catalog</span></a>,
a <a class="reference internal" href="../object-storage/metastores.html#iceberg-nessie-catalog"><span class="std std-ref">Nessie server</span></a>, or a
<a class="reference internal" href="../object-storage/metastores.html#iceberg-snowflake-catalog"><span class="std std-ref">Snowflake catalog</span></a>.</p></li>
<li><p>Data files stored in the file formats
<a class="reference internal" href="../object-storage/file-formats.html#parquet-format-configuration"><span class="std std-ref">Parquet</span></a>(default),
<a class="reference internal" href="../object-storage/file-formats.html#orc-format-configuration"><span class="std std-ref">ORC</span></a>, or Avro on a <a class="reference internal" href="iceberg.html#iceberg-file-system-configuration"><span class="std std-ref">supported file
system</span></a>.</p></li>
</ul>
</section>
<section id="general-configuration">
<h2 id="general-configuration">General configuration<a class="headerlink" href="iceberg.html#general-configuration" title="Link to this heading">#</a></h2>
<p>To configure the Iceberg connector, create a catalog properties file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code> that references the <code class="docutils literal notranslate"><span class="pre">iceberg</span></code> connector.</p>
<p>The <a class="reference internal" href="../object-storage/metastores.html#hive-thrift-metastore"><span class="std std-ref">Hive metastore catalog</span></a> is the default
implementation.</p>
<p>You must select and configure one of the <a class="reference internal" href="iceberg.html#iceberg-file-system-configuration"><span class="std std-ref">supported file
systems</span></a>.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">iceberg</span>
<span class="na">hive.metastore.uri</span><span class="o">=</span><span class="s">thrift://example.net:9083</span>
<span class="na">fs.x.enabled</span><span class="o">=</span><span class="s">true</span>
</pre></div>
</div>
<p>Replace the <code class="docutils literal notranslate"><span class="pre">fs.x.enabled</span></code> configuration property with the desired file system.</p>
<p>Other metadata catalog types as listed in the requirements section of this topic
are available. Each metastore type has specific configuration properties along
with <a class="reference internal" href="../object-storage/metastores.html#general-metastore-properties"><span class="std std-ref">general metastore configuration properties</span></a>.</p>
<p>The following configuration properties are independent of which catalog
implementation is used:</p>
<table id="id2">
<caption><span class="caption-text">Iceberg general configuration properties</span><a class="headerlink" href="iceberg.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 58%"/>
<col style="width: 12%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.catalog.type</span></code></p></td>
<td><p>Define the <a class="reference internal" href="../object-storage/metastores.html#general-metastore-properties"><span class="std std-ref">metastore type</span></a> to use. Possible
values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">hive_metastore</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">glue</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">jdbc</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rest</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">nessie</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">snowflake</span></code></p></li>
</ul>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">hive_metastore</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.file-format</span></code></p></td>
<td><p>Define the data storage file format for Iceberg tables. Possible values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">PARQUET</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ORC</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">AVRO</span></code></p></li>
</ul>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">PARQUET</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.compression-codec</span></code></p></td>
<td><p>The compression codec used when writing files. Possible values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SNAPPY</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LZ4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ZSTD</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GZIP</span></code></p></li>
</ul>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">ZSTD</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.use-file-size-from-metadata</span></code></p></td>
<td><p>Read file sizes from metadata instead of file system. This property must
only be used as a workaround for <a class="reference external" href="https://github.com/apache/iceberg/issues/1980">this
issue</a>. The problem was fixed
in Iceberg version 0.11.0.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.max-partitions-per-writer</span></code></p></td>
<td><p>Maximum number of partitions handled per writer. The equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">max_partitions_per_writer</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">100</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.target-max-file-size</span></code></p></td>
<td><p>Target maximum size of written files; the actual size may be larger.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1GB</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.unique-table-location</span></code></p></td>
<td><p>Use randomized, unique table locations.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.dynamic-filtering.wait-timeout</span></code></p></td>
<td><p>Maximum duration to wait for completion of dynamic filters during split
generation.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">1s</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.delete-schema-locations-fallback</span></code></p></td>
<td><p>Whether schema locations are deleted when Trino can’t determine whether
they contain external files.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.minimum-assigned-split-weight</span></code></p></td>
<td><p>A decimal value in the range <code class="docutils literal notranslate"><span class="pre">(0,</span> <span class="pre">1]</span></code> used as a minimum for weights assigned
to each split. A low value may improve performance on tables with small
files. A higher value may improve performance for queries with highly skewed
aggregations or joins.</p></td>
<td><p>0.05</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.table-statistics-enabled</span></code></p></td>
<td><p>Enable <a class="reference internal" href="../optimizer/statistics.html"><span class="doc std std-doc">Table statistics</span></a>. The equivalent <a class="reference internal" href="../sql/set-session.html"><span class="doc std std-doc">catalog session
property</span></a> is <code class="docutils literal notranslate"><span class="pre">statistics_enabled</span></code> for session specific
use. Set to <code class="docutils literal notranslate"><span class="pre">false</span></code> to prevent statistics usage by the
<a class="reference internal" href="../optimizer/cost-based-optimizations.html"><span class="doc std std-doc">Cost-based optimizations</span></a> to make better decisions about the
query plan and therefore improve query processing performance. Setting to
<code class="docutils literal notranslate"><span class="pre">false</span></code> is not recommended and does not disable statistics gathering.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.extended-statistics.enabled</span></code></p></td>
<td><p>Enable statistics collection with <a class="reference internal" href="../sql/analyze.html"><span class="doc std std-doc">ANALYZE</span></a> and use of extended
statistics. The equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">extended_statistics_enabled</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.extended-statistics.collect-on-write</span></code></p></td>
<td><p>Enable collection of extended statistics for write operations. The
equivalent catalog session property is
<code class="docutils literal notranslate"><span class="pre">collect_extended_statistics_on_write</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.projection-pushdown-enabled</span></code></p></td>
<td><p>Enable <a class="reference internal" href="../optimizer/pushdown.html"><span class="doc std std-doc">projection pushdown</span></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.hive-catalog-name</span></code></p></td>
<td><p>Catalog to redirect to when a Hive table is referenced.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.register-table-procedure.enabled</span></code></p></td>
<td><p>Enable to allow user to call <a class="reference internal" href="iceberg.html#iceberg-register-table"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">register_table</span></code> procedure</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.add-files-procedure.enabled</span></code></p></td>
<td><p>Enable to allow user to call <a class="reference internal" href="iceberg.html#iceberg-add-files"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">add_files</span></code> procedure</span></a>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.query-partition-filter-required</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to force a query to use a partition filter for schemas
specified with <code class="docutils literal notranslate"><span class="pre">iceberg.query-partition-filter-required-schemas</span></code>. Equivalent
catalog session property is <code class="docutils literal notranslate"><span class="pre">query_partition_filter_required</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.query-partition-filter-required-schemas</span></code></p></td>
<td><p>Specify the list of schemas for which Trino can enforce that queries use a
filter on partition keys for source tables. Equivalent session property is
<code class="docutils literal notranslate"><span class="pre">query_partition_filter_required_schemas</span></code>. The list is used if the
<code class="docutils literal notranslate"><span class="pre">iceberg.query-partition-filter-required</span></code> configuration property or the
<code class="docutils literal notranslate"><span class="pre">query_partition_filter_required</span></code> catalog session property is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[]</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.incremental-refresh-enabled</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">false</span></code> to force the materialized view refresh operation to always
perform a full refresh. You can use the <code class="docutils literal notranslate"><span class="pre">incremental_refresh_enabled</span></code>
catalog session property for temporary, catalog specific use. In the
majority of cases, using incremental refresh, as compared to a full refresh,
is beneficial since a much smaller subset of the source tables needs to be
scanned. While incremental refresh may scan less data, it may result in the
creation of more data files, since it uses the append operation to insert
the new records.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.metadata-cache.enabled</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">false</span></code> to disable in-memory caching of metadata files on the
coordinator. This cache is not used when <code class="docutils literal notranslate"><span class="pre">fs.cache.enabled</span></code> is set to true.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.object-store-layout.enabled</span></code></p></td>
<td><p>Set to <code class="docutils literal notranslate"><span class="pre">true</span></code> to enable Iceberg’s <a class="reference external" href="https://iceberg.apache.org/docs/latest/aws/#object-store-file-layout">object store file layout</a>.
Enabling the object store file layout appends a deterministic hash directly
after the data write path.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">false</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.expire-snapshots.min-retention</span></code></p></td>
<td><p>Minimal retention period for the
<a class="reference internal" href="iceberg.html#iceberg-expire-snapshots"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">expire_snapshot</span></code> command</span></a>.
Equivalent session property is <code class="docutils literal notranslate"><span class="pre">expire_snapshots_min_retention</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">7d</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.remove-orphan-files.min-retention</span></code></p></td>
<td><p>Minimal retention period for the
<a class="reference internal" href="iceberg.html#iceberg-remove-orphan-files"><span class="std std-ref"><code class="docutils literal notranslate"><span class="pre">remove_orphan_files</span></code> command</span></a>.
Equivalent session property is <code class="docutils literal notranslate"><span class="pre">remove_orphan_files_min_retention</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">7d</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.idle-writer-min-file-size</span></code></p></td>
<td><p>Minimum data written by a single partition writer before it can
be considered as idle and can be closed by the engine. Equivalent
session property is <code class="docutils literal notranslate"><span class="pre">idle_writer_min_file_size</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">16MB</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.sorted-writing-enabled</span></code></p></td>
<td><p>Enable <a class="reference internal" href="iceberg.html#iceberg-sorted-files"><span class="std std-ref">sorted writing</span></a> to tables with a specified sort order. Equivalent
session property is <code class="docutils literal notranslate"><span class="pre">sorted_writing_enabled</span></code>.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.allowed-extra-properties</span></code></p></td>
<td><p>List of extra properties that are allowed to be set on Iceberg tables.
Use <code class="docutils literal notranslate"><span class="pre">*</span></code> to allow all properties.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">[]</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.split-manager-threads</span></code></p></td>
<td><p>Number of threads to use for generating splits.</p></td>
<td><p>Double the number of processors on the coordinator node.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.metadata.parallelism</span></code></p></td>
<td><p>Number of threads used for retrieving metadata. Currently, only table loading
is parallelized.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">8</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">iceberg.bucket-execution</span></code></p></td>
<td><p>Enable bucket-aware execution. This allows the engine to use physical
bucketing information to optimize queries by reducing data exchanges.</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">true</span></code></p></td>
</tr>
</tbody>
</table>
<section id="fault-tolerant-execution-support">
<span id="iceberg-fte-support"></span><h3 id="fault-tolerant-execution-support">Fault-tolerant execution support<a class="headerlink" href="iceberg.html#fault-tolerant-execution-support" title="Link to this heading">#</a></h3>
<p>The connector supports <a class="reference internal" href="../admin/fault-tolerant-execution.html"><span class="doc">Fault-tolerant execution</span></a> of query
processing. Read and write operations are both supported with any retry policy.</p>
</section>
</section>
<section id="file-system-access-configuration">
<span id="iceberg-file-system-configuration"></span><h2 id="file-system-access-configuration">File system access configuration<a class="headerlink" href="iceberg.html#file-system-access-configuration" title="Link to this heading">#</a></h2>
<p>The connector supports accessing the following file systems:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../object-storage/file-system-azure.html"><span class="doc std std-doc">Azure Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-gcs.html"><span class="doc std std-doc">Google Cloud Storage file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-s3.html"><span class="doc std std-doc">S3 file system support</span></a></p></li>
<li><p><a class="reference internal" href="../object-storage/file-system-hdfs.html"><span class="doc std std-doc">HDFS file system support</span></a></p></li>
</ul>
<p>You must enable and configure the specific file system access. <a class="reference internal" href="../object-storage.html#file-system-legacy"><span class="std std-ref">Legacy
support</span></a> is not recommended and will be removed.</p>
</section>
<section id="type-mapping">
<h2 id="type-mapping">Type mapping<a class="headerlink" href="iceberg.html#type-mapping" title="Link to this heading">#</a></h2>
<p>The connector reads and writes data into the supported data file formats Avro,
ORC, and Parquet, following the Iceberg specification.</p>
<p>Because Trino and Iceberg each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading or
writing data. Data types may not map the same way in both directions between
Trino and the data source. Refer to the following sections for type mapping in
each direction.</p>
<p>The Iceberg specification includes supported data types and the mapping to the
formating in the Avro, ORC, or Parquet files:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://iceberg.apache.org/spec/#avro">Iceberg to Avro</a></p></li>
<li><p><a class="reference external" href="https://iceberg.apache.org/spec/#orc">Iceberg to ORC</a></p></li>
<li><p><a class="reference external" href="https://iceberg.apache.org/spec/#parquet">Iceberg to Parquet</a></p></li>
</ul>
<section id="iceberg-to-trino-type-mapping">
<h3 id="iceberg-to-trino-type-mapping">Iceberg to Trino type mapping<a class="headerlink" href="iceberg.html#iceberg-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Iceberg types to the corresponding Trino types according to
the following table:</p>
<table id="id3">
<caption><span class="caption-text">Iceberg to Trino type mapping</span><a class="headerlink" href="iceberg.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Iceberg type</p></th>
<th class="head"><p>Trino type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMPTZ</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FIXED</span> <span class="pre">(L)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRUCT(...)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LIST(e)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY(e)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">MAP(k,v)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP(k,v)</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
<section id="trino-to-iceberg-type-mapping">
<h3 id="trino-to-iceberg-type-mapping">Trino to Iceberg type mapping<a class="headerlink" href="iceberg.html#trino-to-iceberg-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Trino types to the corresponding Iceberg types according to
the following table:</p>
<table id="id4">
<caption><span class="caption-text">Trino to Iceberg type mapping</span><a class="headerlink" href="iceberg.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trino type</p></th>
<th class="head"><p>Iceberg type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIME(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIME</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(6)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMPTZ</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">UUID</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BINARY</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">STRUCT(...)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ARRAY(e)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">LIST(e)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAP(k,v)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">MAP(k,v)</span></code></p></td>
</tr>
</tbody>
</table>
<p>No other types are supported.</p>
</section>
</section>
<section id="security">
<h2 id="security">Security<a class="headerlink" href="iceberg.html#security" title="Link to this heading">#</a></h2>
<section id="kerberos-authentication">
<h3 id="kerberos-authentication">Kerberos authentication<a class="headerlink" href="iceberg.html#kerberos-authentication" title="Link to this heading">#</a></h3>
<p>The Iceberg connector supports Kerberos authentication for the Hive metastore
and HDFS and is configured using the same parameters as the Hive connector. Find
more information in the <a class="reference internal" href="../object-storage/file-system-hdfs.html"><span class="doc std std-doc">HDFS file system support</span></a> section.</p>
</section>
<section id="authorization">
<span id="iceberg-authorization"></span><h3 id="authorization">Authorization<a class="headerlink" href="iceberg.html#authorization" title="Link to this heading">#</a></h3>
<p>The Iceberg connector allows you to choose one of several means of providing
authorization at the catalog level.</p>
<p>You can enable authorization checks for the connector by setting the
<code class="docutils literal notranslate"><span class="pre">iceberg.security</span></code> property in the catalog properties file. This property must
be one of the following values:</p>
<table id="id5">
<caption><span class="caption-text">Iceberg security values</span><a class="headerlink" href="iceberg.html#id5" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 33%"/>
<col style="width: 67%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ALLOW_ALL</span></code></p></td>
<td><p>No authorization checks are enforced.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SYSTEM</span></code></p></td>
<td><p>The connector relies on system-level access control.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">READ_ONLY</span></code></p></td>
<td><p>Operations that read data or metadata, such as <a class="reference internal" href="../sql/select.html"><span class="doc std std-doc">SELECT</span></a> are
permitted. No operations that write data or metadata, such as
<a class="reference internal" href="../sql/create-table.html"><span class="doc std std-doc">CREATE TABLE</span></a>, <a class="reference internal" href="../sql/insert.html"><span class="doc std std-doc">INSERT</span></a>, or <a class="reference internal" href="../sql/delete.html"><span class="doc std std-doc">DELETE</span></a> are allowed.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FILE</span></code></p></td>
<td><p>Authorization checks are enforced using a catalog-level access control
configuration file whose path is specified in the <code class="docutils literal notranslate"><span class="pre">security.config-file</span></code>
catalog configuration property. See <a class="reference internal" href="../security/file-system-access-control.html#catalog-file-based-access-control"><span class="std std-ref">Catalog-level access control files</span></a>
for information on the authorization configuration file.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sql-support">
<span id="iceberg-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="iceberg.html#sql-support" title="Link to this heading">#</a></h2>
<p>This connector provides read access and write access to data and metadata in
Iceberg. In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a>
and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements, the connector
supports the following features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../language/sql-support.html#sql-write-operations"><span class="std std-ref">Write operations</span></a>:</p>
<ul>
<li><p><a class="reference internal" href="iceberg.html#iceberg-schema-table-management"><span class="std std-ref">Schema and table management</span></a> and <a class="reference internal" href="iceberg.html#iceberg-tables"><span class="std std-ref">Partitioned tables</span></a></p></li>
<li><p><a class="reference internal" href="iceberg.html#iceberg-data-management"><span class="std std-ref">Data management</span></a></p></li>
<li><p><a class="reference internal" href="../language/sql-support.html#sql-view-management"><span class="std std-ref">View management</span></a></p></li>
<li><p><a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">Materialized view management</span></a>, see also <a class="reference internal" href="iceberg.html#iceberg-materialized-views"><span class="std std-ref">Materialized views</span></a></p></li>
</ul>
</li>
</ul>
<section id="basic-usage-examples">
<h3 id="basic-usage-examples">Basic usage examples<a class="headerlink" href="iceberg.html#basic-usage-examples" title="Link to this heading">#</a></h3>
<p>The connector supports creating schemas. You can create a schema with or without
a specified location.</p>
<p>You can create a schema with the <a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a> statement and the
<code class="docutils literal notranslate"><span class="pre">location</span></code> schema property. The tables in this schema, which have no explicit
<code class="docutils literal notranslate"><span class="pre">location</span></code> set in <a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a> statement, are located in a
subdirectory under the directory corresponding to the schema location.</p>
<p>Create a schema on S3:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_s3_schema</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path/'</span><span class="p">);</span>
</pre></div>
</div>
<p>Create a schema on an S3-compatible object storage such as MinIO:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_s3a_schema</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'s3a://my-bucket/a/path/'</span><span class="p">);</span>
</pre></div>
</div>
<p>Create a schema on HDFS:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_hdfs_schema</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="k">location</span><span class="o">=</span><span class="s1">'hdfs://hadoop-master:9000/user/hive/warehouse/a/path/'</span><span class="p">);</span>
</pre></div>
</div>
<p>Optionally, on HDFS, the location can be omitted:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_hdfs_schema</span><span class="p">;</span>
</pre></div>
</div>
<p>The Iceberg connector supports creating tables using the <a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a> syntax. Optionally, specify the <a class="reference internal" href="iceberg.html#iceberg-table-properties"><span class="std std-ref">table properties</span></a> supported by this connector:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example_table</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">c1</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">c2</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">c3</span><span class="w"> </span><span class="n">DOUBLE</span>
<span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'PARQUET'</span><span class="p">,</span>
<span class="w">    </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'c1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'c2'</span><span class="p">],</span>
<span class="w">    </span><span class="n">sorted_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'c3'</span><span class="p">],</span>
<span class="w">    </span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path/'</span>
<span class="p">);</span>
</pre></div>
</div>
<p>When the <code class="docutils literal notranslate"><span class="pre">location</span></code> table property is omitted, the content of the table is
stored in a subdirectory under the directory corresponding to the schema
location.</p>
<p>The Iceberg connector supports creating tables using the <a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a> with <a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a> syntax:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">tiny_nation</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'PARQUET'</span>
<span class="p">)</span>
<span class="k">AS</span>
<span class="w">    </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="w">    </span><span class="k">FROM</span><span class="w"> </span><span class="n">nation</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">nationkey</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span>
</pre></div>
</div>
<p>Another flavor of creating tables with <a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a> is with <a class="reference internal" href="../sql/values.html"><span class="doc">VALUES</span></a> syntax:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">yearly_clicks</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">year</span><span class="p">,</span>
<span class="w">    </span><span class="n">clicks</span>
<span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'year'</span><span class="p">]</span>
<span class="p">)</span>
<span class="k">AS</span><span class="w"> </span><span class="k">VALUES</span>
<span class="w">    </span><span class="p">(</span><span class="mi">2021</span><span class="p">,</span><span class="w"> </span><span class="mi">10000</span><span class="p">),</span>
<span class="w">    </span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">20000</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="procedures">
<h3 id="procedures">Procedures<a class="headerlink" href="iceberg.html#procedures" title="Link to this heading">#</a></h3>
<p>Use the <a class="reference internal" href="../sql/call.html"><span class="doc">CALL</span></a> statement to perform data manipulation or
administrative tasks. Procedures are available in the system schema of each
catalog. The following code snippet displays how to call the
<code class="docutils literal notranslate"><span class="pre">example_procedure</span></code> in the <code class="docutils literal notranslate"><span class="pre">examplecatalog</span></code> catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">examplecatalog</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">example_procedure</span><span class="p">();</span>
</pre></div>
</div>
<section id="register-table">
<span id="iceberg-register-table"></span><h4 id="register-table">Register table<a class="headerlink" href="iceberg.html#register-table" title="Link to this heading">#</a></h4>
<p>The connector can register existing Iceberg tables into the metastore if
<code class="docutils literal notranslate"><span class="pre">iceberg.register-table-procedure.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code> for the catalog.</p>
<p>The procedure <code class="docutils literal notranslate"><span class="pre">system.register_table</span></code> allows the caller to register an
existing Iceberg table in the metastore, using its existing metadata and data
files:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">register_table</span><span class="p">(</span>
<span class="w">  </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="n">table_location</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'hdfs://hadoop-master:9000/user/hive/warehouse/customer_orders-581fad8517934af6be1857a903559d44'</span><span class="p">);</span>
</pre></div>
</div>
<p>In addition, you can provide a file name to register a table with specific
metadata. This may be used to register the table with some specific table state,
or may be necessary if the connector cannot automatically figure out the
metadata version to use:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">register_table</span><span class="p">(</span>
<span class="w">  </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="n">table_location</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'hdfs://hadoop-master:9000/user/hive/warehouse/customer_orders-581fad8517934af6be1857a903559d44'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="n">metadata_file_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'00003-409702ba-4735-4645-8f14-09537cc0b2c8.metadata.json'</span><span class="p">);</span>
</pre></div>
</div>
<p>To prevent unauthorized users from accessing data, this procedure is disabled by
default. The procedure is enabled only when
<code class="docutils literal notranslate"><span class="pre">iceberg.register-table-procedure.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
</section>
<section id="unregister-table">
<span id="iceberg-unregister-table"></span><h4 id="unregister-table">Unregister table<a class="headerlink" href="iceberg.html#unregister-table" title="Link to this heading">#</a></h4>
<p>The connector can remove existing Iceberg tables from the metastore. Once
unregistered, you can no longer query the table from Trino.</p>
<p>The procedure <code class="docutils literal notranslate"><span class="pre">system.unregister_table</span></code> allows the caller to unregister an
existing Iceberg table from the metastores without deleting the data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">unregister_table</span><span class="p">(</span>
<span class="w">  </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span><span class="w"> </span>
<span class="w">  </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="migrate-table">
<h4 id="migrate-table">Migrate table<a class="headerlink" href="iceberg.html#migrate-table" title="Link to this heading">#</a></h4>
<p>The connector can read from or write to Hive tables that have been migrated to
Iceberg.</p>
<p>Use the procedure <code class="docutils literal notranslate"><span class="pre">system.migrate</span></code> to move a table from the Hive format to the
Iceberg format, loaded with the source’s data files. Table schema, partitioning,
properties, and location are copied from the source table. A bucketed Hive table
will be migrated as a non-bucketed Iceberg table. The data files in the Hive table
must use the Parquet, ORC, or Avro file format.</p>
<p>The procedure must be called for a specific catalog <code class="docutils literal notranslate"><span class="pre">example</span></code> with the
relevant schema and table names supplied with the required parameters
<code class="docutils literal notranslate"><span class="pre">schema_name</span></code> and <code class="docutils literal notranslate"><span class="pre">table_name</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">migrate</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">);</span>
</pre></div>
</div>
<p>Migrate fails if any table partition uses an unsupported file format.</p>
<p>In addition, you can provide a <code class="docutils literal notranslate"><span class="pre">recursive_directory</span></code> argument to migrate a
Hive table that contains subdirectories:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">migrate</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'testdb'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span>
<span class="w">    </span><span class="n">recursive_directory</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'true'</span><span class="p">);</span>
</pre></div>
</div>
<p>The default value is <code class="docutils literal notranslate"><span class="pre">fail</span></code>, which causes the migrate procedure to throw an
exception if subdirectories are found. Set the value to <code class="docutils literal notranslate"><span class="pre">true</span></code> to migrate
nested directories, or <code class="docutils literal notranslate"><span class="pre">false</span></code> to ignore them.</p>
</section>
<section id="add-files">
<span id="iceberg-add-files"></span><h4 id="add-files">Add files<a class="headerlink" href="iceberg.html#add-files" title="Link to this heading">#</a></h4>
<p>The connector can add files from tables or locations to an existing Iceberg
table if <code class="docutils literal notranslate"><span class="pre">iceberg.add-files-procedure.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code> for the catalog.</p>
<p>Use the procedure <code class="docutils literal notranslate"><span class="pre">add_files_from_table</span></code> to add existing files from a Hive table
in the current catalog, or <code class="docutils literal notranslate"><span class="pre">add_files</span></code> to add existing files from a specified
location, to an existing Iceberg table.</p>
<p>The data files must be the Parquet, ORC, or Avro file format.</p>
<p>The procedure adds the files to the target table, specified after <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span></code>,
and loads them from the source table specified with the required parameters
<code class="docutils literal notranslate"><span class="pre">schema_name</span></code> and <code class="docutils literal notranslate"><span class="pre">table_name</span></code>. The source table must be accessible in the same
catalog as the target table and use the Hive format. The target table must use
the Iceberg format. The catalog must use the Iceberg connector.</p>
<p>The following examples copy data from the Hive table <code class="docutils literal notranslate"><span class="pre">hive_customer_orders</span></code> in
the <code class="docutils literal notranslate"><span class="pre">legacy</span></code> schema of the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog into the Iceberg table
<code class="docutils literal notranslate"><span class="pre">iceberg_customer_orders</span></code> in the <code class="docutils literal notranslate"><span class="pre">lakehouse</span></code> schema of the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">lakehouse</span><span class="p">.</span><span class="n">iceberg_customer_orders</span><span class="w"> </span>
<span class="k">EXECUTE</span><span class="w"> </span><span class="n">add_files_from_table</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'legacy'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">);</span>
</pre></div>
</div>
<p>Alternatively, you can set the current catalog and schema with a <code class="docutils literal notranslate"><span class="pre">USE</span></code>
statement, and omit catalog and schema information:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">lakehouse</span><span class="p">;</span>
<span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">iceberg_customer_orders</span><span class="w"> </span>
<span class="k">EXECUTE</span><span class="w"> </span><span class="n">add_files_from_table</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'legacy'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">);</span>
</pre></div>
</div>
<p>Use a <code class="docutils literal notranslate"><span class="pre">partition_filter</span></code> argument to add files from specified partitions. The
following example adds files from a partition where the <code class="docutils literal notranslate"><span class="pre">region</span></code> is <code class="docutils literal notranslate"><span class="pre">ASIA</span></code> and
<code class="docutils literal notranslate"><span class="pre">country</span></code> is <code class="docutils literal notranslate"><span class="pre">JAPAN</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">lakehouse</span><span class="p">.</span><span class="n">iceberg_customer_orders</span><span class="w"> </span>
<span class="k">EXECUTE</span><span class="w"> </span><span class="n">add_files_from_table</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'legacy'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span>
<span class="w">    </span><span class="n">partition_filter</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="k">map</span><span class="p">(</span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'region'</span><span class="p">,</span><span class="w"> </span><span class="s1">'country'</span><span class="p">],</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'ASIA'</span><span class="p">,</span><span class="w"> </span><span class="s1">'JAPAN'</span><span class="p">]));</span>
</pre></div>
</div>
<p>In addition, you can provide a <code class="docutils literal notranslate"><span class="pre">recursive_directory</span></code> argument to migrate a
Hive table that contains subdirectories:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">lakehouse</span><span class="p">.</span><span class="n">iceberg_customer_orders</span><span class="w"> </span>
<span class="k">EXECUTE</span><span class="w"> </span><span class="n">add_files_from_table</span><span class="p">(</span>
<span class="w">    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'legacy'</span><span class="p">,</span>
<span class="w">    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'customer_orders'</span><span class="p">,</span>
<span class="w">    </span><span class="n">recursive_directory</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'true'</span><span class="p">);</span>
</pre></div>
</div>
<p>The default value of <code class="docutils literal notranslate"><span class="pre">recursive_directory</span></code> is <code class="docutils literal notranslate"><span class="pre">fail</span></code>, which causes the procedure
to throw an exception if subdirectories are found. Set the value to <code class="docutils literal notranslate"><span class="pre">true</span></code> to
add files from nested directories, or <code class="docutils literal notranslate"><span class="pre">false</span></code> to ignore them.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">add_files</span></code> procedure supports adding files, and therefore the contained
data, to a target table, specified after <code class="docutils literal notranslate"><span class="pre">ALTER</span> <span class="pre">TABLE</span></code>. It loads the files from
a object storage path specified with the required <code class="docutils literal notranslate"><span class="pre">location</span></code> parameter. The
files must use the specified <code class="docutils literal notranslate"><span class="pre">format</span></code>, with <code class="docutils literal notranslate"><span class="pre">ORC</span></code> and <code class="docutils literal notranslate"><span class="pre">PARQUET</span></code> as valid values.
The target Iceberg table must use the same format as the added files. The
procedure does not validate file schemas for compatibility with the target
Iceberg table. The <code class="docutils literal notranslate"><span class="pre">location</span></code> property is supported for partitioned tables.</p>
<p>The following examples copy <code class="docutils literal notranslate"><span class="pre">ORC</span></code>-format files from the location
<code class="docutils literal notranslate"><span class="pre">s3://my-bucket/a/path</span></code> into the Iceberg table <code class="docutils literal notranslate"><span class="pre">iceberg_customer_orders</span></code> in the
<code class="docutils literal notranslate"><span class="pre">lakehouse</span></code> schema of the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">lakehouse</span><span class="p">.</span><span class="n">iceberg_customer_orders</span><span class="w"> </span>
<span class="k">EXECUTE</span><span class="w"> </span><span class="n">add_files</span><span class="p">(</span>
<span class="w">    </span><span class="k">location</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'s3://my-bucket/a/path'</span><span class="p">,</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ORC'</span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
<section id="functions">
<span id="iceberg-functions"></span><h3 id="functions">Functions<a class="headerlink" href="iceberg.html#functions" title="Link to this heading">#</a></h3>
<p>Functions are available in the <code class="docutils literal notranslate"><span class="pre">system</span></code> schema of each catalog. Functions can
be called in a SQL statement. For example, the following code snippet
displays how to execute the <code class="docutils literal notranslate"><span class="pre">system.bucket</span></code> function in an Iceberg catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">bucket</span><span class="p">(</span><span class="s1">'trino'</span><span class="p">,</span><span class="w"> </span><span class="mi">16</span><span class="p">);</span>
</pre></div>
</div>
<section id="bucket">
<span id="iceberg-bucket-function"></span><h4 id="bucket">bucket<a class="headerlink" href="iceberg.html#bucket" title="Link to this heading">#</a></h4>
<p>This function exposes the <a class="reference external" href="https://iceberg.apache.org/spec/#bucket-transform-details">Iceberg bucket transform</a>
so that users can determine what bucket a particular value falls into. The
function takes two arguments: the partition value and the number of buckets.</p>
<p>The supported types for the 1st argument to this function are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">TINYINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SMALLINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DATE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></li>
</ul>
<p>For example, if we wanted to see what bucket number a particular string would
be assigned, we can execute:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">bucket</span><span class="p">(</span><span class="s1">'trino'</span><span class="p">,</span><span class="w"> </span><span class="mi">16</span><span class="p">);</span>
</pre></div>
</div>
<p>This function can be used in a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause to only operate on a particular
bucket:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">customer</span>
<span class="k">WHERE</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">bucket</span><span class="p">(</span><span class="n">custkey</span><span class="p">,</span><span class="w"> </span><span class="mi">16</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
<section id="data-management">
<span id="iceberg-data-management"></span><h3 id="data-management">Data management<a class="headerlink" href="iceberg.html#data-management" title="Link to this heading">#</a></h3>
<p>The <a class="reference internal" href="../language/sql-support.html#sql-data-management"><span class="std std-ref">Data management</span></a> functionality includes support for <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>,
<code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, and <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> statements.</p>
<section id="deletion-by-partition">
<span id="iceberg-delete"></span><h4 id="deletion-by-partition">Deletion by partition<a class="headerlink" href="iceberg.html#deletion-by-partition" title="Link to this heading">#</a></h4>
<p>For partitioned tables, the Iceberg connector supports the deletion of entire
partitions if the <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause specifies filters only on the
identity-transformed partitioning columns, that can match entire partitions.
Given the table definition from <a class="reference internal" href="iceberg.html#iceberg-tables"><span class="std std-ref">Partitioned Tables</span></a>
section, the following SQL statement deletes all partitions for which
<code class="docutils literal notranslate"><span class="pre">country</span></code> is <code class="docutils literal notranslate"><span class="pre">US</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">DELETE</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">country</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'US'</span><span class="p">;</span>
</pre></div>
</div>
<p>A partition delete is performed if the <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause meets these conditions.</p>
</section>
<section id="row-level-deletion">
<h4 id="row-level-deletion">Row level deletion<a class="headerlink" href="iceberg.html#row-level-deletion" title="Link to this heading">#</a></h4>
<p>Tables using v2 of the Iceberg specification support deletion of individual rows
by writing position delete files.</p>
</section>
</section>
<section id="schema-and-table-management">
<span id="iceberg-schema-table-management"></span><h3 id="schema-and-table-management">Schema and table management<a class="headerlink" href="iceberg.html#schema-and-table-management" title="Link to this heading">#</a></h3>
<p>The <a class="reference internal" href="../language/sql-support.html#sql-schema-table-management"><span class="std std-ref">Schema and table management</span></a> functionality includes support for:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../sql/create-schema.html"><span class="doc">CREATE SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-schema.html"><span class="doc">DROP SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-schema.html"><span class="doc">ALTER SCHEMA</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
<section id="schema-evolution">
<h4 id="schema-evolution">Schema evolution<a class="headerlink" href="iceberg.html#schema-evolution" title="Link to this heading">#</a></h4>
<p>Iceberg supports schema evolution, with safe column add, drop, and
rename operations, including in nested structures.</p>
<p>Iceberg supports updating column types only for widening operations:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code> to <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code> to <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DECIMAL(p,s)</span></code> to <code class="docutils literal notranslate"><span class="pre">DECIMAL(p2,s)</span></code> when <code class="docutils literal notranslate"><span class="pre">p2</span></code> &gt; <code class="docutils literal notranslate"><span class="pre">p</span></code> (scale cannot change)</p></li>
</ul>
<p>Partitioning can also be changed and the connector can still query data
created before the partitioning change.</p>
</section>
<section id="alter-table-execute">
<span id="iceberg-alter-table-execute"></span><h4 id="alter-table-execute">ALTER TABLE EXECUTE<a class="headerlink" href="iceberg.html#alter-table-execute" title="Link to this heading">#</a></h4>
<p>The connector supports the following commands for use with <a class="reference internal" href="../sql/alter-table.html#alter-table-execute"><span class="std std-ref">ALTER TABLE EXECUTE</span></a>.</p>
<section id="optimize">
<h5 id="optimize">optimize<a class="headerlink" href="iceberg.html#optimize" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">optimize</span></code> command is used for rewriting the content of the specified
table so that it is merged into fewer but larger files. If the table is
partitioned, the data compaction acts separately on each partition selected for
optimization. This operation improves read performance.</p>
<p>All files with a size below the optional <code class="docutils literal notranslate"><span class="pre">file_size_threshold</span></code> parameter
(default value for the threshold is <code class="docutils literal notranslate"><span class="pre">100MB</span></code>) are merged in case any of the
following conditions are met per partition:</p>
<ul class="simple">
<li><p>more than one data file to merge is present</p></li>
<li><p>at least one data file, with delete files attached, is present</p></li>
</ul>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
</pre></div>
</div>
<p>The following statement merges files in a table that are
under 128 megabytes in size:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span><span class="p">(</span><span class="n">file_size_threshold</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'128MB'</span><span class="p">)</span>
</pre></div>
</div>
<p>You can use a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause with the columns used to partition the table
to filter which partitions are optimized:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_partitioned_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">partition_key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span>
</pre></div>
</div>
<p>You can use a more complex <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause to narrow down the scope of the
<code class="docutils literal notranslate"><span class="pre">optimize</span></code> procedure. The following example casts the timestamp values to
dates, and uses a comparison to only optimize partitions with data from the year
2022 or newer:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="n">timestamp_tz</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="nb">DATE</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2021-12-31'</span>
</pre></div>
</div>
<p>Use a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause with <a class="reference internal" href="iceberg.html#iceberg-metadata-columns"><span class="std std-ref">metadata columns</span></a> to filter
which files are optimized.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$file_modified_time"</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">date_trunc</span><span class="p">(</span><span class="s1">'day'</span><span class="p">,</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="optimize-manifests">
<span id="iceberg-optimize-manifests"></span><h5 id="optimize-manifests">optimize_manifests<a class="headerlink" href="iceberg.html#optimize-manifests" title="Link to this heading">#</a></h5>
<p>Rewrites manifest files to cluster them by partitioning columns.
This can be used to optimize scan planning when there are many small manifest files
or when there are partition filters in read queries but the manifest files are
not grouped by partitions.
The iceberg table property <code class="docutils literal notranslate"><span class="pre">commit.manifest.target-size-bytes</span></code> controls
the maximum size of manifest files produced by this procedure.</p>
<p><code class="docutils literal notranslate"><span class="pre">optimize_manifests</span></code> can be run as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">optimize_manifests</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="expire-snapshots">
<span id="iceberg-expire-snapshots"></span><h5 id="expire-snapshots">expire_snapshots<a class="headerlink" href="iceberg.html#expire-snapshots" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">expire_snapshots</span></code> command removes all snapshots and all related metadata
and data files. Regularly expiring snapshots is recommended to delete data files
that are no longer needed, and to keep the size of table metadata small. The
procedure affects all snapshots that are older than the time period configured
with the <code class="docutils literal notranslate"><span class="pre">retention_threshold</span></code> parameter.</p>
<p><code class="docutils literal notranslate"><span class="pre">expire_snapshots</span></code> can be run as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">expire_snapshots</span><span class="p">(</span><span class="n">retention_threshold</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'7d'</span><span class="p">);</span>
</pre></div>
</div>
<p>The value for <code class="docutils literal notranslate"><span class="pre">retention_threshold</span></code> must be higher than or equal to
<code class="docutils literal notranslate"><span class="pre">iceberg.expire-snapshots.min-retention</span></code> in the catalog, otherwise the
procedure fails with a similar message: <code class="docutils literal notranslate"><span class="pre">Retention</span> <span class="pre">specified</span> <span class="pre">(1.00d)</span> <span class="pre">is</span> <span class="pre">shorter</span> <span class="pre">than</span> <span class="pre">the</span> <span class="pre">minimum</span> <span class="pre">retention</span> <span class="pre">configured</span> <span class="pre">in</span> <span class="pre">the</span> <span class="pre">system</span> <span class="pre">(7.00d)</span></code>. The default value
for this property is <code class="docutils literal notranslate"><span class="pre">7d</span></code>.</p>
</section>
<section id="remove-orphan-files">
<span id="iceberg-remove-orphan-files"></span><h5 id="remove-orphan-files">remove_orphan_files<a class="headerlink" href="iceberg.html#remove-orphan-files" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">remove_orphan_files</span></code> command removes all files from a table’s data
directory that are not linked from metadata files and that are older than the
value of <code class="docutils literal notranslate"><span class="pre">retention_threshold</span></code> parameter. Deleting orphan files from time to
time is recommended to keep size of a table’s data directory under control.</p>
<p><code class="docutils literal notranslate"><span class="pre">remove_orphan_files</span></code> can be run as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">remove_orphan_files</span><span class="p">(</span><span class="n">retention_threshold</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'7d'</span><span class="p">);</span>
</pre></div>
</div>
<p>The value for <code class="docutils literal notranslate"><span class="pre">retention_threshold</span></code> must be higher than or equal to
<code class="docutils literal notranslate"><span class="pre">iceberg.remove-orphan-files.min-retention</span></code> in the catalog otherwise the
procedure fails with a similar message: <code class="docutils literal notranslate"><span class="pre">Retention</span> <span class="pre">specified</span> <span class="pre">(1.00d)</span> <span class="pre">is</span> <span class="pre">shorter</span> <span class="pre">than</span> <span class="pre">the</span> <span class="pre">minimum</span> <span class="pre">retention</span> <span class="pre">configured</span> <span class="pre">in</span> <span class="pre">the</span> <span class="pre">system</span> <span class="pre">(7.00d)</span></code>. The default value
for this property is <code class="docutils literal notranslate"><span class="pre">7d</span></code>.</p>
</section>
<section id="drop-extended-stats">
<span id="id1"></span><h5 id="drop-extended-stats">drop_extended_stats<a class="headerlink" href="iceberg.html#drop-extended-stats" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">drop_extended_stats</span></code> command removes all extended statistics information
from the table.</p>
<p><code class="docutils literal notranslate"><span class="pre">drop_extended_stats</span></code> can be run as follows:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">drop_extended_stats</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
<section id="alter-table-set-properties">
<span id="iceberg-alter-table-set-properties"></span><h4 id="alter-table-set-properties">ALTER TABLE SET PROPERTIES<a class="headerlink" href="iceberg.html#alter-table-set-properties" title="Link to this heading">#</a></h4>
<p>The connector supports modifying the properties on existing tables using
<a class="reference internal" href="../sql/alter-table.html#alter-table-set-properties"><span class="std std-ref">ALTER TABLE SET PROPERTIES</span></a>.</p>
<p>The following table properties can be updated after a table is created:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">format_version</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">partitioning</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sorted_by</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_commit_retry</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">object_store_layout_enabled</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">data_location</span></code></p></li>
</ul>
<p>For example, to update a table from v1 of the Iceberg specification to v2:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">PROPERTIES</span><span class="w"> </span><span class="n">format_version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>
</pre></div>
</div>
<p>Or to set the column <code class="docutils literal notranslate"><span class="pre">my_new_partition_column</span></code> as a partition column on a
table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">PROPERTIES</span><span class="w"> </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="o">&lt;</span><span class="k">existing</span><span class="w"> </span><span class="n">partition</span><span class="w"> </span><span class="n">columns</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="s1">'my_new_partition_column'</span><span class="p">];</span>
</pre></div>
</div>
<p>The current values of a table’s properties can be shown using <a class="reference internal" href="../sql/show-create-table.html"><span class="doc">SHOW CREATE TABLE</span></a>.</p>
<section id="table-properties">
<span id="iceberg-table-properties"></span><h5 id="table-properties">Table properties<a class="headerlink" href="iceberg.html#table-properties" title="Link to this heading">#</a></h5>
<p>Table properties supply or set metadata for the underlying tables. This is key
for <a class="reference internal" href="../sql/create-table-as.html"><span class="doc">CREATE TABLE AS</span></a> statements. Table properties are passed to the
connector using a <a class="reference internal" href="../sql/create-table-as.html"><span class="doc">WITH</span></a> clause.</p>
<table id="id6">
<caption><span class="caption-text">Iceberg table properties</span><a class="headerlink" href="iceberg.html#id6" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">format</span></code></p></td>
<td><p>Optionally specifies the format of table data files; either <code class="docutils literal notranslate"><span class="pre">PARQUET</span></code>,
<code class="docutils literal notranslate"><span class="pre">ORC</span></code>, or <code class="docutils literal notranslate"><span class="pre">AVRO</span></code>. Defaults to the value of the <code class="docutils literal notranslate"><span class="pre">iceberg.file-format</span></code> catalog
configuration property, which defaults to <code class="docutils literal notranslate"><span class="pre">PARQUET</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">partitioning</span></code></p></td>
<td><p>Optionally specifies table partitioning. If a table is partitioned by
columns <code class="docutils literal notranslate"><span class="pre">c1</span></code> and <code class="docutils literal notranslate"><span class="pre">c2</span></code>, the partitioning property is <code class="docutils literal notranslate"><span class="pre">partitioning</span> <span class="pre">=</span> <span class="pre">ARRAY['c1',</span> <span class="pre">'c2']</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">sorted_by</span></code></p></td>
<td><p>The sort order to be applied during writes to the content of
each file written to the table. If the table files are sorted by columns
<code class="docutils literal notranslate"><span class="pre">c1</span></code> and <code class="docutils literal notranslate"><span class="pre">c2</span></code>, the sort order property is <code class="docutils literal notranslate"><span class="pre">sorted_by</span> <span class="pre">=</span> <span class="pre">ARRAY['c1',</span> <span class="pre">'c2']</span></code>.
The sort order applies to the contents written within each output file
independently and not the entire dataset.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">location</span></code></p></td>
<td><p>Optionally specifies the file system location URI for the table.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">format_version</span></code></p></td>
<td><p>Optionally specifies the format version of the Iceberg specification to use
for new tables; either <code class="docutils literal notranslate"><span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">2</span></code>. Defaults to <code class="docutils literal notranslate"><span class="pre">2</span></code>. Version <code class="docutils literal notranslate"><span class="pre">2</span></code> is required
for row level deletes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">max_commit_retry</span></code></p></td>
<td><p>Number of times to retry a commit before failing. Defaults to the value of
the <code class="docutils literal notranslate"><span class="pre">iceberg.max-commit-retry</span></code> catalog configuration property, which
defaults to <code class="docutils literal notranslate"><span class="pre">4</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">orc_bloom_filter_columns</span></code></p></td>
<td><p>Comma-separated list of columns to use for ORC bloom filter. It improves the
performance of queries using Equality and IN predicates when reading ORC
files. Requires ORC format. Defaults to <code class="docutils literal notranslate"><span class="pre">[]</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">orc_bloom_filter_fpp</span></code></p></td>
<td><p>The ORC bloom filters false positive probability. Requires ORC format.
Defaults to <code class="docutils literal notranslate"><span class="pre">0.05</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parquet_bloom_filter_columns</span></code></p></td>
<td><p>Comma-separated list of columns to use for Parquet bloom filter. It improves
the performance of queries using Equality and IN predicates when reading
Parquet files. Requires Parquet format. Defaults to <code class="docutils literal notranslate"><span class="pre">[]</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">object_store_layout_enabled</span></code></p></td>
<td><p>Whether Iceberg’s <a class="reference external" href="https://iceberg.apache.org/docs/latest/aws/#object-store-file-layout">object store file layout</a> is enabled.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">data_location</span></code></p></td>
<td><p>Optionally specifies the file system location URI for the table’s data files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">extra_properties</span></code></p></td>
<td><p>Additional properties added to an Iceberg table. The properties are not used by Trino,
and are available in the <code class="docutils literal notranslate"><span class="pre">$properties</span></code> metadata table.
The properties are not included in the output of <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">CREATE</span> <span class="pre">TABLE</span></code> statements.</p></td>
</tr>
</tbody>
</table>
<p>The table definition below specifies to use Parquet files, partitioning by columns
<code class="docutils literal notranslate"><span class="pre">c1</span></code> and <code class="docutils literal notranslate"><span class="pre">c2</span></code>, and a file system location of
<code class="docutils literal notranslate"><span class="pre">/var/example_tables/test_table</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">c1</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">c2</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">c3</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'PARQUET'</span><span class="p">,</span>
<span class="w">    </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'c1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'c2'</span><span class="p">],</span>
<span class="w">    </span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'/var/example_tables/test_table'</span><span class="p">);</span>
</pre></div>
</div>
<p>The table definition below specifies to use ORC files, bloom filter index by columns
<code class="docutils literal notranslate"><span class="pre">c1</span></code> and <code class="docutils literal notranslate"><span class="pre">c2</span></code>, fpp is 0.05, and a file system location of
<code class="docutils literal notranslate"><span class="pre">/var/example_tables/test_table</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">c1</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">c2</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">c3</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'ORC'</span><span class="p">,</span>
<span class="w">    </span><span class="k">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'/var/example_tables/test_table'</span><span class="p">,</span>
<span class="w">    </span><span class="n">orc_bloom_filter_columns</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'c1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'c2'</span><span class="p">],</span>
<span class="w">    </span><span class="n">orc_bloom_filter_fpp</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">05</span><span class="p">);</span>
</pre></div>
</div>
<p>The table definition below specifies to use Avro files, partitioning
by <code class="docutils literal notranslate"><span class="pre">child1</span></code> field in <code class="docutils literal notranslate"><span class="pre">parent</span></code> column:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_table</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">data</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">parent</span><span class="w"> </span><span class="k">ROW</span><span class="p">(</span><span class="n">child1</span><span class="w"> </span><span class="n">DOUBLE</span><span class="p">,</span><span class="w"> </span><span class="n">child2</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">))</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'AVRO'</span><span class="p">,</span>
<span class="w">    </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'"parent.child1"'</span><span class="p">]);</span>
</pre></div>
</div>
</section>
</section>
<section id="metadata-tables">
<span id="iceberg-metadata-tables"></span><h4 id="metadata-tables">Metadata tables<a class="headerlink" href="iceberg.html#metadata-tables" title="Link to this heading">#</a></h4>
<p>The connector exposes several metadata tables for each Iceberg table. These
metadata tables contain information about the internal structure of the Iceberg
table. You can query each metadata table by appending the metadata table name to
the table name:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$properties"</span><span class="p">;</span>
</pre></div>
</div>
<section id="properties-table">
<h5 id="properties-table"><code class="docutils literal notranslate"><span class="pre">$properties</span></code> table<a class="headerlink" href="iceberg.html#properties-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$properties</span></code> table provides access to general information about Iceberg
table configuration and any additional metadata key/value pairs that the table
is tagged with.</p>
<p>You can retrieve the properties of the current snapshot of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$properties"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> key                   | value    |
-----------------------+----------+
write.format.default   | PARQUET  |
</pre></div>
</div>
</section>
<section id="history-table">
<span id="iceberg-history-table"></span><h5 id="history-table"><code class="docutils literal notranslate"><span class="pre">$history</span></code> table<a class="headerlink" href="iceberg.html#history-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$history</span></code> table provides a log of the metadata changes performed on the
Iceberg table.</p>
<p>You can retrieve the changelog of the Iceberg table <code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the
following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$history"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> made_current_at                  | snapshot_id          | parent_id            | is_current_ancestor
----------------------------------+----------------------+----------------------+--------------------
2022-01-10 08:11:20 Europe/Vienna | 8667764846443717831  |  &lt;null&gt;              |  true
2022-01-10 08:11:34 Europe/Vienna | 7860805980949777961  | 8667764846443717831  |  true
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id7">
<caption><span class="caption-text">History columns</span><a class="headerlink" href="iceberg.html#id7" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">made_current_at</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p>The time when the snapshot became active.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier of the snapshot.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parent_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier of the parent snapshot.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">is_current_ancestor</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code></p></td>
<td><p>Whether or not this snapshot is an ancestor of the current snapshot.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="metadata-log-entries-table">
<h5 id="metadata-log-entries-table"><code class="docutils literal notranslate"><span class="pre">$metadata_log_entries</span></code> table<a class="headerlink" href="iceberg.html#metadata-log-entries-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$metadata_log_entries</span></code> table provides a view of metadata log entries
of the Iceberg table.</p>
<p>You can retrieve the information about the metadata log entries of the Iceberg
table <code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$metadata_log_entries"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>             timestamp                 |                                                              file                                                          | latest_snapshot_id  | latest_schema_id | latest_sequence_number
---------------------------------------+----------------------------------------------------------------------------------------------------------------------------+---------------------+------------------+------------------------
 2024-01-16 15:55:31.172 Europe/Vienna | hdfs://hadoop-master:9000/user/hive/warehouse/test_table/metadata/00000-39174715-be2a-48fa-9949-35413b8b736e.metadata.json | 1221802298419195590 |                0 |                      1
 2024-01-16 17:19:56.118 Europe/Vienna | hdfs://hadoop-master:9000/user/hive/warehouse/test_table/metadata/00001-e40178c9-271f-4a96-ad29-eed5e7aef9b0.metadata.json | 7124386610209126943 |                0 |                      2
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id8">
<caption><span class="caption-text">Metadata log entries columns</span><a class="headerlink" href="iceberg.html#id8" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p>The time when the metadata was created.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">file</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The location of the metadata file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">latest_snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier of the latest snapshot when the metadata was updated.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">latest_schema_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>The identifier of the latest schema when the metadata was updated.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">latest_sequence_number</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The data sequence number of the metadata file.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="snapshots-table">
<h5 id="snapshots-table"><code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> table<a class="headerlink" href="iceberg.html#snapshots-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> table provides a detailed view of snapshots of the Iceberg
table. A snapshot consists of one or more file manifests, and the complete table
contents are represented by the union of all the data files in those manifests.</p>
<p>You can retrieve the information about the snapshots of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$snapshots"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> committed_at                      | snapshot_id          | parent_id            | operation          |  manifest_list                                                                                                                           |   summary
----------------------------------+----------------------+----------------------+--------------------+------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
2022-01-10 08:11:20 Europe/Vienna | 8667764846443717831  |  &lt;null&gt;              |  append            |   hdfs://hadoop-master:9000/user/hive/warehouse/test_table/metadata/snap-8667764846443717831-1-100cf97e-6d56-446e-8961-afdaded63bc4.avro | {changed-partition-count=0, total-equality-deletes=0, total-position-deletes=0, total-delete-files=0, total-files-size=0, total-records=0, total-data-files=0}
2022-01-10 08:11:34 Europe/Vienna | 7860805980949777961  | 8667764846443717831  |  append            |   hdfs://hadoop-master:9000/user/hive/warehouse/test_table/metadata/snap-7860805980949777961-1-faa19903-1455-4bb8-855a-61a1bbafbaa7.avro | {changed-partition-count=1, added-data-files=1, total-equality-deletes=0, added-records=1, total-position-deletes=0, added-files-size=442, total-delete-files=0, total-files-size=442, total-records=1, total-data-files=1}
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id9">
<caption><span class="caption-text">Snapshots columns</span><a class="headerlink" href="iceberg.html#id9" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 20%"/>
<col style="width: 30%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">committed_at</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP(3)</span> <span class="pre">WITH</span> <span class="pre">TIME</span> <span class="pre">ZONE</span></code></p></td>
<td><p>The time when the snapshot became active.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier for the snapshot.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">parent_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier for the parent snapshot.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">operation</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The type of operation performed on the Iceberg table. The supported
operation types in Iceberg are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">append</span></code> when new data is appended.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">replace</span></code> when files are removed and replaced without changing the
data in the table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">overwrite</span></code> when new data is added to overwrite existing data.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">delete</span></code> when data is deleted from the table and no new data is added.</p></li>
</ul>
</td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">manifest_list</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The list of Avro manifest files containing the detailed information about
the snapshot changes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">summary</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(VARCHAR,</span> <span class="pre">VARCHAR)</span></code></p></td>
<td><p>A summary of the changes made from the previous snapshot to the current
snapshot.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="manifests-and-all-manifests-tables">
<h5 id="manifests-and-all-manifests-tables"><code class="docutils literal notranslate"><span class="pre">$manifests</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_manifests</span></code> tables<a class="headerlink" href="iceberg.html#manifests-and-all-manifests-tables" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$manifests</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_manifests</span></code> tables provide a detailed overview of the
manifests corresponding to the snapshots performed in the log of the Iceberg
table. The <code class="docutils literal notranslate"><span class="pre">$manifests</span></code> table contains data for the current snapshot. The
<code class="docutils literal notranslate"><span class="pre">$all_manifests</span></code> table contains data for all snapshots.</p>
<p>You can retrieve the information about the manifests of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$manifests"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> path                                                                                                           | length          | partition_spec_id    | added_snapshot_id     | added_data_files_count  | added_rows_count | existing_data_files_count   | existing_rows_count | deleted_data_files_count    | deleted_rows_count | partition_summaries
----------------------------------------------------------------------------------------------------------------+-----------------+----------------------+-----------------------+-------------------------+------------------+-----------------------------+---------------------+-----------------------------+--------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------
 hdfs://hadoop-master:9000/user/hive/warehouse/test_table/metadata/faa19903-1455-4bb8-855a-61a1bbafbaa7-m0.avro |  6277           |   0                  | 7860805980949777961   | 1                       | 100              | 0                           | 0                   | 0                           | 0                  | {{contains_null=false, contains_nan= false, lower_bound=1, upper_bound=1},{contains_null=false, contains_nan= false, lower_bound=2021-01-12, upper_bound=2021-01-12}}
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id10">
<caption><span class="caption-text">Manifests columns</span><a class="headerlink" href="iceberg.html#id10" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">path</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The manifest file location.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">length</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The manifest file length.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">partition_spec_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>The identifier for the partition specification used to write the manifest
file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">added_snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The identifier of the snapshot during which this manifest entry has been
added.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">added_data_files_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>The number of data files with status <code class="docutils literal notranslate"><span class="pre">ADDED</span></code> in the manifest file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">added_rows_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The total number of rows in all data files with status <code class="docutils literal notranslate"><span class="pre">ADDED</span></code> in the
manifest file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">existing_data_files_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>The number of data files with status <code class="docutils literal notranslate"><span class="pre">EXISTING</span></code> in the manifest file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">existing_rows_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The total number of rows in all data files with status <code class="docutils literal notranslate"><span class="pre">EXISTING</span></code> in the
manifest file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">deleted_data_files_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>The number of data files with status <code class="docutils literal notranslate"><span class="pre">DELETED</span></code> in the manifest file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">deleted_rows_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The total number of rows in all data files with status <code class="docutils literal notranslate"><span class="pre">DELETED</span></code> in the
manifest file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">partition_summaries</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ARRAY(row(contains_null</span> <span class="pre">BOOLEAN,</span> <span class="pre">contains_nan</span> <span class="pre">BOOLEAN,</span> <span class="pre">lower_bound</span> <span class="pre">VARCHAR,</span> <span class="pre">upper_bound</span> <span class="pre">VARCHAR))</span></code></p></td>
<td><p>Partition range metadata.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="partitions-table">
<h5 id="partitions-table"><code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table<a class="headerlink" href="iceberg.html#partitions-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$partitions</span></code> table provides a detailed overview of the partitions of the
Iceberg table.</p>
<p>You can retrieve the information about the partitions of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$partitions"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> partition             | record_count  | file_count    | total_size    |  data
-----------------------+---------------+---------------+---------------+------------------------------------------------------
{c1=1, c2=2021-01-12}  |  2            | 2             |  884          | {c3={min=1.0, max=2.0, null_count=0, nan_count=NULL}}
{c1=1, c2=2021-01-13}  |  1            | 1             |  442          | {c3={min=1.0, max=1.0, null_count=0, nan_count=NULL}}
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id11">
<caption><span class="caption-text">Partitions columns</span><a class="headerlink" href="iceberg.html#id11" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 20%"/>
<col style="width: 30%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">partition</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
<td><p>A row that contains the mapping of the partition column names to the
partition column values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">record_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The number of records in the partition.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">file_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The number of files mapped in the partition.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">total_size</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The size of all the files in the partition.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">data</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...</span> <span class="pre">ROW</span> <span class="pre">(min</span> <span class="pre">...,</span> <span class="pre">max</span> <span class="pre">...</span> <span class="pre">,</span> <span class="pre">null_count</span> <span class="pre">BIGINT,</span> <span class="pre">nan_count</span> <span class="pre">BIGINT))</span></code></p></td>
<td><p>Partition range metadata.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="files-table">
<h5 id="files-table"><code class="docutils literal notranslate"><span class="pre">$files</span></code> table<a class="headerlink" href="iceberg.html#files-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$files</span></code> table provides a detailed overview of the data files in current
snapshot of the  Iceberg table.</p>
<p>To retrieve the information about the data files of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code>, use the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$files"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> content  | file_path                                                                                                                     | record_count    | file_format   | file_size_in_bytes   |  column_sizes        |  value_counts     |  null_value_counts | nan_value_counts  | lower_bounds                |  upper_bounds               |  key_metadata  | split_offsets  |  equality_ids
----------+-------------------------------------------------------------------------------------------------------------------------------+-----------------+---------------+----------------------+----------------------+-------------------+--------------------+-------------------+-----------------------------+-----------------------------+----------------+----------------+---------------
 0        | hdfs://hadoop-master:9000/user/hive/warehouse/test_table/data/c1=3/c2=2021-01-14/af9872b2-40f3-428f-9c87-186d2750d84e.parquet |  1              |  PARQUET      |  442                 | {1=40, 2=40, 3=44}   |  {1=1, 2=1, 3=1}  |  {1=0, 2=0, 3=0}   | &lt;null&gt;            |  {1=3, 2=2021-01-14, 3=1.3} |  {1=3, 2=2021-01-14, 3=1.3} |  &lt;null&gt;        | &lt;null&gt;         |   &lt;null&gt;
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id12">
<caption><span class="caption-text">Files columns</span><a class="headerlink" href="iceberg.html#id12" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 30%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">content</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>Type of content stored in the file. The supported content types in Iceberg
are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">DATA(0)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POSITION_DELETES(1)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">EQUALITY_DELETES(2)</span></code></p></li>
</ul>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">file_path</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The data file location.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">file_format</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The format of the data file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">spec_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>Spec ID used to track the file containing a row.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">partition</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW(...)</span></code></p></td>
<td><p>A row that contains the mapping of the partition column names to the
partition column values.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">record_count</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The number of entries contained in the data file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">file_size_in_bytes</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The data file size</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">column_sizes</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding size in the
file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">value_counts</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding count of entries
in the file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">null_value_counts</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding count of <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
values in the file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">nan_value_counts</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding count of
non-numerical values in the file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">lower_bounds</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding lower bound in
the file.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">upper_bounds</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">map(INTEGER,</span> <span class="pre">BIGINT)</span></code></p></td>
<td><p>Mapping between the Iceberg column ID and its corresponding upper bound in
the file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">key_metadata</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARBINARY</span></code></p></td>
<td><p>Metadata about the encryption key used to encrypt this file, if applicable.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">split_offsets</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">array(BIGINT)</span></code></p></td>
<td><p>List of recommended split locations.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">equality_ids</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">array(INTEGER)</span></code></p></td>
<td><p>The set of field IDs used for equality comparison in equality delete files.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">sort_order_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>ID representing sort order for this file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">readable_metrics</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p>File metrics in human-readable form.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="entries-and-all-entries-tables">
<h5 id="entries-and-all-entries-tables"><code class="docutils literal notranslate"><span class="pre">$entries</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_entries</span></code> tables<a class="headerlink" href="iceberg.html#entries-and-all-entries-tables" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$entries</span></code> and <code class="docutils literal notranslate"><span class="pre">$all_entries</span></code> tables provide the table’s manifest entries
for both data and delete files. The <code class="docutils literal notranslate"><span class="pre">$entries</span></code> table contains data for the
current snapshot. The <code class="docutils literal notranslate"><span class="pre">$all_entries</span></code> table contains data for all snapshots.</p>
<p>To retrieve the information about the entries of the Iceberg table
<code class="docutils literal notranslate"><span class="pre">test_table</span></code>, use the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$entries"</span><span class="p">;</span>
</pre></div>
</div>
<p>Abbreviated sample output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> status |   snapshot_id  | sequence_number | file_sequence_number |              data_file              |                readable_metrics                |
--------+----------------+-----------------+----------------------+-------------------------------------+------------------------------------------------+
      2 | 57897183625154 |              0  |                   0  | {"content":0,...,"sort_order_id":0} | {"c1":{"column_size":103,...,"upper_bound":3}} |
</pre></div>
</div>
<p>The metadata tables include the following columns:</p>
<table id="id13">
<caption><span class="caption-text">Files columns</span><a class="headerlink" href="iceberg.html#id13" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 30%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">status</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>Numeric status indication to track additions and deletions. Deletes are
informational only and not used in scans:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">EXISTING(0)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ADDED(1)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DELETED(2)</span></code></p></li>
</ul>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The snapshot ID of the reference.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">sequence_number</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>Data sequence number of the file. Inherited when null and status is 1.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">file_sequence_number</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>File sequence number indicating when the file was added. Inherited when null
and status is 1.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">data_file</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ROW</span></code></p></td>
<td><p>Metadata including file path, file format, file size and other information.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">readable_metrics</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">JSON</span></code></p></td>
<td><p>JSON-formatted file metrics such as column size, value count, and others.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="refs-table">
<h5 id="refs-table"><code class="docutils literal notranslate"><span class="pre">$refs</span></code> table<a class="headerlink" href="iceberg.html#refs-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">$refs</span></code> table provides information about Iceberg references including
branches and tags.</p>
<p>You can retrieve the references of the Iceberg table <code class="docutils literal notranslate"><span class="pre">test_table</span></code> by using the
following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="ss">"test_table$refs"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>name            | type   | snapshot_id | max_reference_age_in_ms | min_snapshots_to_keep | max_snapshot_age_in_ms |
----------------+--------+-------------+-------------------------+-----------------------+------------------------+
example_tag     | TAG    | 10000000000 | 10000                   | null                  | null                   |
example_branch  | BRANCH | 20000000000 | 20000                   | 2                     | 30000                  |
</pre></div>
</div>
<p>The output of the query has the following columns:</p>
<table id="id14">
<caption><span class="caption-text">Refs columns</span><a class="headerlink" href="iceberg.html#id14" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 20%"/>
<col style="width: 30%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">name</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>Name of the reference.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">type</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>Type of the reference, either <code class="docutils literal notranslate"><span class="pre">BRANCH</span></code> or <code class="docutils literal notranslate"><span class="pre">TAG</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">snapshot_id</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The snapshot ID of the reference.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">max_reference_age_in_ms</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>The maximum age of the reference before it could be expired.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">min_snapshots_to_keep</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p>For branch only, the minimum number of snapshots to keep in a branch.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">max_snapshot_age_in_ms</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>For branch only, the max snapshot age allowed in a branch. Older snapshots
in the branch will be expired.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="metadata-columns">
<span id="iceberg-metadata-columns"></span><h4 id="metadata-columns">Metadata columns<a class="headerlink" href="iceberg.html#metadata-columns" title="Link to this heading">#</a></h4>
<p>In addition to the defined columns, the Iceberg connector automatically exposes
path metadata as a hidden column in each table:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$partition</span></code>: Partition path for this row</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$path</span></code>: Full file system path name of the file for this row</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$file_modified_time</span></code>: Timestamp of the last modification of the file for
this row</p></li>
</ul>
<p>You can use these columns in your SQL statements like any other column. This can
be selected directly, or used in conditional statements. For example, you can
inspect the file path for each record:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="p">,</span><span class="w"> </span><span class="ss">"$partition"</span><span class="p">,</span><span class="w"> </span><span class="ss">"$path"</span><span class="p">,</span><span class="w"> </span><span class="ss">"$file_modified_time"</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">page_views</span><span class="p">;</span>
</pre></div>
</div>
<p>Retrieve all records that belong to a specific file using <code class="docutils literal notranslate"><span class="pre">"$path"</span></code> filter:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">page_views</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$path"</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'/usr/iceberg/table/web.page_views/data/file_01.parquet'</span>
</pre></div>
</div>
<p>Retrieve all records that belong to a specific file using
<code class="docutils literal notranslate"><span class="pre">"$file_modified_time"</span></code> filter:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">web</span><span class="p">.</span><span class="n">page_views</span>
<span class="k">WHERE</span><span class="w"> </span><span class="ss">"$file_modified_time"</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">CAST</span><span class="p">(</span><span class="s1">'2022-07-01 01:02:03.456 UTC'</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="system-tables">
<span id="iceberg-system-tables"></span><h4 id="system-tables">System tables<a class="headerlink" href="iceberg.html#system-tables" title="Link to this heading">#</a></h4>
<p>The connector exposes metadata tables in the system schema.</p>
<section id="iceberg-tables-table">
<h5 id="iceberg-tables-table"><code class="docutils literal notranslate"><span class="pre">iceberg_tables</span></code> table<a class="headerlink" href="iceberg.html#iceberg-tables-table" title="Link to this heading">#</a></h5>
<p>The <code class="docutils literal notranslate"><span class="pre">iceberg_tables</span></code> table allows listing only Iceberg tables from a given catalog.
The <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">TABLES</span></code> statement, <code class="docutils literal notranslate"><span class="pre">information_schema.tables</span></code>, and <code class="docutils literal notranslate"><span class="pre">jdbc.tables</span></code> will all
return all tables that exist in the underlying metastore, even if the table cannot
be handled in any way by the iceberg connector. This can happen if other connectors
like Hive or Delta Lake, use the same metastore, catalog, and schema to store its tables.</p>
<p>The table includes following columns:</p>
<table id="id15">
<caption><span class="caption-text">iceberg_tables columns</span><a class="headerlink" href="iceberg.html#id15" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 30%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">table_schema</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The name of the schema the table is in.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">table_name</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td><p>The name of the table.</p></td>
</tr>
</tbody>
</table>
<p>The following query lists Iceberg tables from all schemas in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">iceberg_tables</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> table_schema | table_name  |
--------------+-------------+
 tpcds        | store_sales |
 tpch         | nation      |
 tpch         | region      |
 tpch         | orders      |       
</pre></div>
</div>
</section>
</section>
<section id="drop-table">
<h4 id="drop-table">DROP TABLE<a class="headerlink" href="iceberg.html#drop-table" title="Link to this heading">#</a></h4>
<p>The Iceberg connector supports dropping a table by using the
<a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a> syntax. When the command succeeds, both the data of the
Iceberg table and also the information related to the table in the metastore
service are removed. Dropping tables that have their data/metadata stored in a
different location than the table’s corresponding base directory on the object
store is not supported.</p>
</section>
<section id="comment">
<span id="iceberg-comment"></span><h4 id="comment">COMMENT<a class="headerlink" href="iceberg.html#comment" title="Link to this heading">#</a></h4>
<p>The Iceberg connector supports setting comments on the following objects:</p>
<ul class="simple">
<li><p>tables</p></li>
<li><p>views</p></li>
<li><p>table columns</p></li>
<li><p>materialized view columns</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">COMMENT</span></code> option is supported on both the table and the table columns for
the <a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a> operation.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">COMMENT</span></code> option is supported for adding table columns through the
<a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a> operations.</p>
<p>The connector supports the command <a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a> for setting
comments on existing entities.</p>
</section>
<section id="partitioned-tables">
<span id="iceberg-tables"></span><h4 id="partitioned-tables">Partitioned tables<a class="headerlink" href="iceberg.html#partitioned-tables" title="Link to this heading">#</a></h4>
<p>Iceberg supports partitioning by specifying transforms over the table columns. A
partition is created for each unique tuple value produced by the transforms.
Identity transforms are simply the column name. Other transforms are:</p>
<table id="id16">
<caption><span class="caption-text">Iceberg column transforms</span><a class="headerlink" href="iceberg.html#id16" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Transform</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">year(ts)</span></code></p></td>
<td><p>A partition is created for each year. The partition value is the integer
difference in years between <code class="docutils literal notranslate"><span class="pre">ts</span></code> and January 1 1970.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">month(ts)</span></code></p></td>
<td><p>A partition is created for each month of each year. The partition value is
the integer difference in months between <code class="docutils literal notranslate"><span class="pre">ts</span></code> and January 1 1970.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">day(ts)</span></code></p></td>
<td><p>A partition is created for each day of each year. The partition value is the
integer difference in days between <code class="docutils literal notranslate"><span class="pre">ts</span></code> and January 1 1970.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">hour(ts)</span></code></p></td>
<td><p>A partition is created hour of each day. The partition value is a timestamp
with the minutes and seconds set to zero.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bucket(x,</span> <span class="pre">nbuckets)</span></code></p></td>
<td><p>The data is hashed into the specified number of buckets. The partition value
is an integer hash of <code class="docutils literal notranslate"><span class="pre">x</span></code>, with a value between 0 and <code class="docutils literal notranslate"><span class="pre">nbuckets</span> <span class="pre">-</span> <span class="pre">1</span></code>
inclusive.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">truncate(s,</span> <span class="pre">nchars)</span></code></p></td>
<td><p>The partition value is the first <code class="docutils literal notranslate"><span class="pre">nchars</span></code> characters of <code class="docutils literal notranslate"><span class="pre">s</span></code>.</p></td>
</tr>
</tbody>
</table>
<p>In this example, the table is partitioned by the month of <code class="docutils literal notranslate"><span class="pre">order_date</span></code>, a hash
of <code class="docutils literal notranslate"><span class="pre">account_number</span></code> (with 10 buckets), and <code class="docutils literal notranslate"><span class="pre">country</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">order_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">order_date</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">account_number</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">country</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'month(order_date)'</span><span class="p">,</span><span class="w"> </span><span class="s1">'bucket(account_number, 10)'</span><span class="p">,</span><span class="w"> </span><span class="s1">'country'</span><span class="p">]);</span>
</pre></div>
</div>
</section>
<section id="sorted-tables">
<span id="iceberg-sorted-files"></span><h4 id="sorted-tables">Sorted tables<a class="headerlink" href="iceberg.html#sorted-tables" title="Link to this heading">#</a></h4>
<p>The connector supports sorted files as a performance improvement. Data is sorted
during writes within each file based on the specified array of one or more
columns.</p>
<p>Sorting is particularly beneficial when the sorted columns show a high
cardinality and are used as a filter for selective reads.</p>
<p>The sort order is configured with the <code class="docutils literal notranslate"><span class="pre">sorted_by</span></code> table property. Specify an
array of one or more columns to use for sorting when creating the table. The
following example configures the <code class="docutils literal notranslate"><span class="pre">order_date</span></code> column of the <code class="docutils literal notranslate"><span class="pre">orders</span></code> table
in the <code class="docutils literal notranslate"><span class="pre">customers</span></code> schema in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">customers</span><span class="p">.</span><span class="n">orders</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">order_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">order_date</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">account_number</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">country</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">sorted_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'order_date'</span><span class="p">]);</span>
</pre></div>
</div>
<p>You can explicitly configure sort directions or null ordering in the following way:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">customers</span><span class="p">.</span><span class="n">orders</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">order_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">order_date</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">account_number</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">country</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">sorted_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'order_date DESC NULLS FIRST'</span><span class="p">,</span><span class="w"> </span><span class="s1">'order_id ASC NULLS LAST'</span><span class="p">]);</span>
</pre></div>
</div>
<p>Sorting can be combined with partitioning on the same column. For example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">customers</span><span class="p">.</span><span class="n">orders</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">order_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">order_date</span><span class="w"> </span><span class="nb">DATE</span><span class="p">,</span>
<span class="w">    </span><span class="n">account_number</span><span class="w"> </span><span class="nb">BIGINT</span><span class="p">,</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">country</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">)</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'month(order_date)'</span><span class="p">],</span>
<span class="w">    </span><span class="n">sorted_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'order_date'</span><span class="p">]</span>
<span class="p">);</span>
</pre></div>
</div>
<p>You can disable sorted writing with the session property
<code class="docutils literal notranslate"><span class="pre">sorted_writing_enabled</span></code> set to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</section>
<section id="using-snapshots">
<h4 id="using-snapshots">Using snapshots<a class="headerlink" href="iceberg.html#using-snapshots" title="Link to this heading">#</a></h4>
<p>Iceberg supports a snapshot model of data, where table snapshots are
identified by a snapshot ID.</p>
<p>The connector provides a system table exposing snapshot information for every
Iceberg table. Snapshots are identified by <code class="docutils literal notranslate"><span class="pre">BIGINT</span></code> snapshot IDs. For example,
you can find the snapshot IDs for the <code class="docutils literal notranslate"><span class="pre">customer_orders</span></code> table by running the
following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">snapshot_id</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="ss">"customer_orders$snapshots"</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">committed_at</span><span class="w"> </span><span class="k">DESC</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="replace-tables">
<span id="iceberg-create-or-replace"></span><h4 id="replace-tables">Replace tables<a class="headerlink" href="iceberg.html#replace-tables" title="Link to this heading">#</a></h4>
<p>The connector supports replacing an existing table, as an atomic operation.
Atomic table replacement creates a new snapshot with the new table definition as
part of the <a class="reference internal" href="iceberg.html#iceberg-history-table">table history</a>.</p>
<p>To replace a table, use <a class="reference internal" href="../sql/create-table.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">OR</span> <span class="pre">REPLACE</span> <span class="pre">TABLE</span></code></span></a> or
<a class="reference internal" href="../sql/create-table-as.html"><span class="doc std std-doc"><code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">OR</span> <span class="pre">REPLACE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></span></a>.</p>
<p>Earlier snapshots of the table can be queried through <a class="reference internal" href="iceberg.html#iceberg-time-travel"><span class="std std-ref">Time travel queries</span></a>.</p>
<p>In the following example, a table <code class="docutils literal notranslate"><span class="pre">example_table</span></code> can be replaced by a
completely new definition and data from the source table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example_table</span>
<span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">sorted_by</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'a'</span><span class="p">])</span>
<span class="k">AS</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">another_table</span><span class="p">;</span>
</pre></div>
</div>
<section id="time-travel-queries">
<span id="iceberg-time-travel"></span><h5 id="time-travel-queries">Time travel queries<a class="headerlink" href="iceberg.html#time-travel-queries" title="Link to this heading">#</a></h5>
<p>The connector offers the ability to query historical data. This allows you to
query the table as it was when a previous snapshot of the table was taken, even
if the data has since been modified or deleted.</p>
<p>The historical data of the table can be retrieved by specifying the snapshot
identifier corresponding to the version of the table to be retrieved:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">VERSION</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="mi">8954597067493422955</span><span class="p">;</span>
</pre></div>
</div>
<p>A different approach of retrieving historical data is to specify a point in time
in the past, such as a day or week ago. The latest snapshot of the table taken
before or at the specified timestamp in the query is internally used for
providing the previous state of the table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 09:59:29.803 Europe/Vienna'</span><span class="p">;</span>
</pre></div>
</div>
<p>The connector allows to create a new snapshot through Iceberg’s <a class="reference internal" href="iceberg.html#iceberg-create-or-replace"><span class="std std-ref">replace table</span></a>.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">AS</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 09:59:29.803 Europe/Vienna'</span><span class="p">;</span>
</pre></div>
</div>
<p>You can use a date to specify a point a time in the past for using a snapshot of a table in a query.
Assuming that the session time zone is <code class="docutils literal notranslate"><span class="pre">Europe/Vienna</span></code> the following queries are equivalent:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="nb">DATE</span><span class="w"> </span><span class="s1">'2022-03-23'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 00:00:00'</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="s1">'2022-03-23 00:00:00.000 Europe/Vienna'</span><span class="p">;</span>
</pre></div>
</div>
<p>Iceberg supports named references of snapshots via branches and tags.
Time travel can be performed to branches and tags in the table.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">VERSION</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="s1">'historical-tag'</span><span class="p">;</span>

<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">FOR</span><span class="w"> </span><span class="k">VERSION</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="s1">'test-branch'</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="rolling-back-to-a-previous-snapshot">
<h5 id="rolling-back-to-a-previous-snapshot">Rolling back to a previous snapshot<a class="headerlink" href="iceberg.html#rolling-back-to-a-previous-snapshot" title="Link to this heading">#</a></h5>
<p>Use the <code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> metadata table to determine the latest snapshot ID of the
table like in the following query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">snapshot_id</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">testdb</span><span class="p">.</span><span class="ss">"customer_orders$snapshots"</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">committed_at</span><span class="w"> </span><span class="k">DESC</span><span class="w"> </span><span class="k">LIMIT</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
</pre></div>
</div>
<p>The table procedure <code class="docutils literal notranslate"><span class="pre">rollback_to_snapshot</span></code> allows the caller to roll back the
state of the table to a previous snapshot id:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">testdb</span><span class="p">.</span><span class="n">customer_orders</span><span class="w"> </span><span class="k">EXECUTE</span><span class="w"> </span><span class="n">rollback_to_snapshot</span><span class="p">(</span><span class="mi">8954597067493422955</span><span class="p">);</span>
</pre></div>
</div>
</section>
</section>
<section id="not-null-column-constraint">
<h4 id="not-null-column-constraint"><code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> column constraint<a class="headerlink" href="iceberg.html#not-null-column-constraint" title="Link to this heading">#</a></h4>
<p>The Iceberg connector supports setting <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> constraints on the table
columns.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> constraint can be set on the columns, while creating tables by
using the <a class="reference internal" href="../sql/create-table.html"><span class="doc">CREATE TABLE</span></a> syntax:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">example_table</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="k">year</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">age</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">address</span><span class="w"> </span><span class="nb">VARCHAR</span>
<span class="p">);</span>
</pre></div>
</div>
<p>When trying to insert/update data in the table, the query fails if trying to set
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> value on a column having the <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">NULL</span></code> constraint.</p>
</section>
</section>
<section id="materialized-views">
<span id="iceberg-materialized-views"></span><h3 id="materialized-views">Materialized views<a class="headerlink" href="iceberg.html#materialized-views" title="Link to this heading">#</a></h3>
<p>The Iceberg connector supports <a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">Materialized view management</span></a>. In the
underlying system, each materialized view consists of a view definition and an
Iceberg storage table. The storage table name is stored as a materialized view
property. The data is stored in that storage table.</p>
<p>You can use the <a class="reference internal" href="iceberg.html#iceberg-table-properties"><span class="std std-ref">Table properties</span></a> to control the created storage
table and therefore the layout and performance. For example, you can use the
following clause with <a class="reference internal" href="../sql/create-materialized-view.html"><span class="doc">CREATE MATERIALIZED VIEW</span></a> to use the ORC format
for the data files and partition the storage per day using the column
<code class="docutils literal notranslate"><span class="pre">event_date</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="n">format</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'ORC'</span><span class="p">,</span><span class="w"> </span><span class="n">partitioning</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'event_date'</span><span class="p">]</span><span class="w"> </span><span class="p">)</span>
</pre></div>
</div>
<p>By default, the storage table is created in the same schema as the materialized
view definition. The <code class="docutils literal notranslate"><span class="pre">storage_schema</span></code> materialized view property can be
used to specify the schema where the storage table is created.</p>
<p>Creating a materialized view does not automatically populate it with data. You
must run <a class="reference internal" href="../sql/refresh-materialized-view.html"><span class="doc">REFRESH MATERIALIZED VIEW</span></a> to populate data in the
materialized view.</p>
<p>Updating the data in the materialized view can be achieved using the <code class="docutils literal notranslate"><span class="pre">REFRESH</span> <span class="pre">MATERIALIZED</span> <span class="pre">VIEW</span></code> command. This operation may perform either an incremental or
a full refresh, depending on the complexity of the materialized view definition
and the snapshot history of the source tables. For a full refresh, the operation
deletes the data from the storage table, and inserts the data that is the result
of executing the materialized view query into the existing table. For
incremental refresh, the existing data is not deleted from the storage table and
only the delta records are processed from the source tables and appended into
the storage table as needed. In both cases, data is replaced or appended
atomically, so users can continue to query the materialized view while it is
being refreshed. Refreshing a materialized view also stores the snapshot-ids of
all Iceberg tables that are part of the materialized view’s query in the
materialized view metadata. When the materialized view is queried, the
snapshot-ids are used to check if the data in the storage table is up to date.</p>
<p>Materialized views that use non-Iceberg tables in the query show the <a class="reference internal" href="../sql/create-materialized-view.html#mv-grace-period"><span class="std std-ref">default
behavior around grace periods</span></a>. If all tables are Iceberg
tables, the connector can determine if the data has not changed and continue to
use the data from the storage tables, even after the grace period expired.</p>
<p>Dropping a materialized view with <a class="reference internal" href="../sql/drop-materialized-view.html"><span class="doc">DROP MATERIALIZED VIEW</span></a> removes
the definition and the storage table.</p>
</section>
<section id="table-functions">
<h3 id="table-functions">Table functions<a class="headerlink" href="iceberg.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector supports the table functions described in the following sections.</p>
<section id="table-changes">
<h4 id="table-changes">table_changes<a class="headerlink" href="iceberg.html#table-changes" title="Link to this heading">#</a></h4>
<p>Allows reading row-level changes between two versions of an Iceberg table.
The following query shows an example of displaying the changes of the <code class="docutils literal notranslate"><span class="pre">t1</span></code>
table in the <code class="docutils literal notranslate"><span class="pre">default</span></code> schema in the current catalog.
All changes between the start and end snapshots are returned.</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="k">system</span><span class="p">.</span><span class="n">table_changes</span><span class="p">(</span>
<span class="w">      </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'default'</span><span class="p">,</span>
<span class="w">      </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'t1'</span><span class="p">,</span>
<span class="w">      </span><span class="n">start_snapshot_id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">6541165659943306573</span><span class="p">,</span>
<span class="w">      </span><span class="n">end_snapshot_id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">6745790645714043599</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<p>The function takes the following required parameters:</p>
<ul class="simple">
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">schema_name</span></code></dt><dd><p>Name of the schema for which the function is called.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">table_name</span></code></dt><dd><p>Name of the table for which the function is called.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">start_snapshot_id</span></code></dt><dd><p>The identifier of the exclusive starting snapshot.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">end_snapshot_id</span></code></dt><dd><p>The identifier of the inclusive end snapshot.</p>
</dd>
</dl>
</li>
</ul>
<p>Use the <code class="docutils literal notranslate"><span class="pre">$snapshots</span></code> metadata table to determine the snapshot IDs of the
table.</p>
<p>The function returns the columns present in the table, and the following values
for each change:</p>
<ul class="simple">
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_change_type</span></code></dt><dd><p>The type of change that occurred. Possible values are <code class="docutils literal notranslate"><span class="pre">insert</span></code> and <code class="docutils literal notranslate"><span class="pre">delete</span></code>.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_change_version_id</span></code></dt><dd><p>The identifier of the snapshot in which the change occurred.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_change_timestamp</span></code></dt><dd><p>Timestamp when the snapshot became active.</p>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt><code class="docutils literal notranslate"><span class="pre">_change_ordinal</span></code></dt><dd><p>Order number of the change, useful for sorting the results.</p>
</dd>
</dl>
</li>
</ul>
<p><strong>Example:</strong></p>
<p>Create a table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span><span class="w"> </span><span class="p">(</span><span class="n">page_url</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="k">domain</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span><span class="w"> </span><span class="n">views</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">);</span>
</pre></div>
</div>
<p>Insert some data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span>
<span class="w">    </span><span class="k">VALUES</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url2'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain2'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url3'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="n">pages</span>
<span class="w">    </span><span class="k">VALUES</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url4'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain1'</span><span class="p">,</span><span class="w"> </span><span class="mi">400</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url5'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain2'</span><span class="p">,</span><span class="w"> </span><span class="mi">500</span><span class="p">),</span>
<span class="w">        </span><span class="p">(</span><span class="s1">'url6'</span><span class="p">,</span><span class="w"> </span><span class="s1">'domain3'</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span>
</pre></div>
</div>
<p>Retrieve the snapshot identifiers of the changes performed on the table:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">    </span><span class="n">snapshot_id</span><span class="p">,</span>
<span class="w">    </span><span class="n">parent_id</span><span class="p">,</span>
<span class="w">    </span><span class="k">operation</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">test_schema</span><span class="p">.</span><span class="ss">"pages$snapshots"</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>     snapshot_id     |      parent_id      | operation
---------------------+---------------------+-----------
 2009020668682716382 |                NULL | append
 2135434251890923160 | 2009020668682716382 | append
 3108755571950643966 | 2135434251890923160 | append
(3 rows)
</pre></div>
</div>
<p>Select the changes performed in the previously-mentioned <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> statements:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">    </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">    </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">            </span><span class="k">system</span><span class="p">.</span><span class="n">table_changes</span><span class="p">(</span>
<span class="w">                    </span><span class="k">schema_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'test_schema'</span><span class="p">,</span>
<span class="w">                    </span><span class="k">table_name</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'pages'</span><span class="p">,</span>
<span class="w">                    </span><span class="n">start_snapshot_id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">2009020668682716382</span><span class="p">,</span>
<span class="w">                    </span><span class="n">end_snapshot_id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="mi">3108755571950643966</span>
<span class="w">            </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">_change_ordinal</span><span class="w"> </span><span class="k">ASC</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span> page_url | domain  | views | _change_type | _change_version_id  |      _change_timestamp      | _change_ordinal
----------+---------+-------+--------------+---------------------+-----------------------------+-----------------
 url1     | domain1 |     1 | insert       | 2135434251890923160 | 2024-04-04 21:24:26.105 UTC |               0
 url2     | domain2 |     2 | insert       | 2135434251890923160 | 2024-04-04 21:24:26.105 UTC |               0
 url3     | domain1 |     3 | insert       | 2135434251890923160 | 2024-04-04 21:24:26.105 UTC |               0
 url4     | domain1 |   400 | insert       | 3108755571950643966 | 2024-04-04 21:24:28.318 UTC |               1
 url5     | domain2 |   500 | insert       | 3108755571950643966 | 2024-04-04 21:24:28.318 UTC |               1
 url6     | domain3 |     2 | insert       | 3108755571950643966 | 2024-04-04 21:24:28.318 UTC |               1
(6 rows)
</pre></div>
</div>
</section>
</section>
</section>
<section id="performance">
<h2 id="performance">Performance<a class="headerlink" href="iceberg.html#performance" title="Link to this heading">#</a></h2>
<p>The connector includes a number of performance improvements, detailed in the
following sections.</p>
<section id="table-statistics">
<span id="iceberg-table-statistics"></span><h3 id="table-statistics">Table statistics<a class="headerlink" href="iceberg.html#table-statistics" title="Link to this heading">#</a></h3>
<p>The Iceberg connector can collect column statistics using <a class="reference internal" href="../sql/analyze.html"><span class="doc">ANALYZE</span></a>
statement. This can be disabled using <code class="docutils literal notranslate"><span class="pre">iceberg.extended-statistics.enabled</span></code>
catalog configuration property, or the corresponding
<code class="docutils literal notranslate"><span class="pre">extended_statistics_enabled</span></code> session property.</p>
<section id="updating-table-statistics">
<span id="iceberg-analyze"></span><h4 id="updating-table-statistics">Updating table statistics<a class="headerlink" href="iceberg.html#updating-table-statistics" title="Link to this heading">#</a></h4>
<p>If your queries are complex and include joining large data sets, running
<a class="reference internal" href="../sql/analyze.html"><span class="doc">ANALYZE</span></a> on tables may improve query performance by collecting
statistical information about the data:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ANALYZE</span><span class="w"> </span><span class="k">table_name</span><span class="p">;</span>
</pre></div>
</div>
<p>This query collects statistics for all columns.</p>
<p>On wide tables, collecting statistics for all columns can be expensive. It is
also typically unnecessary - statistics are only useful on specific columns,
like join keys, predicates, or grouping keys. You can specify a subset of
columns to analyzed with the optional <code class="docutils literal notranslate"><span class="pre">columns</span></code> property:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ANALYZE</span><span class="w"> </span><span class="k">table_name</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="p">(</span><span class="n">columns</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">ARRAY</span><span class="p">[</span><span class="s1">'col_1'</span><span class="p">,</span><span class="w"> </span><span class="s1">'col_2'</span><span class="p">]);</span>
</pre></div>
</div>
<p>This query collects statistics for columns <code class="docutils literal notranslate"><span class="pre">col_1</span></code> and <code class="docutils literal notranslate"><span class="pre">col_2</span></code>.</p>
<p>Note that if statistics were previously collected for all columns, they must be
dropped using the <a class="reference internal" href="iceberg.html#drop-extended-stats"><span class="std std-ref">drop_extended_stats</span></a> command
before re-analyzing.</p>
</section>
</section>
<section id="table-redirection">
<span id="iceberg-table-redirection"></span><h3 id="table-redirection">Table redirection<a class="headerlink" href="iceberg.html#table-redirection" title="Link to this heading">#</a></h3>
<p>Trino offers the possibility to transparently redirect operations on an existing
table to the appropriate catalog based on the format of the table and catalog configuration.</p>
<p>In the context of connectors which depend on a metastore service
(for example, <a class="reference internal" href="hive.html"><span class="doc">Hive connector</span></a>, <a class="reference internal" href="iceberg.html#"><span class="doc">Iceberg connector</span></a> and <a class="reference internal" href="delta-lake.html"><span class="doc">Delta Lake connector</span></a>),
the metastore (Hive metastore service, <a class="reference external" href="https://aws.amazon.com/glue/">AWS Glue Data Catalog</a>)
can be used to accustom tables with different table formats.
Therefore, a metastore database can hold a variety of tables with different table formats.</p>
<p>As a concrete example, let’s use the following
simple scenario which makes use of table redirection:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>

<span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example_table</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                               Query Plan
-------------------------------------------------------------------------
Fragment 0 [SOURCE]
     ...
     Output[columnNames = [...]]
     │   ...
     └─ TableScan[table = another_catalog:example_schema:example_table]
            ...
</pre></div>
</div>
<p>The output of the <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code> statement points out the actual
catalog which is handling the <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> query over the table <code class="docutils literal notranslate"><span class="pre">example_table</span></code>.</p>
<p>The table redirection functionality works also when using
fully qualified names for the tables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">.</span><span class="n">example_table</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                               Query Plan
-------------------------------------------------------------------------
Fragment 0 [SOURCE]
     ...
     Output[columnNames = [...]]
     │   ...
     └─ TableScan[table = another_catalog:example_schema:example_table]
            ...
</pre></div>
</div>
<p>Trino offers table redirection support for the following operations:</p>
<ul class="simple">
<li><p>Table read operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/select.html"><span class="doc">SELECT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/describe.html"><span class="doc">DESCRIBE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-stats.html"><span class="doc">SHOW STATS</span></a></p></li>
<li><p><a class="reference internal" href="../sql/show-create-table.html"><span class="doc">SHOW CREATE TABLE</span></a></p></li>
</ul>
</li>
<li><p>Table write operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/insert.html"><span class="doc">INSERT</span></a></p></li>
<li><p><a class="reference internal" href="../sql/update.html"><span class="doc">UPDATE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/merge.html"><span class="doc">MERGE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/delete.html"><span class="doc">DELETE</span></a></p></li>
</ul>
</li>
<li><p>Table management operations</p>
<ul>
<li><p><a class="reference internal" href="../sql/alter-table.html"><span class="doc">ALTER TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/drop-table.html"><span class="doc">DROP TABLE</span></a></p></li>
<li><p><a class="reference internal" href="../sql/comment.html"><span class="doc">COMMENT</span></a></p></li>
</ul>
</li>
</ul>
<p>Trino does not offer view redirection support.</p>
<p>The connector supports redirection from Iceberg tables to Hive tables with the
<code class="docutils literal notranslate"><span class="pre">iceberg.hive-catalog-name</span></code> catalog configuration property.</p>
</section>
<section id="file-system-cache">
<h3 id="file-system-cache">File system cache<a class="headerlink" href="iceberg.html#file-system-cache" title="Link to this heading">#</a></h3>
<p>The connector supports configuring and using <a class="reference internal" href="../object-storage/file-system-cache.html"><span class="doc std std-doc">file system
caching</span></a>.</p>
</section>
<section id="iceberg-metadata-caching">
<h3 id="iceberg-metadata-caching">Iceberg metadata caching<a class="headerlink" href="iceberg.html#iceberg-metadata-caching" title="Link to this heading">#</a></h3>
<p>The Iceberg connector supports caching metadata in coordinator memory. This
metadata caching is enabled by default and can be disabled by setting the
<code class="docutils literal notranslate"><span class="pre">iceberg.metadata-cache.enabled</span></code> configuration property to <code class="docutils literal notranslate"><span class="pre">false</span></code>.
When <code class="docutils literal notranslate"><span class="pre">fs.cache.enabled</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>, metadata is cached on local disks
using the <a class="reference internal" href="../object-storage/file-system-cache.html"><span class="doc std std-doc">file system caching
implementation</span></a>. If <code class="docutils literal notranslate"><span class="pre">fs.cache.enabled</span></code> is
enabled, metadata caching in coordinator memory is deactivated.</p>
<p>Additionally, you can use the following catalog configuration properties:</p>
<table id="id17">
<caption><span class="caption-text">Memory metadata caching configuration properties :widths: 25, 75</span><a class="headerlink" href="iceberg.html#id17" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.memory-cache.ttl</span></code></p></td>
<td><p>The maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> to keep files in the cache prior
to eviction. The minimum value of <code class="docutils literal notranslate"><span class="pre">0s</span></code> means that caching is effectively
turned off. Defaults to <code class="docutils literal notranslate"><span class="pre">1h</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">fs.memory-cache.max-size</span></code></p></td>
<td><p>The maximum total <a class="reference internal" href="../admin/properties.html#prop-type-data-size"><span class="std std-ref">data size</span></a> of the cache. When
raising this value, keep in mind that the coordinator memory is used.
Defaults to <code class="docutils literal notranslate"><span class="pre">200MB</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">fs.memory-cache.max-content-length</span></code></p></td>
<td><p>The maximum file size that can be cached. Defaults to <code class="docutils literal notranslate"><span class="pre">15MB</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="hudi.html" title="Hudi connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Hudi connector </span>
              </div>
            </a>
          
          
            <a href="ignite.html" title="Ignite connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Ignite connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>