<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>System connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="system.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Thrift connector" href="thrift.html" />
    <link rel="prev" title="SQL Server connector" href="sqlserver.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="system.html#connector/system" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> System connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="druid.html" class="md-nav__link">Druid</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> System </label>
    
      <a href="system.html#" class="md-nav__link md-nav__link--active">System</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="system.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="system.html#using-the-system-connector" class="md-nav__link">Using the System connector</a>
        </li>
        <li class="md-nav__item"><a href="system.html#system-connector-tables" class="md-nav__link">System connector tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="system.html#metadata-catalogs" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.catalogs</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-schema-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.schema_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-table-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.table_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-materialized-views" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_views</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-materialized-view-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_view_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-table-comments" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.table_comments</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-optimizer-rule-stats" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.optimizer_rule_stats</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-queries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.queries</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-tasks" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.tasks</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-transactions" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.transactions</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="system.html#system-connector-procedures" class="md-nav__link">System connector procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="system.html#runtime.kill_query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.kill_query()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="system.html#type-mapping" class="md-nav__link">Type mapping</a>
        </li>
        <li class="md-nav__item"><a href="system.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="system.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="system.html#using-the-system-connector" class="md-nav__link">Using the System connector</a>
        </li>
        <li class="md-nav__item"><a href="system.html#system-connector-tables" class="md-nav__link">System connector tables</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="system.html#metadata-catalogs" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.catalogs</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-schema-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.schema_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-table-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.table_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-materialized-views" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_views</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-materialized-view-properties" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_view_properties</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#metadata-table-comments" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">metadata.table_comments</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-nodes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.nodes</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-optimizer-rule-stats" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.optimizer_rule_stats</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-queries" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.queries</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-tasks" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.tasks</span></code></a>
        </li>
        <li class="md-nav__item"><a href="system.html#runtime-transactions" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.transactions</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="system.html#system-connector-procedures" class="md-nav__link">System connector procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="system.html#runtime.kill_query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">runtime.kill_query()</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="system.html#type-mapping" class="md-nav__link">Type mapping</a>
        </li>
        <li class="md-nav__item"><a href="system.html#sql-support" class="md-nav__link">SQL support</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="system-connector">
<h1 id="connector-system--page-root">System connector<a class="headerlink" href="system.html#connector-system--page-root" title="Link to this heading">#</a></h1>
<p>The System connector provides information and metrics about the currently
running Trino cluster. It makes this available via normal SQL queries.</p>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="system.html#configuration" title="Link to this heading">#</a></h2>
<p>The System connector doesn’t need to be configured: it is automatically
available via a catalog named <code class="docutils literal notranslate"><span class="pre">system</span></code>.</p>
</section>
<section id="using-the-system-connector">
<h2 id="using-the-system-connector">Using the System connector<a class="headerlink" href="system.html#using-the-system-connector" title="Link to this heading">#</a></h2>
<p>List the available system schemas:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span><span class="w"> </span><span class="n">SCHEMAS</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">system</span><span class="p">;</span>
</pre></div>
</div>
<p>List the tables in one of the schemas:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span><span class="w"> </span><span class="n">TABLES</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">runtime</span><span class="p">;</span>
</pre></div>
</div>
<p>Query one of the tables:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">runtime</span><span class="p">.</span><span class="n">nodes</span><span class="p">;</span>
</pre></div>
</div>
<p>Kill a running query:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">runtime</span><span class="p">.</span><span class="n">kill_query</span><span class="p">(</span><span class="n">query_id</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'20151207_215727_00146_tx3nr'</span><span class="p">,</span><span class="w"> </span><span class="n">message</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'Using too many resources'</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="system-connector-tables">
<h2 id="system-connector-tables">System connector tables<a class="headerlink" href="system.html#system-connector-tables" title="Link to this heading">#</a></h2>
<section id="metadata-catalogs">
<h3 id="metadata-catalogs"><code class="docutils literal notranslate"><span class="pre">metadata.catalogs</span></code><a class="headerlink" href="system.html#metadata-catalogs" title="Link to this heading">#</a></h3>
<p>The catalogs table contains the list of available catalogs.</p>
</section>
<section id="metadata-schema-properties">
<h3 id="metadata-schema-properties"><code class="docutils literal notranslate"><span class="pre">metadata.schema_properties</span></code><a class="headerlink" href="system.html#metadata-schema-properties" title="Link to this heading">#</a></h3>
<p>The schema properties table contains the list of available properties
that can be set when creating a new schema.</p>
</section>
<section id="metadata-table-properties">
<h3 id="metadata-table-properties"><code class="docutils literal notranslate"><span class="pre">metadata.table_properties</span></code><a class="headerlink" href="system.html#metadata-table-properties" title="Link to this heading">#</a></h3>
<p>The table properties table contains the list of available properties
that can be set when creating a new table.</p>
</section>
<section id="metadata-materialized-views">
<span id="system-metadata-materialized-views"></span><h3 id="metadata-materialized-views"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_views</span></code><a class="headerlink" href="system.html#metadata-materialized-views" title="Link to this heading">#</a></h3>
<p>The materialized views table contains the following information about all
<a class="reference internal" href="../language/sql-support.html#sql-materialized-view-management"><span class="std std-ref">materialized views</span></a>:</p>
<table id="id1">
<caption><span class="caption-text">Metadata for materialized views</span><a class="headerlink" href="system.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 30%"/>
<col style="width: 70%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">catalog_name</span></code></p></td>
<td><p>Name of the catalog containing the materialized view.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">schema_name</span></code></p></td>
<td><p>Name of the schema in <code class="docutils literal notranslate"><span class="pre">catalog_name</span></code> containing the materialized view.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">name</span></code></p></td>
<td><p>Name of the materialized view.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">storage_catalog</span></code></p></td>
<td><p>Name of the catalog used for the storage table backing the materialized
view.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">storage_schema</span></code></p></td>
<td><p>Name of the schema in <code class="docutils literal notranslate"><span class="pre">storage_catalog</span></code> used for the storage table backing
the materialized view.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">storage_table</span></code></p></td>
<td><p>Name of the storage table backing the materialized view.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">freshness</span></code></p></td>
<td><p>Freshness of data in the storage table. Queries on the materialized view
access the storage table if not <code class="docutils literal notranslate"><span class="pre">STALE</span></code>, otherwise the <code class="docutils literal notranslate"><span class="pre">definition</span></code> is used
to access the underlying data in the source tables.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">last_fresh_time</span></code></p></td>
<td><p>Date and time of the last refresh of the materialized view.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">comment</span></code></p></td>
<td><p>User supplied text about the materialized view.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">definition</span></code></p></td>
<td><p>SQL query that defines the data provided by the materialized view.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="metadata-materialized-view-properties">
<h3 id="metadata-materialized-view-properties"><code class="docutils literal notranslate"><span class="pre">metadata.materialized_view_properties</span></code><a class="headerlink" href="system.html#metadata-materialized-view-properties" title="Link to this heading">#</a></h3>
<p>The materialized view properties table contains the list of available properties
that can be set when creating a new materialized view.</p>
</section>
<section id="metadata-table-comments">
<h3 id="metadata-table-comments"><code class="docutils literal notranslate"><span class="pre">metadata.table_comments</span></code><a class="headerlink" href="system.html#metadata-table-comments" title="Link to this heading">#</a></h3>
<p>The table comments table contains the list of table comment.</p>
</section>
<section id="runtime-nodes">
<h3 id="runtime-nodes"><code class="docutils literal notranslate"><span class="pre">runtime.nodes</span></code><a class="headerlink" href="system.html#runtime-nodes" title="Link to this heading">#</a></h3>
<p>The nodes table contains the list of visible nodes in the Trino
cluster along with their status.</p>
</section>
<section id="runtime-optimizer-rule-stats">
<span id="optimizer-rule-stats"></span><h3 id="runtime-optimizer-rule-stats"><code class="docutils literal notranslate"><span class="pre">runtime.optimizer_rule_stats</span></code><a class="headerlink" href="system.html#runtime-optimizer-rule-stats" title="Link to this heading">#</a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">optimizer_rule_stats</span></code> table contains the statistics for optimizer
rule invocations during the query planning phase. The statistics are
aggregated over all queries since the server start-up. The table contains
information about invocation frequency, failure rates and performance for
optimizer rules. For example, you can look at the multiplication of columns
<code class="docutils literal notranslate"><span class="pre">invocations</span></code> and <code class="docutils literal notranslate"><span class="pre">average_time</span></code> to get an idea about which rules
generally impact query planning times the most.</p>
</section>
<section id="runtime-queries">
<h3 id="runtime-queries"><code class="docutils literal notranslate"><span class="pre">runtime.queries</span></code><a class="headerlink" href="system.html#runtime-queries" title="Link to this heading">#</a></h3>
<p>The queries table contains information about currently and recently
running queries on the Trino cluster. From this table you can find out
the original query SQL text, the identity of the user who ran the query,
and performance information about the query, including how long the query
was queued and analyzed.</p>
</section>
<section id="runtime-tasks">
<h3 id="runtime-tasks"><code class="docutils literal notranslate"><span class="pre">runtime.tasks</span></code><a class="headerlink" href="system.html#runtime-tasks" title="Link to this heading">#</a></h3>
<p>The tasks table contains information about the tasks involved in a
Trino query, including where they were executed, and how many rows
and bytes each task processed.</p>
</section>
<section id="runtime-transactions">
<h3 id="runtime-transactions"><code class="docutils literal notranslate"><span class="pre">runtime.transactions</span></code><a class="headerlink" href="system.html#runtime-transactions" title="Link to this heading">#</a></h3>
<p>The transactions table contains the list of currently open transactions
and related metadata. This includes information such as the create time,
idle time, initialization parameters, and accessed catalogs.</p>
</section>
</section>
<section id="system-connector-procedures">
<h2 id="system-connector-procedures">System connector procedures<a class="headerlink" href="system.html#system-connector-procedures" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="runtime.kill_query">
<span class="sig-prename descclassname"><span class="pre">runtime.</span></span><span class="sig-name descname"><span class="pre">kill_query</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="system.html#runtime.kill_query" title="Link to this definition">#</a></dt>
<dd><p>Kill the query identified by <code class="docutils literal notranslate"><span class="pre">query_id</span></code>. The query failure message includes the
specified <code class="docutils literal notranslate"><span class="pre">message</span></code>. <code class="docutils literal notranslate"><span class="pre">message</span></code> is optional.</p>
</dd></dl>
</section>
<section id="type-mapping">
<span id="system-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="system.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Trino supports all data types used within the System schemas so no mapping
is required.</p>
</section>
<section id="sql-support">
<span id="system-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="system.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and
<a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read operation</span></a> statements to access Trino system
data and metadata.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="sqlserver.html" title="SQL Server connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> SQL Server connector </span>
              </div>
            </a>
          
          
            <a href="thrift.html" title="Thrift connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Thrift connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>