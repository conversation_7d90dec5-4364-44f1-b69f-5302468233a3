<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Druid connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="druid.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="DuckDB connector" href="duckdb.html" />
    <link rel="prev" title="Delta Lake connector" href="delta-lake.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="druid.html#connector/druid" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Druid connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="bigquery.html" class="md-nav__link">BigQuery</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="blackhole.html" class="md-nav__link">Black Hole</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cassandra.html" class="md-nav__link">Cassandra</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="clickhouse.html" class="md-nav__link">ClickHouse</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="delta-lake.html" class="md-nav__link">Delta Lake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Druid </label>
    
      <a href="druid.html#" class="md-nav__link md-nav__link--active">Druid</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="druid.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#data-source-authentication" class="md-nav__link">Data source authentication</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#appending-query-metadata" class="md-nav__link">Appending query metadata</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#domain-compaction-threshold" class="md-nav__link">Domain compaction threshold</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#case-insensitive-matching" class="md-nav__link">Case insensitive matching</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#druid-type-to-trino-type-mapping" class="md-nav__link">Druid type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="druid.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="duckdb.html" class="md-nav__link">DuckDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="elasticsearch.html" class="md-nav__link">Elasticsearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="exasol.html" class="md-nav__link">Exasol</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="faker.html" class="md-nav__link">Faker</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="googlesheets.html" class="md-nav__link">Google Sheets</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hive.html" class="md-nav__link">Hive</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="hudi.html" class="md-nav__link">Hudi</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="iceberg.html" class="md-nav__link">Iceberg</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ignite.html" class="md-nav__link">Ignite</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jmx.html" class="md-nav__link">JMX</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kafka.html" class="md-nav__link">Kafka</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="loki.html" class="md-nav__link">Loki</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mariadb.html" class="md-nav__link">MariaDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="memory.html" class="md-nav__link">Memory</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mongodb.html" class="md-nav__link">MongoDB</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="mysql.html" class="md-nav__link">MySQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opensearch.html" class="md-nav__link">OpenSearch</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oracle.html" class="md-nav__link">Oracle</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pinot.html" class="md-nav__link">Pinot</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="postgresql.html" class="md-nav__link">PostgreSQL</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="prometheus.html" class="md-nav__link">Prometheus</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redis.html" class="md-nav__link">Redis</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="redshift.html" class="md-nav__link">Redshift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="singlestore.html" class="md-nav__link">SingleStore</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="snowflake.html" class="md-nav__link">Snowflake</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sqlserver.html" class="md-nav__link">SQL Server</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system.html" class="md-nav__link">System</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="thrift.html" class="md-nav__link">Thrift</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpcds.html" class="md-nav__link">TPC-DS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tpch.html" class="md-nav__link">TPC-H</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="vertica.html" class="md-nav__link">Vertica</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="removed.html" class="md-nav__link">404 - Connector removed</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="druid.html#requirements" class="md-nav__link">Requirements</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#configuration" class="md-nav__link">Configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#data-source-authentication" class="md-nav__link">Data source authentication</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#general-configuration-properties" class="md-nav__link">General configuration properties</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#appending-query-metadata" class="md-nav__link">Appending query metadata</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#domain-compaction-threshold" class="md-nav__link">Domain compaction threshold</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#case-insensitive-matching" class="md-nav__link">Case insensitive matching</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#type-mapping" class="md-nav__link">Type mapping</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#druid-type-to-trino-type-mapping" class="md-nav__link">Druid type to Trino type mapping</a>
        </li>
        <li class="md-nav__item"><a href="druid.html#type-mapping-configuration-properties" class="md-nav__link">Type mapping configuration properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#sql-support" class="md-nav__link">SQL support</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#procedures" class="md-nav__link">Procedures</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#system-flush-metadata-cache" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="druid.html#system-execute-query" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="druid.html#table-functions" class="md-nav__link">Table functions</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="druid.html#query-varchar-table" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code></a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="druid-connector">
<h1 id="connector-druid--page-root">Druid connector<a class="headerlink" href="druid.html#connector-druid--page-root" title="Link to this heading">#</a></h1>
<img class="connector-logo" src="../_static/img/druid.png"/><p>The Druid connector allows querying an <a class="reference external" href="https://druid.apache.org/">Apache Druid</a>
database from Trino.</p>
<section id="requirements">
<h2 id="requirements">Requirements<a class="headerlink" href="druid.html#requirements" title="Link to this heading">#</a></h2>
<p>To connect to Druid, you need:</p>
<ul class="simple">
<li><p>Druid version 0.18.0 or higher.</p></li>
<li><p>Network access from the Trino coordinator and workers to your Druid broker.
Port 8082 is the default port.</p></li>
</ul>
</section>
<section id="configuration">
<h2 id="configuration">Configuration<a class="headerlink" href="druid.html#configuration" title="Link to this heading">#</a></h2>
<p>Create a catalog properties file that specifies the Druid connector by setting
the <code class="docutils literal notranslate"><span class="pre">connector.name</span></code> to <code class="docutils literal notranslate"><span class="pre">druid</span></code> and configuring the <code class="docutils literal notranslate"><span class="pre">connection-url</span></code> with
the JDBC string to connect to Druid.</p>
<p>For example, to access a database as <code class="docutils literal notranslate"><span class="pre">example</span></code>, create the file
<code class="docutils literal notranslate"><span class="pre">etc/catalog/example.properties</span></code>. Replace <code class="docutils literal notranslate"><span class="pre">BROKER:8082</span></code> with the correct
host and port of your Druid broker.</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connector.name</span><span class="o">=</span><span class="s">druid</span>
<span class="na">connection-url</span><span class="o">=</span><span class="s">***************************************************************/</span>
</pre></div>
</div>
<p>You can add authentication details to connect to a Druid deployment that is
secured by basic authentication by updating the URL and adding credentials:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">connection-url</span><span class="o">=</span><span class="s">*************************************************************************************</span>
<span class="na">connection-user</span><span class="o">=</span><span class="s">root</span>
<span class="na">connection-password</span><span class="o">=</span><span class="s">secret</span>
</pre></div>
</div>
<p>Now you can access your Druid database in Trino with the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog
name from the properties file.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">connection-user</span></code> and <code class="docutils literal notranslate"><span class="pre">connection-password</span></code> are typically required and
determine the user credentials for the connection, often a service user. You can
use <a class="reference internal" href="../security/secrets.html"><span class="doc">secrets</span></a> to avoid actual values in the catalog
properties files.</p>
<section id="data-source-authentication">
<h3 id="data-source-authentication">Data source authentication<a class="headerlink" href="druid.html#data-source-authentication" title="Link to this heading">#</a></h3>
<p>The connector can provide credentials for the data source connection
in multiple ways:</p>
<ul class="simple">
<li><p>inline, in the connector configuration file</p></li>
<li><p>in a separate properties file</p></li>
<li><p>in a key store file</p></li>
<li><p>as extra credentials set when connecting to Trino</p></li>
</ul>
<p>You can use <a class="reference internal" href="../security/secrets.html"><span class="doc">secrets</span></a> to avoid storing sensitive
values in the catalog properties files.</p>
<p>The following table describes configuration properties
for connection credentials:</p>
<table>
<colgroup>
<col style="width: 40%"/>
<col style="width: 60%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">credential-provider.type</span></code></p></td>
<td><p>Type of the credential provider. Must be one of <code class="docutils literal notranslate"><span class="pre">INLINE</span></code>, <code class="docutils literal notranslate"><span class="pre">FILE</span></code>, or
<code class="docutils literal notranslate"><span class="pre">KEYSTORE</span></code>; defaults to <code class="docutils literal notranslate"><span class="pre">INLINE</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">connection-user</span></code></p></td>
<td><p>Connection user name.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">connection-password</span></code></p></td>
<td><p>Connection password.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">user-credential-name</span></code></p></td>
<td><p>Name of the extra credentials property, whose value to use as the user
name. See <code class="docutils literal notranslate"><span class="pre">extraCredentials</span></code> in <a class="reference internal" href="../client/jdbc.html#jdbc-parameter-reference"><span class="std std-ref">Parameter
reference</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">password-credential-name</span></code></p></td>
<td><p>Name of the extra credentials property, whose value to use as the
password.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">connection-credential-file</span></code></p></td>
<td><p>Location of the properties file where credentials are present. It must
contain the <code class="docutils literal notranslate"><span class="pre">connection-user</span></code> and <code class="docutils literal notranslate"><span class="pre">connection-password</span></code> properties.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-file-path</span></code></p></td>
<td><p>The location of the Java Keystore file, from which to read credentials.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-type</span></code></p></td>
<td><p>File format of the keystore file, for example <code class="docutils literal notranslate"><span class="pre">JKS</span></code> or <code class="docutils literal notranslate"><span class="pre">PEM</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-password</span></code></p></td>
<td><p>Password for the key store.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-user-credential-name</span></code></p></td>
<td><p>Name of the key store entity to use as the user name.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-user-credential-password</span></code></p></td>
<td><p>Password for the user name key store entity.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-password-credential-name</span></code></p></td>
<td><p>Name of the key store entity to use as the password.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">keystore-password-credential-password</span></code></p></td>
<td><p>Password for the password key store entity.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="general-configuration-properties">
<h3 id="general-configuration-properties">General configuration properties<a class="headerlink" href="druid.html#general-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following table describes general catalog configuration properties for the
connector:</p>
<table>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching</span></code></p></td>
<td><p>Support case insensitive schema and table names. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which case insensitive schema and table
names are cached. Defaults to <code class="docutils literal notranslate"><span class="pre">1m</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file</span></code></p></td>
<td><p>Path to a name mapping configuration file in JSON format that allows
Trino to disambiguate between schemas and tables with similar names in
different cases. Defaults to <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file.refresh-period</span></code></p></td>
<td><p>Frequency with which Trino checks the name matching configuration file
for changes. The <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration value</span></a> defaults to <code class="docutils literal notranslate"><span class="pre">0s</span></code>
(refresh disabled).</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which metadata, including table and
column statistics, is cached. Defaults to <code class="docutils literal notranslate"><span class="pre">0s</span></code> (caching disabled).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-missing</span></code></p></td>
<td><p>Cache the fact that metadata, including table and column statistics, is
not available. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.schemas.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which schema metadata is cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.tables.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which table metadata is cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.statistics.cache-ttl</span></code></p></td>
<td><p><a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">Duration</span></a> for which tables statistics are cached.
Defaults to the value of <code class="docutils literal notranslate"><span class="pre">metadata.cache-ttl</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">metadata.cache-maximum-size</span></code></p></td>
<td><p>Maximum number of objects stored in the metadata cache. Defaults to <code class="docutils literal notranslate"><span class="pre">10000</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">write.batch-size</span></code></p></td>
<td><p>Maximum number of statements in a batched execution. Do not change
this setting from the default. Non-default values may negatively
impact performance. Defaults to <code class="docutils literal notranslate"><span class="pre">1000</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">dynamic-filtering.enabled</span></code></p></td>
<td><p>Push down dynamic filters into JDBC queries. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">dynamic-filtering.wait-timeout</span></code></p></td>
<td><p>Maximum <a class="reference internal" href="../admin/properties.html#prop-type-duration"><span class="std std-ref">duration</span></a> for which Trino waits for dynamic
filters to be collected from the build side of joins before starting a
JDBC query. Using a large timeout can potentially result in more detailed
dynamic filters. However, it can also increase latency for some queries.
Defaults to <code class="docutils literal notranslate"><span class="pre">20s</span></code>.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="appending-query-metadata">
<h3 id="appending-query-metadata">Appending query metadata<a class="headerlink" href="druid.html#appending-query-metadata" title="Link to this heading">#</a></h3>
<p>The optional parameter <code class="docutils literal notranslate"><span class="pre">query.comment-format</span></code> allows you to configure a SQL
comment that is sent to the datasource with each query. The format of this
comment can contain any characters and the following metadata:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$QUERY_ID</span></code>: The identifier of the query.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$USER</span></code>: The name of the user who submits the query to Trino.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$SOURCE</span></code>: The identifier of the client tool used to submit the query, for
example <code class="docutils literal notranslate"><span class="pre">trino-cli</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$TRACE_TOKEN</span></code>: The trace token configured with the client tool.</p></li>
</ul>
<p>The comment can provide more context about the query. This additional
information is available in the logs of the datasource. To include environment
variables from the Trino cluster with the comment , use the
<code class="docutils literal notranslate"><span class="pre">${ENV:VARIABLE-NAME}</span></code> syntax.</p>
<p>The following example sets a simple comment that identifies each query sent by
Trino:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>query.comment-format=Query sent by Trino.
</pre></div>
</div>
<p>With this configuration, a query such as <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span> <span class="pre">FROM</span> <span class="pre">example_table;</span></code> is
sent to the datasource with the comment appended:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT * FROM example_table; /*Query sent by Trino.*/
</pre></div>
</div>
<p>The following example improves on the preceding example by using metadata:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>query.comment-format=Query $QUERY_ID sent by user $USER from Trino.
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">Jane</span></code> sent the query with the query identifier
<code class="docutils literal notranslate"><span class="pre">20230622_180528_00000_bkizg</span></code>, the following comment string is sent to the
datasource:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SELECT * FROM example_table; /*Query 20230622_180528_00000_bkizg sent by user Jane from Trino.*/
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Certain JDBC driver settings and logging configurations might cause the
comment to be removed.</p>
</div>
</section>
<section id="domain-compaction-threshold">
<h3 id="domain-compaction-threshold">Domain compaction threshold<a class="headerlink" href="druid.html#domain-compaction-threshold" title="Link to this heading">#</a></h3>
<p>Pushing down a large list of predicates to the data source can compromise
performance. Trino compacts large predicates into a simpler range predicate
by default to ensure a balance between performance and predicate pushdown.
If necessary, the threshold for this compaction can be increased to improve
performance when the data source is capable of taking advantage of large
predicates. Increasing this threshold may improve pushdown of large
<a class="reference internal" href="../admin/dynamic-filtering.html"><span class="doc">dynamic filters</span></a>.
The <code class="docutils literal notranslate"><span class="pre">domain-compaction-threshold</span></code> catalog configuration property or the
<code class="docutils literal notranslate"><span class="pre">domain_compaction_threshold</span></code> <a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">catalog session property</span></a> can be used to adjust the default value of
<code class="docutils literal notranslate"><span class="pre">256</span></code> for this threshold.</p>
</section>
<section id="case-insensitive-matching">
<h3 id="case-insensitive-matching">Case insensitive matching<a class="headerlink" href="druid.html#case-insensitive-matching" title="Link to this heading">#</a></h3>
<p>When <code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching</span></code> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>, Trino
is able to query non-lowercase schemas and tables by maintaining a mapping of
the lowercase name to the actual name in the remote system. However, if two
schemas and/or tables have names that differ only in case (such as “customers”
and “Customers”) then Trino fails to query them due to ambiguity.</p>
<p>In these cases, use the <code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file</span></code> catalog
configuration property to specify a configuration file that maps these remote
schemas and tables to their respective Trino schemas and tables. Additionally,
the JSON file must include both the <code class="docutils literal notranslate"><span class="pre">schemas</span></code> and <code class="docutils literal notranslate"><span class="pre">tables</span></code> properties, even if
only as empty arrays.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"case_insensitive_1"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cASEsENSITIVEnAME"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"case_insensitive_2"</span>
<span class="w">    </span><span class="p">}],</span>
<span class="w">  </span><span class="nt">"tables"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"remoteTable"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tablex"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table_1"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"remoteSchema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"CaseSensitiveName"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"remoteTable"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TABLEX"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"mapping"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table_2"</span>
<span class="w">    </span><span class="p">}]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Queries against one of the tables or schemes defined in the <code class="docutils literal notranslate"><span class="pre">mapping</span></code>
attributes are run against the corresponding remote entity. For example, a query
against tables in the <code class="docutils literal notranslate"><span class="pre">case_insensitive_1</span></code> schema is forwarded to the
CaseSensitiveName schema and a query against <code class="docutils literal notranslate"><span class="pre">case_insensitive_2</span></code> is forwarded
to the <code class="docutils literal notranslate"><span class="pre">cASEsENSITIVEnAME</span></code> schema.</p>
<p>At the table mapping level, a query on <code class="docutils literal notranslate"><span class="pre">case_insensitive_1.table_1</span></code> as
configured above is forwarded to <code class="docutils literal notranslate"><span class="pre">CaseSensitiveName.tablex</span></code>, and a query on
<code class="docutils literal notranslate"><span class="pre">case_insensitive_1.table_2</span></code> is forwarded to <code class="docutils literal notranslate"><span class="pre">CaseSensitiveName.TABLEX</span></code>.</p>
<p>By default, when a change is made to the mapping configuration file, Trino must
be restarted to load the changes. Optionally, you can set the
<code class="docutils literal notranslate"><span class="pre">case-insensitive-name-matching.config-file.refresh-period</span></code> to have Trino
refresh the properties without requiring a restart:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">case-insensitive-name-matching.config-file.refresh-period</span><span class="o">=</span><span class="s">30s</span>
</pre></div>
</div>
</section>
</section>
<section id="type-mapping">
<span id="druid-type-mapping"></span><h2 id="type-mapping">Type mapping<a class="headerlink" href="druid.html#type-mapping" title="Link to this heading">#</a></h2>
<p>Because Trino and Druid each support types that the other does not, this
connector <a class="reference internal" href="../language/types.html#type-mapping-overview"><span class="std std-ref">modifies some types</span></a> when reading data.</p>
<section id="druid-type-to-trino-type-mapping">
<h3 id="druid-type-to-trino-type-mapping">Druid type to Trino type mapping<a class="headerlink" href="druid.html#druid-type-to-trino-type-mapping" title="Link to this heading">#</a></h3>
<p>The connector maps Druid types to the corresponding Trino types according to the
following table:</p>
<table id="id1">
<caption><span class="caption-text">Druid type to Trino type mapping</span><a class="headerlink" href="druid.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 27%"/>
<col style="width: 27%"/>
<col style="width: 45%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Druid type</p></th>
<th class="head"><p>Trino type</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">STRING</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FLOAT</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LONG</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BIGINT</span></code></p></td>
<td><p>Except for the special <code class="docutils literal notranslate"><span class="pre">_time</span></code> column, which is mapped to <code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TIMESTAMP</span></code></p></td>
<td><p>Only applicable to the special <code class="docutils literal notranslate"><span class="pre">_time</span></code> column.</p></td>
</tr>
</tbody>
</table>
<p>No other data types are supported.</p>
<p>Druid does not have a real <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value for any data type. By
default, Druid treats <code class="docutils literal notranslate"><span class="pre">NULL</span></code> as the default value for a data type. For
example, <code class="docutils literal notranslate"><span class="pre">LONG</span></code> would be <code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> would be <code class="docutils literal notranslate"><span class="pre">0.0</span></code>, <code class="docutils literal notranslate"><span class="pre">STRING</span></code> would
be an empty string <code class="docutils literal notranslate"><span class="pre">''</span></code>, and so forth.</p>
</section>
<section id="type-mapping-configuration-properties">
<h3 id="type-mapping-configuration-properties">Type mapping configuration properties<a class="headerlink" href="druid.html#type-mapping-configuration-properties" title="Link to this heading">#</a></h3>
<p>The following properties can be used to configure how data types from the
connected data source are mapped to Trino data types and how the metadata is
cached in Trino.</p>
<table>
<colgroup>
<col style="width: 30%"/>
<col style="width: 40%"/>
<col style="width: 30%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Default value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">unsupported-type-handling</span></code></p></td>
<td><p>Configure how unsupported column data types are handled:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code>, column is not accessible.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CONVERT_TO_VARCHAR</span></code>, column is converted to unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>.</p></li>
</ul>
<p>The respective catalog session property is <code class="docutils literal notranslate"><span class="pre">unsupported_type_handling</span></code>.</p>
</td>
<td><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">jdbc-types-mapped-to-varchar</span></code></p></td>
<td><p>Allow forced mapping of comma separated lists of data types to convert to
unbounded <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="sql-support">
<span id="druid-sql-support"></span><h2 id="sql-support">SQL support<a class="headerlink" href="druid.html#sql-support" title="Link to this heading">#</a></h2>
<p>The connector provides read access to data and metadata in the Druid database.
In addition to the <a class="reference internal" href="../language/sql-support.html#sql-globally-available"><span class="std std-ref">globally available</span></a> and <a class="reference internal" href="../language/sql-support.html#sql-read-operations"><span class="std std-ref">read
operation</span></a> statements, the connector supports the following
features:</p>
<ul class="simple">
<li><p><a class="reference internal" href="druid.html#druid-procedures"><span class="std std-ref">Procedures</span></a></p></li>
<li><p><a class="reference internal" href="druid.html#druid-table-functions"><span class="std std-ref">Table functions</span></a></p></li>
</ul>
<section id="procedures">
<span id="druid-procedures"></span><h3 id="procedures">Procedures<a class="headerlink" href="druid.html#procedures" title="Link to this heading">#</a></h3>
<section id="system-flush-metadata-cache">
<h4 id="system-flush-metadata-cache"><code class="docutils literal notranslate"><span class="pre">system.flush_metadata_cache()</span></code><a class="headerlink" href="druid.html#system-flush-metadata-cache" title="Link to this heading">#</a></h4>
<p>Flush JDBC metadata caches. For example, the following system call
flushes the metadata caches for all schemas in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="n">flush_metadata_cache</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="system-execute-query">
<h4 id="system-execute-query"><code class="docutils literal notranslate"><span class="pre">system.execute('query')</span></code><a class="headerlink" href="druid.html#system-execute-query" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">execute</span></code> procedure allows you to execute a query in the underlying data
source directly. The query must use supported syntax of the connected data
source. Use the procedure to access features which are not available in Trino
or to execute queries that return no result set and therefore can not be used
with the <code class="docutils literal notranslate"><span class="pre">query</span></code> or <code class="docutils literal notranslate"><span class="pre">raw_query</span></code> pass-through table function. Typical use cases
are statements that create or alter objects, and require native feature such
as constraints, default values, automatic identifier creation, or indexes.
Queries can also invoke statements that insert, update, or delete data, and do
not return any data as a result.</p>
<p>The query text is not parsed by Trino, only passed through, and therefore only
subject to any security or access control of the underlying data source.</p>
<p>The following example sets the current database to the <code class="docutils literal notranslate"><span class="pre">example_schema</span></code> of the
<code class="docutils literal notranslate"><span class="pre">example</span></code> catalog. Then it calls the procedure in that schema to drop the
default value from <code class="docutils literal notranslate"><span class="pre">your_column</span></code> on <code class="docutils literal notranslate"><span class="pre">your_table</span></code> table using the standard SQL
syntax in the parameter value assigned for <code class="docutils literal notranslate"><span class="pre">query</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">USE</span><span class="w"> </span><span class="n">example</span><span class="p">.</span><span class="n">example_schema</span><span class="p">;</span>
<span class="k">CALL</span><span class="w"> </span><span class="k">system</span><span class="p">.</span><span class="k">execute</span><span class="p">(</span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'ALTER TABLE your_table ALTER COLUMN your_column DROP DEFAULT'</span><span class="p">);</span>
</pre></div>
</div>
<p>Verify that the specific database supports this syntax, and adapt as necessary
based on the documentation for the specific connected database and database
version.</p>
</section>
</section>
<section id="table-functions">
<span id="druid-table-functions"></span><h3 id="table-functions">Table functions<a class="headerlink" href="druid.html#table-functions" title="Link to this heading">#</a></h3>
<p>The connector provides specific <a class="reference internal" href="../functions/table.html"><span class="doc">table functions</span></a> to
access Druid.</p>
<section id="query-varchar-table">
<span id="druid-query-function"></span><h4 id="query-varchar-table"><code class="docutils literal notranslate"><span class="pre">query(varchar)</span> <span class="pre">-&gt;</span> <span class="pre">table</span></code><a class="headerlink" href="druid.html#query-varchar-table" title="Link to this heading">#</a></h4>
<p>The <code class="docutils literal notranslate"><span class="pre">query</span></code> function allows you to query the underlying database directly. It
requires syntax native to Druid, because the full query is pushed down and
processed in Druid. This can be useful for accessing native features which are
not available in Trino or for improving query performance in situations where
running a query natively may be faster.</p>
<p>The native query passed to the underlying data source is required to return a
table as a result set. Only the data source performs validation or security
checks for these queries using its own configuration. Trino does not perform
these tasks. Only use passthrough queries to read data.</p>
<p>As an example, query the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog and use <code class="docutils literal notranslate"><span class="pre">STRING_TO_MV</span></code> and
<code class="docutils literal notranslate"><span class="pre">MV_LENGTH</span></code> from <a class="reference external" href="https://druid.apache.org/docs/latest/querying/sql-multivalue-string-functions.html">Druid SQL’s multi-value string functions</a>
to split and then count the number of comma-separated values in a column:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="n">num_reports</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="k">TABLE</span><span class="p">(</span>
<span class="w">    </span><span class="n">example</span><span class="p">.</span><span class="k">system</span><span class="p">.</span><span class="n">query</span><span class="p">(</span>
<span class="w">      </span><span class="n">query</span><span class="w"> </span><span class="o">=&gt;</span><span class="w"> </span><span class="s1">'SELECT</span>
<span class="s1">        MV_LENGTH(</span>
<span class="s1">          STRING_TO_MV(direct_reports, ",")</span>
<span class="s1">        ) AS num_reports</span>
<span class="s1">      FROM company.managers'</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The query engine does not preserve the order of the results of this
function. If the passed query contains an <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clause, the
function result may not be ordered as expected.</p>
</div>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="delta-lake.html" title="Delta Lake connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Delta Lake connector </span>
              </div>
            </a>
          
          
            <a href="duckdb.html" title="DuckDB connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> DuckDB connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>