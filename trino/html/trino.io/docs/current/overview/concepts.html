<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Trino concepts &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="concepts.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Installation" href="../installation.html" />
    <link rel="prev" title="Use cases" href="use-cases.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="concepts.html#overview/concepts" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Trino concepts </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="use-cases.html" class="md-nav__link">Use cases</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Trino concepts </label>
    
      <a href="concepts.html#" class="md-nav__link md-nav__link--active">Trino concepts</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="concepts.html#overview" class="md-nav__link">Overview</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#architecture" class="md-nav__link">Architecture</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#cluster" class="md-nav__link">Cluster</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#node" class="md-nav__link">Node</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#coordinator" class="md-nav__link">Coordinator</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#worker" class="md-nav__link">Worker</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="concepts.html#client" class="md-nav__link">Client</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#plugin" class="md-nav__link">Plugin</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#data-source" class="md-nav__link">Data source</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#connector" class="md-nav__link">Connector</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#catalog" class="md-nav__link">Catalog</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#schema" class="md-nav__link">Schema</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#table" class="md-nav__link">Table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="concepts.html#query-execution-model" class="md-nav__link">Query execution model</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#statement" class="md-nav__link">Statement</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#query" class="md-nav__link">Query</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#stage" class="md-nav__link">Stage</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#task" class="md-nav__link">Task</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#split" class="md-nav__link">Split</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#driver" class="md-nav__link">Driver</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#operator" class="md-nav__link">Operator</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#exchange" class="md-nav__link">Exchange</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="concepts.html#overview" class="md-nav__link">Overview</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#architecture" class="md-nav__link">Architecture</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#cluster" class="md-nav__link">Cluster</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#node" class="md-nav__link">Node</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#coordinator" class="md-nav__link">Coordinator</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#worker" class="md-nav__link">Worker</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="concepts.html#client" class="md-nav__link">Client</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#plugin" class="md-nav__link">Plugin</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#data-source" class="md-nav__link">Data source</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#connector" class="md-nav__link">Connector</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#catalog" class="md-nav__link">Catalog</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#schema" class="md-nav__link">Schema</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#table" class="md-nav__link">Table</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="concepts.html#query-execution-model" class="md-nav__link">Query execution model</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="concepts.html#statement" class="md-nav__link">Statement</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#query" class="md-nav__link">Query</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#stage" class="md-nav__link">Stage</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#task" class="md-nav__link">Task</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#split" class="md-nav__link">Split</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#driver" class="md-nav__link">Driver</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#operator" class="md-nav__link">Operator</a>
        </li>
        <li class="md-nav__item"><a href="concepts.html#exchange" class="md-nav__link">Exchange</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="trino-concepts">
<h1 id="overview-concepts--page-root">Trino concepts<a class="headerlink" href="concepts.html#overview-concepts--page-root" title="Link to this heading">#</a></h1>
<section id="overview">
<h2 id="overview">Overview<a class="headerlink" href="concepts.html#overview" title="Link to this heading">#</a></h2>
<p>To understand Trino, you must first understand the terms and concepts
used throughout the Trino documentation.</p>
<p>While it is easy to understand statements and queries, as an end-user
you should have familiarity with concepts such as stages and splits to
take full advantage of Trino to execute efficient queries. As a
Trino administrator or a Trino contributor you should understand how
Trino’s concepts of stages map to tasks and how tasks contain a set
of drivers which process data.</p>
<p>This section provides a solid definition for the core concepts
referenced throughout Trino, and these sections are sorted from most
general to most specific.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The book <a class="reference external" href="https://trino.io/trino-the-definitive-guide.html">Trino: The Definitive Guide</a> and the research
paper <a class="reference external" href="https://trino.io/paper.html">Presto: SQL on Everything</a> can
provide further information about Trino and the concepts in use.</p>
</div>
</section>
<section id="architecture">
<span id="trino-concept-architecture"></span><h2 id="architecture">Architecture<a class="headerlink" href="concepts.html#architecture" title="Link to this heading">#</a></h2>
<p>Trino is a distributed query engine that processes data in parallel across
multiple servers. There are two types of Trino servers,
<a class="reference internal" href="concepts.html#trino-concept-coordinator"><span class="std std-ref">coordinators</span></a> and
<a class="reference internal" href="concepts.html#trino-concept-worker"><span class="std std-ref">workers</span></a>. The following sections describe these
servers and other components of Trino’s architecture.</p>
<section id="cluster">
<span id="trino-concept-cluster"></span><h3 id="cluster">Cluster<a class="headerlink" href="concepts.html#cluster" title="Link to this heading">#</a></h3>
<p>A Trino cluster consists of several Trino <a class="reference internal" href="concepts.html#trino-concept-node"><span class="std std-ref">nodes</span></a> - one
<a class="reference internal" href="concepts.html#trino-concept-coordinator"><span class="std std-ref">coordinator</span></a> and zero or more
<a class="reference internal" href="concepts.html#trino-concept-worker"><span class="std std-ref">workers</span></a>. Users connect to the coordinator with their
<a class="reference internal" href="../glossary.html#glosssql"><span class="std std-ref">SQL</span></a> query tool. The coordinator collaborates with the workers. The
coordinator and the workers access the connected <a class="reference internal" href="concepts.html#trino-concept-data-source"><span class="std std-ref">data
sources</span></a>. This access is configured in
<a class="reference internal" href="concepts.html#trino-concept-catalog"><span class="std std-ref">catalogs</span></a>.</p>
<p>Processing each query is a stateful operation. The workload is orchestrated by
the coordinator and spread parallel across all workers in the cluster. Each node
runs Trino in one JVM instance, and processing is parallelized further using
threads.</p>
</section>
<section id="node">
<span id="trino-concept-node"></span><h3 id="node">Node<a class="headerlink" href="concepts.html#node" title="Link to this heading">#</a></h3>
<p>Any Trino server in a specific Trino cluster is considered a <strong>node</strong> of the
<a class="reference internal" href="concepts.html#trino-concept-cluster"><span class="std std-ref">cluster</span></a>. Technically this refers to the Java process
running the Trino program, but node is often used to refer to the computer
running the process due to the recommendation to run only one Trino process per
computer.</p>
</section>
<section id="coordinator">
<span id="trino-concept-coordinator"></span><h3 id="coordinator">Coordinator<a class="headerlink" href="concepts.html#coordinator" title="Link to this heading">#</a></h3>
<p>The Trino coordinator is the server that is responsible for parsing
statements, planning queries, and managing Trino worker nodes.  It is
the “brain” of a Trino installation and is also the node to which a
client connects to submit statements for execution. Every Trino
installation must have a Trino coordinator alongside one or more
Trino workers. For development or testing purposes, a single
instance of Trino can be configured to perform both roles.</p>
<p>The coordinator keeps track of the activity on each worker and
coordinates the execution of a query. The coordinator creates
a logical model of a query involving a series of stages, which is then
translated into a series of connected tasks running on a cluster of
Trino workers.</p>
<p>Coordinators communicate with workers and clients using a REST API.</p>
</section>
<section id="worker">
<span id="trino-concept-worker"></span><h3 id="worker">Worker<a class="headerlink" href="concepts.html#worker" title="Link to this heading">#</a></h3>
<p>A Trino worker is a server in a Trino installation, which is responsible
for executing tasks and processing data. Worker nodes fetch data from
connectors and exchange intermediate data with each other. The coordinator
is responsible for fetching results from the workers and returning the
final results to the client.</p>
<p>When a Trino worker process starts up, it advertises itself to the discovery
server in the coordinator, which makes it available to the Trino coordinator
for task execution.</p>
<p>Workers communicate with other workers and Trino coordinators
using a REST API.</p>
</section>
</section>
<section id="client">
<span id="trino-concept-client"></span><h2 id="client">Client<a class="headerlink" href="concepts.html#client" title="Link to this heading">#</a></h2>
<p>Clients allow you to connect to Trino, submit SQL queries, and receive the
results. Clients can access all configured data sources using
<a class="reference internal" href="concepts.html#trino-concept-catalog"><span class="std std-ref">catalogs</span></a>. Clients are full-featured client applications
or client drivers and libraries that allow you to connect with any application
supporting that driver, or even your own custom application or script.</p>
<p>Clients applications include command line tools, desktop applications, web-based
applications, and software-as-a-service solutions with features such as
interactive SQL query authoring with editors, or rich user interfaces for
graphical query creation, query running and result rendering, visualizations
with charts and graphs, reporting, and dashboard creation.</p>
<p>Client application that support other query languages or user interface
components to build a query, must translate each request to <a class="reference internal" href="../language.html"><span class="doc std std-doc">SQL as supported by
Trino</span></a>.</p>
<p>More details are available in the <a class="reference internal" href="../client.html"><span class="doc std std-doc">Trino client documentation</span></a>.</p>
</section>
<section id="plugin">
<span id="trino-concept-plugin"></span><h2 id="plugin">Plugin<a class="headerlink" href="concepts.html#plugin" title="Link to this heading">#</a></h2>
<p>Trino uses a plugin architecture to extend its capabilities and integrate with
various data sources and other systems. Details about different types of
plugins, installation, removal, and other aspects are available in the <a class="reference internal" href="../installation/plugins.html"><span class="doc std std-doc">Plugin
documentation</span></a>.</p>
</section>
<section id="data-source">
<span id="trino-concept-data-source"></span><h2 id="data-source">Data source<a class="headerlink" href="concepts.html#data-source" title="Link to this heading">#</a></h2>
<p>Trino is a query engine that you can use to query many different data sources.
They include data lakes and lakehouses, numerous relational database management
systems, key-value stores, and many other data stores.</p>
<p><a class="reference external" href="https://trino.io/ecosystem/data-source">A comprehensive list with more details for each data source is available on the
Trino website</a>.</p>
<p>Data sources provide the data for Trino to query. Configure a
<a class="reference internal" href="concepts.html#trino-concept-catalog"><span class="std std-ref">catalog</span></a> with the required Trino
<a class="reference internal" href="concepts.html#trino-concept-connector"><span class="std std-ref">connector</span></a> for the specific data source to access the
data. With Trino you are ready to use any supported
<a class="reference internal" href="concepts.html#trino-concept-client"><span class="std std-ref">client</span></a> to query the data sources using SQL and the
features of your client.</p>
<p>Throughout this documentation, you’ll read terms such as connector,
catalog, schema, and table. These fundamental concepts cover Trino’s
model of a particular data source and are described in the following
section.</p>
<section id="connector">
<span id="trino-concept-connector"></span><h3 id="connector">Connector<a class="headerlink" href="concepts.html#connector" title="Link to this heading">#</a></h3>
<p>A connector adapts Trino to a data source such as a data lake using Hadoop/Hive
or Apache Iceberg, or a relational database such as PostgreSQL. You can think of
a connector the same way you think of a driver for a database. It is an
implementation of Trino’s <a class="reference internal" href="../develop/spi-overview.html"><span class="doc std std-doc">service provider interface
(SPI)</span></a>, which allows Trino to interact with a resource
using a standard API.</p>
<p>Trino contains <a class="reference internal" href="../connector.html"><span class="doc std std-doc">many built-in connectors</span></a>:</p>
<ul class="simple">
<li><p>Connectors for data lakes and lakehouses including the <a class="reference internal" href="../connector/delta-lake.html"><span class="doc std std-doc">Delta
Lake</span></a>, <a class="reference internal" href="../connector/hive.html"><span class="doc std std-doc">Hive</span></a>,
<a class="reference internal" href="../connector/hudi.html"><span class="doc std std-doc">Hudi</span></a>, and <a class="reference internal" href="../connector/iceberg.html"><span class="doc std std-doc">Iceberg</span></a> connectors.</p></li>
<li><p>Connectors for relational database management systems, including the
<a class="reference internal" href="../connector/mysql.html"><span class="doc std std-doc">MySQL</span></a>, <a class="reference internal" href="../connector/postgresql.html"><span class="doc std std-doc">PostgreSQL</span></a>,
<a class="reference internal" href="../connector/oracle.html"><span class="doc std std-doc">Oracle</span></a>, and <a class="reference internal" href="../connector/sqlserver.html"><span class="doc std std-doc">SQL Server</span></a>
connectors.</p></li>
<li><p>Connectors for a variety of other systems, including the
<a class="reference internal" href="../connector/cassandra.html"><span class="doc std std-doc">Cassandra</span></a>, <a class="reference internal" href="../connector/clickhouse.html"><span class="doc std std-doc">ClickHouse</span></a>,
<a class="reference internal" href="../connector/opensearch.html"><span class="doc std std-doc">OpenSearch</span></a>, <a class="reference internal" href="../connector/pinot.html"><span class="doc std std-doc">Pinot</span></a>,
<a class="reference internal" href="../connector/prometheus.html"><span class="doc std std-doc">Prometheus</span></a>, <a class="reference internal" href="../connector/singlestore.html"><span class="doc std std-doc">SingleStore</span></a>,
and <a class="reference internal" href="../connector/snowflake.html"><span class="doc std std-doc">Snowflake</span></a> connectors.</p></li>
<li><p>A number of other utility connectors such as the <a class="reference internal" href="../connector/jmx.html"><span class="doc std std-doc">JMX</span></a>,
<a class="reference internal" href="../connector/system.html"><span class="doc std std-doc">System</span></a>, and <a class="reference internal" href="../connector/tpch.html"><span class="doc std std-doc">TPC-H</span></a> connectors.</p></li>
</ul>
<p>Every catalog uses a specific connector. If you examine a catalog configuration
file, you see that each contains a mandatory property <code class="docutils literal notranslate"><span class="pre">connector.name</span></code> with the
value identifying the connector.</p>
</section>
<section id="catalog">
<span id="trino-concept-catalog"></span><h3 id="catalog">Catalog<a class="headerlink" href="concepts.html#catalog" title="Link to this heading">#</a></h3>
<p>A Trino catalog is a collection of configuration properties used to access a
specific data source, including the required connector and any other details
such as credentials and URL. Catalogs are defined in properties files stored in
the Trino configuration directory. The name of the properties file determines
the name of the catalog. For example, the properties file
<code class="docutils literal notranslate"><span class="pre">etc/example.properties</span></code> results in a catalog name <code class="docutils literal notranslate"><span class="pre">example</span></code>.</p>
<p>You can configure and use many catalogs, with different or identical connectors,
to access different data sources. For example, if you have two data lakes, you
can configure two catalogs in a single Trino cluster that both use the Hive
connector, allowing you to query data from both clusters, even within the same
SQL query. You can also use a Hive connector for one catalog to access a data
lake, and use the Iceberg connector for another catalog to access the data
lakehouse. Or, you can configure different catalogs to access different
PostgreSQL database. The combination of different catalogs is determined by your
needs to access different data sources only.</p>
<p>A catalog contains one or more schemas, which in turn contain objects such as
tables, views, or materialized views. When addressing an objects such as tables
in Trino, the fully-qualified name is always rooted in a catalog. For example, a
fully-qualified table name of <code class="docutils literal notranslate"><span class="pre">example.test_data.test</span></code> refers to the <code class="docutils literal notranslate"><span class="pre">test</span></code>
table in the <code class="docutils literal notranslate"><span class="pre">test_data</span></code> schema in the <code class="docutils literal notranslate"><span class="pre">example</span></code> catalog.</p>
</section>
<section id="schema">
<h3 id="schema">Schema<a class="headerlink" href="concepts.html#schema" title="Link to this heading">#</a></h3>
<p>Schemas are a way to organize tables. Together, a catalog and schema define a
set of tables and other objects that can be queried. When accessing Hive or a
relational database such as MySQL with Trino, a schema translates to the same
concept in the target database. Other types of connectors may organize tables
into schemas in a way that makes sense for the underlying data source.</p>
</section>
<section id="table">
<h3 id="table">Table<a class="headerlink" href="concepts.html#table" title="Link to this heading">#</a></h3>
<p>A table is a set of unordered rows, which are organized into named columns with
<a class="reference internal" href="../language/types.html"><span class="doc std std-doc">types</span></a>. This is the same as in any relational database. Type
mapping from source data to Trino is defined by the connector, varies across
connectors, and is documented in the specific connector documentation, for
example the <a class="reference internal" href="../connector/postgresql.html#postgresql-type-mapping"><span class="std std-ref">type mapping in the PostgreSQL connector</span></a>.</p>
</section>
</section>
<section id="query-execution-model">
<h2 id="query-execution-model">Query execution model<a class="headerlink" href="concepts.html#query-execution-model" title="Link to this heading">#</a></h2>
<p>Trino executes SQL statements and turns these statements into queries,
that are executed across a distributed cluster of coordinator and workers.</p>
<section id="statement">
<h3 id="statement">Statement<a class="headerlink" href="concepts.html#statement" title="Link to this heading">#</a></h3>
<p>Trino executes ANSI-compatible SQL statements.  When the Trino
documentation refers to a statement, it is referring to statements as
defined in the ANSI SQL standard, which consists of clauses,
expressions, and predicates.</p>
<p>Some readers might be curious why this section lists separate concepts
for statements and queries. This is necessary because, in Trino,
statements simply refer to the textual representation of a statement written
in SQL. When a statement is executed, Trino creates a query along
with a query plan that is then distributed across a series of Trino
workers.</p>
</section>
<section id="query">
<h3 id="query">Query<a class="headerlink" href="concepts.html#query" title="Link to this heading">#</a></h3>
<p>When Trino parses a statement, it converts it into a query and creates
a distributed query plan, which is then realized as a series of
interconnected stages running on Trino workers. When you retrieve
information about a query in Trino, you receive a snapshot of every
component that is involved in producing a result set in response to a
statement.</p>
<p>The difference between a statement and a query is simple. A statement
can be thought of as the SQL text that is passed to Trino, while a query
refers to the configuration and components instantiated to execute
that statement. A query encompasses stages, tasks, splits, connectors,
and other components and data sources working in concert to produce a
result.</p>
</section>
<section id="stage">
<span id="trino-concept-stage"></span><h3 id="stage">Stage<a class="headerlink" href="concepts.html#stage" title="Link to this heading">#</a></h3>
<p>When Trino executes a query, it does so by breaking up the execution
into a hierarchy of stages. For example, if Trino needs to aggregate
data from one billion rows stored in Hive, it does so by creating a
root stage to aggregate the output of several other stages, all of
which are designed to implement different sections of a distributed
query plan.</p>
<p>The hierarchy of stages that comprises a query resembles a tree.
Every query has a root stage, which is responsible for aggregating
the output from other stages. Stages are what the coordinator uses to
model a distributed query plan, but stages themselves don’t run on
Trino workers.</p>
</section>
<section id="task">
<span id="trino-concept-task"></span><h3 id="task">Task<a class="headerlink" href="concepts.html#task" title="Link to this heading">#</a></h3>
<p>As mentioned in the previous section, stages model a particular
section of a distributed query plan, but stages themselves don’t
execute on Trino workers. To understand how a stage is executed,
you need to understand that a stage is implemented as a series of
tasks distributed over a network of Trino workers.</p>
<p>Tasks are the “work horse” in the Trino architecture as a distributed
query plan is deconstructed into a series of stages, which are then
translated to tasks, which then act upon or process splits. A Trino
task has inputs and outputs, and just as a stage can be executed in
parallel by a series of tasks, a task is executing in parallel with a
series of drivers.</p>
</section>
<section id="split">
<span id="trino-concept-splits"></span><h3 id="split">Split<a class="headerlink" href="concepts.html#split" title="Link to this heading">#</a></h3>
<p>Tasks operate on splits, which are sections of a larger data
set. Stages at the lowest level of a distributed query plan retrieve
data via splits from connectors, and intermediate stages at a higher
level of a distributed query plan retrieve data from other stages.</p>
<p>When Trino is scheduling a query, the coordinator queries a
connector for a list of all splits that are available for a table.
The coordinator keeps track of which machines are running which tasks,
and what splits are being processed by which tasks.</p>
</section>
<section id="driver">
<h3 id="driver">Driver<a class="headerlink" href="concepts.html#driver" title="Link to this heading">#</a></h3>
<p>Tasks contain one or more parallel drivers. Drivers act upon data and
combine operators to produce output that is then aggregated by a task
and then delivered to another task in another stage. A driver is a
sequence of operator instances, or you can think of a driver as a
physical set of operators in memory. It is the lowest level of
parallelism in the Trino architecture. A driver has one input and
one output.</p>
</section>
<section id="operator">
<h3 id="operator">Operator<a class="headerlink" href="concepts.html#operator" title="Link to this heading">#</a></h3>
<p>An operator consumes, transforms and produces data. For example, a table
scan fetches data from a connector and produces data that can be consumed
by other operators, and a filter operator consumes data and produces a
subset by applying a predicate over the input data.</p>
</section>
<section id="exchange">
<h3 id="exchange">Exchange<a class="headerlink" href="concepts.html#exchange" title="Link to this heading">#</a></h3>
<p>Exchanges transfer data between Trino nodes for different stages of
a query. Tasks produce data into an output buffer and consume data
from other tasks using an exchange client.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="use-cases.html" title="Use cases"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Use cases </span>
              </div>
            </a>
          
          
            <a href="../installation.html" title="Installation"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Installation </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>