<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>TLS and HTTPS &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="tls.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="PEM files" href="inspect-pem.html" />
    <link rel="prev" title="Security overview" href="overview.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="tls.html#security/tls" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> TLS and HTTPS </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> TLS and HTTPS </label>
    
      <a href="tls.html#" class="md-nav__link md-nav__link--active">TLS and HTTPS</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="tls.html#supported-standards" class="md-nav__link">Supported standards</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#approaches" class="md-nav__link">Approaches</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#use-a-load-balancer-to-terminate-tls-https" class="md-nav__link">Use a load balancer to terminate TLS/HTTPS</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#secure-trino-directly" class="md-nav__link">Secure Trino directly</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tls.html#add-a-tls-certificate" class="md-nav__link">Add a TLS certificate</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#keys-and-certificates" class="md-nav__link">Keys and certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#inspect-received-certificates" class="md-nav__link">Inspect received certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#invalid-certificates" class="md-nav__link">Invalid certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#place-the-certificate-file" class="md-nav__link">Place the certificate file</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#configure-the-coordinator" class="md-nav__link">Configure the coordinator</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#verify-configuration" class="md-nav__link">Verify configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tls.html#limitations-of-self-signed-certificates" class="md-nav__link">Limitations of self-signed certificates</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="tls.html#supported-standards" class="md-nav__link">Supported standards</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#approaches" class="md-nav__link">Approaches</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#use-a-load-balancer-to-terminate-tls-https" class="md-nav__link">Use a load balancer to terminate TLS/HTTPS</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#secure-trino-directly" class="md-nav__link">Secure Trino directly</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tls.html#add-a-tls-certificate" class="md-nav__link">Add a TLS certificate</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#keys-and-certificates" class="md-nav__link">Keys and certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#inspect-received-certificates" class="md-nav__link">Inspect received certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#invalid-certificates" class="md-nav__link">Invalid certificates</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#place-the-certificate-file" class="md-nav__link">Place the certificate file</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#configure-the-coordinator" class="md-nav__link">Configure the coordinator</a>
        </li>
        <li class="md-nav__item"><a href="tls.html#verify-configuration" class="md-nav__link">Verify configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tls.html#limitations-of-self-signed-certificates" class="md-nav__link">Limitations of self-signed certificates</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="tls-and-https">
<h1 id="security-tls--page-root">TLS and HTTPS<a class="headerlink" href="tls.html#security-tls--page-root" title="Link to this heading">#</a></h1>
<p>Trino runs with no security by default. This allows you to connect to the server
using URLs that specify the HTTP protocol when using the Trino <a class="reference internal" href="../client/cli.html"><span class="doc">CLI</span></a>, the <a class="reference internal" href="../admin/web-interface.html"><span class="doc">Web UI</span></a>, or other
clients.</p>
<p>This topic describes how to configure your Trino server to use <a class="reference internal" href="../glossary.html#glosstls"><span class="std std-ref">TLS</span></a> to require clients to use the HTTPS connection protocol.
All authentication technologies supported by Trino require configuring TLS as
the foundational layer.</p>
<div class="admonition important">
<p class="admonition-title">Important</p>
<p>This page discusses only how to prepare the Trino server for secure client
connections from outside the Trino cluster to its coordinator.</p>
</div>
<p>See the <a class="reference internal" href="../glossary.html"><span class="doc">Glossary</span></a> to clarify unfamiliar terms.</p>
<section id="supported-standards">
<span id="tls-version-and-ciphers"></span><h2 id="supported-standards">Supported standards<a class="headerlink" href="tls.html#supported-standards" title="Link to this heading">#</a></h2>
<p>When configured to use TLS, the Trino server responds to client connections
using TLS 1.2 and TLS 1.3 certificates. The server rejects TLS 1.1, TLS 1.0, and
all SSL format certificates.</p>
<p>The Trino server does not specify a set of supported ciphers, instead deferring
to the defaults set by the JVM version in use. The documentation for Java 24
lists its <a class="reference external" href="https://docs.oracle.com/en/java/javase/24/security/oracle-providers.html#GUID-7093246A-31A3-4304-AC5F-5FB6400405E2__SUNJSSE_CIPHER_SUITES">supported cipher suites</a>.</p>
<p>Run the following two-line code on the same JVM from the same vendor as
configured on the coordinator to determine that JVM’s default cipher list.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nb">echo</span><span class="w"> </span><span class="s2">"java.util.Arrays.asList(((javax.net.ssl.SSLServerSocketFactory) \</span>
<span class="s2">javax.net.ssl.SSLServerSocketFactory.getDefault()).getSupportedCipherSuites()).forEach(System.out::println)"</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>jshell<span class="w"> </span>-
</pre></div>
</div>
<p>The default Trino server specifies a set of regular expressions that exclude
older cipher suites that do not support forward secrecy (FS).</p>
<p>Use the <code class="docutils literal notranslate"><span class="pre">http-server.https.included-cipher</span></code> property to specify a
comma-separated list of ciphers in preferred use order. If one of your preferred
selections is a non-FS cipher, you must also set the
<code class="docutils literal notranslate"><span class="pre">http-server.https.excluded-cipher</span></code> property to an empty list to override the
default exclusions. For example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.included-cipher=TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA256
http-server.https.excluded-cipher=
</pre></div>
</div>
<p>Specifying a different cipher suite is a complex issue that should only be
considered in conjunction with your organization’s security managers. Using a
different suite may require downloading and installing a different SunJCE
implementation package. Some locales may have export restrictions on cipher
suites. See the discussion in Java documentation that begins with <a class="reference external" href="https://docs.oracle.com/en/java/javase/24/security/java-secure-socket-extension-jsse-reference-guide.html#GUID-316FB978-7588-442E-B829-B4973DB3B584">Customizing
the Encryption Algorithm Providers</a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you manage the coordinator’s direct TLS implementation, monitor the CPU
usage on the Trino coordinator after enabling HTTPS. Java prefers the more
CPU-intensive cipher suites, if you allow it to choose from a big list of
ciphers. If the CPU usage is unacceptably high after enabling HTTPS, you can
configure Java to use specific cipher suites as described in this section.</p>
<p>However, best practice is to instead use an external load balancer, as
discussed next.</p>
</div>
</section>
<section id="approaches">
<h2 id="approaches">Approaches<a class="headerlink" href="tls.html#approaches" title="Link to this heading">#</a></h2>
<p>To configure Trino with TLS support, consider two alternative paths:</p>
<ul class="simple">
<li><p>Use the <a class="reference internal" href="tls.html#https-load-balancer"><span class="std std-ref">load balancer or proxy</span></a> at your site
or cloud environment to terminate TLS/HTTPS. This approach is the simplest and
strongly preferred solution.</p></li>
<li><p>Secure the Trino <a class="reference internal" href="tls.html#https-secure-directly"><span class="std std-ref">server directly</span></a>. This
requires you to obtain a valid certificate, and add it to the Trino
coordinator’s configuration.</p></li>
</ul>
</section>
<section id="use-a-load-balancer-to-terminate-tls-https">
<span id="https-load-balancer"></span><h2 id="use-a-load-balancer-to-terminate-tls-https">Use a load balancer to terminate TLS/HTTPS<a class="headerlink" href="tls.html#use-a-load-balancer-to-terminate-tls-https" title="Link to this heading">#</a></h2>
<p>Your site or cloud environment may already have a <a class="reference internal" href="../glossary.html#glosslb"><span class="std std-ref">load balancer</span></a>
or proxy server configured and running with a valid, globally trusted TLS
certificate. In this case, you can work with your network administrators to set
up your Trino server behind the load balancer. The load balancer or proxy server
accepts TLS connections and forwards them to the Trino coordinator, which
typically runs with default HTTP configuration on the default port, 8080.</p>
<p>When a load balancer accepts a TLS encrypted connection, it adds a
<a class="reference external" href="https://developer.mozilla.org/docs/Web/HTTP/Proxy_servers_and_tunneling#forwarding_client_information_through_proxies">forwarded</a>
HTTP header to the request, such as <code class="docutils literal notranslate"><span class="pre">X-Forwarded-Proto:</span> <span class="pre">https</span></code>.</p>
<p>This tells the Trino coordinator to process the connection as if a TLS
connection has already been successfully negotiated for it. This is why you do
not need to configure <code class="docutils literal notranslate"><span class="pre">http-server.https.enabled=true</span></code> for a coordinator
behind a load balancer.</p>
<p>However, to enable processing of such forwarded headers, the server’s
<a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">config properties file</span></a> <em>must</em> include the following:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.process-forwarded=true
</pre></div>
</div>
<p>More information about HTTP server configuration is available in
<a class="reference internal" href="../admin/properties-http-server.html"><span class="doc std std-doc">HTTP server properties</span></a>.</p>
<p>This completes any necessary configuration for using HTTPS with a load balancer.
Client tools can access Trino with the URL exposed by the load balancer.</p>
</section>
<section id="secure-trino-directly">
<span id="https-secure-directly"></span><h2 id="secure-trino-directly">Secure Trino directly<a class="headerlink" href="tls.html#secure-trino-directly" title="Link to this heading">#</a></h2>
<p>Instead of the preferred mechanism of using an <a class="reference internal" href="tls.html#https-load-balancer"><span class="std std-ref">external load balancer</span></a>, you can secure the Trino coordinator itself. This
requires you to obtain and install a TLS <a class="reference internal" href="../glossary.html#glosscert"><span class="std std-ref">certificate</span></a>, and
configure Trino to use it for client connections.</p>
<section id="add-a-tls-certificate">
<h3 id="add-a-tls-certificate">Add a TLS certificate<a class="headerlink" href="tls.html#add-a-tls-certificate" title="Link to this heading">#</a></h3>
<p>Obtain a TLS certificate file for use with your Trino server. Consider the
following types of certificates:</p>
<ul class="simple">
<li><p><strong>Globally trusted certificates</strong> — A certificate that is automatically
trusted by all browsers and clients. This is the easiest type to use because
you do not need to configure clients. Obtain a certificate of this type from:</p>
<ul>
<li><p>A commercial certificate vendor</p></li>
<li><p>Your cloud infrastructure provider</p></li>
<li><p>A domain name registrar, such as Verisign or GoDaddy</p></li>
<li><p>A free certificate generator, such as
<a class="reference external" href="https://letsencrypt.org/">letsencrypt.org</a> or
<a class="reference external" href="https://www.sslforfree.com/">sslforfree.com</a></p></li>
</ul>
</li>
<li><p><strong>Corporate trusted certificates</strong> — A certificate trusted by browsers and
clients in your organization. Typically, a site’s IT department runs a local
<a class="reference internal" href="../glossary.html#glossca"><span class="std std-ref">certificate authority</span></a> and preconfigures clients and servers
to trust this CA.</p></li>
<li><p><strong>Generated self-signed certificates</strong> — A certificate generated just for
Trino that is not automatically trusted by any client. Before using, make sure
you understand the <a class="reference internal" href="tls.html#self-signed-limits"><span class="std std-ref">limitations of self-signed certificates</span></a>.</p></li>
</ul>
<p>The most convenient option and strongly recommended option is a globally trusted
certificate. It may require a little more work up front, but it is worth it to
not have to configure every single client.</p>
</section>
<section id="keys-and-certificates">
<h3 id="keys-and-certificates">Keys and certificates<a class="headerlink" href="tls.html#keys-and-certificates" title="Link to this heading">#</a></h3>
<p>Trino can read certificates and private keys encoded in PEM encoded PKCS #1, PEM
encoded PKCS #8, PKCS #12, and the legacy Java KeyStore (JKS) format.
Certificates and private keys encoded in a binary format such as DER must be
converted.</p>
<p>Make sure you obtain a certificate that is validated by a recognized
<a class="reference internal" href="../glossary.html#glossca"><span class="std std-ref">certificate authority</span></a>.</p>
</section>
<section id="inspect-received-certificates">
<h3 id="inspect-received-certificates">Inspect received certificates<a class="headerlink" href="tls.html#inspect-received-certificates" title="Link to this heading">#</a></h3>
<p>Before installing your certificate, inspect and validate the received key and
certificate files to make sure they reference the correct information to access
your Trino server. Much unnecessary debugging time is saved by taking the time
to validate your certificates before proceeding to configure the server.</p>
<p>Inspect PEM-encoded files as described in <a class="reference internal" href="inspect-pem.html"><span class="doc">Inspect PEM files</span></a>.</p>
<p>Inspect PKCS # 12 and JKS keystores as described in <a class="reference internal" href="inspect-jks.html"><span class="doc">Inspect JKS files</span></a>.</p>
</section>
<section id="invalid-certificates">
<h3 id="invalid-certificates">Invalid certificates<a class="headerlink" href="tls.html#invalid-certificates" title="Link to this heading">#</a></h3>
<p>If your certificate does not pass validation, or does not show the expected
information on inspection, contact the group or vendor who provided it for a
replacement.</p>
</section>
<section id="place-the-certificate-file">
<span id="cert-placement"></span><h3 id="place-the-certificate-file">Place the certificate file<a class="headerlink" href="tls.html#place-the-certificate-file" title="Link to this heading">#</a></h3>
<p>There are no location requirements for a certificate file as long as:</p>
<ul class="simple">
<li><p>The file can be read by the Trino coordinator server process.</p></li>
<li><p>The location is secure from copying or tampering by malicious actors.</p></li>
</ul>
<p>You can place your file in the Trino coordinator’s <code class="docutils literal notranslate"><span class="pre">etc</span></code> directory, which
allows you to use a relative path reference in configuration files. However,
this location can require you to keep track of the certificate file, and move it
to a new <code class="docutils literal notranslate"><span class="pre">etc</span></code> directory when you upgrade your Trino version.</p>
</section>
<section id="configure-the-coordinator">
<span id="configure-https"></span><h3 id="configure-the-coordinator">Configure the coordinator<a class="headerlink" href="tls.html#configure-the-coordinator" title="Link to this heading">#</a></h3>
<p>On the coordinator, add the following lines to the <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">config properties file</span></a> to enable TLS/HTTPS support for the server.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Legacy <code class="docutils literal notranslate"><span class="pre">keystore</span></code> and <code class="docutils literal notranslate"><span class="pre">truststore</span></code> wording is used in property names, even
when directly using PEM-encoded certificates.</p>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.enabled=true
http-server.https.port=8443
http-server.https.keystore.path=etc/clustercoord.pem
</pre></div>
</div>
<p>Possible alternatives for the third line include:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.keystore.path=etc/clustercoord.jks
http-server.https.keystore.path=/usr/local/certs/clustercoord.p12
</pre></div>
</div>
<p>Relative paths are relative to the Trino server’s root directory. In a
<code class="docutils literal notranslate"><span class="pre">tar.gz</span></code> installation, the root directory is one level above <code class="docutils literal notranslate"><span class="pre">etc</span></code>.</p>
<p>JKS keystores always require a password, while PEM files with passwords are not
supported by Trino. For JKS, add the following line to the configuration:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.keystore.key=&lt;keystore-password&gt;
</pre></div>
</div>
<p>It is possible for a key inside a keystore to have its own password,
independent of the keystore’s password. In this case, specify the key’s password
with the following property:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.keymanager.password=&lt;key-password&gt;
</pre></div>
</div>
<p>When your Trino coordinator has an authenticator enabled along with HTTPS
enabled, HTTP access is automatically disabled for all clients, including the
<a class="reference internal" href="../admin/web-interface.html"><span class="doc">Web UI</span></a>. Although not recommended, you can
re-enable it by setting:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.authentication.allow-insecure-over-http=true
</pre></div>
</div>
</section>
<section id="verify-configuration">
<span id="verify-tls"></span><h3 id="verify-configuration">Verify configuration<a class="headerlink" href="tls.html#verify-configuration" title="Link to this heading">#</a></h3>
<p>To verify TLS/HTTPS configuration, log in to the <a class="reference internal" href="../admin/web-interface.html"><span class="doc">Web UI</span></a>, and send a query with the Trino <a class="reference internal" href="../client/cli.html"><span class="doc">CLI</span></a>.</p>
<ul class="simple">
<li><p>Connect to the Web UI from your browser using a URL that uses HTTPS, such as
<code class="docutils literal notranslate"><span class="pre">https://trino.example.com:8443</span></code>. Enter any username into the <code class="docutils literal notranslate"><span class="pre">Username</span></code>
text box, and log in to the UI. The <code class="docutils literal notranslate"><span class="pre">Password</span></code> box is disabled while
<a class="reference internal" href="authentication-types.html"><span class="doc">authentication</span></a> is not configured.</p></li>
<li><p>Connect with the Trino CLI using a URL that uses HTTPS, such as
<code class="docutils literal notranslate"><span class="pre">https://trino.example.com:8443</span></code>:</p></li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>./trino --server https://trino.example.com:8443
</pre></div>
</div>
<p>Send a query to test the connection:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; SELECT 'rocks' AS trino;

trino
-------
rocks
(1 row)

Query 20220919_113804_00017_54qfi, FINISHED, 1 node
Splits: 1 total, 1 done (100.00%)
0.12 [0 rows, 0B] [0 rows/s, 0B/s]
</pre></div>
</div>
</section>
</section>
<section id="limitations-of-self-signed-certificates">
<span id="self-signed-limits"></span><h2 id="limitations-of-self-signed-certificates">Limitations of self-signed certificates<a class="headerlink" href="tls.html#limitations-of-self-signed-certificates" title="Link to this heading">#</a></h2>
<p>It is possible to generate a self-signed certificate with the <code class="docutils literal notranslate"><span class="pre">openssl</span></code>,
<code class="docutils literal notranslate"><span class="pre">keytool</span></code>, or on Linux, <code class="docutils literal notranslate"><span class="pre">certtool</span></code> commands. Self-signed certificates can be
useful during development of a cluster for internal use only. We recommend never
using a self-signed certificate for a production Trino server.</p>
<p>Self-signed certificates are not trusted by anyone. They are typically created
by an administrator for expediency, because they do not require getting trust
signoff from anyone.</p>
<p>To use a self-signed certificate while developing your cluster requires:</p>
<ul class="simple">
<li><p>distributing to every client a local truststore that validates the certificate</p></li>
<li><p>configuring every client to use this certificate</p></li>
</ul>
<p>However, even with this client configuration, modern browsers reject these
certificates, which makes self-signed servers difficult to work with.</p>
<p>There is a difference between self-signed and unsigned certificates. Both types
are created with the same tools, but unsigned certificates are meant to be
forwarded to a CA with a Certificate Signing Request (CSR). The CA returns the
certificate signed by the CA and now globally trusted.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="overview.html" title="Security overview"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Security overview </span>
              </div>
            </a>
          
          
            <a href="inspect-pem.html" title="PEM files"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> PEM files </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>