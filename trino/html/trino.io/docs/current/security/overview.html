<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Security overview &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="overview.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="TLS and HTTPS" href="tls.html" />
    <link rel="prev" title="Security" href="../security.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="overview.html#security/overview" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Security overview </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Security overview </label>
    
      <a href="overview.html#" class="md-nav__link md-nav__link--active">Security overview</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="overview.html#aspects-of-configuring-security" class="md-nav__link">Aspects of configuring security</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#suggested-configuration-workflow" class="md-nav__link">Suggested configuration workflow</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-client-access-to-the-cluster" class="md-nav__link">Securing client access to the cluster</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="overview.html#encryption" class="md-nav__link">Encryption</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="overview.html#user-name-management" class="md-nav__link">User name management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="overview.html#authorization-and-access-control" class="md-nav__link">Authorization and access control</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-inside-the-cluster" class="md-nav__link">Securing inside the cluster</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-cluster-access-to-data-sources" class="md-nav__link">Securing cluster access to data sources</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="overview.html#aspects-of-configuring-security" class="md-nav__link">Aspects of configuring security</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#suggested-configuration-workflow" class="md-nav__link">Suggested configuration workflow</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-client-access-to-the-cluster" class="md-nav__link">Securing client access to the cluster</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="overview.html#encryption" class="md-nav__link">Encryption</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#authentication" class="md-nav__link">Authentication</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="overview.html#user-name-management" class="md-nav__link">User name management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="overview.html#authorization-and-access-control" class="md-nav__link">Authorization and access control</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-inside-the-cluster" class="md-nav__link">Securing inside the cluster</a>
        </li>
        <li class="md-nav__item"><a href="overview.html#securing-cluster-access-to-data-sources" class="md-nav__link">Securing cluster access to data sources</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="security-overview">
<h1 id="security-overview--page-root">Security overview<a class="headerlink" href="overview.html#security-overview--page-root" title="Link to this heading">#</a></h1>
<p>After the initial <a class="reference internal" href="../installation.html"><span class="doc">installation</span></a> of your cluster, security
is the next major concern for successfully operating Trino. This overview
provides an introduction to different aspects of configuring security for your
Trino cluster.</p>
<section id="aspects-of-configuring-security">
<h2 id="aspects-of-configuring-security">Aspects of configuring security<a class="headerlink" href="overview.html#aspects-of-configuring-security" title="Link to this heading">#</a></h2>
<p>The default installation of Trino has no security features enabled. Security
can be enabled for different parts of the Trino architecture:</p>
<ul class="simple">
<li><p><a class="reference internal" href="overview.html#security-client"><span class="std std-ref">Securing client access to the cluster</span></a></p></li>
<li><p><a class="reference internal" href="overview.html#security-inside-cluster"><span class="std std-ref">Securing inside the cluster</span></a></p></li>
<li><p><a class="reference internal" href="overview.html#security-data-sources"><span class="std std-ref">Securing cluster access to data sources</span></a></p></li>
</ul>
</section>
<section id="suggested-configuration-workflow">
<h2 id="suggested-configuration-workflow">Suggested configuration workflow<a class="headerlink" href="overview.html#suggested-configuration-workflow" title="Link to this heading">#</a></h2>
<p>To configure security for a new Trino cluster, follow this best practice
order of steps. Do not skip or combine steps.</p>
<ol class="arabic">
<li><p><strong>Enable</strong> <a class="reference internal" href="tls.html"><span class="doc">TLS/HTTPS</span></a></p>
<ul class="simple">
<li><p>Work with your security team.</p></li>
<li><p>Use a <a class="reference internal" href="tls.html#https-load-balancer"><span class="std std-ref">load balancer or proxy</span></a> to terminate
HTTPS, if possible.</p></li>
<li><p>Use a globally trusted TLS certificate.</p></li>
</ul>
<p><a class="reference internal" href="tls.html#verify-tls"><span class="std std-ref">Verify this step is working correctly.</span></a></p>
</li>
<li><p><strong>Configure</strong> a <a class="reference internal" href="internal-communication.html"><span class="doc">a shared secret</span></a></p>
<p><a class="reference internal" href="internal-communication.html#verify-secrets"><span class="std std-ref">Verify this step is working correctly.</span></a></p>
</li>
<li><p><strong>Enable authentication</strong></p>
<ul class="simple">
<li><p>Start with <a class="reference internal" href="password-file.html"><span class="doc">password file authentication</span></a> to get up
and running.</p></li>
<li><p>Then configure your preferred authentication provider, such as <a class="reference internal" href="ldap.html"><span class="doc">LDAP</span></a>.</p></li>
<li><p>Avoid the complexity of Kerberos for client authentication, if possible.</p></li>
</ul>
<p><a class="reference internal" href="password-file.html#verify-authentication"><span class="std std-ref">Verify this step is working correctly.</span></a></p>
</li>
<li><p><strong>Enable authorization and access control</strong></p>
<ul class="simple">
<li><p>Start with <a class="reference internal" href="file-system-access-control.html"><span class="doc">file-based rules</span></a>.</p></li>
<li><p>Then configure another access control method as required.</p></li>
</ul>
<p><a class="reference internal" href="file-system-access-control.html#verify-rules"><span class="std std-ref">Verify this step is working correctly.</span></a></p>
</li>
</ol>
<p>Configure one step at a time. Always restart the Trino server after each
change, and verify the results before proceeding.</p>
</section>
<section id="securing-client-access-to-the-cluster">
<span id="security-client"></span><h2 id="securing-client-access-to-the-cluster">Securing client access to the cluster<a class="headerlink" href="overview.html#securing-client-access-to-the-cluster" title="Link to this heading">#</a></h2>
<p>Trino <a class="reference internal" href="../client.html"><span class="doc">clients</span></a> include the Trino <a class="reference internal" href="../client/cli.html"><span class="doc">CLI</span></a>,
the <a class="reference internal" href="../admin/web-interface.html"><span class="doc">Web UI</span></a>, the <a class="reference internal" href="../client/jdbc.html"><span class="doc">JDBC driver</span></a>, <a class="reference external" href="https://trino.io/resources.html">Python, Go, or other clients</a>, and any applications using these tools.</p>
<p>All access to the Trino cluster is managed by the coordinator. Thus, securing
access to the cluster means securing access to the coordinator.</p>
<p>There are three aspects to consider:</p>
<ul class="simple">
<li><p><a class="reference internal" href="overview.html#cl-access-encrypt"><span class="std std-ref">Encryption</span></a>: protecting the integrity of client to server
communication in transit.</p></li>
<li><p><a class="reference internal" href="overview.html#cl-access-auth"><span class="std std-ref">Authentication</span></a>: identifying users and user name management.</p></li>
<li><p><a class="reference internal" href="overview.html#cl-access-control"><span class="std std-ref">Authorization and access control</span></a>: validating each user’s access rights.</p></li>
</ul>
<section id="encryption">
<span id="cl-access-encrypt"></span><h3 id="encryption">Encryption<a class="headerlink" href="overview.html#encryption" title="Link to this heading">#</a></h3>
<p>The Trino server uses the standard <a class="reference internal" href="tls.html"><span class="doc">HTTPS protocol and TLS encryption</span></a>, formerly known as SSL.</p>
</section>
<section id="authentication">
<span id="cl-access-auth"></span><h3 id="authentication">Authentication<a class="headerlink" href="overview.html#authentication" title="Link to this heading">#</a></h3>
<p>Trino supports several authentication providers. When setting up a new cluster,
start with simple password file authentication before configuring another
provider.</p>
<ul class="simple">
<li><p><a class="reference internal" href="password-file.html"><span class="doc">Password file authentication</span></a></p></li>
<li><p><a class="reference internal" href="ldap.html"><span class="doc">LDAP authentication</span></a></p></li>
<li><p><a class="reference internal" href="salesforce.html"><span class="doc">Salesforce authentication</span></a></p></li>
<li><p><a class="reference internal" href="oauth2.html"><span class="doc">OAuth 2.0 authentication</span></a></p></li>
<li><p><a class="reference internal" href="certificate.html"><span class="doc">Certificate authentication</span></a></p></li>
<li><p><a class="reference internal" href="jwt.html"><span class="doc">JSON Web Token (JWT) authentication</span></a></p></li>
<li><p><a class="reference internal" href="kerberos.html"><span class="doc">Kerberos authentication</span></a></p></li>
</ul>
<section id="user-name-management">
<span id="id1"></span><h4 id="user-name-management">User name management<a class="headerlink" href="overview.html#user-name-management" title="Link to this heading">#</a></h4>
<p>Trino provides ways to map the user and group names from authentication
providers to Trino usernames.</p>
<ul class="simple">
<li><p><a class="reference internal" href="user-mapping.html"><span class="doc">User mapping</span></a> applies to all authentication systems,
and allows for regular expression rules to be specified that map complex
usernames from other systems (<code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="26474a4f454366435e474b564a430845494b">[email&#160;protected]</a></span></code>) to simple usernames
(<code class="docutils literal notranslate"><span class="pre">alice</span></code>).</p></li>
<li><p><a class="reference internal" href="group-file.html"><span class="doc">File group provider</span></a> provides a way to assign a set
of usernames to a group name to ease access control.</p></li>
</ul>
</section>
</section>
<section id="authorization-and-access-control">
<span id="cl-access-control"></span><h3 id="authorization-and-access-control">Authorization and access control<a class="headerlink" href="overview.html#authorization-and-access-control" title="Link to this heading">#</a></h3>
<p>Trino’s <a class="reference internal" href="built-in-system-access-control.html"><span class="doc">default method of access control</span></a>
allows all operations for all authenticated users.</p>
<p>To implement access control, use:</p>
<ul class="simple">
<li><p><a class="reference internal" href="file-system-access-control.html"><span class="doc">File-based system access control</span></a>, where
you configure JSON files that specify fine-grained user access restrictions at
the catalog, schema, or table level.</p></li>
<li><p><a class="reference internal" href="opa-access-control.html"><span class="doc std std-doc">Open Policy Agent access control</span></a>, where you use Open Policy Agent to make access control
decisions on a fined-grained level.</p></li>
<li><p><a class="reference internal" href="ranger-access-control.html"><span class="doc std std-doc">Ranger access control</span></a>, where you use Apache Ranger to make fine-grained
access control decisions, apply dynamic row-filters and column-masking at
query execution time, and generate audit logs.</p></li>
</ul>
<p>In addition, Trino <a class="reference internal" href="../develop/system-access-control.html"><span class="doc">provides an API</span></a> that
allows you to create a custom access control method, or to extend an existing
one.</p>
<p>Access control can limit access to columns of a table. The default behavior
of a query to all columns with a <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">*</span></code> statement is to show an error
denying access to any inaccessible columns.</p>
<p>You can change this behavior to silently hide inaccessible columns with the
global property <code class="docutils literal notranslate"><span class="pre">hide-inaccessible-columns</span></code> configured in
<a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">Config properties</span></a>:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">hide-inaccessible-columns</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">true</span>
</pre></div>
</div>
</section>
</section>
<section id="securing-inside-the-cluster">
<span id="security-inside-cluster"></span><h2 id="securing-inside-the-cluster">Securing inside the cluster<a class="headerlink" href="overview.html#securing-inside-the-cluster" title="Link to this heading">#</a></h2>
<p>You can <a class="reference internal" href="internal-communication.html"><span class="doc">secure the internal communication</span></a>
between coordinator and workers inside the clusters.</p>
<p>Secrets in properties files, such as passwords in catalog files, can be secured
with <a class="reference internal" href="secrets.html"><span class="doc">secrets management</span></a>.</p>
</section>
<section id="securing-cluster-access-to-data-sources">
<span id="security-data-sources"></span><h2 id="securing-cluster-access-to-data-sources">Securing cluster access to data sources<a class="headerlink" href="overview.html#securing-cluster-access-to-data-sources" title="Link to this heading">#</a></h2>
<p>Communication between the Trino cluster and data sources is configured for each
catalog. Each catalog uses a connector, which supports a variety of
security-related configurations.</p>
<p>More information is available with the documentation for individual
<a class="reference internal" href="../connector.html"><span class="doc">connectors</span></a>.</p>
<p><a class="reference internal" href="secrets.html"><span class="doc">Secrets management</span></a> can be used for the catalog properties files
content.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../security.html" title="Security"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Security </span>
              </div>
            </a>
          
          
            <a href="tls.html" title="TLS and HTTPS"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> TLS and HTTPS </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>