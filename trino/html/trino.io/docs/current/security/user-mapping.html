<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>User mapping &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="user-mapping.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="File group provider" href="group-file.html" />
    <link rel="prev" title="JWT authentication" href="jwt.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-**********');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="user-mapping.html#security/user-mapping" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> User mapping </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> User mapping </label>
    
      <a href="user-mapping.html#" class="md-nav__link md-nav__link--active">User mapping</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="user-mapping.html#pattern-mapping-rule" class="md-nav__link">Pattern mapping rule</a>
        </li>
        <li class="md-nav__item"><a href="user-mapping.html#file-mapping-rules" class="md-nav__link">File mapping rules</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="user-mapping.html#pattern-mapping-rule" class="md-nav__link">Pattern mapping rule</a>
        </li>
        <li class="md-nav__item"><a href="user-mapping.html#file-mapping-rules" class="md-nav__link">File mapping rules</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="user-mapping">
<h1 id="security-user-mapping--page-root">User mapping<a class="headerlink" href="user-mapping.html#security-user-mapping--page-root" title="Link to this heading">#</a></h1>
<p>User mapping defines rules for mapping from users in the authentication method to Trino users. This
mapping is particularly important for <a class="reference internal" href="kerberos.html"><span class="doc">Kerberos</span></a> or
certificate authentication where the user names
are complex, such as <code class="docutils literal notranslate"><span class="pre">alice@example</span></code> or <code class="docutils literal notranslate"><span class="pre">CN=Alice</span> <span class="pre">Smith,OU=Finance,O=Acme,C=US</span></code>.</p>
<p>There are two ways to map the username format of a given authentication
provider into the simple username format of Trino users:</p>
<ul class="simple">
<li><p>With a single regular expression (regex) <a class="reference internal" href="user-mapping.html#pattern-rule"><span class="std std-ref">pattern mapping rule</span></a></p></li>
<li><p>With a <a class="reference internal" href="user-mapping.html#pattern-file"><span class="std std-ref">file of regex mapping rules</span></a> in JSON format</p></li>
</ul>
<section id="pattern-mapping-rule">
<span id="pattern-rule"></span><h2 id="pattern-mapping-rule">Pattern mapping rule<a class="headerlink" href="user-mapping.html#pattern-mapping-rule" title="Link to this heading">#</a></h2>
<p>If you can map all of your authentication method’s usernames with a single
regular expression, consider using a <strong>Pattern mapping rule</strong>.</p>
<p>For example, your authentication method uses all usernames in the form
<code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="ea8b8683898faa8f928b879a868fc4898587">[email&#160;protected]</a></span></code>, with no exceptions. In this case, choose a regex that
breaks incoming usernames into at least two regex capture groups, such that the
first capture group includes only the name before the <code class="docutils literal notranslate"><span class="pre">@</span></code> sign. You can use
the simple regex <code class="docutils literal notranslate"><span class="pre">(.*)(@.*)</span></code> for this case.</p>
<p>Trino automatically uses the first capture group – the $1 group – as the
username to emit after the regex substitution. If the regular expression does
not match the incoming username, authentication is denied.</p>
<p>Specify your regex pattern in the appropriate property in your coordinator’s
<code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file, using one of the <code class="docutils literal notranslate"><span class="pre">*user-mapping.pattern</span></code>
properties from the table below that matches the authentication type of your
configured authentication provider. For example, for an <a class="reference internal" href="ldap.html"><span class="doc">LDAP</span></a> authentication provider:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.authentication.password.user-mapping.pattern=(.*)(@.*)
</pre></div>
</div>
<p>Remember that an <a class="reference internal" href="authentication-types.html"><span class="doc">authentication type</span></a>
represents a category, such as <code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code>, <code class="docutils literal notranslate"><span class="pre">OAUTH2</span></code>, <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>. More than
one authentication method can have the same authentication type. For example,
the Password file, LDAP, and Salesforce authentication methods all share the
<code class="docutils literal notranslate"><span class="pre">PASSWORD</span></code> authentication type.</p>
<p>You can specify different user mapping patterns for different authentication
types when multiple authentication methods are enabled:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Authentication type</p></th>
<th class="head"><p>Property</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Password (file, LDAP, Salesforce)</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>OAuth2</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Certificate</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Header</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.header.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-even"><td><p>JSON Web Token</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Kerberos</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Insecure</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.insecure.user-mapping.pattern</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="file-mapping-rules">
<span id="pattern-file"></span><h2 id="file-mapping-rules">File mapping rules<a class="headerlink" href="user-mapping.html#file-mapping-rules" title="Link to this heading">#</a></h2>
<p>Use the <strong>File mapping rules</strong> method if your authentication provider expresses
usernames in a way that cannot be reduced to a single rule, or if you want to
exclude a set of users from accessing the cluster.</p>
<p>The rules are loaded from a JSON file identified in a configuration property.
The mapping is based on the first matching rule, processed from top to bottom.
If no rules match, authentication is denied.  Each rule is composed of the
following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pattern</span></code> (required): regex to match against the authentication method’s
username.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): replacement string to substitute against <em>pattern</em>.
The default value is <code class="docutils literal notranslate"><span class="pre">$1</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (optional): boolean indicating whether authentication is to be
allowed for the current match.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">case</span></code> (optional): one of:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">keep</span></code> - keep the matched username as is (default behavior)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">lower</span></code> - lowercase the matched username; thus both <code class="docutils literal notranslate"><span class="pre">Admin</span></code> and <code class="docutils literal notranslate"><span class="pre">ADMIN</span></code> become <code class="docutils literal notranslate"><span class="pre">admin</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">upper</span></code> - uppercase the matched username; thus both <code class="docutils literal notranslate"><span class="pre">admin</span></code> and <code class="docutils literal notranslate"><span class="pre">Admin</span></code> become <code class="docutils literal notranslate"><span class="pre">ADMIN</span></code></p></li>
</ul>
</li>
</ul>
<p>The following example maps all usernames in the form <code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="9dfcf1f4fef8ddf8e5fcf0edf1f8b3fef2f0">[email&#160;protected]</a></span></code> to
just <code class="docutils literal notranslate"><span class="pre">alice</span></code>, except for the <code class="docutils literal notranslate"><span class="pre">test</span></code> user, which is denied authentication. It
also maps users in the form <code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="bad8d5d8facfd194dfc2dbd7cad6df94d9d5d7">[email&#160;protected]</a></span></code> to <code class="docutils literal notranslate"><span class="pre">bob_uk</span></code>:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"rules"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test@example\\.com"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"(.+)@example\\.com"</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"(?&lt;user&gt;.+)@(?&lt;region&gt;.+)\\.example\\.com"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"${user}_${region}"</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"(.*)@uppercase.com"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"case"</span><span class="p">:</span><span class="w"> </span><span class="s2">"upper"</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Set up the preceding example to use the <a class="reference internal" href="ldap.html"><span class="doc">LDAP</span></a>
authentication method with the <a class="reference internal" href="authentication-types.html"><span class="doc">PASSWORD</span></a>
authentication type by adding the following line to your coordinator’s
<code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.authentication.password.user-mapping.file=etc/user-mapping.json
</pre></div>
</div>
<p>You can place your user mapping JSON file in any local file system location on
the coordinator, but placement in the <code class="docutils literal notranslate"><span class="pre">etc</span></code> directory is typical. There is no
naming standard for the file or its extension, although using <code class="docutils literal notranslate"><span class="pre">.json</span></code> as the
extension is traditional. Specify an absolute path or a path relative to the
Trino installation root.</p>
<p>You can specify different user mapping files for different authentication
types when multiple authentication methods are enabled:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Authentication type</p></th>
<th class="head"><p>Property</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Password (file, LDAP, Salesforce)</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.user-mapping.file</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>OAuth2</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.oauth2.user-mapping.file</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Certificate</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.certificate.user-mapping.file</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Header</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.header.user-mapping.pattern</span></code></p></td>
</tr>
<tr class="row-even"><td><p>JSON Web Token</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.user-mapping.file</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Kerberos</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.user-mapping.file</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Insecure</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.insecure.user-mapping.file</span></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="jwt.html" title="JWT authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> JWT authentication </span>
              </div>
            </a>
          
          
            <a href="group-file.html" title="File group provider"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> File group provider </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>