<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>JWT authentication &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="jwt.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="User mapping" href="user-mapping.html" />
    <link rel="prev" title="Certificate authentication" href="certificate.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="jwt.html#security/jwt" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> JWT authentication </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> JWT authentication </label>
    
      <a href="jwt.html#" class="md-nav__link md-nav__link--active">JWT authentication</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="jwt.html#using-jwt-authentication" class="md-nav__link">Using JWT authentication</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#jwt-authentication-configuration" class="md-nav__link">JWT authentication configuration</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#using-jwts-with-clients" class="md-nav__link">Using JWTs with clients</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#resources" class="md-nav__link">Resources</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="jwt.html#using-jwt-authentication" class="md-nav__link">Using JWT authentication</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#jwt-authentication-configuration" class="md-nav__link">JWT authentication configuration</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#using-jwts-with-clients" class="md-nav__link">Using JWTs with clients</a>
        </li>
        <li class="md-nav__item"><a href="jwt.html#resources" class="md-nav__link">Resources</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="jwt-authentication">
<h1 id="security-jwt--page-root">JWT authentication<a class="headerlink" href="jwt.html#security-jwt--page-root" title="Link to this heading">#</a></h1>
<p>Trino can be configured to authenticate client access using <a class="reference external" href="https://wikipedia.org/wiki/JSON_Web_Token">JSON web tokens</a>. A JWT is a small, web-safe
JSON file that contains cryptographic information similar to a certificate,
including:</p>
<ul class="simple">
<li><p>Subject</p></li>
<li><p>Valid time period</p></li>
<li><p>Signature</p></li>
</ul>
<p>A JWT is designed to be passed between servers as proof of prior authentication
in a workflow like the following:</p>
<ol class="arabic simple">
<li><p>An end user logs into a client application and requests access to a server.</p></li>
<li><p>The server sends the user’s credentials to a separate authentication service
that:</p>
<ul class="simple">
<li><p>validates the user</p></li>
<li><p>generates a JWT as proof of validation</p></li>
<li><p>returns the JWT to the requesting server</p></li>
</ul>
</li>
<li><p>The same JWT can then be forwarded to other services to maintain the user’s
validation without further credentials.</p></li>
</ol>
<div class="admonition important">
<p class="admonition-title">Important</p>
<p>If you are trying to configure OAuth2 or OIDC, there is a dedicated system
for that in Trino, as described in <a class="reference internal" href="oauth2.html"><span class="doc">OAuth 2.0 authentication</span></a>. When using
OAuth2 authentication, you do not need to configure JWT authentication,
because JWTs are handled automatically by the OAuth2 code.</p>
<p>A typical use for JWT authentication is to support administrators at large
sites who are writing their own single sign-on or proxy system to stand
between users and the Trino coordinator, where their new system submits
queries on behalf of users.</p>
</div>
<p>Using <a class="reference internal" href="tls.html"><span class="doc">TLS</span></a> and <a class="reference internal" href="internal-communication.html"><span class="doc">a configured shared secret</span></a> is required for JWT authentication.</p>
<section id="using-jwt-authentication">
<h2 id="using-jwt-authentication">Using JWT authentication<a class="headerlink" href="jwt.html#using-jwt-authentication" title="Link to this heading">#</a></h2>
<p>Trino supports Base64 encoded JWTs, but not encrypted JWTs.</p>
<p>There are two ways to get the encryption key necessary to validate the JWT
signature:</p>
<ul class="simple">
<li><p>Load the key from a JSON web key set (JWKS) endpoint service (the
typical case)</p></li>
<li><p>Load the key from the local file system on the Trino coordinator</p></li>
</ul>
<p>A JWKS endpoint is a read-only service that contains public key information in
<a class="reference external" href="https://datatracker.ietf.org/doc/html/rfc7517">JWK</a> format. These public
keys are the counterpart of the private keys that sign JSON web tokens.</p>
</section>
<section id="jwt-authentication-configuration">
<h2 id="jwt-authentication-configuration">JWT authentication configuration<a class="headerlink" href="jwt.html#jwt-authentication-configuration" title="Link to this heading">#</a></h2>
<p>Enable JWT authentication by setting the <a class="reference internal" href="authentication-types.html"><span class="doc">JWT authentication type</span></a> in <a class="reference internal" href="../installation/deployment.html#config-properties"><span class="std std-ref">etc/config.properties</span></a>, and
specifying a URL or path to a key file:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">http-server.authentication.type</span><span class="o">=</span><span class="s">JWT</span>
<span class="na">http-server.authentication.jwt.key-file</span><span class="o">=</span><span class="s">https://cluster.example.net/.well-known/jwks.json</span>
</pre></div>
</div>
<p>JWT authentication is typically used in addition to other authentication
methods:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">http-server.authentication.type</span><span class="o">=</span><span class="s">PASSWORD,JWT</span>
<span class="na">http-server.authentication.jwt.key-file</span><span class="o">=</span><span class="s">https://cluster.example.net/.well-known/jwks.json</span>
</pre></div>
</div>
<p>The following configuration properties are available:</p>
<table id="id1">
<caption><span class="caption-text">Configuration properties for JWT authentication</span><a class="headerlink" href="jwt.html#id1" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 50%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.key-file</span></code></p></td>
<td><p>Required. Specifies either the URL to a JWKS service or the path to a PEM or
HMAC file, as described below this table.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.required-issuer</span></code></p></td>
<td><p>Specifies a string that must match the value of the JWT’s issuer (<code class="docutils literal notranslate"><span class="pre">iss</span></code>)
field in order to consider this JWT valid. The <code class="docutils literal notranslate"><span class="pre">iss</span></code> field in the JWT
identifies the principal that issued the JWT.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.required-audience</span></code></p></td>
<td><p>Specifies a string that must match the value of the JWT’s Audience (<code class="docutils literal notranslate"><span class="pre">aud</span></code>)
field in order to consider this JWT valid. The <code class="docutils literal notranslate"><span class="pre">aud</span></code> field in the JWT
identifies the recipients that the JWT is intended for.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.principal-field</span></code></p></td>
<td><p>String to identify the field in the JWT that identifies the subject of the
JWT. The default value is <code class="docutils literal notranslate"><span class="pre">sub</span></code>. This field is used to create the Trino
principal.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.user-mapping.pattern</span></code></p></td>
<td><p>A regular expression pattern to <a class="reference internal" href="user-mapping.html"><span class="doc std std-doc">map all user names</span></a>
for this authentication system to the format expected by the Trino server.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.user-mapping.file</span></code></p></td>
<td><p>The path to a JSON file that contains a set of <a class="reference internal" href="user-mapping.html"><span class="doc std std-doc">user mapping
rules</span></a> for this authentication system.</p></td>
</tr>
</tbody>
</table>
<p>Use the <code class="docutils literal notranslate"><span class="pre">http-server.authentication.jwt.key-file</span></code> property to specify
either:</p>
<ul>
<li><p>The URL to a JWKS endpoint service, where the URL begins with <code class="docutils literal notranslate"><span class="pre">https://</span></code>.
The JWKS service must be reachable from the coordinator. If the coordinator
is running in a secured or firewalled network, the administrator <em>may</em> have
to open access to the JWKS server host.</p>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>The Trino server also accepts JWKS URLs that begin with <code class="docutils literal notranslate"><span class="pre">http://</span></code>, but
using this protocol results in a severe security risk. Only use this
protocol for short-term testing during development of your cluster.</p>
</div>
</li>
<li><p>The path to a local file in <a class="reference internal" href="inspect-pem.html"><span class="doc">PEM</span></a> or <a class="reference external" href="https://wikipedia.org/wiki/HMAC">HMAC</a> format that contains a single key.
If the file path contains <code class="docutils literal notranslate"><span class="pre">${KID}</span></code>, then Trino interpolates the <code class="docutils literal notranslate"><span class="pre">kid</span></code>
from the JWT header into the file path before loading this key. This enables support
for setups with multiple keys.</p></li>
</ul>
</section>
<section id="using-jwts-with-clients">
<h2 id="using-jwts-with-clients">Using JWTs with clients<a class="headerlink" href="jwt.html#using-jwts-with-clients" title="Link to this heading">#</a></h2>
<p>When using the Trino <a class="reference internal" href="../client/cli.html"><span class="doc">CLI</span></a>, specify a JWT as described
in <a class="reference internal" href="../client/cli.html#cli-jwt-auth"><span class="std std-ref">JWT authentication</span></a>.</p>
<p>When using the Trino JDBC driver, specify a JWT with the <code class="docutils literal notranslate"><span class="pre">accessToken</span></code>
<a class="reference internal" href="../client/jdbc.html#jdbc-parameter-reference"><span class="std std-ref">parameter</span></a>.</p>
</section>
<section id="resources">
<h2 id="resources">Resources<a class="headerlink" href="jwt.html#resources" title="Link to this heading">#</a></h2>
<p>The following resources may prove useful in your work with JWTs and JWKs.</p>
<ul class="simple">
<li><p><a class="reference external" href="https://jwt.io">jwt.io</a> helps you decode and verify a JWT.</p></li>
<li><p><a class="reference external" href="https://auth0.com/blog/navigating-rs256-and-jwks/">An article on using RS256</a>
to sign and verify your JWTs.</p></li>
<li><p>An <a class="reference external" href="https://mkjwk.org">online JSON web key</a> generator.</p></li>
<li><p>A <a class="reference external" href="https://connect2id.com/products/nimbus-jose-jwt/generator">command line JSON web key</a> generator.</p></li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="certificate.html" title="Certificate authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Certificate authentication </span>
              </div>
            </a>
          
          
            <a href="user-mapping.html" title="User mapping"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> User mapping </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>