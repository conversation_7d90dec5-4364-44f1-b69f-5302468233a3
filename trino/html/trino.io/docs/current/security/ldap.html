<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>LDAP authentication &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="ldap.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Salesforce authentication" href="salesforce.html" />
    <link rel="prev" title="Password file authentication" href="password-file.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="ldap.html#security/ldap" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> LDAP authentication </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> LDAP authentication </label>
    
      <a href="ldap.html#" class="md-nav__link md-nav__link--active">LDAP authentication</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="ldap.html#trino-server-configuration" class="md-nav__link">Trino server configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#trino-coordinator-node-configuration" class="md-nav__link">Trino coordinator node configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#server-config-properties" class="md-nav__link">Server config properties</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#password-authenticator-configuration" class="md-nav__link">Password authenticator configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#active-directory" class="md-nav__link">Active Directory</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#openldap" class="md-nav__link">OpenLDAP</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authorization-based-on-ldap-group-membership" class="md-nav__link">Authorization based on LDAP group membership</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authorization-using-trino-ldap-service-user" class="md-nav__link">Authorization using Trino LDAP service user</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#ldap-group-authorization-examples" class="md-nav__link">LDAP group authorization examples</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#trino-cli" class="md-nav__link">Trino CLI</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#environment-configuration" class="md-nav__link">Environment configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#tls-configuration" class="md-nav__link">TLS configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#trino-cli-execution" class="md-nav__link">Trino CLI execution</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#troubleshooting" class="md-nav__link">Troubleshooting</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#java-keystore-file-verification" class="md-nav__link">Java keystore file verification</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#debug-trino-to-ldap-server-issues" class="md-nav__link">Debug Trino to LDAP server issues</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#tls-debugging-for-trino-cli" class="md-nav__link">TLS debugging for Trino CLI</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#common-tls-ssl-errors" class="md-nav__link">Common TLS/SSL errors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#java-security-cert-certificateexception-no-subject-alternative-names-present" class="md-nav__link">java.security.cert.CertificateException: No subject alternative names present</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authentication-or-tls-errors-with-jdk-upgrade" class="md-nav__link">Authentication or TLS errors with JDK upgrade</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="ldap.html#trino-server-configuration" class="md-nav__link">Trino server configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#trino-coordinator-node-configuration" class="md-nav__link">Trino coordinator node configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#server-config-properties" class="md-nav__link">Server config properties</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#password-authenticator-configuration" class="md-nav__link">Password authenticator configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#active-directory" class="md-nav__link">Active Directory</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#openldap" class="md-nav__link">OpenLDAP</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authorization-based-on-ldap-group-membership" class="md-nav__link">Authorization based on LDAP group membership</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authorization-using-trino-ldap-service-user" class="md-nav__link">Authorization using Trino LDAP service user</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#ldap-group-authorization-examples" class="md-nav__link">LDAP group authorization examples</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#trino-cli" class="md-nav__link">Trino CLI</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#environment-configuration" class="md-nav__link">Environment configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#tls-configuration" class="md-nav__link">TLS configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#trino-cli-execution" class="md-nav__link">Trino CLI execution</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#troubleshooting" class="md-nav__link">Troubleshooting</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#java-keystore-file-verification" class="md-nav__link">Java keystore file verification</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#debug-trino-to-ldap-server-issues" class="md-nav__link">Debug Trino to LDAP server issues</a>
        </li>
        <li class="md-nav__item"><a href="ldap.html#tls-debugging-for-trino-cli" class="md-nav__link">TLS debugging for Trino CLI</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#common-tls-ssl-errors" class="md-nav__link">Common TLS/SSL errors</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="ldap.html#java-security-cert-certificateexception-no-subject-alternative-names-present" class="md-nav__link">java.security.cert.CertificateException: No subject alternative names present</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="ldap.html#authentication-or-tls-errors-with-jdk-upgrade" class="md-nav__link">Authentication or TLS errors with JDK upgrade</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="ldap-authentication">
<h1 id="security-ldap--page-root">LDAP authentication<a class="headerlink" href="ldap.html#security-ldap--page-root" title="Link to this heading">#</a></h1>
<p>Trino can be configured to enable frontend LDAP authentication over
HTTPS for clients, such as the <a class="reference internal" href="ldap.html#cli-ldap"><span class="std std-ref">Trino CLI</span></a>, or the JDBC and ODBC
drivers. At present, only simple LDAP authentication mechanism involving
username and password is supported. The Trino client sends a username
and password to the coordinator, and the coordinator validates these
credentials using an external LDAP service.</p>
<p>To enable LDAP authentication for Trino, LDAP-related configuration changes are
made on the Trino coordinator.</p>
<p>Using <a class="reference internal" href="tls.html"><span class="doc">TLS</span></a> and <a class="reference internal" href="internal-communication.html"><span class="doc">a configured shared secret</span></a> is required for LDAP authentication.</p>
<section id="trino-server-configuration">
<h2 id="trino-server-configuration">Trino server configuration<a class="headerlink" href="ldap.html#trino-server-configuration" title="Link to this heading">#</a></h2>
<section id="trino-coordinator-node-configuration">
<h3 id="trino-coordinator-node-configuration">Trino coordinator node configuration<a class="headerlink" href="ldap.html#trino-coordinator-node-configuration" title="Link to this heading">#</a></h3>
<p>Access to the Trino coordinator should be through HTTPS, configured as described
on <a class="reference internal" href="tls.html"><span class="doc">TLS and HTTPS</span></a>.</p>
<p>You also need to make changes to the Trino configuration files.
LDAP authentication is configured on the coordinator in two parts.
The first part is to enable HTTPS support and password authentication
in the coordinator’s <code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file. The second part is
to configure LDAP as the password authenticator plugin.</p>
<section id="server-config-properties">
<h4 id="server-config-properties">Server config properties<a class="headerlink" href="ldap.html#server-config-properties" title="Link to this heading">#</a></h4>
<p>The following is an example of the required properties that need to be added
to the coordinator’s <code class="docutils literal notranslate"><span class="pre">config.properties</span></code> file:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.authentication.type=PASSWORD

http-server.https.enabled=true
http-server.https.port=8443

http-server.https.keystore.path=/etc/trino/keystore.jks
http-server.https.keystore.key=keystore_password
</pre></div>
</div>
<p>Find detailed description for the available properties in
<a class="reference internal" href="../admin/properties-http-server.html"><span class="doc std std-doc">HTTP server properties</span></a> and the following table:</p>
<table>
<colgroup>
<col style="width: 20%"/>
<col style="width: 80%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.user-mapping.pattern</span></code></p></td>
<td><p>Regex to match against user.  If matched, user is replaced with first regex
group. If not matched, authentication is denied.  Defaults to <code class="docutils literal notranslate"><span class="pre">(.*)</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.password.user-mapping.file</span></code></p></td>
<td><p>File containing rules for mapping user.  See <a class="reference internal" href="user-mapping.html"><span class="doc std std-doc">User mapping</span></a>
for more information.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="password-authenticator-configuration">
<h4 id="password-authenticator-configuration">Password authenticator configuration<a class="headerlink" href="ldap.html#password-authenticator-configuration" title="Link to this heading">#</a></h4>
<p>Password authentication must be configured to use LDAP. Create an
<code class="docutils literal notranslate"><span class="pre">etc/password-authenticator.properties</span></code> file on the coordinator. Example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>password-authenticator.name=ldap
ldap.url=ldaps://ldap-server:636
ldap.ssl.truststore.path=/path/to/ldap_server.pem
ldap.user-bind-pattern=&lt;Refer below for usage&gt;
</pre></div>
</div>
<table>
<colgroup>
<col style="width: 20%"/>
<col style="width: 80%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.url</span></code></p></td>
<td><p>The URL to the LDAP server. The URL scheme must be <code class="docutils literal notranslate"><span class="pre">ldap://</span></code> or <code class="docutils literal notranslate"><span class="pre">ldaps://</span></code>.
Connecting to the LDAP server without TLS enabled requires
<code class="docutils literal notranslate"><span class="pre">ldap.allow-insecure=true</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.allow-insecure</span></code></p></td>
<td><p>Allow using an LDAP connection that is not secured with TLS.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.ssl.keystore.path</span></code></p></td>
<td><p>The path to the <a class="reference internal" href="inspect-pem.html"><span class="doc">PEM</span></a> or <a class="reference internal" href="inspect-jks.html"><span class="doc">JKS</span></a> keystore file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.ssl.keystore.password</span></code></p></td>
<td><p>Password for the key store.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.ssl.truststore.path</span></code></p></td>
<td><p>The path to the <a class="reference internal" href="inspect-pem.html"><span class="doc">PEM</span></a> or <a class="reference internal" href="inspect-jks.html"><span class="doc">JKS</span></a> truststore file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.ssl.truststore.password</span></code></p></td>
<td><p>Password for the truststore.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.user-bind-pattern</span></code></p></td>
<td><p>This property can be used to specify the LDAP user bind string for password
authentication. This property must contain the pattern <code class="docutils literal notranslate"><span class="pre">${USER}</span></code>, which is
replaced by the actual username during the password authentication. The
property can contain multiple patterns separated by a colon. Each pattern is
checked in order until a login succeeds or all logins fail. Example:
<code class="docutils literal notranslate"><span class="pre">${USER}@corp.example.com:${USER}@corp.example.co.uk</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.ignore-referrals</span></code></p></td>
<td><p>Ignore referrals to other LDAP servers while performing search queries.
Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.cache-ttl</span></code></p></td>
<td><p>LDAP cache duration. Defaults to <code class="docutils literal notranslate"><span class="pre">1h</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.timeout.connect</span></code></p></td>
<td><p>Timeout for establishing an LDAP connection.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.timeout.read</span></code></p></td>
<td><p>Timeout for reading data from an LDAP connection.</p></td>
</tr>
</tbody>
</table>
<p>Based on the LDAP server implementation type, the property
<code class="docutils literal notranslate"><span class="pre">ldap.user-bind-pattern</span></code> can be used as described below.</p>
<section id="active-directory">
<h5 id="active-directory">Active Directory<a class="headerlink" href="ldap.html#active-directory" title="Link to this heading">#</a></h5>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.user-bind-pattern=${USER}@&lt;domain_name_of_the_server&gt;
</pre></div>
</div>
<p>Example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.user-bind-pattern=${USER}@corp.example.com
</pre></div>
</div>
</section>
<section id="openldap">
<h5 id="openldap">OpenLDAP<a class="headerlink" href="ldap.html#openldap" title="Link to this heading">#</a></h5>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.user-bind-pattern=uid=${USER},&lt;distinguished_name_of_the_user&gt;
</pre></div>
</div>
<p>Example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.user-bind-pattern=uid=${USER},OU=America,DC=corp,DC=example,DC=com
</pre></div>
</div>
</section>
</section>
<section id="authorization-based-on-ldap-group-membership">
<h4 id="authorization-based-on-ldap-group-membership">Authorization based on LDAP group membership<a class="headerlink" href="ldap.html#authorization-based-on-ldap-group-membership" title="Link to this heading">#</a></h4>
<p>You can further restrict the set of users allowed to connect to the Trino
coordinator, based on their group membership, by setting the optional
<code class="docutils literal notranslate"><span class="pre">ldap.group-auth-pattern</span></code> and <code class="docutils literal notranslate"><span class="pre">ldap.user-base-dn</span></code> properties, in addition
to the basic LDAP authentication properties.</p>
<table>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.user-base-dn</span></code></p></td>
<td><p>The base LDAP distinguished name for the user who tries to connect to the
server. Example: <code class="docutils literal notranslate"><span class="pre">OU=America,DC=corp,DC=example,DC=com</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.group-auth-pattern</span></code></p></td>
<td><p>This property is used to specify the LDAP query for the LDAP group
membership authorization. This query is executed against the LDAP server and
if successful, the user is authorized.</p>
<p>This property must contain a pattern <code class="docutils literal notranslate"><span class="pre">${USER}</span></code>, which is replaced by the
actual username in the group authorization search query. See details in the
<a class="reference internal" href="ldap.html#ldap-group-auth-examples"><span class="std std-ref">examples section</span></a>.</p>
</td>
</tr>
</tbody>
</table>
</section>
<section id="authorization-using-trino-ldap-service-user">
<h4 id="authorization-using-trino-ldap-service-user">Authorization using Trino LDAP service user<a class="headerlink" href="ldap.html#authorization-using-trino-ldap-service-user" title="Link to this heading">#</a></h4>
<p>Trino server can use dedicated LDAP service user for doing user group membership
queries. In such case Trino first issues a group membership query for a Trino
user that needs to be authenticated. A user distinguished name is extracted from
a group membership query result. Trino then validates user password by creating
LDAP context with user distinguished name and user password. In order to use
this mechanism <code class="docutils literal notranslate"><span class="pre">ldap.bind-dn</span></code>, <code class="docutils literal notranslate"><span class="pre">ldap.bind-password</span></code> and
<code class="docutils literal notranslate"><span class="pre">ldap.group-auth-pattern</span></code> properties need to be defined.</p>
<table>
<colgroup>
<col style="width: 35%"/>
<col style="width: 65%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.bind-dn</span></code></p></td>
<td><p>Bind distinguished name used by Trino when issuing group membership queries.
Example: <code class="docutils literal notranslate"><span class="pre">CN=admin,OU=CITY_OU,OU=STATE_OU,DC=domain</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.bind-password</span></code></p></td>
<td><p>Bind password used by Trino when issuing group membership queries. Example:
<code class="docutils literal notranslate"><span class="pre">password1234</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ldap.group-auth-pattern</span></code></p></td>
<td><p>This property is used to specify the LDAP query for the LDAP group
membership authorization. This query is executed against the LDAP server
during login to check if the user belongs to the specified group. If
successful, a user distinguished name is extracted from the query result.
Trino then validates the user and password by creating an LDAP context with
the user’s distinguished name and password.</p>
<p>This property must contain a pattern <code class="docutils literal notranslate"><span class="pre">${USER}</span></code>, which is replaced by the
actual username in the group authorization search query. See details in the
<a class="reference internal" href="ldap.html#ldap-group-auth-examples"><span class="std std-ref">examples section</span></a>.</p>
</td>
</tr>
</tbody>
</table>
<section id="ldap-group-authorization-examples">
<span id="ldap-group-auth-examples"></span><h5 id="ldap-group-authorization-examples">LDAP group authorization examples<a class="headerlink" href="ldap.html#ldap-group-authorization-examples" title="Link to this heading">#</a></h5>
<p>With Active Directory, the following syntax can be used:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.group-auth-pattern=(&amp;(objectClass=&lt;objectclass_of_user&gt;)(sAMAccountName=${USER})(memberof=&lt;dn_of_the_authorized_group&gt;))
</pre></div>
</div>
<p>Example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.group-auth-pattern=(&amp;(objectClass=person)(sAMAccountName=${USER})(memberof=CN=AuthorizedGroup,OU=Asia,DC=corp,DC=example,DC=com))
</pre></div>
</div>
<p>With OpenLDAP, the following syntax can be used:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.group-auth-pattern=(&amp;(objectClass=&lt;objectclass_of_user&gt;)(uid=${USER})(memberof=&lt;dn_of_the_authorized_group&gt;))
</pre></div>
</div>
<p>Example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.group-auth-pattern=(&amp;(objectClass=inetOrgPerson)(uid=${USER})(memberof=CN=AuthorizedGroup,OU=Asia,DC=corp,DC=example,DC=com))
</pre></div>
</div>
<p>For OpenLDAP, for this query to work, make sure you enable the
<code class="docutils literal notranslate"><span class="pre">memberOf</span></code> <a class="reference external" href="http://www.openldap.org/doc/admin24/overlays.html">overlay</a>.</p>
<p>You can use this property for scenarios where you want to authorize a user
based on complex group authorization search queries. For example, if you want to
authorize a user belonging to any one of multiple groups (in OpenLDAP), this
property may be set as follows:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>ldap.group-auth-pattern=(&amp;(|(memberOf=CN=normal_group,DC=corp,DC=com)(memberOf=CN=another_group,DC=com))(objectClass=inetOrgPerson)(uid=${USER}))
</pre></div>
</div>
</section>
</section>
</section>
</section>
<section id="trino-cli">
<span id="cli-ldap"></span><h2 id="trino-cli">Trino CLI<a class="headerlink" href="ldap.html#trino-cli" title="Link to this heading">#</a></h2>
<section id="environment-configuration">
<h3 id="environment-configuration">Environment configuration<a class="headerlink" href="ldap.html#environment-configuration" title="Link to this heading">#</a></h3>
<section id="tls-configuration">
<h4 id="tls-configuration">TLS configuration<a class="headerlink" href="ldap.html#tls-configuration" title="Link to this heading">#</a></h4>
<p>When using LDAP authentication, access to the Trino coordinator must be through
<a class="reference internal" href="tls.html"><span class="doc">TLS/HTTPS</span></a>.</p>
</section>
</section>
<section id="trino-cli-execution">
<h3 id="trino-cli-execution">Trino CLI execution<a class="headerlink" href="ldap.html#trino-cli-execution" title="Link to this heading">#</a></h3>
<p>In addition to the options that are required when connecting to a Trino
coordinator that does not require LDAP authentication, invoking the CLI
with LDAP support enabled requires a number of additional command line
options. You can either use <code class="docutils literal notranslate"><span class="pre">--keystore-*</span></code> or <code class="docutils literal notranslate"><span class="pre">--truststore-*</span></code> properties
to secure TLS connection. The simplest way to invoke the CLI is with a
wrapper script.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>#!/bin/bash

./trino \
--server https://trino-coordinator.example.com:8443 \
--keystore-path /tmp/trino.jks \
--keystore-password password \
--truststore-path /tmp/trino_truststore.jks \
--truststore-password password \
--catalog &lt;catalog&gt; \
--schema &lt;schema&gt; \
--user &lt;LDAP user&gt; \
--password
</pre></div>
</div>
<p>Find details on the options used in <a class="reference internal" href="../client/cli.html#cli-tls"><span class="std std-ref">TLS/HTTPS</span></a> and
<a class="reference internal" href="../client/cli.html#cli-username-password-auth"><span class="std std-ref">Username and password authentication</span></a>.</p>
</section>
</section>
<section id="troubleshooting">
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="ldap.html#troubleshooting" title="Link to this heading">#</a></h2>
<section id="java-keystore-file-verification">
<h3 id="java-keystore-file-verification">Java keystore file verification<a class="headerlink" href="ldap.html#java-keystore-file-verification" title="Link to this heading">#</a></h3>
<p>Verify the password for a keystore file and view its contents using
<a class="reference internal" href="inspect-jks.html#troubleshooting-keystore"><span class="std std-ref">Inspect and validate keystore</span></a>.</p>
</section>
<section id="debug-trino-to-ldap-server-issues">
<h3 id="debug-trino-to-ldap-server-issues">Debug Trino to LDAP server issues<a class="headerlink" href="ldap.html#debug-trino-to-ldap-server-issues" title="Link to this heading">#</a></h3>
<p>If you need to debug issues with Trino communicating with the LDAP server,
you can change the <a class="reference internal" href="../admin/logging.html#logging-configuration"><span class="std std-ref">log level</span></a> for the LDAP authenticator:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>io.trino.plugin.password=DEBUG
</pre></div>
</div>
</section>
<section id="tls-debugging-for-trino-cli">
<h3 id="tls-debugging-for-trino-cli">TLS debugging for Trino CLI<a class="headerlink" href="ldap.html#tls-debugging-for-trino-cli" title="Link to this heading">#</a></h3>
<p>If you encounter any TLS related errors when running the Trino CLI, you can run
the CLI using the <code class="docutils literal notranslate"><span class="pre">-Djavax.net.debug=ssl</span></code> parameter for debugging. Use the
Trino CLI executable JAR to enable this. For example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>java -Djavax.net.debug=ssl \
-jar \
trino-cli-&lt;version&gt;-executable.jar \
--server https://coordinator:8443 \
&lt;other_cli_arguments&gt;
</pre></div>
</div>
<section id="common-tls-ssl-errors">
<h4 id="common-tls-ssl-errors">Common TLS/SSL errors<a class="headerlink" href="ldap.html#common-tls-ssl-errors" title="Link to this heading">#</a></h4>
<section id="java-security-cert-certificateexception-no-subject-alternative-names-present">
<h5 id="java-security-cert-certificateexception-no-subject-alternative-names-present">java.security.cert.CertificateException: No subject alternative names present<a class="headerlink" href="ldap.html#java-security-cert-certificateexception-no-subject-alternative-names-present" title="Link to this heading">#</a></h5>
<p>This error is seen when the Trino coordinator’s certificate is invalid, and does not have the IP you provide
in the <code class="docutils literal notranslate"><span class="pre">--server</span></code> argument of the CLI. You have to regenerate the coordinator’s TLS certificate
with the appropriate <abbr title="Subject Alternative Name">SAN</abbr> added.</p>
<p>Adding a SAN to this certificate is required in cases where <code class="docutils literal notranslate"><span class="pre">https://</span></code> uses IP address in the URL, rather
than the domain contained in the coordinator’s certificate, and the certificate does not contain the
<abbr title="Subject Alternative Name">SAN</abbr> parameter with the matching IP address as an alternative attribute.</p>
</section>
</section>
<section id="authentication-or-tls-errors-with-jdk-upgrade">
<h4 id="authentication-or-tls-errors-with-jdk-upgrade">Authentication or TLS errors with JDK upgrade<a class="headerlink" href="ldap.html#authentication-or-tls-errors-with-jdk-upgrade" title="Link to this heading">#</a></h4>
<p>Starting with the JDK 8u181 release, to improve the robustness of LDAPS
(secure LDAP over TLS) connections, endpoint identification algorithms were
enabled by default. See release notes
<a class="reference external" href="https://www.oracle.com/technetwork/java/javase/8u181-relnotes-4479407.html#JDK-8200666.">from Oracle</a>.
The same LDAP server certificate on the Trino coordinator, running on JDK
version &gt;= 8u181, that was previously able to successfully connect to an
LDAPS server, may now fail with the following error:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>javax.naming.CommunicationException: simple bind failed: ldapserver:636
[Root exception is javax.net.ssl.SSLHandshakeException: java.security.cert.CertificateException: No subject alternative DNS name matching ldapserver found.]
</pre></div>
</div>
<p>If you want to temporarily disable endpoint identification, you can add the
property <code class="docutils literal notranslate"><span class="pre">-Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true</span></code>
to Trino’s <code class="docutils literal notranslate"><span class="pre">jvm.config</span></code> file. However, in a production environment, we
suggest fixing the issue by regenerating the LDAP server certificate so that
the certificate <abbr title="Subject Alternative Name">SAN</abbr> or certificate subject
name matches the LDAP server.</p>
</section>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="password-file.html" title="Password file authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Password file authentication </span>
              </div>
            </a>
          
          
            <a href="salesforce.html" title="Salesforce authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Salesforce authentication </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>