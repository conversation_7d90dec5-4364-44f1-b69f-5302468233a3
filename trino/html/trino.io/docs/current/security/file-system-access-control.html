<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>File-based access control &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="file-system-access-control.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Open Policy Agent access control" href="opa-access-control.html" />
    <link rel="prev" title="System access control" href="built-in-system-access-control.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="file-system-access-control.html#security/file-system-access-control" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> File-based access control </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> File-based access control </label>
    
      <a href="file-system-access-control.html#" class="md-nav__link md-nav__link--active">File-based access control</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-access-control.html#system-level-access-control-files" class="md-nav__link">System-level access control files</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#refresh" class="md-nav__link">Refresh</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-schema-and-table-access" class="md-nav__link">Catalog, schema, and table access</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#visibility" class="md-nav__link">Visibility</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-rules" class="md-nav__link">Catalog rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#schema-rules" class="md-nav__link">Schema rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#table-rules" class="md-nav__link">Table rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#column-constraint" class="md-nav__link">Column constraint</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#filter-and-mask-environment" class="md-nav__link">Filter and mask environment</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#function-rules" class="md-nav__link">Function rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#procedure-rules" class="md-nav__link">Procedure rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#table-procedure-rules" class="md-nav__link">Table procedure rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#verify-configuration" class="md-nav__link">Verify configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#session-property-rules" class="md-nav__link">Session property rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#query-rules" class="md-nav__link">Query rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#impersonation-rules" class="md-nav__link">Impersonation rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#principal-rules" class="md-nav__link">Principal rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#system-information-rules" class="md-nav__link">System information rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#authorization-rules" class="md-nav__link">Authorization rules</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-level-access-control-files" class="md-nav__link">Catalog-level access control files</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#configure-a-catalog-rules-file" class="md-nav__link">Configure a catalog rules file</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#id2" class="md-nav__link">Schema rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id3" class="md-nav__link">Table rules</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#column-constraints" class="md-nav__link">Column constraints</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#filter-environment-and-mask-environment" class="md-nav__link">Filter environment and mask environment</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id4" class="md-nav__link">Function rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id5" class="md-nav__link">Session property rules</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#example" class="md-nav__link">Example</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="file-system-access-control.html#system-level-access-control-files" class="md-nav__link">System-level access control files</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#configuration" class="md-nav__link">Configuration</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#refresh" class="md-nav__link">Refresh</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-schema-and-table-access" class="md-nav__link">Catalog, schema, and table access</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#visibility" class="md-nav__link">Visibility</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-rules" class="md-nav__link">Catalog rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#schema-rules" class="md-nav__link">Schema rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#table-rules" class="md-nav__link">Table rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#column-constraint" class="md-nav__link">Column constraint</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#filter-and-mask-environment" class="md-nav__link">Filter and mask environment</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#function-rules" class="md-nav__link">Function rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#procedure-rules" class="md-nav__link">Procedure rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#table-procedure-rules" class="md-nav__link">Table procedure rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#verify-configuration" class="md-nav__link">Verify configuration</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#session-property-rules" class="md-nav__link">Session property rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#query-rules" class="md-nav__link">Query rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#impersonation-rules" class="md-nav__link">Impersonation rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#principal-rules" class="md-nav__link">Principal rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#system-information-rules" class="md-nav__link">System information rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#authorization-rules" class="md-nav__link">Authorization rules</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#catalog-level-access-control-files" class="md-nav__link">Catalog-level access control files</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#configure-a-catalog-rules-file" class="md-nav__link">Configure a catalog rules file</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#id2" class="md-nav__link">Schema rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id3" class="md-nav__link">Table rules</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="file-system-access-control.html#column-constraints" class="md-nav__link">Column constraints</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#filter-environment-and-mask-environment" class="md-nav__link">Filter environment and mask environment</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id4" class="md-nav__link">Function rules</a>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#id5" class="md-nav__link">Session property rules</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="file-system-access-control.html#example" class="md-nav__link">Example</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="file-based-access-control">
<h1 id="security-file-system-access-control--page-root">File-based access control<a class="headerlink" href="file-system-access-control.html#security-file-system-access-control--page-root" title="Link to this heading">#</a></h1>
<p>To secure access to data in your cluster, you can implement file-based access
control where access to data and operations is defined by rules declared in
manually-configured JSON files.</p>
<p>There are two types of file-based access control:</p>
<ul class="simple">
<li><p><strong>System-level access control</strong> uses the access control plugin with a single
JSON file that specifies authorization rules for the whole cluster.</p></li>
<li><p><strong>Catalog-level access control</strong> uses individual JSON files for each catalog
for granular control over the data in that catalog, including column-level
authorization.</p></li>
</ul>
<section id="system-level-access-control-files">
<span id="system-file-based-access-control"></span><h2 id="system-level-access-control-files">System-level access control files<a class="headerlink" href="file-system-access-control.html#system-level-access-control-files" title="Link to this heading">#</a></h2>
<p>The access control plugin allows you to specify authorization rules for the
cluster in a single JSON file.</p>
<section id="configuration">
<h3 id="configuration">Configuration<a class="headerlink" href="file-system-access-control.html#configuration" title="Link to this heading">#</a></h3>
<p>To use the access control plugin, add an <code class="docutils literal notranslate"><span class="pre">etc/access-control.properties</span></code> file
containing two required properties: <code class="docutils literal notranslate"><span class="pre">access-control.name</span></code>, which must be set
to <code class="docutils literal notranslate"><span class="pre">file</span></code>, and <code class="docutils literal notranslate"><span class="pre">security.config-file</span></code>, which must be set to the location
of the config file. The configuration file location can either point to the local
disc or to a http endpoint. For example, if a config file named <code class="docutils literal notranslate"><span class="pre">rules.json</span></code> resides
in <code class="docutils literal notranslate"><span class="pre">etc</span></code>, add an <code class="docutils literal notranslate"><span class="pre">etc/access-control.properties</span></code> with the following
contents:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>access-control.name=file
security.config-file=etc/rules.json
</pre></div>
</div>
<p>If the config should be loaded via the http endpoint <code class="docutils literal notranslate"><span class="pre">http://trino-test/config</span></code> and
is wrapped into a JSON object and available via the <code class="docutils literal notranslate"><span class="pre">data</span></code> key <code class="docutils literal notranslate"><span class="pre">etc/access-control.properties</span></code>
should look like this:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>access-control.name=file
security.config-file=http://trino-test/config
security.json-pointer=/data
</pre></div>
</div>
<p>The config file is specified in JSON format. It contains rules that define which
users have access to which resources. The rules are read from top to bottom and
the first matching rule is applied. If no rule matches, access is denied. A JSON
pointer (RFC 6901) can be specified using the <code class="docutils literal notranslate"><span class="pre">security.json-pointer</span></code> property
to specify a nested object inside the JSON content containing the rules. Per default,
the file is assumed to contain a single object defining the rules rendering
the specification of <code class="docutils literal notranslate"><span class="pre">security.json-pointer</span></code> unnecessary in that case.</p>
</section>
<section id="refresh">
<h3 id="refresh">Refresh<a class="headerlink" href="file-system-access-control.html#refresh" title="Link to this heading">#</a></h3>
<p>By default, when a change is made to the JSON rules file, Trino must be
restarted to load the changes. There is an optional property to refresh the
properties without requiring a Trino restart. The refresh period is specified in
the <code class="docutils literal notranslate"><span class="pre">etc/access-control.properties</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>security.refresh-period=1s
</pre></div>
</div>
</section>
<section id="catalog-schema-and-table-access">
<h3 id="catalog-schema-and-table-access">Catalog, schema, and table access<a class="headerlink" href="file-system-access-control.html#catalog-schema-and-table-access" title="Link to this heading">#</a></h3>
<p>Access to catalogs, schemas, tables, and views is controlled by the catalog,
schema, and table rules. The catalog rules are coarse-grained rules used to
restrict all access or write access to catalogs. They do not explicitly grant
any specific schema or table permissions. The table and schema rules are used to
specify who can create, drop, alter, select, insert, delete, etc. for schemas
and tables.</p>
<p>For each rule set, permission is based on the first matching rule, read from the
top to the bottom of the configuration file. If no rule matches, access is
denied.</p>
<p>If no rules are provided at all, then access is granted. You can remove
access grant by adding a section with an empty set of rules at that particular
level, for example:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>At the catalog level you have to add a single “dummy” rule for each accessible
catalog.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These rules do not apply to system-defined tables in the
<code class="docutils literal notranslate"><span class="pre">information_schema</span></code> schema.</p>
</div>
<p>The following table summarizes the permissions required for each SQL command:</p>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>SQL command</p></th>
<th class="head"><p>Catalog</p></th>
<th class="head"><p>Schema</p></th>
<th class="head"><p>Table</p></th>
<th class="head"><p>Note</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>SHOW CATALOGS</p></td>
<td><p></p></td>
<td><p></p></td>
<td><p></p></td>
<td><p>Always allowed</p></td>
</tr>
<tr class="row-odd"><td><p>SHOW SCHEMAS</p></td>
<td><p>read-only</p></td>
<td><p>any*</p></td>
<td><p>any*</p></td>
<td><p>Allowed if catalog is <a class="reference internal" href="file-system-access-control.html#system-file-auth-visibility"><span class="std std-ref">visible</span></a></p></td>
</tr>
<tr class="row-even"><td><p>SHOW TABLES</p></td>
<td><p>read-only</p></td>
<td><p>any*</p></td>
<td><p>any*</p></td>
<td><p>Allowed if schema <a class="reference internal" href="file-system-access-control.html#system-file-auth-visibility"><span class="std std-ref">visible</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>CREATE SCHEMA</p></td>
<td><p>read-only</p></td>
<td><p>owner</p></td>
<td><p></p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>DROP SCHEMA</p></td>
<td><p>all</p></td>
<td><p>owner</p></td>
<td><p></p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>SHOW CREATE SCHEMA</p></td>
<td><p>all</p></td>
<td><p>owner</p></td>
<td><p></p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>ALTER SCHEMA … RENAME TO</p></td>
<td><p>all</p></td>
<td><p>owner*</p></td>
<td><p></p></td>
<td><p>Ownership is required on both old and new schemas</p></td>
</tr>
<tr class="row-odd"><td><p>ALTER SCHEMA … SET AUTHORIZATION</p></td>
<td><p>all</p></td>
<td><p>owner</p></td>
<td><p></p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>CREATE TABLE</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>DROP TABLE</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>ALTER TABLE … RENAME TO</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner*</p></td>
<td><p>Ownership is required on both old and new tables</p></td>
</tr>
<tr class="row-odd"><td><p>ALTER TABLE … SET PROPERTIES</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>CREATE VIEW</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>DROP VIEW</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>ALTER VIEW … RENAME TO</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner*</p></td>
<td><p>Ownership is required on both old and new views</p></td>
</tr>
<tr class="row-odd"><td><p>REFRESH MATERIALIZED VIEW</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>update</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>COMMENT ON TABLE</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>COMMENT ON COLUMN</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>ALTER TABLE … ADD COLUMN</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>ALTER TABLE … DROP COLUMN</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>ALTER TABLE … RENAME COLUMN</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>owner</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>SHOW COLUMNS</p></td>
<td><p>read-only</p></td>
<td><p></p></td>
<td><p>any</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>SELECT FROM table</p></td>
<td><p>read-only</p></td>
<td><p></p></td>
<td><p>select</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>SELECT FROM view</p></td>
<td><p>read-only</p></td>
<td><p></p></td>
<td><p>select, grant_select</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>INSERT INTO</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>insert</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>DELETE FROM</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>delete</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>UPDATE</p></td>
<td><p>all</p></td>
<td><p></p></td>
<td><p>update</p></td>
<td><p></p></td>
</tr>
</tbody>
</table>
<p>Permissions required for executing functions:</p>
<table>
<colgroup>
<col style="width: 30%"/>
<col style="width: 10%"/>
<col style="width: 20%"/>
<col style="width: 40%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>SQL command</p></th>
<th class="head"><p>Catalog</p></th>
<th class="head"><p>Function permission</p></th>
<th class="head"><p>Note</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">function()</span></code></p></td>
<td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">execute</span></code>, <code class="docutils literal notranslate"><span class="pre">grant_execute*</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">grant_execute</span></code> is required when the function is used in a <code class="docutils literal notranslate"><span class="pre">SECURITY</span> <span class="pre">DEFINER</span></code> view.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">FUNCTION</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">all</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ownership</span></code></p></td>
<td><p>Not all connectors support <a class="reference internal" href="../udf/introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DROP</span> <span class="pre">FUNCTION</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">all</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">ownership</span></code></p></td>
<td><p>Not all connectors support <a class="reference internal" href="../udf/introduction.html#udf-catalog"><span class="std std-ref">Catalog user-defined functions</span></a>.</p></td>
</tr>
</tbody>
</table>
<section id="visibility">
<span id="system-file-auth-visibility"></span><h4 id="visibility">Visibility<a class="headerlink" href="file-system-access-control.html#visibility" title="Link to this heading">#</a></h4>
<p>For a catalog, schema, or table to be visible in a <code class="docutils literal notranslate"><span class="pre">SHOW</span></code> command, the user
must have at least one permission on the item or any nested item. The nested
items do not need to already exist as any potential permission makes the item
visible. Specifically:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code>: Visible if user is the owner of any nested schema, has
permissions on any nested table or function, or has permissions to
set session properties in the catalog.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code>: Visible if the user is the owner of the schema, or has permissions
on any nested table or function.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">table</span></code>: Visible if the user has any permissions on the table.</p></li>
</ul>
</section>
<section id="catalog-rules">
<h4 id="catalog-rules">Catalog rules<a class="headerlink" href="file-system-access-control.html#catalog-rules" title="Link to this heading">#</a></h4>
<p>Each catalog rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against group names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regex to match against catalog name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): string indicating whether a user has access to the
catalog. This value can be <code class="docutils literal notranslate"><span class="pre">all</span></code>, <code class="docutils literal notranslate"><span class="pre">read-only</span></code> or <code class="docutils literal notranslate"><span class="pre">none</span></code>, and defaults to
<code class="docutils literal notranslate"><span class="pre">none</span></code>. Setting this value to <code class="docutils literal notranslate"><span class="pre">read-only</span></code> has the same behavior as the
<code class="docutils literal notranslate"><span class="pre">read-only</span></code> system access control plugin.</p></li>
</ul>
<p>In order for a rule to apply the username must match the regular expression
specified in <code class="docutils literal notranslate"><span class="pre">user</span></code> attribute.</p>
<p>For role names, a rule can be applied if at least one of the currently enabled
roles matches the <code class="docutils literal notranslate"><span class="pre">role</span></code> regular expression.</p>
<p>For group names, a rule can be applied if at least one group name of this user
matches the <code class="docutils literal notranslate"><span class="pre">group</span></code> regular expression.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">all</span></code> value for <code class="docutils literal notranslate"><span class="pre">allow</span></code> means these rules do not restrict access in any
way, but the schema and table rules can restrict access.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>By default, all users have access to the <code class="docutils literal notranslate"><span class="pre">system</span></code> catalog. You can
override this behavior by adding a rule.</p>
<p>Boolean <code class="docutils literal notranslate"><span class="pre">true</span></code> and <code class="docutils literal notranslate"><span class="pre">false</span></code> are also supported as legacy values for
<code class="docutils literal notranslate"><span class="pre">allow</span></code>, to support backwards compatibility.  <code class="docutils literal notranslate"><span class="pre">true</span></code> maps to <code class="docutils literal notranslate"><span class="pre">all</span></code>,
and <code class="docutils literal notranslate"><span class="pre">false</span></code> maps to <code class="docutils literal notranslate"><span class="pre">none</span></code>.</p>
</div>
<p>For example, if you want to allow only the role <code class="docutils literal notranslate"><span class="pre">admin</span></code> to access the
<code class="docutils literal notranslate"><span class="pre">mysql</span></code> and the <code class="docutils literal notranslate"><span class="pre">system</span></code> catalog, allow users from the <code class="docutils literal notranslate"><span class="pre">finance</span></code> and
<code class="docutils literal notranslate"><span class="pre">human_resources</span></code> groups access to <code class="docutils literal notranslate"><span class="pre">postgres</span></code> catalog, allow all users to
access the <code class="docutils literal notranslate"><span class="pre">hive</span></code> catalog, and deny all other access, you can use the
following rules:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"catalogs"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"(mysql|system)"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="s2">"all"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"finance|human_resources"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"postgres"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hive"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="s2">"all"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"alice"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"postgresql"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="s2">"read-only"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="s2">"none"</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>For group-based rules to match, users need to be assigned to groups by a
<a class="reference internal" href="../develop/group-provider.html"><span class="doc">Group provider</span></a>.</p>
</section>
<section id="schema-rules">
<h4 id="schema-rules">Schema rules<a class="headerlink" href="file-system-access-control.html#schema-rules" title="Link to this heading">#</a></h4>
<p>Each schema rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against group names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regex to match against catalog name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regex to match against schema name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">owner</span></code> (required): boolean indicating whether the user is to be considered
an owner of the schema. Defaults to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p></li>
</ul>
<p>For example, to provide ownership of all schemas to role <code class="docutils literal notranslate"><span class="pre">admin</span></code>, treat all
users as owners of the <code class="docutils literal notranslate"><span class="pre">default.default</span></code> schema and prevent user <code class="docutils literal notranslate"><span class="pre">guest</span></code>
from ownership of any schema, you can use the following rules:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"guest"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="table-rules">
<h4 id="table-rules">Table rules<a class="headerlink" href="file-system-access-control.html#table-rules" title="Link to this heading">#</a></h4>
<p>Each table rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against group names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regex to match against catalog name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regex to match against schema name. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">table</span></code> (optional): regex to match against table names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code> (required): zero or more of <code class="docutils literal notranslate"><span class="pre">SELECT</span></code>, <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>,
<code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNERSHIP</span></code>, <code class="docutils literal notranslate"><span class="pre">GRANT_SELECT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">columns</span></code> (optional): list of column constraints.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">filter</span></code> (optional): boolean filter expression for the table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">filter_environment</span></code> (optional): environment use during filter evaluation.</p></li>
</ul>
</section>
<section id="column-constraint">
<h4 id="column-constraint">Column constraint<a class="headerlink" href="file-system-access-control.html#column-constraint" title="Link to this heading">#</a></h4>
<p>These constraints can be used to restrict access to column data.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code>: name of the column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (optional): if false, column can not be accessed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mask</span></code> (optional): mask expression applied to column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mask_environment</span></code> (optional): environment use during mask evaluation.</p></li>
</ul>
</section>
<section id="filter-and-mask-environment">
<h4 id="filter-and-mask-environment">Filter and mask environment<a class="headerlink" href="file-system-access-control.html#filter-and-mask-environment" title="Link to this heading">#</a></h4>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): username for checking permission of subqueries in mask.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These rules do not apply to <code class="docutils literal notranslate"><span class="pre">information_schema</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">mask</span></code> can contain conditional expressions such as <code class="docutils literal notranslate"><span class="pre">IF</span></code> or <code class="docutils literal notranslate"><span class="pre">CASE</span></code>, which achieves conditional masking.</p>
</div>
<p>The example below defines the following table access policy:</p>
<ul class="simple">
<li><p>Role <code class="docutils literal notranslate"><span class="pre">admin</span></code> has all privileges across all tables and schemas</p></li>
<li><p>User <code class="docutils literal notranslate"><span class="pre">banned_user</span></code> has no privileges</p></li>
<li><p>All users have <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> privileges on <code class="docutils literal notranslate"><span class="pre">default.hr.employees</span></code>, but the
table is filtered to only the row for the current user.</p></li>
<li><p>All users have <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> privileges on all tables in the <code class="docutils literal notranslate"><span class="pre">default.default</span></code>
schema, except for the <code class="docutils literal notranslate"><span class="pre">address</span></code> column which is blocked, and <code class="docutils literal notranslate"><span class="pre">ssn</span></code> which
is masked.</p></li>
</ul>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"tables"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"INSERT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DELETE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"UPDATE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"OWNERSHIP"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"banned_user"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hr"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"table"</span><span class="p">:</span><span class="w"> </span><span class="s2">"employee"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"filter"</span><span class="p">:</span><span class="w"> </span><span class="s2">"user = current_user"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"filter_environment"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system_user"</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"table"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"columns"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">         </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"address"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">         </span><span class="p">},</span>
<span class="w">         </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SSN"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"mask"</span><span class="p">:</span><span class="w"> </span><span class="s2">"'XXX-XX-' + substring(credit_card, -4)"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"mask_environment"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system_user"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">         </span><span class="p">}</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="function-rules">
<span id="system-file-function-rules"></span><h4 id="function-rules">Function rules<a class="headerlink" href="file-system-access-control.html#function-rules" title="Link to this heading">#</a></h4>
<p>These rules control the ability of a user to create, drop, and execute functions.</p>
<p>When these rules are present, the authorization is based on the first matching
rule, processed from top to bottom. If no rules match, the authorization is
denied. If function rules are not present, only functions in<code class="docutils literal notranslate"><span class="pre">system.builtin</span></code> can
be executed.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Users always have access to functions in the <code class="docutils literal notranslate"><span class="pre">system.builtin</span></code> schema, and
you cannot override this behavior by adding a rule.</p>
</div>
<p>Each function rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regular expression to match against username.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regular expression to match against role names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regular expression to match against group names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regular expression to match against catalog name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regular expression to match against schema name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">function</span></code> (optional): regular expression to match against function names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code> (required): zero or more of <code class="docutils literal notranslate"><span class="pre">EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">GRANT_EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNERSHIP</span></code>.</p></li>
</ul>
<p>Care should be taken when granting permission to the <code class="docutils literal notranslate"><span class="pre">system</span></code> schema of a
catalog, as this is the schema Trino uses for table function such as <code class="docutils literal notranslate"><span class="pre">query</span></code>.
These table functions can be used to access or modify the underlying data of
the catalog.</p>
<p>The following example allows the <code class="docutils literal notranslate"><span class="pre">admin</span></code> user to execute <code class="docutils literal notranslate"><span class="pre">system.query</span></code> table function in
any catalog, and allows all users to create, drop, and execute functions (including
<code class="docutils literal notranslate"><span class="pre">SECURITY</span> <span class="pre">DEFINER</span></code> views) in the <code class="docutils literal notranslate"><span class="pre">hive.function</span></code> schema:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"functions"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"function"</span><span class="p">:</span><span class="w"> </span><span class="s2">"query"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hive"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"function"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"GRANT_EXECUTE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"OWNERSHIP"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="procedure-rules">
<span id="system-file-procedure-rules"></span><h4 id="procedure-rules">Procedure rules<a class="headerlink" href="file-system-access-control.html#procedure-rules" title="Link to this heading">#</a></h4>
<p>These rules control the ability of a user to execute procedures using the
<a class="reference internal" href="../sql/call.html"><span class="doc std std-doc">CALL</span></a> statement.</p>
<p>Procedures are used for administrative operations on a specific catalog, such as
registering external tables or flushing the connector’s cache. Available
procedures are detailed in the connector documentation pages.</p>
<p>When procedure rules are present, the authorization is based on the first
matching rule, processed from top to bottom. If no rules match, the
authorization is denied. If procedure rules are not present, only procedures in
<code class="docutils literal notranslate"><span class="pre">system.builtin</span></code> can be executed.</p>
<p>Each procedure rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regular expression to match against username.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regular expression to match against role names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regular expression to match against group names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regular expression to match against catalog name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regular expression to match against schema name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">procedure</span></code> (optional): regular expression to match against procedure names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code> (required): zero or more of <code class="docutils literal notranslate"><span class="pre">EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">GRANT_EXECUTE</span></code>.</p></li>
</ul>
<p>The following example allows the <code class="docutils literal notranslate"><span class="pre">admin</span></code> user to execute and grant execution
rights to call <code class="docutils literal notranslate"><span class="pre">register_table</span></code> and <code class="docutils literal notranslate"><span class="pre">unregister_table</span></code> in the <code class="docutils literal notranslate"><span class="pre">system</span></code> schema of
a catalog called  <code class="docutils literal notranslate"><span class="pre">delta</span></code>, that uses the <a class="reference internal" href="../connector/delta-lake.html"><span class="doc std std-doc">Delta Lake
connector</span></a>. It allows all users to execute the
<code class="docutils literal notranslate"><span class="pre">delta.sytem.vacuum</span></code> procedure.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"procedures"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"delta"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"procedure"</span><span class="p">:</span><span class="w"> </span><span class="s2">"register_table|unregister_table"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span><span class="p">,</span>
<span class="w">        </span><span class="s2">"GRANT_EXECUTE"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"delta"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"procedure"</span><span class="p">:</span><span class="w"> </span><span class="s2">"vacuum"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="table-procedure-rules">
<span id="system-file-table-procedure-rules"></span><h4 id="table-procedure-rules">Table procedure rules<a class="headerlink" href="file-system-access-control.html#table-procedure-rules" title="Link to this heading">#</a></h4>
<p>Table procedures are executed using the
<a class="reference internal" href="../sql/alter-table.html#alter-table-execute"><span class="std std-ref">ALTER TABLE … EXECUTE</span></a> syntax.</p>
<p>File-based access control does not support privileges for table procedures and
therefore all are effectively allowed.</p>
</section>
<section id="verify-configuration">
<span id="verify-rules"></span><h4 id="verify-configuration">Verify configuration<a class="headerlink" href="file-system-access-control.html#verify-configuration" title="Link to this heading">#</a></h4>
<p>To verify the system-access control file is configured properly, set the
rules to completely block access to all users of the system:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"catalogs"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="s2">"none"</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Restart your cluster to activate the rules for your cluster. With the
Trino <a class="reference internal" href="../client/cli.html"><span class="doc">CLI</span></a> run a query to test authorization:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>trino&gt; SELECT * FROM system.runtime.nodes;
Query 20200824_183358_00000_c62aw failed: Access Denied: Cannot access catalog system
</pre></div>
</div>
<p>Remove these rules and restart the Trino cluster.</p>
</section>
</section>
<section id="session-property-rules">
<span id="system-file-auth-session-property"></span><h3 id="session-property-rules">Session property rules<a class="headerlink" href="file-system-access-control.html#session-property-rules" title="Link to this heading">#</a></h3>
<p>These rules control the ability of a user to set system and catalog session
properties. The user is granted or denied access, based on the first matching
rule, read from top to bottom. If no rules are specified, all users are allowed
set any session property. If no rule matches, setting the session property is
denied. System session property rules are composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against group names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">property</span></code> (optional): regex to match against the property name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): boolean indicating if the setting the session
property should be allowed.</p></li>
</ul>
<p>The catalog session property rules have the additional field:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">catalog</span></code> (optional): regex to match against catalog name. Defaults to
<code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
</ul>
<p>The example below defines the following table access policy:</p>
<ul class="simple">
<li><p>Role <code class="docutils literal notranslate"><span class="pre">admin</span></code> can set all session property</p></li>
<li><p>User <code class="docutils literal notranslate"><span class="pre">banned_user</span></code> can not set any session properties</p></li>
<li><p>All users can set the <code class="docutils literal notranslate"><span class="pre">resource_overcommit</span></code> system session property, and the
<code class="docutils literal notranslate"><span class="pre">bucket_execution_enabled</span></code> session property in the <code class="docutils literal notranslate"><span class="pre">hive</span></code> catalog.</p></li>
</ul>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"system_session_properties"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"banned_user"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"property"</span><span class="p">:</span><span class="w"> </span><span class="s2">"resource_overcommit"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">"catalog_session_properties"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"banned_user"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"catalog"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hive"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"property"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bucket_execution_enabled"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="query-rules">
<span id="id1"></span><h3 id="query-rules">Query rules<a class="headerlink" href="file-system-access-control.html#query-rules" title="Link to this heading">#</a></h3>
<p>These rules control the ability of a user to execute, view, or kill a query. The
user is granted or denied access, based on the first matching rule read from top
to bottom. If no rules are specified, all users are allowed to execute queries,
and to view or kill queries owned by any user. If no rule matches, query
management is denied. Each rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against group names. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">queryOwner</span></code> (optional): regex to match against the query owner name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): set of query permissions granted to user. Values:
<code class="docutils literal notranslate"><span class="pre">execute</span></code>, <code class="docutils literal notranslate"><span class="pre">view</span></code>, <code class="docutils literal notranslate"><span class="pre">kill</span></code></p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Users always have permission to view or kill their own queries.</p>
<p>A rule that includes <code class="docutils literal notranslate"><span class="pre">queryOwner</span></code> may not include the <code class="docutils literal notranslate"><span class="pre">execute</span></code> access mode.
Queries are only owned by a user once their execution has begun.</p>
</div>
<p>For example, if you want to allow the role <code class="docutils literal notranslate"><span class="pre">admin</span></code> full query access, allow
the user <code class="docutils literal notranslate"><span class="pre">alice</span></code> to execute and kill queries, allow members of the group
<code class="docutils literal notranslate"><span class="pre">contractors</span></code> to view queries owned by users <code class="docutils literal notranslate"><span class="pre">alice</span></code> or <code class="docutils literal notranslate"><span class="pre">dave</span></code>, allow any
user to execute queries, and deny all other access, you can use the following
rules:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"queries"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"execute"</span><span class="p">,</span><span class="w"> </span><span class="s2">"kill"</span><span class="p">,</span><span class="w"> </span><span class="s2">"view"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"alice"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"execute"</span><span class="p">,</span><span class="w"> </span><span class="s2">"kill"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"contractors"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"queryOwner"</span><span class="p">:</span><span class="w"> </span><span class="s2">"alice|dave"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"view"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"execute"</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="impersonation-rules">
<span id="system-file-auth-impersonation-rules"></span><h3 id="impersonation-rules">Impersonation rules<a class="headerlink" href="file-system-access-control.html#impersonation-rules" title="Link to this heading">#</a></h3>
<p>These rules control the ability of a user to impersonate another user. In
some environments it is desirable for an administrator (or managed system) to
run queries on behalf of other users. In these cases, the administrator
authenticates using their credentials, and then submits a query as a different
user. When the user context is changed, Trino verifies that the administrator
is authorized to run queries as the target user.</p>
<p>When these rules are present, the authorization is based on the first matching
rule, processed from top to bottom. If no rules match, the authorization is
denied. If impersonation rules are not present but the legacy principal rules
are specified, it is assumed impersonation access control is being handled by
the principal rules, so impersonation is allowed. If neither impersonation nor
principal rules are defined, impersonation is not allowed.</p>
<p>Each impersonation rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">original_user</span></code> (optional): regex to match against the user requesting the
impersonation. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">original_role</span></code> (optional): regex to match against role names of the
requesting impersonation. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">new_user</span></code> (required): regex to match against the user to impersonate. Can
contain references to subsequences captured during the match against
<em>original_user</em>, and each reference is replaced by the result of evaluating
the corresponding group respectively.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (optional): boolean indicating if the authentication should be
allowed. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></li>
</ul>
<p>The impersonation rules are a bit different from the other rules: The attribute
<code class="docutils literal notranslate"><span class="pre">new_user</span></code> is required to not accidentally prevent more access than intended.
Doing so it was possible to make the attribute <code class="docutils literal notranslate"><span class="pre">allow</span></code> optional.</p>
<p>The following example allows the <code class="docutils literal notranslate"><span class="pre">admin</span></code> role, to impersonate any user, except
for <code class="docutils literal notranslate"><span class="pre">bob</span></code>. It also allows any user to impersonate the <code class="docutils literal notranslate"><span class="pre">test</span></code> user. It also
allows a user in the form <code class="docutils literal notranslate"><span class="pre">team_backend</span></code> to impersonate the
<code class="docutils literal notranslate"><span class="pre">team_backend_sandbox</span></code> user, but not arbitrary users:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"impersonation"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"original_role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"original_role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"original_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"original_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"team_(.*)"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"team_$1_sandbox"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="principal-rules">
<span id="system-file-auth-principal-rules"></span><h3 id="principal-rules">Principal rules<a class="headerlink" href="file-system-access-control.html#principal-rules" title="Link to this heading">#</a></h3>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Principal rules are deprecated. Instead, use <a class="reference internal" href="user-mapping.html"><span class="doc">User mapping</span></a>
which specifies how a complex authentication username is mapped to a simple
username for Trino, and impersonation rules defined above.</p>
</div>
<p>These rules serve to enforce a specific matching between a principal and a
specified username. The principal is granted authorization as a user, based
on the first matching rule read from top to bottom. If no rules are specified,
no checks are performed. If no rule matches, user authorization is denied.
Each rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">principal</span></code> (required): regex to match and group against principal.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. If matched, it
grants or denies the authorization based on the value of <code class="docutils literal notranslate"><span class="pre">allow</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">principal_to_user</span></code> (optional): replacement string to substitute against
principal. If the result of the substitution is same as the username, it
grants or denies the authorization based on the value of <code class="docutils literal notranslate"><span class="pre">allow</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): boolean indicating whether a principal can be authorized
as a user.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You would at least specify one criterion in a principal rule. If you specify
both criteria in a principal rule, it returns the desired conclusion when
either of criteria is satisfied.</p>
</div>
<p>The following implements an exact matching of the full principal name for LDAP
and Kerberos authentication:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"principals"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"principal"</span><span class="p">:</span><span class="w"> </span><span class="s2">"(.*)"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"principal_to_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"$1"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"principal"</span><span class="p">:</span><span class="w"> </span><span class="s2">"([^/]+)(/.*)?@.*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"principal_to_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"$1"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p>If you want to allow users to use the exact same name as their Kerberos
principal name, and allow <code class="docutils literal notranslate"><span class="pre">alice</span></code> and <code class="docutils literal notranslate"><span class="pre">bob</span></code> to use a group principal named
as <code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="e087928f9590a08598818d908c85ce8e8594">[email&#160;protected]</a></span></code>, you can use the following rules.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"principals"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"principal"</span><span class="p">:</span><span class="w"> </span><span class="s2">"([^/]+)/?.*@example.net"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"principal_to_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"$1"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"principal"</span><span class="p">:</span><span class="w"> </span><span class="s2">"<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="8cebfee3f9fccce9f4ede1fce0e9a2e2e9f8">[email&#160;protected]</a>"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"alice|bob"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="system-information-rules">
<span id="system-file-auth-system-information"></span><h3 id="system-information-rules">System information rules<a class="headerlink" href="file-system-access-control.html#system-information-rules" title="Link to this heading">#</a></h3>
<p>These rules specify which users can access the system information management
interface. System information access includes the following aspects:</p>
<ul class="simple">
<li><p>Read access to sensitive information from REST endpoints, such as <code class="docutils literal notranslate"><span class="pre">/v1/node</span></code>
and <code class="docutils literal notranslate"><span class="pre">/v1/thread</span></code>.</p></li>
<li><p>Read access with the <a class="reference internal" href="../functions/system.html"><span class="doc">system information functions</span></a>.</p></li>
<li><p>Read access with the <a class="reference internal" href="../connector/system.html"><span class="doc">System connector</span></a>.</p></li>
<li><p>Write access to trigger <a class="reference internal" href="../admin/graceful-shutdown.html"><span class="doc">Graceful shutdown</span></a>.</p></li>
</ul>
<p>The following REST endpoints are always public and not affected by these rules:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">GET</span> <span class="pre">/v1/info</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GET</span> <span class="pre">/v1/info/state</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GET</span> <span class="pre">/v1/status</span></code></p></li>
</ul>
<p>The user is granted or denied access based on the first matching
rule read from top to bottom. If no rules are specified, all access to system
information is denied. If no rule matches, system access is denied. Each rule is
composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): regex to match against role. If matched, it
grants or denies the authorization based on the value of <code class="docutils literal notranslate"><span class="pre">allow</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username. If matched, it
grants or denies the authorization based on the value of <code class="docutils literal notranslate"><span class="pre">allow</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): set of access permissions granted to user. Values:
<code class="docutils literal notranslate"><span class="pre">read</span></code>, <code class="docutils literal notranslate"><span class="pre">write</span></code></p></li>
</ul>
<p>The following configuration provides and example:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"system_information"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"read"</span><span class="p">,</span><span class="w"> </span><span class="s2">"write"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"alice"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"read"</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<ul class="simple">
<li><p>All users with the <code class="docutils literal notranslate"><span class="pre">admin</span></code> role have read and write access to system
information. This includes the ability to trigger
<a class="reference internal" href="../admin/graceful-shutdown.html"><span class="doc">Graceful shutdown</span></a>.</p></li>
<li><p>The user <code class="docutils literal notranslate"><span class="pre">alice</span></code> can read system information.</p></li>
<li><p>All other users and roles are denied access to system information.</p></li>
</ul>
<p>A fixed user can be set for management interfaces using the <code class="docutils literal notranslate"><span class="pre">management.user</span></code>
configuration property.  When this is configured, system information rules must
still be set to authorize this user to read or write to management information.
The fixed management user only applies to HTTP by default. To enable the fixed
user over HTTPS, set the <code class="docutils literal notranslate"><span class="pre">management.user.https-enabled</span></code> configuration
property.</p>
</section>
<section id="authorization-rules">
<span id="system-file-auth-authorization"></span><h3 id="authorization-rules">Authorization rules<a class="headerlink" href="file-system-access-control.html#authorization-rules" title="Link to this heading">#</a></h3>
<p>These rules control the ability of how owner of schema, table or view can
be altered. These rules are applicable to commands like:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span><span class="w"> </span><span class="k">SCHEMA</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">AUTHORIZATION</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">USER</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">ROLE</span><span class="w"> </span><span class="k">role</span><span class="w"> </span><span class="p">)</span>
<span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">AUTHORIZATION</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">USER</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">ROLE</span><span class="w"> </span><span class="k">role</span><span class="w"> </span><span class="p">)</span>
<span class="k">ALTER</span><span class="w"> </span><span class="k">VIEW</span><span class="w"> </span><span class="n">name</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="k">AUTHORIZATION</span><span class="w"> </span><span class="p">(</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">USER</span><span class="w"> </span><span class="k">user</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="k">ROLE</span><span class="w"> </span><span class="k">role</span><span class="w"> </span><span class="p">)</span>
</pre></div>
</div>
<p>When these rules are present, the authorization is based on the first matching
rule, processed from top to bottom. If no rules match, the authorization is
denied.</p>
<p>Notice that in order to execute <code class="docutils literal notranslate"><span class="pre">ALTER</span></code> command on schema, table or view user requires <code class="docutils literal notranslate"><span class="pre">OWNERSHIP</span></code>
privilege.</p>
<p>Each authorization rule is composed of the following fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">original_user</span></code> (optional): regex to match against the user requesting the
authorization. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">original_group</span></code> (optional): regex to match against group names of the
requesting authorization. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">original_role</span></code> (optional): regex to match against role names of the
requesting authorization. Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">new_user</span></code> (optional): regex to match against the new owner user of the schema, table or view.
By default it does not match.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">new_role</span></code> (optional): regex to match against the new owner role of the schema, table or view.
By default it does not match.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (optional): boolean indicating if the authentication should be
allowed. Defaults to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></li>
</ul>
<p>Notice that <code class="docutils literal notranslate"><span class="pre">new_user</span></code> and <code class="docutils literal notranslate"><span class="pre">new_role</span></code> are optional, however it is required to provide at least one of them.</p>
<p>The following example allows the <code class="docutils literal notranslate"><span class="pre">admin</span></code> role, to change owner of any schema, table or view
to any user, except to``bob``.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"authorization"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"original_role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bob"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"original_role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"new_user"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"new_role"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"tables"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"OWNERSHIP"</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="catalog-level-access-control-files">
<span id="catalog-file-based-access-control"></span><h2 id="catalog-level-access-control-files">Catalog-level access control files<a class="headerlink" href="file-system-access-control.html#catalog-level-access-control-files" title="Link to this heading">#</a></h2>
<p>You can create JSON files for individual catalogs that define authorization
rules specific to that catalog. To enable catalog-level access control files,
add a connector-specific catalog configuration property that sets the
authorization type to <code class="docutils literal notranslate"><span class="pre">FILE</span></code> and the <code class="docutils literal notranslate"><span class="pre">security.config-file</span></code> catalog
configuration property that specifies the JSON rules file.</p>
<p>For example, the following Iceberg catalog configuration properties use the
<code class="docutils literal notranslate"><span class="pre">rules.json</span></code> file for catalog-level access control:</p>
<div class="highlight-properties notranslate"><div class="highlight"><pre><span></span><span class="na">iceberg.security</span><span class="o">=</span><span class="s">FILE</span>
<span class="na">security.config-file</span><span class="o">=</span><span class="s">etc/catalog/rules.json</span>
</pre></div>
</div>
<p>Catalog-level access control files are supported on a per-connector basis, refer
to the connector documentation for more information.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These rules do not apply to system-defined tables in the
<code class="docutils literal notranslate"><span class="pre">information_schema</span></code> schema.</p>
</div>
<section id="configure-a-catalog-rules-file">
<h3 id="configure-a-catalog-rules-file">Configure a catalog rules file<a class="headerlink" href="file-system-access-control.html#configure-a-catalog-rules-file" title="Link to this heading">#</a></h3>
<p>The configuration file is specified in JSON format. This file is composed of
the following sections, each of which is a list of rules that are processed in
order from top to bottom:</p>
<ol class="arabic simple">
<li><p><code class="docutils literal notranslate"><span class="pre">schemas</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tables</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">session_properties</span></code></p></li>
</ol>
<p>The user is granted the privileges from the first matching rule. All regexes
default to <code class="docutils literal notranslate"><span class="pre">.*</span></code> if not specified.</p>
<section id="id2">
<h4 id="id2">Schema rules<a class="headerlink" href="file-system-access-control.html#id2" title="Link to this heading">#</a></h4>
<p>These rules govern who is considered an owner of a schema.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against every user group the user belongs
to.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regex to match against schema name.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">owner</span></code> (required): boolean indicating ownership.</p></li>
</ul>
</section>
<section id="id3">
<h4 id="id3">Table rules<a class="headerlink" href="file-system-access-control.html#id3" title="Link to this heading">#</a></h4>
<p>These rules govern the privileges granted on specific tables.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against every user group the user belongs
to.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regex to match against schema name.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">table</span></code> (optional): regex to match against table name.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code> (required): zero or more of <code class="docutils literal notranslate"><span class="pre">SELECT</span></code>, <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>,
<code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNERSHIP</span></code>, <code class="docutils literal notranslate"><span class="pre">GRANT_SELECT</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">columns</span></code> (optional): list of column constraints.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">filter</span></code> (optional): boolean filter expression for the table.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">filter_environment</span></code> (optional): environment used during filter evaluation.</p></li>
</ul>
<section id="column-constraints">
<h5 id="column-constraints">Column constraints<a class="headerlink" href="file-system-access-control.html#column-constraints" title="Link to this heading">#</a></h5>
<p>These constraints can be used to restrict access to column data.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code>: name of the column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (optional): if false, column can not be accessed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mask</span></code> (optional): mask expression applied to column.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mask_environment</span></code> (optional): environment use during mask evaluation.</p></li>
</ul>
</section>
<section id="filter-environment-and-mask-environment">
<h5 id="filter-environment-and-mask-environment">Filter environment and mask environment<a class="headerlink" href="file-system-access-control.html#filter-environment-and-mask-environment" title="Link to this heading">#</a></h5>
<p>These rules apply to <code class="docutils literal notranslate"><span class="pre">filter_environment</span></code> and <code class="docutils literal notranslate"><span class="pre">mask_environment</span></code>.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): username for checking permission of subqueries in a mask.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">mask</span></code> can contain conditional expressions such as <code class="docutils literal notranslate"><span class="pre">IF</span></code> or <code class="docutils literal notranslate"><span class="pre">CASE</span></code>, which achieves conditional masking.</p>
</div>
</section>
</section>
<section id="id4">
<h4 id="id4">Function rules<a class="headerlink" href="file-system-access-control.html#id4" title="Link to this heading">#</a></h4>
<p>These rules control the ability of a user to create, drop, and execute functions.</p>
<p>When these rules are present, the authorization is based on the first matching
rule, processed from top to bottom. If no rules match, the authorization is
denied. If function rules are not present, access is not allowed.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regular expression to match against username.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regular expression to match against group names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">schema</span></code> (optional): regular expression to match against schema name.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">function</span></code> (optional): regular expression to match against function names.
Defaults to <code class="docutils literal notranslate"><span class="pre">.*</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code> (required): zero or more of <code class="docutils literal notranslate"><span class="pre">EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">GRANT_EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNERSHIP</span></code>.</p></li>
</ul>
<p>Care should be taken when granting permission to the <code class="docutils literal notranslate"><span class="pre">system</span></code> schema of a
catalog, as this is the schema Trino uses for table function such as <code class="docutils literal notranslate"><span class="pre">query</span></code>.
These table functions can be used to access or modify the underlying data of
the catalog.</p>
<p>The following example allows the <code class="docutils literal notranslate"><span class="pre">admin</span></code> user to execute <code class="docutils literal notranslate"><span class="pre">system.query</span></code> table function from
any catalog, and all users to create, drop, and execute functions (including from views)
in the <code class="docutils literal notranslate"><span class="pre">function</span></code> schema of this catalog:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"functions"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"function"</span><span class="p">:</span><span class="w"> </span><span class="s2">"query"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"function"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">"EXECUTE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"GRANT_EXECUTE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"OWNERSHIP"</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="id5">
<h4 id="id5">Session property rules<a class="headerlink" href="file-system-access-control.html#id5" title="Link to this heading">#</a></h4>
<p>These rules govern who may set session properties.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user</span></code> (optional): regex to match against username.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">group</span></code> (optional): regex to match against every user group the user belongs
to.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">property</span></code> (optional): regex to match against session property name.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">allow</span></code> (required): boolean indicating whether this session property may be
set.</p></li>
</ul>
</section>
</section>
<section id="example">
<h3 id="example">Example<a class="headerlink" href="file-system-access-control.html#example" title="Link to this heading">#</a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">"schemas"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"group"</span><span class="p">:</span><span class="w"> </span><span class="s2">"finance|human_resources"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"employees"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"guest"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"owner"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"tables"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"INSERT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DELETE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"UPDATE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"OWNERSHIP"</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"banned_user"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hr"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"table"</span><span class="p">:</span><span class="w"> </span><span class="s2">"employee"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"filter"</span><span class="p">:</span><span class="w"> </span><span class="s2">"user = current_user"</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"default"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"table"</span><span class="p">:</span><span class="w"> </span><span class="s2">".*"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"privileges"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SELECT"</span><span class="p">],</span>
<span class="w">      </span><span class="nt">"columns"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">         </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"address"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">         </span><span class="p">},</span>
<span class="w">         </span><span class="p">{</span>
<span class="w">            </span><span class="nt">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ssn"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"mask"</span><span class="p">:</span><span class="w"> </span><span class="s2">"'XXX-XX-' + substring(credit_card, -4)"</span><span class="p">,</span>
<span class="w">            </span><span class="nt">"mask_environment"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">         </span><span class="p">}</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">"session_properties"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"property"</span><span class="p">:</span><span class="w"> </span><span class="s2">"force_local_scheduling"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"property"</span><span class="p">:</span><span class="w"> </span><span class="s2">"max_split_size"</span><span class="p">,</span>
<span class="w">      </span><span class="nt">"allow"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="built-in-system-access-control.html" title="System access control"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> System access control </span>
              </div>
            </a>
          
          
            <a href="opa-access-control.html" title="Open Policy Agent access control"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Open Policy Agent access control </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>