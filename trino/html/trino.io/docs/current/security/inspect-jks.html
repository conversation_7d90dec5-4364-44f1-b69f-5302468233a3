<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>JKS files &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="inspect-jks.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Authentication types" href="authentication-types.html" />
    <link rel="prev" title="PEM files" href="inspect-pem.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="inspect-jks.html#security/inspect-jks" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> JKS files </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> JKS files </label>
    
      <a href="inspect-jks.html#" class="md-nav__link md-nav__link--active">JKS files</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="inspect-jks.html#inspect-and-validate-keystore" class="md-nav__link">Inspect and validate keystore</a>
        </li>
        <li class="md-nav__item"><a href="inspect-jks.html#extra-add-pem-to-keystore" class="md-nav__link">Extra: add PEM to keystore</a>
        </li>
        <li class="md-nav__item"><a href="inspect-jks.html#extra-java-truststores" class="md-nav__link">Extra: Java truststores</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="kerberos.html" class="md-nav__link">Kerberos authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="inspect-jks.html#inspect-and-validate-keystore" class="md-nav__link">Inspect and validate keystore</a>
        </li>
        <li class="md-nav__item"><a href="inspect-jks.html#extra-add-pem-to-keystore" class="md-nav__link">Extra: add PEM to keystore</a>
        </li>
        <li class="md-nav__item"><a href="inspect-jks.html#extra-java-truststores" class="md-nav__link">Extra: Java truststores</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="jks-files">
<h1 id="security-inspect-jks--page-root">JKS files<a class="headerlink" href="inspect-jks.html#security-inspect-jks--page-root" title="Link to this heading">#</a></h1>
<p>This topic describes how to validate a <a class="reference internal" href="../glossary.html#glossjks"><span class="std std-ref">Java keystore (JKS)</span></a>
file used to configure <a class="reference internal" href="tls.html"><span class="doc">TLS and HTTPS</span></a>.</p>
<p>The Java KeyStore (JKS) system is provided as part of your Java installation.
Private keys and certificates for your server are stored in a <em>keystore</em> file.
The JKS system supports both PKCS #12 <code class="docutils literal notranslate"><span class="pre">.p12</span></code> files as well as legacy
keystore <code class="docutils literal notranslate"><span class="pre">.jks</span></code> files.</p>
<p>The keystore file itself is always password-protected. The keystore file can
have more than one key in the same file, each addressed by its <strong>alias</strong>
name.</p>
<p>If you receive a keystore file from your site’s network admin group, verify that
it shows the correct information for your Trino cluster, as described next.</p>
<section id="inspect-and-validate-keystore">
<span id="troubleshooting-keystore"></span><h2 id="inspect-and-validate-keystore">Inspect and validate keystore<a class="headerlink" href="inspect-jks.html#inspect-and-validate-keystore" title="Link to this heading">#</a></h2>
<p>Inspect the keystore file to make sure it contains the correct information for
your Trino server. Use the <code class="docutils literal notranslate"><span class="pre">keytool</span></code> command, which is installed as part of
your Java installation, to retrieve information from your keystore file:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>keytool -list -v -keystore yourKeystore.jks
</pre></div>
</div>
<p>Keystores always require a password. If not provided on the <code class="docutils literal notranslate"><span class="pre">keytool</span></code> command
line, <code class="docutils literal notranslate"><span class="pre">keytool</span></code> prompts for the password.</p>
<p>Independent of the keystore’s password, it is possible that an individual key
has its own password. It is easiest to make sure these passwords are the same.
If the JKS key inside the keystore has a different password, you are prompted
twice.</p>
<p>In the output of the <code class="docutils literal notranslate"><span class="pre">keytool</span> <span class="pre">-list</span></code> command, look for:</p>
<ul>
<li><p>The keystore may contain either a private key (<code class="docutils literal notranslate"><span class="pre">Entry</span> <span class="pre">type:</span> <span class="pre">PrivateKeyEntry</span></code>) or certificate (<code class="docutils literal notranslate"><span class="pre">Entry</span> <span class="pre">type:</span> <span class="pre">trustedCertEntry</span></code>) or both.</p></li>
<li><p>Modern browsers now enforce 398 days as the maximum validity period for a
certificate. Look for the <code class="docutils literal notranslate"><span class="pre">Valid</span> <span class="pre">from</span> <span class="pre">...</span> <span class="pre">until</span></code> entry, and make sure the
time span does not exceed 398 days.</p></li>
<li><p>Modern browsers and clients require the <strong>SubjectAlternativeName</strong> (SAN)
field. Make sure this shows the DNS name of your server, such as
<code class="docutils literal notranslate"><span class="pre">DNS:cluster.example.com</span></code>. Certificates without SANs are not
supported.</p>
<p>Example:</p>
</li>
</ul>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>SubjectAlternativeName [
    DNSName:  cluster.example.com
]
</pre></div>
</div>
<p>If your keystore shows valid information for your cluster, proceed to configure
the Trino server, as described in <a class="reference internal" href="tls.html#cert-placement"><span class="std std-ref">Place the certificate file</span></a> and
<a class="reference internal" href="tls.html#configure-https"><span class="std std-ref">Configure the coordinator</span></a>.</p>
<p>The rest of this page describes additional steps that may apply in certain
circumstances.</p>
</section>
<section id="extra-add-pem-to-keystore">
<span id="import-to-keystore"></span><h2 id="extra-add-pem-to-keystore">Extra: add PEM to keystore<a class="headerlink" href="inspect-jks.html#extra-add-pem-to-keystore" title="Link to this heading">#</a></h2>
<p>Your site may have standardized on using JKS semantics for all servers. If a
vendor sends you a PEM-encoded certificate file for your Trino server, you can
import it into a keystore with a command like the following. Consult <code class="docutils literal notranslate"><span class="pre">keytool</span></code>
references for different options.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>keytool<span class="w"> </span>-trustcacerts<span class="w"> </span>-import<span class="w"> </span>-alias<span class="w"> </span>cluster<span class="w"> </span>-file<span class="w"> </span>localhost.pem<span class="w"> </span>-keystore<span class="w"> </span>localkeys.jks
</pre></div>
</div>
<p>If the specified keystore file exists, <code class="docutils literal notranslate"><span class="pre">keytool</span></code> prompts for its password. If
you are creating a new keystore, <code class="docutils literal notranslate"><span class="pre">keytool</span></code> prompts for a new password, then
prompts you to confirm the same password. <code class="docutils literal notranslate"><span class="pre">keytool</span></code> shows you the
contents of the key being added, similar to the <code class="docutils literal notranslate"><span class="pre">keytool</span> <span class="pre">-list</span></code> format, then
prompts:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Trust this certificate? [no]:
</pre></div>
</div>
<p>Type <code class="docutils literal notranslate"><span class="pre">yes</span></code> to add the PEM certificate to the keystore.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">alias</span></code> name is an arbitrary string used as a handle for the certificate
you are adding. A keystore can contain multiple keys and certs, so <code class="docutils literal notranslate"><span class="pre">keytool</span></code>
uses the alias to address individual entries.</p>
</section>
<section id="extra-java-truststores">
<span id="cli-java-truststore"></span><h2 id="extra-java-truststores">Extra: Java truststores<a class="headerlink" href="inspect-jks.html#extra-java-truststores" title="Link to this heading">#</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Remember that there may be no need to identify a local truststore when
directly using a signed PEM-encoded certificate, independent of a keystore.
PEM certs can contain the server’s private key and the certificate chain all
the way back to a recognized CA.</p>
</div>
<p>Truststore files contain a list of <a class="reference internal" href="../glossary.html#glossca"><span class="std std-ref">Certificate Authorities</span></a>
trusted by Java to validate the private keys of servers, plus a list of the
certificates of trusted TLS servers. The standard Java-provided truststore file,
<code class="docutils literal notranslate"><span class="pre">cacerts</span></code>, is part of your Java installation in a standard location.</p>
<p>Keystores normally rely on the default location of the system truststore, which
therefore does not need to be configured.</p>
<p>However, there are cases in which you need to use an alternate truststore. For
example, if your site relies on the JKS system, your network managers may have
appended site-specific, local CAs to the standard list, to validate locally
signed keys.</p>
<p>If your server must use a custom truststore, identify its location in the
server’s config properties file. For example:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.https.truststore.path=/mnt/shared/certs/localcacerts
http-server.https.truststore.key=&lt;truststore-password&gt;
</pre></div>
</div>
<p>If connecting clients such as browsers or the Trino CLI must be separately
configured, contact your site’s network administrators for assistance.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="inspect-pem.html" title="PEM files"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> PEM files </span>
              </div>
            </a>
          
          
            <a href="authentication-types.html" title="Authentication types"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Authentication types </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>