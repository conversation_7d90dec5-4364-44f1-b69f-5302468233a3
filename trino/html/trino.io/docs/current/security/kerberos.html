<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Kerberos authentication &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="kerberos.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Certificate authentication" href="certificate.html" />
    <link rel="prev" title="OAuth 2.0 authentication" href="oauth2.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="kerberos.html#security/kerberos" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Kerberos authentication </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Security overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tls.html" class="md-nav__link">TLS and HTTPS</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-pem.html" class="md-nav__link">PEM files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="inspect-jks.html" class="md-nav__link">JKS files</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="authentication-types.html" class="md-nav__link">Authentication types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-file.html" class="md-nav__link">Password file authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ldap.html" class="md-nav__link">LDAP authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="salesforce.html" class="md-nav__link">Salesforce authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="oauth2.html" class="md-nav__link">OAuth 2.0 authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Kerberos authentication </label>
    
      <a href="kerberos.html#" class="md-nav__link md-nav__link--active">Kerberos authentication</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kerberos.html#environment-configuration" class="md-nav__link">Environment configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#kerberos-services" class="md-nav__link">Kerberos services</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#mit-kerberos-configuration" class="md-nav__link">MIT Kerberos configuration</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#kerberos-principals-and-keytab-files" class="md-nav__link">Kerberos principals and keytab files</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#configuration-for-tls" class="md-nav__link">Configuration for TLS</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#system-access-control-plugin" class="md-nav__link">System access control plugin</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#trino-coordinator-node-configuration" class="md-nav__link">Trino coordinator node configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#config-properties" class="md-nav__link">config.properties</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#access-control-properties" class="md-nav__link">access-control.properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#user-mapping" class="md-nav__link">User mapping</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#troubleshooting" class="md-nav__link">Troubleshooting</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#kerberos-verification" class="md-nav__link">Kerberos verification</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#java-keystore-file-verification" class="md-nav__link">Java keystore file verification</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#additional-kerberos-debugging-information" class="md-nav__link">Additional Kerberos debugging information</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#additional-resources" class="md-nav__link">Additional resources</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate.html" class="md-nav__link">Certificate authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="jwt.html" class="md-nav__link">JWT authentication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="user-mapping.html" class="md-nav__link">User mapping</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-file.html" class="md-nav__link">File group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="built-in-system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="file-system-access-control.html" class="md-nav__link">File-based access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="opa-access-control.html" class="md-nav__link">Open Policy Agent access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="ranger-access-control.html" class="md-nav__link">Ranger access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="internal-communication.html" class="md-nav__link">Secure internal communication</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="secrets.html" class="md-nav__link">Secrets</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="kerberos.html#environment-configuration" class="md-nav__link">Environment configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#kerberos-services" class="md-nav__link">Kerberos services</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#mit-kerberos-configuration" class="md-nav__link">MIT Kerberos configuration</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#kerberos-principals-and-keytab-files" class="md-nav__link">Kerberos principals and keytab files</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#configuration-for-tls" class="md-nav__link">Configuration for TLS</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#system-access-control-plugin" class="md-nav__link">System access control plugin</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#trino-coordinator-node-configuration" class="md-nav__link">Trino coordinator node configuration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#config-properties" class="md-nav__link">config.properties</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#access-control-properties" class="md-nav__link">access-control.properties</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#user-mapping" class="md-nav__link">User mapping</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#troubleshooting" class="md-nav__link">Troubleshooting</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="kerberos.html#kerberos-verification" class="md-nav__link">Kerberos verification</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#java-keystore-file-verification" class="md-nav__link">Java keystore file verification</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#additional-kerberos-debugging-information" class="md-nav__link">Additional Kerberos debugging information</a>
        </li>
        <li class="md-nav__item"><a href="kerberos.html#additional-resources" class="md-nav__link">Additional resources</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="kerberos-authentication">
<h1 id="security-kerberos--page-root">Kerberos authentication<a class="headerlink" href="kerberos.html#security-kerberos--page-root" title="Link to this heading">#</a></h1>
<p>Trino can be configured to enable Kerberos authentication over HTTPS for
clients, such as the <a class="reference internal" href="../client/cli.html"><span class="doc">Trino CLI</span></a>, or the JDBC and ODBC
drivers.</p>
<p>To enable Kerberos authentication for Trino, Kerberos-related configuration
changes are made on the Trino coordinator.</p>
<p>Using <a class="reference internal" href="tls.html"><span class="doc">TLS</span></a> and <a class="reference internal" href="internal-communication.html"><span class="doc">a configured shared secret</span></a> is required for Kerberos authentication.</p>
<section id="environment-configuration">
<h2 id="environment-configuration">Environment configuration<a class="headerlink" href="kerberos.html#environment-configuration" title="Link to this heading">#</a></h2>
<section id="kerberos-services">
<span id="server-kerberos-services"></span><h3 id="kerberos-services">Kerberos services<a class="headerlink" href="kerberos.html#kerberos-services" title="Link to this heading">#</a></h3>
<p>You will need a Kerberos <abbr title="Key Distribution Center">KDC</abbr> running on a
node that the Trino coordinator can reach over the network. The KDC is
responsible for authenticating principals and issuing session keys that can be
used with Kerberos-enabled services. KDCs typically run on port 88, which is
the IANA-assigned port for Kerberos.</p>
</section>
<section id="mit-kerberos-configuration">
<span id="server-kerberos-configuration"></span><h3 id="mit-kerberos-configuration">MIT Kerberos configuration<a class="headerlink" href="kerberos.html#mit-kerberos-configuration" title="Link to this heading">#</a></h3>
<p>Kerberos needs to be configured on the Trino coordinator. At a minimum, there needs
to be a <code class="docutils literal notranslate"><span class="pre">kdc</span></code> entry in the <code class="docutils literal notranslate"><span class="pre">[realms]</span></code> section of the <code class="docutils literal notranslate"><span class="pre">/etc/krb5.conf</span></code>
file. You may also want to include an <code class="docutils literal notranslate"><span class="pre">admin_server</span></code> entry and ensure that
the Trino coordinator can reach the Kerberos admin server on port 749.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>[realms]
  TRINO.EXAMPLE.COM = {
    kdc = kdc.example.com
    admin_server = kdc.example.com
  }

[domain_realm]
  .trino.example.com = TRINO.EXAMPLE.COM
  trino.example.com = TRINO.EXAMPLE.COM
</pre></div>
</div>
<p>The complete <a class="reference external" href="http://web.mit.edu/kerberos/krb5-latest/doc/admin/conf_files/kdc_conf.html">documentation</a>
for <code class="docutils literal notranslate"><span class="pre">krb5.conf</span></code> is hosted by the MIT Kerberos Project. If you are using a
different implementation of the Kerberos protocol, you will need to adapt the
configuration to your environment.</p>
</section>
<section id="kerberos-principals-and-keytab-files">
<span id="server-kerberos-principals"></span><h3 id="kerberos-principals-and-keytab-files">Kerberos principals and keytab files<a class="headerlink" href="kerberos.html#kerberos-principals-and-keytab-files" title="Link to this heading">#</a></h3>
<p>The Trino coordinator needs a Kerberos principal, as do users who are going to
connect to the Trino coordinator. You need to create these users in Kerberos
using <a class="reference external" href="http://web.mit.edu/kerberos/krb5-latest/doc/admin/admin_commands/kadmin_local.html">kadmin</a>.</p>
<p>In addition, the Trino coordinator needs a <a class="reference external" href="http://web.mit.edu/kerberos/krb5-devel/doc/basic/keytab_def.html">keytab file</a>. After you
create the principal, you can create the keytab file using <strong class="command">kadmin</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>kadmin
&gt; addprinc -randkey <a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="daaea8b3b4b59a9f829b978a969ff4999597">[email&#160;protected]</a>
&gt; addprinc -randkey trino/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="582c2a313637753b37372a3c3136392c372a763d20393528343d763b3735181d00191508141d761b1715">[email&#160;protected]</a>
&gt; ktadd -k /etc/trino/trino.keytab <a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="7d090f1413123d38253c302d3138533e3230">[email&#160;protected]</a>
&gt; ktadd -k /etc/trino/trino.keytab trino/<a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="e591978c8b8ac8868a8a97818c8b84918a97cb809d8488958980cb868a88a5a0bda4a8b5a9a0cba6aaa8">[email&#160;protected]</a>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Running <strong class="command">ktadd</strong> randomizes the principal’s keys. If you have just
created the principal, this does not matter. If the principal already exists,
and if existing users or services rely on being able to authenticate using a
password or a keytab, use the <code class="docutils literal notranslate"><span class="pre">-norandkey</span></code> option to <strong class="command">ktadd</strong>.</p>
</div>
</section>
<section id="configuration-for-tls">
<h3 id="configuration-for-tls">Configuration for TLS<a class="headerlink" href="kerberos.html#configuration-for-tls" title="Link to this heading">#</a></h3>
<p>When using Kerberos authentication, access to the Trino coordinator must be
through <a class="reference internal" href="tls.html"><span class="doc">TLS and HTTPS</span></a>.</p>
</section>
</section>
<section id="system-access-control-plugin">
<h2 id="system-access-control-plugin">System access control plugin<a class="headerlink" href="kerberos.html#system-access-control-plugin" title="Link to this heading">#</a></h2>
<p>A Trino coordinator with Kerberos enabled probably needs a
<a class="reference internal" href="../develop/system-access-control.html"><span class="doc">System access control</span></a> plugin to achieve the desired level of
security.</p>
</section>
<section id="trino-coordinator-node-configuration">
<h2 id="trino-coordinator-node-configuration">Trino coordinator node configuration<a class="headerlink" href="kerberos.html#trino-coordinator-node-configuration" title="Link to this heading">#</a></h2>
<p>You must make the above changes to the environment prior to configuring the
Trino coordinator to use Kerberos authentication and HTTPS. After making the
following environment changes, you can make the changes to the Trino
configuration files.</p>
<ul class="simple">
<li><p><a class="reference internal" href="tls.html"><span class="doc">TLS and HTTPS</span></a></p></li>
<li><p><a class="reference internal" href="kerberos.html#server-kerberos-services"><span class="std std-ref">Kerberos services</span></a></p></li>
<li><p><a class="reference internal" href="kerberos.html#server-kerberos-configuration"><span class="std std-ref">MIT Kerberos configuration</span></a></p></li>
<li><p><a class="reference internal" href="kerberos.html#server-kerberos-principals"><span class="std std-ref">Kerberos principals and keytab files</span></a></p></li>
<li><p><a class="reference internal" href="../develop/system-access-control.html"><span class="doc">System Access Control Plugin</span></a></p></li>
</ul>
<section id="config-properties">
<h3 id="config-properties">config.properties<a class="headerlink" href="kerberos.html#config-properties" title="Link to this heading">#</a></h3>
<p>Kerberos authentication is configured in the coordinator node’s
<code class="file docutils literal notranslate"><span class="pre">config.properties</span></code> file. The entries that need to be added are listed
below.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http-server.authentication.type=KERBEROS

http-server.authentication.krb5.service-name=trino
http-server.authentication.krb5.principal-hostname=trino.example.com
http-server.authentication.krb5.keytab=/etc/trino/trino.keytab
http.authentication.krb5.config=/etc/krb5.conf

http-server.https.enabled=true
http-server.https.port=7778

http-server.https.keystore.path=/etc/trino/keystore.jks
http-server.https.keystore.key=keystore_password

node.internal-address-source=FQDN
</pre></div>
</div>
<table>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.type</span></code></p></td>
<td><p>Authentication type for the Trino coordinator. Must be set to <code class="docutils literal notranslate"><span class="pre">KERBEROS</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.service-name</span></code></p></td>
<td><p>The Kerberos service name for the Trino coordinator. Must match the Kerberos principal.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.principal-hostname</span></code></p></td>
<td><p>The Kerberos hostname for the Trino coordinator. Must match the Kerberos principal. This parameter is optional. If included, Trino uses this value in the host part of the Kerberos principal instead of the machine’s hostname.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.keytab</span></code></p></td>
<td><p>The location of the keytab that can be used to authenticate the Kerberos principal.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http.authentication.krb5.config</span></code></p></td>
<td><p>The location of the Kerberos configuration file.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.https.enabled</span></code></p></td>
<td><p>Enables HTTPS access for the Trino coordinator. Should be set to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.https.port</span></code></p></td>
<td><p>HTTPS server port.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.path</span></code></p></td>
<td><p>The location of the Java Keystore file that is used to secure TLS.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.https.keystore.key</span></code></p></td>
<td><p>The password for the keystore. This must match the password you specified when creating the keystore.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.user-mapping.pattern</span></code></p></td>
<td><p>Regex to match against user.  If matched, user will be replaced with first regex group. If not matched, authentication is denied.  Default is <code class="docutils literal notranslate"><span class="pre">(.*)</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.user-mapping.file</span></code></p></td>
<td><p>File containing rules for mapping user.  See <a class="reference internal" href="user-mapping.html"><span class="doc">User mapping</span></a> for more information.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">node.internal-address-source</span></code></p></td>
<td><p>Kerberos is typically sensitive to DNS names. Setting this property to use <code class="docutils literal notranslate"><span class="pre">FQDN</span></code> ensures correct operation and usage of valid DNS host names.</p></td>
</tr>
</tbody>
</table>
<p>See <a class="reference internal" href="tls.html#tls-version-and-ciphers"><span class="std std-ref">Standards supported</span></a> for a discussion of the
supported TLS versions and cipher suites.</p>
</section>
<section id="access-control-properties">
<h3 id="access-control-properties">access-control.properties<a class="headerlink" href="kerberos.html#access-control-properties" title="Link to this heading">#</a></h3>
<p>At a minimum, an <code class="file docutils literal notranslate"><span class="pre">access-control.properties</span></code> file must contain an
<code class="docutils literal notranslate"><span class="pre">access-control.name</span></code> property.  All other configuration is specific for the
implementation being configured. See <a class="reference internal" href="../develop/system-access-control.html"><span class="doc">System access control</span></a> for
details.</p>
</section>
</section>
<section id="user-mapping">
<span id="coordinator-troubleshooting"></span><h2 id="user-mapping">User mapping<a class="headerlink" href="kerberos.html#user-mapping" title="Link to this heading">#</a></h2>
<p>After authenticating with Kerberos, the Trino server receives the user’s
principal which is typically similar to an email address. For example, when
<code class="docutils literal notranslate"><span class="pre">alice</span></code> logs in Trino might receive <code class="docutils literal notranslate"><span class="pre"><a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="e7868b8e8482a7829f868a978b82c984888a">[email&#160;protected]</a></span></code>. By default, Trino
uses the full Kerberos principal name, but this can be mapped to a shorter
name using a user-mapping pattern. For simple mapping rules, the
<code class="docutils literal notranslate"><span class="pre">http-server.authentication.krb5.user-mapping.pattern</span></code> configuration property
can be set to a Java regular expression, and Trino uses the value of the
first matcher group. If the regular expression does not match, the
authentication is denied. For more complex user-mapping rules, see
<a class="reference internal" href="user-mapping.html"><span class="doc">User mapping</span></a>.</p>
</section>
<section id="troubleshooting">
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="kerberos.html#troubleshooting" title="Link to this heading">#</a></h2>
<p>Getting Kerberos authentication working can be challenging. You can
independently verify some of the configuration outside Trino to help narrow
your focus when trying to solve a problem.</p>
<section id="kerberos-verification">
<h3 id="kerberos-verification">Kerberos verification<a class="headerlink" href="kerberos.html#kerberos-verification" title="Link to this heading">#</a></h3>
<p>Ensure that you can connect to the KDC from the Trino coordinator using
<strong class="command">telnet</strong>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ telnet kdc.example.com 88
</pre></div>
</div>
<p>Verify that the keytab file can be used to successfully obtain a ticket using
<a class="reference external" href="http://web.mit.edu/kerberos/krb5-1.12/doc/user/user_commands/kinit.html">kinit</a> and
<a class="reference external" href="http://web.mit.edu/kerberos/krb5-1.12/doc/user/user_commands/klist.html">klist</a></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>$ kinit -kt /etc/trino/trino.keytab <a href="https://trino.io/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="2256504b4c4d62677a636f726e670c616d6f">[email&#160;protected]</a>
$ klist
</pre></div>
</div>
</section>
<section id="java-keystore-file-verification">
<h3 id="java-keystore-file-verification">Java keystore file verification<a class="headerlink" href="kerberos.html#java-keystore-file-verification" title="Link to this heading">#</a></h3>
<p>Verify the password for a keystore file and view its contents using
<a class="reference internal" href="inspect-jks.html#troubleshooting-keystore"><span class="std std-ref">Inspect and validate keystore</span></a>.</p>
</section>
<section id="additional-kerberos-debugging-information">
<span id="kerberos-debug"></span><h3 id="additional-kerberos-debugging-information">Additional Kerberos debugging information<a class="headerlink" href="kerberos.html#additional-kerberos-debugging-information" title="Link to this heading">#</a></h3>
<p>You can enable additional Kerberos debugging information for the Trino
coordinator process by adding the following lines to the Trino <code class="docutils literal notranslate"><span class="pre">jvm.config</span></code>
file:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>-Dsun.security.krb5.debug=true
-Dlog.enable-console=true
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">-Dsun.security.krb5.debug=true</span></code> enables Kerberos debugging output from the
JRE Kerberos libraries. The debugging output goes to <code class="docutils literal notranslate"><span class="pre">stdout</span></code>, which Trino
redirects to the logging system. <code class="docutils literal notranslate"><span class="pre">-Dlog.enable-console=true</span></code> enables output
to <code class="docutils literal notranslate"><span class="pre">stdout</span></code> to appear in the logs.</p>
<p>The amount and usefulness of the information the Kerberos debugging output
sends to the logs varies depending on where the authentication is failing.
Exception messages and stack traces can provide useful clues about the
nature of the problem.</p>
<p>See <a class="reference external" href="https://docs.oracle.com/en/java/javase/11/security/troubleshooting-security.html">Troubleshooting Security</a>
in the Java documentation for more details about the <code class="docutils literal notranslate"><span class="pre">-Djava.security.debug</span></code>
flag, and <a class="reference external" href="https://docs.oracle.com/en/java/javase/11/security/troubleshooting.html">Troubleshooting</a> for
more details about the Java GSS-API and Kerberos issues.</p>
</section>
<section id="additional-resources">
<span id="server-additional-resources"></span><h3 id="additional-resources">Additional resources<a class="headerlink" href="kerberos.html#additional-resources" title="Link to this heading">#</a></h3>
<p><a class="reference external" href="http://docs.oracle.com/cd/E19253-01/816-4557/trouble-6/index.html">Common Kerberos Error Messages (A-M)</a></p>
<p><a class="reference external" href="http://docs.oracle.com/cd/E19253-01/816-4557/trouble-27/index.html">Common Kerberos Error Messages (N-Z)</a></p>
<p><a class="reference external" href="http://web.mit.edu/kerberos/krb5-latest/doc/admin/troubleshoot.html">MIT Kerberos Documentation: Troubleshooting</a></p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="oauth2.html" title="OAuth 2.0 authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> OAuth 2.0 authentication </span>
              </div>
            </a>
          
          
            <a href="certificate.html" title="Certificate authentication"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Certificate authentication </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>