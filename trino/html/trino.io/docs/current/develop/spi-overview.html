<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>SPI overview &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="spi-overview.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Test writing guidelines" href="tests.html" />
    <link rel="prev" title="Developer guide" href="../develop.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="spi-overview.html#develop/spi-overview" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> SPI overview </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> SPI overview </label>
    
      <a href="spi-overview.html#" class="md-nav__link md-nav__link--active">SPI overview</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="spi-overview.html#code" class="md-nav__link">Code</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#plugin-metadata" class="md-nav__link">Plugin metadata</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#plugin" class="md-nav__link">Plugin</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#building-plugins-via-maven" class="md-nav__link">Building plugins via Maven</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#deploying-a-custom-plugin" class="md-nav__link">Deploying a custom plugin</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#compatibility" class="md-nav__link">Compatibility</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="spi-overview.html#code" class="md-nav__link">Code</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#plugin-metadata" class="md-nav__link">Plugin metadata</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#plugin" class="md-nav__link">Plugin</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#building-plugins-via-maven" class="md-nav__link">Building plugins via Maven</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#deploying-a-custom-plugin" class="md-nav__link">Deploying a custom plugin</a>
        </li>
        <li class="md-nav__item"><a href="spi-overview.html#compatibility" class="md-nav__link">Compatibility</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="spi-overview">
<h1 id="develop-spi-overview--page-root">SPI overview<a class="headerlink" href="spi-overview.html#develop-spi-overview--page-root" title="Link to this heading">#</a></h1>
<p>Trino uses a plugin architecture to extend its capabilities and integrate with
various data sources and other systems. Plugins must implement the interfaces
and override methods defined by the Service Provider Interface (SPI).</p>
<p>General user information about plugins is available in the <a class="reference internal" href="../installation/plugins.html"><span class="doc std std-doc">Plugin
documentation</span></a>, and specific details are documented in
dedicated pages about each plugin.</p>
<p>This section details plugin development including specific pages for
capabilities of different plugins. A custom plugin enables you to add further
features to Trino, such as a connector for another data source.</p>
<section id="code">
<h2 id="code">Code<a class="headerlink" href="spi-overview.html#code" title="Link to this heading">#</a></h2>
<p>The SPI source can be found in the <code class="docutils literal notranslate"><span class="pre">core/trino-spi</span></code> directory in the Trino
source tree.</p>
</section>
<section id="plugin-metadata">
<h2 id="plugin-metadata">Plugin metadata<a class="headerlink" href="spi-overview.html#plugin-metadata" title="Link to this heading">#</a></h2>
<p>Each plugin identifies an entry point: an implementation of the
<code class="docutils literal notranslate"><span class="pre">Plugin</span></code> interface. This class name is provided to Trino via
the standard Java <code class="docutils literal notranslate"><span class="pre">ServiceLoader</span></code> interface: the classpath contains
a resource file named <code class="docutils literal notranslate"><span class="pre">io.trino.spi.Plugin</span></code> in the
<code class="docutils literal notranslate"><span class="pre">META-INF/services</span></code> directory. The content of this file is a
single line listing the name of the plugin class:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>com.example.plugin.ExamplePlugin
</pre></div>
</div>
<p>For a built-in plugin that is included in the Trino source code,
this resource file is created whenever the <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code> file of a plugin
contains the following line:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;packaging&gt;</span>trino-plugin<span class="nt">&lt;/packaging&gt;</span>
</pre></div>
</div>
</section>
<section id="plugin">
<h2 id="plugin">Plugin<a class="headerlink" href="spi-overview.html#plugin" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">Plugin</span></code> interface is a good starting place for developers looking
to understand the Trino SPI. It contains access methods to retrieve
various classes that a Plugin can provide. For example, the <code class="docutils literal notranslate"><span class="pre">getConnectorFactories()</span></code>
method is a top-level function that Trino calls to retrieve a <code class="docutils literal notranslate"><span class="pre">ConnectorFactory</span></code> when Trino
is ready to create an instance of a connector to back a catalog. There are similar
methods for <code class="docutils literal notranslate"><span class="pre">Type</span></code>, <code class="docutils literal notranslate"><span class="pre">ParametricType</span></code>, <code class="docutils literal notranslate"><span class="pre">Function</span></code>, <code class="docutils literal notranslate"><span class="pre">SystemAccessControl</span></code>, and
<code class="docutils literal notranslate"><span class="pre">EventListenerFactory</span></code> objects.</p>
</section>
<section id="building-plugins-via-maven">
<h2 id="building-plugins-via-maven">Building plugins via Maven<a class="headerlink" href="spi-overview.html#building-plugins-via-maven" title="Link to this heading">#</a></h2>
<p>Plugins depend on the SPI from Trino:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>io.trino<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>trino-spi<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;scope&gt;</span>provided<span class="nt">&lt;/scope&gt;</span>
<span class="nt">&lt;/dependency&gt;</span>
</pre></div>
</div>
<p>The plugin uses the Maven <code class="docutils literal notranslate"><span class="pre">provided</span></code> scope because Trino provides
the classes from the SPI at runtime and thus the plugin should not
include them in the plugin assembly.</p>
<p>There are a few other dependencies that are provided by Trino,
including Slice and Jackson annotations. In particular, Jackson is
used for serializing connector handles and thus plugins must use the
annotations version provided by Trino.</p>
<p>All other dependencies are based on what the plugin needs for its
own implementation. Plugins are loaded in a separate class loader
to provide isolation and to allow plugins to use a different version
of a library that Trino uses internally.</p>
<p>For an example <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code> file, see the example HTTP connector in the
<code class="docutils literal notranslate"><span class="pre">plugin/trino-example-http</span></code> directory in the Trino source tree.</p>
</section>
<section id="deploying-a-custom-plugin">
<h2 id="deploying-a-custom-plugin">Deploying a custom plugin<a class="headerlink" href="spi-overview.html#deploying-a-custom-plugin" title="Link to this heading">#</a></h2>
<p>Trino plugins must use the <code class="docutils literal notranslate"><span class="pre">trino-plugin</span></code> Maven packaging type provided by the
<a class="reference external" href="https://github.com/trinodb/trino-maven-plugin">trino-maven-plugin</a>. Building a
plugin generates the required service descriptor and invokes
<a class="reference external" href="https://github.com/jvanzyl/provisio">Provisio</a> to create a ZIP file in the
<code class="docutils literal notranslate"><span class="pre">target</span></code> directory. The file contains the plugin JAR and all its dependencies as
JAR files, and is suitable for <a class="reference internal" href="../installation/plugins.html#plugins-installation"><span class="std std-ref">plugin installation</span></a>.</p>
</section>
<section id="compatibility">
<span id="spi-compatibility"></span><h2 id="compatibility">Compatibility<a class="headerlink" href="spi-overview.html#compatibility" title="Link to this heading">#</a></h2>
<p>Successful <a class="reference internal" href="../installation/plugins.html#plugins-download"><span class="std std-ref">download</span></a>, <a class="reference internal" href="../installation/plugins.html#plugins-installation"><span class="std std-ref">installation</span></a>,
and use of a plugin depends on compatibility of the plugin with the target Trino
cluster. Full compatibility is only guaranteed when using the same Trino version
used for the plugin build and the deployment, and therefore using the same
version is recommended.</p>
<p>For example, a Trino plugin compiled for Trino 470 may not work with older or
newer versions of Trino such as Trino 430 or Trino 490. This is specifically
important when installing plugins from other projects, vendors, or your custom
development.</p>
<p>Trino plugins implement the SPI, which may change with every Trino release.
There are no runtime checks for SPI compatibility by default, and it is up to
the plugin author to verify compatibility using runtime testing.</p>
<p>If the source code of a plugin is available, you can confirm the Trino version
by inspecting the <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code>. A plugin must declare a dependency to the SPI, and
therefore compatibility with the Trino release specified in the <code class="docutils literal notranslate"><span class="pre">version</span></code> tag:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>io.trino<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>trino-spi<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>470<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;scope&gt;</span>provided<span class="nt">&lt;/scope&gt;</span>
<span class="nt">&lt;/dependency&gt;</span>
</pre></div>
</div>
<p>A good practice for plugins is to use a property for the version value, which is
then declared elsewhere in the <code class="docutils literal notranslate"><span class="pre">pom.xml</span></code>:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span>...
<span class="nt">&lt;dep.trino.version&gt;</span>470<span class="nt">&lt;/dep.trino.version&gt;</span>
...
<span class="nt">&lt;dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>io.trino<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>trino-spi<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>${dep.trino.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;scope&gt;</span>provided<span class="nt">&lt;/scope&gt;</span>
<span class="nt">&lt;/dependency&gt;</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="../develop.html" title="Developer guide"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Developer guide </span>
              </div>
            </a>
          
          
            <a href="tests.html" title="Test writing guidelines"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Test writing guidelines </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>