<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Supporting MERGE &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="supporting-merge.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Types" href="types.html" />
    <link rel="prev" title="Supporting INSERT and CREATE TABLE AS" href="insert.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="supporting-merge.html#develop/supporting-merge" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Supporting MERGE </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> </label>
    
      <a href="supporting-merge.html#" class="md-nav__link md-nav__link--active">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="supporting-merge.html#standard-sql-merge" class="md-nav__link">Standard SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#rowchangeparadigm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#overview-of-merge-processing" class="md-nav__link">Overview of <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> processing</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#merge-redistribution" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">MERGE</span></code> redistribution</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="supporting-merge.html#connector-support-for-merge" class="md-nav__link">Connector support for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#rowchangeprocessor-implementation-for-merge" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code> implementation for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#detecting-duplicate-matching-target-rows" class="md-nav__link">Detecting duplicate matching target rows</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormergetablehandle-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectorpagesinkprovider-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormergesink-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormetadata-merge-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code> <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> API</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="supporting-merge.html#standard-sql-merge" class="md-nav__link">Standard SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#rowchangeparadigm" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#overview-of-merge-processing" class="md-nav__link">Overview of <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> processing</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#merge-redistribution" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">MERGE</span></code> redistribution</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="supporting-merge.html#connector-support-for-merge" class="md-nav__link">Connector support for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#rowchangeprocessor-implementation-for-merge" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code> implementation for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#detecting-duplicate-matching-target-rows" class="md-nav__link">Detecting duplicate matching target rows</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormergetablehandle-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectorpagesinkprovider-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormergesink-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> API</a>
        </li>
        <li class="md-nav__item"><a href="supporting-merge.html#connectormetadata-merge-api" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code> <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> API</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="supporting-merge">
<h1 id="develop-supporting-merge--page-root">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code><a class="headerlink" href="supporting-merge.html#develop-supporting-merge--page-root" title="Link to this heading">#</a></h1>
<p>The Trino engine provides APIs to support row-level SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>.
To implement <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, a connector must provide the following:</p>
<ul class="simple">
<li><p>An implementation of <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code>, which is typically
layered on top of a <code class="docutils literal notranslate"><span class="pre">ConnectorPageSink</span></code>.</p></li>
<li><p>Methods in <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code> to get a “rowId” column handle, get the
row change paradigm, and to start and complete the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation.</p></li>
</ul>
<p>The Trino engine machinery used to implement SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> is also used to
support SQL <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> and <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>. This means that all a connector needs to
do is implement support for SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, and the connector gets all the Data
Modification Language (DML) operations.</p>
<section id="standard-sql-merge">
<h2 id="standard-sql-merge">Standard SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code><a class="headerlink" href="supporting-merge.html#standard-sql-merge" title="Link to this heading">#</a></h2>
<p>Different query engines support varying definitions of SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>.
Trino supports the strict SQL specification <code class="docutils literal notranslate"><span class="pre">ISO/IEC</span> <span class="pre">9075</span></code>, published
in 2016. As a simple example, given tables <code class="docutils literal notranslate"><span class="pre">target_table</span></code> and
<code class="docutils literal notranslate"><span class="pre">source_table</span></code> defined as:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">accounts</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">purchases</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">,</span>
<span class="w">    </span><span class="n">address</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">accounts</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">...;</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">monthly_accounts_update</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">customer</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">,</span>
<span class="w">    </span><span class="n">purchases</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">,</span>
<span class="w">    </span><span class="n">address</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">);</span>
<span class="k">INSERT</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">monthly_accounts_update</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="p">)</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="p">...;</span>
</pre></div>
</div>
<p>Here is a possible <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation, from <code class="docutils literal notranslate"><span class="pre">monthly_accounts_update</span></code> to
<code class="docutils literal notranslate"><span class="pre">accounts</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">MERGE</span><span class="w"> </span><span class="k">INTO</span><span class="w"> </span><span class="n">accounts</span><span class="w"> </span><span class="n">t</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">monthly_accounts_update</span><span class="w"> </span><span class="n">s</span>
<span class="w">    </span><span class="k">ON</span><span class="w"> </span><span class="p">(</span><span class="n">t</span><span class="p">.</span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="p">)</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">MATCHED</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'Berkeley'</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">DELETE</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">MATCHED</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'Joe Shmoe'</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">UPDATE</span><span class="w"> </span><span class="k">SET</span><span class="w"> </span><span class="n">purchases</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">purchases</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="n">MATCHED</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">UPDATE</span>
<span class="w">            </span><span class="k">SET</span><span class="w"> </span><span class="n">purchases</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">purchases</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">t</span><span class="p">.</span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span>
<span class="w">    </span><span class="k">WHEN</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="n">MATCHED</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">INSERT</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="p">)</span>
<span class="w">            </span><span class="k">VALUES</span><span class="w"> </span><span class="p">(</span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span><span class="p">);</span>
</pre></div>
</div>
<p>SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> tries to match each <code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clause in source order. When
a match is found, the corresponding <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> or <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>
is executed and subsequent <code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clauses are ignored.</p>
<p>SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> supports two operations on the target table and source
when a row from the source table or query matches a row in the target table:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, in which the columns in the target row are updated.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, in which the target row is deleted.</p></li>
</ul>
<p>In the <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">MATCHED</span></code> case, SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> supports only <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>
operations. The values inserted are arbitrary but usually come from
the unmatched row of the source table or query.</p>
</section>
<section id="rowchangeparadigm">
<h2 id="rowchangeparadigm"><code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code><a class="headerlink" href="supporting-merge.html#rowchangeparadigm" title="Link to this heading">#</a></h2>
<p>Different connectors have different ways of representing row updates,
imposed by the underlying storage systems. The  Trino engine classifies
these different paradigms as elements of the <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code>
enumeration, returned by enumeration, returned by method
<code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getRowChangeParadigm(...)</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code> enumeration values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CHANGE_ONLY_UPDATED_COLUMNS</span></code>, intended for connectors that can update
individual columns of rows identified by a <code class="docutils literal notranslate"><span class="pre">rowId</span></code>. The corresponding
merge processor class is <code class="docutils literal notranslate"><span class="pre">ChangeOnlyUpdatedColumnsMergeProcessor</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DELETE_ROW_AND_INSERT_ROW</span></code>, intended for connectors that represent a
row change as a row deletion paired with a row insertion. The corresponding
merge processor class is <code class="docutils literal notranslate"><span class="pre">DeleteAndInsertMergeProcessor</span></code>.</p></li>
</ul>
</section>
<section id="overview-of-merge-processing">
<h2 id="overview-of-merge-processing">Overview of <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> processing<a class="headerlink" href="supporting-merge.html#overview-of-merge-processing" title="Link to this heading">#</a></h2>
<p>A <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> statement is processed by creating a <code class="docutils literal notranslate"><span class="pre">RIGHT</span> <span class="pre">JOIN</span></code> between the
target table and the source, on the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> criteria. The source may be
a table or an arbitrary query. For each row in the source table or query,
<code class="docutils literal notranslate"><span class="pre">MERGE</span></code> produces a <code class="docutils literal notranslate"><span class="pre">ROW</span></code> object containing:</p>
<ul class="simple">
<li><p>the data column values from the <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code> or <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> cases. For the
<code class="docutils literal notranslate"><span class="pre">DELETE</span></code> cases, only the partition columns, which determine
partitioning and bucketing, are non-null.</p></li>
<li><p>a boolean column containing <code class="docutils literal notranslate"><span class="pre">true</span></code> for source rows that matched some
target row, and <code class="docutils literal notranslate"><span class="pre">false</span></code> otherwise.</p></li>
<li><p>an integer that identifies whether the merge case operation is <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>,
<code class="docutils literal notranslate"><span class="pre">DELETE</span></code> or <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, or a source row for which no case matched. If a
source row doesn’t match any merge case, all data column values except
those that determine distribution are null, and the operation number
is -1.</p></li>
</ul>
<p>A <code class="docutils literal notranslate"><span class="pre">SearchedCaseExpression</span></code> is constructed from <code class="docutils literal notranslate"><span class="pre">RIGHT</span> <span class="pre">JOIN</span></code> result
to represent the <code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clauses of the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>. In the example preceding
the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> is executed as if the <code class="docutils literal notranslate"><span class="pre">SearchedCaseExpression</span></code> were written as:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w"> </span><span class="k">CASE</span>
<span class="w">   </span><span class="k">WHEN</span><span class="w"> </span><span class="n">present</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'Berkeley'</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">       </span><span class="c1">-- Null values for delete; present=true; operation DELETE=2, case_number=0</span>
<span class="w">       </span><span class="k">row</span><span class="p">(</span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">   </span><span class="k">WHEN</span><span class="w"> </span><span class="n">present</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">'Joe Shmoe'</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">       </span><span class="c1">-- Update column values; present=true; operation UPDATE=3, case_number=1</span>
<span class="w">       </span><span class="k">row</span><span class="p">(</span><span class="n">t</span><span class="p">.</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">t</span><span class="p">.</span><span class="n">purchases</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">t</span><span class="p">.</span><span class="n">address</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span>
<span class="w">   </span><span class="k">WHEN</span><span class="w"> </span><span class="n">present</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">       </span><span class="c1">-- Update column values; present=true; operation UPDATE=3, case_number=2</span>
<span class="w">       </span><span class="k">row</span><span class="p">(</span><span class="n">t</span><span class="p">.</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">purchases</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">t</span><span class="p">.</span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span>
<span class="w">   </span><span class="k">WHEN</span><span class="w"> </span><span class="p">(</span><span class="n">present</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">       </span><span class="c1">-- Insert column values; present=false; operation INSERT=1, case_number=3</span>
<span class="w">       </span><span class="k">row</span><span class="p">(</span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">purchases</span><span class="p">,</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">address</span><span class="p">,</span><span class="w"> </span><span class="k">false</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span>
<span class="w">   </span><span class="k">ELSE</span>
<span class="w">       </span><span class="c1">-- Null values for no case matched; present=false; operation=-1,</span>
<span class="w">       </span><span class="c1">--     case_number=-1</span>
<span class="w">       </span><span class="k">row</span><span class="p">(</span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">null</span><span class="p">,</span><span class="w"> </span><span class="k">false</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
<span class="w"> </span><span class="k">END</span>
<span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="p">,</span><span class="w"> </span><span class="k">true</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">present</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">target_table</span><span class="p">)</span><span class="w"> </span><span class="n">t</span>
<span class="w">   </span><span class="k">RIGHT</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">source_table</span><span class="w"> </span><span class="n">s</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">t</span><span class="p">.</span><span class="n">customer</span><span class="p">;</span>
</pre></div>
</div>
<p>The Trino engine executes the <code class="docutils literal notranslate"><span class="pre">RIGHT</span> <span class="pre">JOIN</span></code> and <code class="docutils literal notranslate"><span class="pre">CASE</span></code> expression,
and ensures that no target table row matches more than one source expression
row, and ultimately creates a sequence of pages to be routed to the node that
runs the <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink.storeMergedRows(...)</span></code> method.</p>
<p>Like <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> and <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> target table rows are identified by
a connector-specific <code class="docutils literal notranslate"><span class="pre">rowId</span></code> column handle. For <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, the <code class="docutils literal notranslate"><span class="pre">rowId</span></code>
handle is returned by <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getMergeRowIdColumnHandle(...)</span></code>.</p>
</section>
<section id="merge-redistribution">
<h2 id="merge-redistribution"><code class="docutils literal notranslate"><span class="pre">MERGE</span></code> redistribution<a class="headerlink" href="supporting-merge.html#merge-redistribution" title="Link to this heading">#</a></h2>
<p>The Trino <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> implementation allows <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code> to change
the values of columns that determine partitioning and/or bucketing, and so
it must “redistribute” rows from the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation to the worker
nodes responsible for writing rows with the merged partitioning and/or
bucketing columns.</p>
<p>Since the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> process in general requires redistribution of
merged rows among Trino nodes, the order of rows in pages to be stored
are indeterminate. Connectors like Hive that depend on an ascending
rowId order for deleted rows must sort the deleted rows before storing
them.</p>
<p>To ensure that all inserted rows for a given partition end up on a
single node, the redistribution hash on the partition key/bucket columns
is applied to the page partition keys. As a result of the hash, all
rows for a specific partition/bucket hash together, whether they
were <code class="docutils literal notranslate"><span class="pre">MATCHED</span></code> rows or <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">MATCHED</span></code> rows.</p>
<p>For connectors whose <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code> is <code class="docutils literal notranslate"><span class="pre">DELETE_ROW_AND_INSERT_ROW</span></code>,
inserted rows are distributed using the layout supplied by
<code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getInsertLayout()</span></code>. For some connectors, the same
layout is used for updated rows. Other connectors require a special
layout for updated rows, supplied by <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getUpdateLayout()</span></code>.</p>
<section id="connector-support-for-merge">
<h3 id="connector-support-for-merge">Connector support for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code><a class="headerlink" href="supporting-merge.html#connector-support-for-merge" title="Link to this heading">#</a></h3>
<p>To start <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> processing, the Trino engine calls:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getMergeRowIdColumnHandle(...)</span></code> to get the
<code class="docutils literal notranslate"><span class="pre">rowId</span></code> column handle.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getRowChangeParadigm(...)</span></code> to get the paradigm
supported by the connector for changing existing table rows.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.beginMerge(...)</span></code> to get the a
<code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> for the merge operation. That
<code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> object contains whatever information the
connector needs to specify the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getInsertLayout(...)</span></code>, from which it extracts the
list of partition or table columns that impact write redistribution.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getUpdateLayout(...)</span></code>. If that layout is non-empty,
it is used to distribute updated rows resulting from the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>
operation.</p></li>
</ul>
<p>On nodes that are targets of the hash, the Trino engine calls
<code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider.createMergeSink(...)</span></code> to create a
<code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code>.</p>
<p>To write out each page of merged rows, the Trino engine calls
<code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink.storeMergedRows(Page)</span></code>. The <code class="docutils literal notranslate"><span class="pre">storeMergedRows(Page)</span></code>
method iterates over the rows in the page, performing updates and deletes
in the <code class="docutils literal notranslate"><span class="pre">MATCHED</span></code> cases, and inserts in the <code class="docutils literal notranslate"><span class="pre">NOT</span> <span class="pre">MATCHED</span></code> cases.</p>
<p>When using <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm.DELETE_ROW_AND_INSERT_ROW</span></code>, the engine
translates <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code> operations into a pair of <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> and <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>
operations before <code class="docutils literal notranslate"><span class="pre">storeMergedRows(Page)</span></code> is called.</p>
<p>To complete the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation, the Trino engine calls
<code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.finishMerge(...)</span></code>, passing the table handle
and a collection of JSON objects encoded as <code class="docutils literal notranslate"><span class="pre">Slice</span></code> instances. These
objects contain connector-specific information specifying what was changed
by the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation. Typically this JSON object contains the files
written and table and partition statistics generated by the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>
operation. The connector takes appropriate actions, if any.</p>
</section>
</section>
<section id="rowchangeprocessor-implementation-for-merge">
<h2 id="rowchangeprocessor-implementation-for-merge"><code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code> implementation for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code><a class="headerlink" href="supporting-merge.html#rowchangeprocessor-implementation-for-merge" title="Link to this heading">#</a></h2>
<p>In the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> implementation, each <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code>
corresponds to an internal Trino engine class that implements interface
<code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code>. <code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code> has one interesting method:
<code class="docutils literal notranslate"><span class="pre">Page</span> <span class="pre">transformPage(Page)</span></code>. The format of the output page depends
on the <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code>.</p>
<p>The connector has no access to the <code class="docutils literal notranslate"><span class="pre">RowChangeProcessor</span></code> instance – it
is used inside the Trino engine to transform the merge page rows into rows
to be stored, based on the connector’s choice of <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code>.</p>
<p>The page supplied to <code class="docutils literal notranslate"><span class="pre">transformPage()</span></code> consists of:</p>
<ul class="simple">
<li><p>The write redistribution columns if any</p></li>
<li><p>For partitioned or bucketed tables, a long hash value column.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">rowId</span></code> column for the row from the target table if matched, or
null if not matched</p></li>
<li><p>The merge case <code class="docutils literal notranslate"><span class="pre">RowBlock</span></code></p></li>
<li><p>The integer case number block</p></li>
<li><p>The byte <code class="docutils literal notranslate"><span class="pre">is_distinct</span></code> block, with value 0 if not distinct.</p></li>
</ul>
<p>The merge case <code class="docutils literal notranslate"><span class="pre">RowBlock</span></code> has the following layout:</p>
<ul class="simple">
<li><p>Blocks for each column in the table, including partition columns, in
table column order.</p></li>
<li><p>A block containing the boolean “present” value which is true if the
source row matched a target row, and false otherwise.</p></li>
<li><p>A block containing the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> case operation number, encoded as
<code class="docutils literal notranslate"><span class="pre">INSERT</span></code> = 1, <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> = 2, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code> = 3 and if no <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>
case matched, -1.</p></li>
<li><p>A block containing the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> case number, the number starting with 0, for the
<code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clause that matched for the row, or -1 if no clause
matched.</p></li>
</ul>
<p>The page returned from <code class="docutils literal notranslate"><span class="pre">transformPage</span></code> consists of:</p>
<ul class="simple">
<li><p>All table columns, in table column order.</p></li>
<li><p>The tinyint type merge case operation block.</p></li>
<li><p>The integer type merge case number block.</p></li>
<li><p>The rowId block remains unchanged from the provided input page.</p></li>
<li><p>A byte block containing 1 if the row is an insert derived from an
update operation, and 0 otherwise. This block is used to correctly
calculate the count of rows changed for connectors that represent
updates and deletes plus inserts.</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">transformPage</span></code>
must ensure that there are no rows whose operation number is -1 in
the page it returns.</p>
</section>
<section id="detecting-duplicate-matching-target-rows">
<h2 id="detecting-duplicate-matching-target-rows">Detecting duplicate matching target rows<a class="headerlink" href="supporting-merge.html#detecting-duplicate-matching-target-rows" title="Link to this heading">#</a></h2>
<p>The SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> specification requires that in each <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> case,
a single target table row must match at most one source row, after
applying the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> case condition expression. The first step
toward finding these error is done by labeling each row in the target
table with a unique id, using an <code class="docutils literal notranslate"><span class="pre">AssignUniqueId</span></code> node above the
target table scan. The projected results from the <code class="docutils literal notranslate"><span class="pre">RIGHT</span> <span class="pre">JOIN</span></code>
have these unique ids for matched target table rows as well as
the <code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clause number. A <code class="docutils literal notranslate"><span class="pre">MarkDistinct</span></code> node adds an
<code class="docutils literal notranslate"><span class="pre">is_distinct</span></code> column which is true if no other row has the same
unique id and <code class="docutils literal notranslate"><span class="pre">WHEN</span></code> clause number, and false otherwise. If
any row has <code class="docutils literal notranslate"><span class="pre">is_distinct</span></code> equal to false, a
<code class="docutils literal notranslate"><span class="pre">MERGE_TARGET_ROW_MULTIPLE_MATCHES</span></code> exception is raised and
the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation fails.</p>
</section>
<section id="connectormergetablehandle-api">
<h2 id="connectormergetablehandle-api"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> API<a class="headerlink" href="supporting-merge.html#connectormergetablehandle-api" title="Link to this heading">#</a></h2>
<p>Interface <code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> defines one method,
<code class="docutils literal notranslate"><span class="pre">getTableHandle()</span></code> to retrieve the <code class="docutils literal notranslate"><span class="pre">ConnectorTableHandle</span></code>
originally passed to <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.beginMerge()</span></code>.</p>
</section>
<section id="connectorpagesinkprovider-api">
<h2 id="connectorpagesinkprovider-api"><code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider</span></code> API<a class="headerlink" href="supporting-merge.html#connectorpagesinkprovider-api" title="Link to this heading">#</a></h2>
<p>To support SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, <code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider</span></code> must implement
the method that creates the <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code>:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">createMergeSink</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">ConnectorMergeSink</span><span class="w"> </span><span class="n">createMergeSink</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorTransactionHandle</span><span class="w"> </span><span class="n">transactionHandle</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorMergeTableHandle</span><span class="w"> </span><span class="n">mergeHandle</span><span class="p">)</span>
</pre></div>
</div>
</li>
</ul>
</section>
<section id="connectormergesink-api">
<h2 id="connectormergesink-api"><code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> API<a class="headerlink" href="supporting-merge.html#connectormergesink-api" title="Link to this heading">#</a></h2>
<p>To support <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, the connector must define an
implementation of <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code>, usually layered over the
connector’s <code class="docutils literal notranslate"><span class="pre">ConnectorPageSink</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> is created by a call to
<code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider.createMergeSink()</span></code>.</p>
<p>The only interesting methods are:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">storeMergedRows</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">void</span><span class="w"> </span><span class="n">storeMergedRows</span><span class="p">(</span><span class="n">Page</span><span class="w"> </span><span class="n">page</span><span class="p">)</span>
</pre></div>
</div>
<p>The Trino engine calls the <code class="docutils literal notranslate"><span class="pre">storeMergedRows(Page)</span></code> method of the
<code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> instance returned by
<code class="docutils literal notranslate"><span class="pre">ConnectorPageSinkProvider.createMergeSink()</span></code>, passing the page
generated by the <code class="docutils literal notranslate"><span class="pre">RowChangeProcessor.transformPage()</span></code> method.
That page consists of all table columns, in table column order,
followed by the <code class="docutils literal notranslate"><span class="pre">TINYINT</span></code> operation column, followed by the <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>
merge case number column, followed by the rowId column.</p>
<p>The job of <code class="docutils literal notranslate"><span class="pre">storeMergedRows()</span></code> is iterate over the rows in the page,
and process them based on the value of the operation column, <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>,
<code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, or ignore the row. By choosing appropriate
paradigm, the connector can request that the UPDATE operation be
transformed into <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> and <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> operations.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">finish</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">CompletableFuture</span><span class="o">&lt;</span><span class="n">Collection</span><span class="o">&lt;</span><span class="n">Slice</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">finish</span><span class="p">()</span>
</pre></div>
</div>
<p>The Trino engine calls <code class="docutils literal notranslate"><span class="pre">finish()</span></code> when all the data has been processed by
a specific <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink</span></code> instance. The connector returns a future
containing a collection of <code class="docutils literal notranslate"><span class="pre">Slice</span></code>, representing connector-specific
information about the rows processed. Usually this includes the row count,
and might include information like the files or partitions created or
changed.</p>
</li>
</ul>
</section>
<section id="connectormetadata-merge-api">
<h2 id="connectormetadata-merge-api"><code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code> <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> API<a class="headerlink" href="supporting-merge.html#connectormetadata-merge-api" title="Link to this heading">#</a></h2>
<p>A connector implementing <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> must implement these <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code>
methods.</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">getRowChangeParadigm()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">RowChangeParadigm</span><span class="w"> </span><span class="n">getRowChangeParadigm</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">)</span>
</pre></div>
</div>
<p>This method is called as the engine starts processing a <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> statement.
The connector must return a <code class="docutils literal notranslate"><span class="pre">RowChangeParadigm</span></code> enumeration instance. If
the connector doesn’t support <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>, then it should throw a
<code class="docutils literal notranslate"><span class="pre">NOT_SUPPORTED</span></code> exception to indicate that SQL <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> isn’t supported by
the connector. Note that the default implementation already throws this
exception when the method isn’t implemented.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">getMergeRowIdColumnHandle()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">ColumnHandle</span><span class="w"> </span><span class="n">getMergeRowIdColumnHandle</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">)</span>
</pre></div>
</div>
<p>This method is called in the early stages of query planning for <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>
statements. The ColumnHandle returned provides the <code class="docutils literal notranslate"><span class="pre">rowId</span></code> used by the
connector to identify rows to be merged, as well as any other fields of
the row that the connector needs to complete the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">getInsertLayout()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">Optional</span><span class="o">&lt;</span><span class="n">ConnectorTableLayout</span><span class="o">&gt;</span><span class="w"> </span><span class="n">getInsertLayout</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">)</span>
</pre></div>
</div>
<p>This method is called during query planning to get the table layout to be
used for rows inserted by the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation. For some connectors,
this layout is used for rows deleted as well.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">getUpdateLayout()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">Optional</span><span class="o">&lt;</span><span class="n">ConnectorTableLayout</span><span class="o">&gt;</span><span class="w"> </span><span class="n">getUpdateLayout</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">)</span>
</pre></div>
</div>
<p>This method is called during query planning to get the table layout to be
used for rows deleted by the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation. If the optional return
value is present, the Trino engine uses the layout for updated rows.
Otherwise, it uses the result of <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata.getInsertLayout</span></code> to
distribute updated rows.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">beginMerge()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">ConnectorMergeTableHandle</span><span class="w"> </span><span class="n">beginMerge</span><span class="p">(</span>
<span class="w">     </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">     </span><span class="n">ConnectorTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">)</span>
</pre></div>
</div>
<p>As the last step in creating the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> execution plan, the connector’s
<code class="docutils literal notranslate"><span class="pre">beginMerge()</span></code> method is called, passing the <code class="docutils literal notranslate"><span class="pre">session</span></code>, and the
<code class="docutils literal notranslate"><span class="pre">tableHandle</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">beginMerge()</span></code> performs any orchestration needed in the connector to
start processing the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code>. This orchestration varies from connector
to connector. In the case of Hive connector operating on transactional tables,
for example, <code class="docutils literal notranslate"><span class="pre">beginMerge()</span></code> checks that the table is transactional and
starts a Hive Metastore transaction.</p>
<p><code class="docutils literal notranslate"><span class="pre">beginMerge()</span></code> returns a <code class="docutils literal notranslate"><span class="pre">ConnectorMergeTableHandle</span></code> with any added
information the connector needs when the handle is passed back to
<code class="docutils literal notranslate"><span class="pre">finishMerge()</span></code> and the split generation machinery. For most
connectors, the returned table handle contains at least a flag identifying
the table handle as a table handle for a <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">finishMerge()</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="n">void</span><span class="w"> </span><span class="n">finishMerge</span><span class="p">(</span>
<span class="w">    </span><span class="n">ConnectorSession</span><span class="w"> </span><span class="k">session</span><span class="p">,</span>
<span class="w">    </span><span class="n">ConnectorMergeTableHandle</span><span class="w"> </span><span class="n">tableHandle</span><span class="p">,</span>
<span class="w">    </span><span class="n">Collection</span><span class="o">&lt;</span><span class="n">Slice</span><span class="o">&gt;</span><span class="w"> </span><span class="n">fragments</span><span class="p">)</span>
</pre></div>
</div>
<p>During <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> processing, the Trino engine accumulates the <code class="docutils literal notranslate"><span class="pre">Slice</span></code>
collections returned by <code class="docutils literal notranslate"><span class="pre">ConnectorMergeSink.finish()</span></code>. The engine calls
<code class="docutils literal notranslate"><span class="pre">finishMerge()</span></code>, passing the table handle and that collection of
<code class="docutils literal notranslate"><span class="pre">Slice</span></code> fragments. In response, the connector takes appropriate actions
to complete the <code class="docutils literal notranslate"><span class="pre">MERGE</span></code> operation. Those actions might include
committing an underlying transaction, if any, or freeing any other
resources.</p>
</li>
</ul>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="insert.html" title="Supporting INSERT and CREATE TABLE AS"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code> </span>
              </div>
            </a>
          
          
            <a href="types.html" title="Types"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Types </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>