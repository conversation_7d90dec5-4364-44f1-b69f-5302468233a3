<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Trino client REST API &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="client-protocol.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Glossary" href="../glossary.html" />
    <link rel="prev" title="Event listener" href="event-listener.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="client-protocol.html#develop/client-protocol" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Trino client REST API </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Trino client REST API </label>
    
      <a href="client-protocol.html#" class="md-nav__link md-nav__link--active">Trino client REST API</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="client-protocol.html#http-methods" class="md-nav__link">HTTP methods</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#overview-of-query-processing" class="md-nav__link">Overview of query processing</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#important-queryresults-attributes" class="md-nav__link">Important <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> attributes</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#queryresults-diagnostic-attributes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> diagnostic attributes</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#client-request-headers" class="md-nav__link">Client request headers</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#client-response-headers" class="md-nav__link">Client response headers</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#protocolheaders" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ProtocolHeaders</span></code></a>
        </li>
    </ul>
</nav>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="client-protocol.html#http-methods" class="md-nav__link">HTTP methods</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#overview-of-query-processing" class="md-nav__link">Overview of query processing</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#important-queryresults-attributes" class="md-nav__link">Important <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> attributes</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#queryresults-diagnostic-attributes" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> diagnostic attributes</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#client-request-headers" class="md-nav__link">Client request headers</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#client-response-headers" class="md-nav__link">Client response headers</a>
        </li>
        <li class="md-nav__item"><a href="client-protocol.html#protocolheaders" class="md-nav__link"><code class="docutils literal notranslate"><span class="pre">ProtocolHeaders</span></code></a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="trino-client-rest-api">
<h1 id="develop-client-protocol--page-root">Trino client REST API<a class="headerlink" href="client-protocol.html#develop-client-protocol--page-root" title="Link to this heading">#</a></h1>
<p>The REST API allows clients to submit SQL queries to Trino and receive the
results. Clients include the CLI, the JDBC driver, and others provided by
the community. The preferred method to interact with Trino is using these
existing clients. This document provides details about the API for reference.
It can also be used to implement your own client, if necessary.</p>
<p>Find more information about client drivers, client applications, and the client
protocol configuration in the <a class="reference internal" href="../client.html"><span class="doc std std-doc">client documentation</span></a>.</p>
<section id="http-methods">
<h2 id="http-methods">HTTP methods<a class="headerlink" href="client-protocol.html#http-methods" title="Link to this heading">#</a></h2>
<ul class="simple">
<li><p>A <code class="docutils literal notranslate"><span class="pre">POST</span></code> to <code class="docutils literal notranslate"><span class="pre">/v1/statement</span></code> runs the query string in the <code class="docutils literal notranslate"><span class="pre">POST</span></code> body,
and returns a JSON document containing the query results. If there are more
results, the JSON document contains a <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> URL attribute.</p></li>
<li><p>A <code class="docutils literal notranslate"><span class="pre">GET</span></code> to the <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> attribute returns the next batch of query results.</p></li>
<li><p>A <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> to <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> terminates a running query.</p></li>
</ul>
</section>
<section id="overview-of-query-processing">
<h2 id="overview-of-query-processing">Overview of query processing<a class="headerlink" href="client-protocol.html#overview-of-query-processing" title="Link to this heading">#</a></h2>
<p>A Trino client request is initiated by an HTTP <code class="docutils literal notranslate"><span class="pre">POST</span></code> to the endpoint
<code class="docutils literal notranslate"><span class="pre">/v1/statement</span></code>, with a <code class="docutils literal notranslate"><span class="pre">POST</span></code> body consisting of the SQL query string. The
caller may set various <a class="reference internal" href="client-protocol.html#client-request-headers"><span class="std std-ref">Client request headers</span></a>. The headers are only
required in the initial <code class="docutils literal notranslate"><span class="pre">POST</span></code> request, and not when following the <code class="docutils literal notranslate"><span class="pre">nextUri</span></code>
links.</p>
<p>If the client request returns an HTTP 502, 503, or 504, that means there was
an intermittent problem processing request and the client should try again in
50-100 ms. Trino does not generate those codes by itself, but those can be
generated by load balancers in front of Trino.</p>
<p>Additionally, if the request returns a 429 status code, the client should
retry the request using the <code class="docutils literal notranslate"><span class="pre">Retry-After</span></code> header value provided.</p>
<p>Any HTTP status other than 502, 503, 504 or 200 means that query processing
has failed.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">/v1/statement</span></code> <code class="docutils literal notranslate"><span class="pre">POST</span></code> request returns a JSON document of type
<code class="docutils literal notranslate"><span class="pre">QueryResults</span></code>, as well as a collection of response headers. The <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code>
document contains an <code class="docutils literal notranslate"><span class="pre">error</span></code> field of type <code class="docutils literal notranslate"><span class="pre">QueryError</span></code> if the query has failed,
and if that object is not present, the query succeeded. Important aspects of
<code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> are documented in the following sections.</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">data</span></code> field of the JSON document is set, it contains a list of the rows
of data. The <code class="docutils literal notranslate"><span class="pre">columns</span></code> field is set to a list of the names and types of the
columns returned by the query. Most of the response headers are treated like
browser cookies by the client, and echoed back as request headers in subsequent
client requests, as documented below.</p>
<p>If the JSON document returned by the <code class="docutils literal notranslate"><span class="pre">POST</span></code> to <code class="docutils literal notranslate"><span class="pre">/v1/statement</span></code> does not contain
a <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> link, the query has completed, either successfully or
unsuccessfully, and no additional requests need to be made. If the <code class="docutils literal notranslate"><span class="pre">nextUri</span></code>
link is present in the document, there are more query results to be fetched. The
client should loop executing a <code class="docutils literal notranslate"><span class="pre">GET</span></code> request to the <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> returned in the
<code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> response object until <code class="docutils literal notranslate"><span class="pre">nextUri</span></code> is absent from the response.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">status</span></code> field of the JSON document is for human consumption only, and
provides a hint about the query state. It cannot be used to tell if the query
is finished.</p>
</section>
<section id="important-queryresults-attributes">
<h2 id="important-queryresults-attributes">Important <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> attributes<a class="headerlink" href="client-protocol.html#important-queryresults-attributes" title="Link to this heading">#</a></h2>
<p>The most important attributes of the <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> JSON document returned by
the REST API endpoints are listed in this table. For more details, refer to the
class <code class="docutils literal notranslate"><span class="pre">io.trino.client.QueryResults</span></code> in module <code class="docutils literal notranslate"><span class="pre">trino-client</span></code> in the
<code class="docutils literal notranslate"><span class="pre">client</span></code> directory of the Trino source code.</p>
<table id="id2">
<caption><span class="caption-text">QueryResults attributes</span><a class="headerlink" href="client-protocol.html#id2" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 31%"/>
<col style="width: 69%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Attribute</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">id</span></code></p></td>
<td><p>The ID of the query.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">nextUri</span></code></p></td>
<td><p>If present, the URL to use for subsequent <code class="docutils literal notranslate"><span class="pre">GET</span></code> or <code class="docutils literal notranslate"><span class="pre">DELETE</span></code> requests. If not
present, the query is complete or ended in error.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">columns</span></code></p></td>
<td><p>A list of the names and types of the columns returned by the query.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">data</span></code></p></td>
<td><p>The <code class="docutils literal notranslate"><span class="pre">data</span></code> attribute contains a list of the rows returned by the query
request. Each row is itself a list that holds values of the columns in the
row, in the order specified by the <code class="docutils literal notranslate"><span class="pre">columns</span></code> attribute.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">updateType</span></code></p></td>
<td><p>A human-readable string representing the operation. For a <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span></code>
request, the <code class="docutils literal notranslate"><span class="pre">updateType</span></code> is “CREATE TABLE”; for <code class="docutils literal notranslate"><span class="pre">SET</span> <span class="pre">SESSION</span></code> it is “SET
SESSION”; etc.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">error</span></code></p></td>
<td><p>If query failed, the <code class="docutils literal notranslate"><span class="pre">error</span></code> attribute contains a <code class="docutils literal notranslate"><span class="pre">QueryError</span></code> object. That
object contains a <code class="docutils literal notranslate"><span class="pre">message</span></code>, an <code class="docutils literal notranslate"><span class="pre">errorCode</span></code> and other information about the
error. See the <code class="docutils literal notranslate"><span class="pre">io.trino.client.QueryError</span></code> class in module <code class="docutils literal notranslate"><span class="pre">trino-client</span></code>
in the <code class="docutils literal notranslate"><span class="pre">client</span></code> directory for more details.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="queryresults-diagnostic-attributes">
<h2 id="queryresults-diagnostic-attributes"><code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> diagnostic attributes<a class="headerlink" href="client-protocol.html#queryresults-diagnostic-attributes" title="Link to this heading">#</a></h2>
<p>These <code class="docutils literal notranslate"><span class="pre">QueryResults</span></code> data members may be useful in tracking down problems:</p>
<table id="id3">
<caption><span class="caption-text">QueryResults diagnostic attributes</span><a class="headerlink" href="client-protocol.html#id3" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 25%"/>
<col style="width: 25%"/>
<col style="width: 50%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Attribute</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">queryError</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">QueryError</span></code></p></td>
<td><p>Non-null only if the query resulted in an error.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">failureInfo</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">FailureInfo</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">failureInfo</span></code> has detail on the reason for the failure, including a stack
trace, and <code class="docutils literal notranslate"><span class="pre">FailureInfo.errorLocation</span></code>, providing the query line number and
column number where the failure was detected.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">warnings</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">List&lt;TrinoWarning&gt;</span></code></p></td>
<td><p>A usually empty list of warnings.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">statementStats</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">StatementStats</span></code></p></td>
<td><p>A class containing statistics about the query execution. Of particular
interest is <code class="docutils literal notranslate"><span class="pre">StatementStats.rootStage</span></code>, of type <code class="docutils literal notranslate"><span class="pre">StageStats</span></code>, providing
statistics on the execution of each of the stages of query processing.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="client-request-headers">
<span id="id1"></span><h2 id="client-request-headers">Client request headers<a class="headerlink" href="client-protocol.html#client-request-headers" title="Link to this heading">#</a></h2>
<p>This table lists all supported client request headers. Many of the headers can
be updated in the client as response headers, and supplied in subsequent
requests, just like browser cookies.</p>
<table id="id4">
<caption><span class="caption-text">Client request headers</span><a class="headerlink" href="client-protocol.html#id4" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 38%"/>
<col style="width: 63%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Header name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-User</span></code></p></td>
<td><p>Specifies the session user. If not supplied, the session user is
automatically determined via <a class="reference internal" href="../security/user-mapping.html"><span class="doc std std-doc">User mapping</span></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Original-User</span></code></p></td>
<td><p>Specifies the session’s original user.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Source</span></code></p></td>
<td><p>For reporting purposes, this supplies the name of the software that
submitted the query.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Catalog</span></code></p></td>
<td><p>The catalog context for query processing. Set by response header
<code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Catalog</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Schema</span></code></p></td>
<td><p>The schema context for query processing. Set by response header
<code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Schema</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Time-Zone</span></code></p></td>
<td><p>The timezone for query processing. Defaults to the timezone of the Trino
cluster, and not the timezone of the client.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Language</span></code></p></td>
<td><p>The language to use when processing the query and formatting results,
formatted as a Java <code class="docutils literal notranslate"><span class="pre">Locale</span></code> string, for example <code class="docutils literal notranslate"><span class="pre">en-US</span></code> for US English. The
language of the session can be set on a per-query basis using the
<code class="docutils literal notranslate"><span class="pre">X-Trino-Language</span></code> HTTP header.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Trace-Token</span></code></p></td>
<td><p>Supplies a trace token to the Trino engine to help identify log lines that
originate with this query request.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Session</span></code></p></td>
<td><p>Supplies a comma-separated list of name=value pairs as session properties.
When the Trino client run a <code class="docutils literal notranslate"><span class="pre">SET</span> <span class="pre">SESSION</span> <span class="pre">name=value</span></code> query, the name=value
pair is returned in the <code class="docutils literal notranslate"><span class="pre">X-Set-Trino-Session</span></code> response header, and added to
the client’s list of session properties. If the response header
<code class="docutils literal notranslate"><span class="pre">X-Trino-Clear-Session</span></code> is returned, its value is the name of a session
property that is removed from the client’s accumulated list.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Role</span></code></p></td>
<td><p>Sets the “role” for query processing. A “role” represents a collection of
permissions. Set by response header <code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Role</span></code>. See
<a class="reference internal" href="../sql/create-role.html"><span class="doc std std-doc">CREATE ROLE</span></a> to understand roles.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Prepared-Statement</span></code></p></td>
<td><p>A comma-separated list of the name=value pairs, where the names are names of
previously prepared SQL statements, and the values are keys that identify
the executable form of the named prepared statements.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Transaction-Id</span></code></p></td>
<td><p>The transaction ID to use for query processing. Set by response header
<code class="docutils literal notranslate"><span class="pre">X-Trino-Started-Transaction-Id</span></code> and cleared by
<code class="docutils literal notranslate"><span class="pre">X-Trino-Clear-Transaction-Id</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Client-Info</span></code></p></td>
<td><p>Contains arbitrary information about the client program submitting the
query.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Client-Tags</span></code></p></td>
<td><p>A comma-separated list of “tag” strings, used to identify Trino resource
groups.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Resource-Estimate</span></code></p></td>
<td><p>A comma-separated list of <code class="docutils literal notranslate"><span class="pre">resource=value</span></code> type assignments. The possible
choices of <code class="docutils literal notranslate"><span class="pre">resource</span></code> are <code class="docutils literal notranslate"><span class="pre">EXECUTION_TIME</span></code>, <code class="docutils literal notranslate"><span class="pre">CPU_TIME</span></code>,  <code class="docutils literal notranslate"><span class="pre">PEAK_MEMORY</span></code> and
<code class="docutils literal notranslate"><span class="pre">PEAK_TASK_MEMORY</span></code>. <code class="docutils literal notranslate"><span class="pre">EXECUTION_TIME</span></code> and <code class="docutils literal notranslate"><span class="pre">CPU_TIME</span></code> have values specified
as airlift <code class="docutils literal notranslate"><span class="pre">Duration</span></code> strings The format is a double precision number
followed by a <code class="docutils literal notranslate"><span class="pre">TimeUnit</span></code> string, for example <code class="docutils literal notranslate"><span class="pre">s</span></code> for seconds, <code class="docutils literal notranslate"><span class="pre">m</span></code> for minutes,
<code class="docutils literal notranslate"><span class="pre">h</span></code> for hours, etc. “PEAK_MEMORY” and “PEAK_TASK_MEMORY” are specified as
airlift <code class="docutils literal notranslate"><span class="pre">DataSize</span></code> strings, whose format is an integer followed by <code class="docutils literal notranslate"><span class="pre">B</span></code> for
bytes; <code class="docutils literal notranslate"><span class="pre">kB</span></code> for kilobytes; <code class="docutils literal notranslate"><span class="pre">mB</span></code> for megabytes, <code class="docutils literal notranslate"><span class="pre">gB</span></code> for gigabytes, etc.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Extra-Credential</span></code></p></td>
<td><p>Provides extra credentials to the connector. The header is a name=value
string that is saved in the session <code class="docutils literal notranslate"><span class="pre">Identity</span></code> object. The name and value
are only meaningful to the connector.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="client-response-headers">
<h2 id="client-response-headers">Client response headers<a class="headerlink" href="client-protocol.html#client-response-headers" title="Link to this heading">#</a></h2>
<p>This table lists the supported client response headers. After receiving a
response, a client must update the request headers used in
subsequent requests to be consistent with the response headers received.</p>
<table id="id5">
<caption><span class="caption-text">Client response headers</span><a class="headerlink" href="client-protocol.html#id5" title="Link to this table">#</a></caption>
<colgroup>
<col style="width: 38%"/>
<col style="width: 63%"/>
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Header name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Catalog</span></code></p></td>
<td><p>Instructs the client to set the catalog in the <code class="docutils literal notranslate"><span class="pre">X-Trino-Catalog</span></code> request
header in subsequent client requests.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Schema</span></code></p></td>
<td><p>Instructs the client to set the schema in the <code class="docutils literal notranslate"><span class="pre">X-Trino-Schema</span></code> request
header in subsequent client requests.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Authorization-User</span></code></p></td>
<td><p>Instructs the client to set the session authorization user in the
<code class="docutils literal notranslate"><span class="pre">X-Trino-User</span></code> request header in subsequent client requests.
<code class="docutils literal notranslate"><span class="pre">X-Trino-Original-User</span></code> should also be set.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Reset-Authorization-User</span></code></p></td>
<td><p>Instructs the client to reset <code class="docutils literal notranslate"><span class="pre">X-Trino-User</span></code> request header to its original
value in subsequent client requests and remove <code class="docutils literal notranslate"><span class="pre">X-Trino-Original-User</span></code>
to reset the authorization user back to the original user.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Original-Roles</span></code></p></td>
<td><p>Instructs the client to set the roles of the original user in the
<code class="docutils literal notranslate"><span class="pre">X-Trino-Original-Roles</span></code> request header in subsequent client requests.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Session</span></code></p></td>
<td><p>The value of the <code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Session</span></code> response header is a string of the
form <em>property</em> = <em>value</em>. It instructs the client include session property
<em>property</em> with value <em>value</em> in the <code class="docutils literal notranslate"><span class="pre">X-Trino-Session</span></code> header of subsequent
client requests.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Clear-Session</span></code></p></td>
<td><p>Instructs the client to remove the session property whose name is the value
of the <code class="docutils literal notranslate"><span class="pre">X-Trino-Clear-Session</span></code> header from the list of session properties in
the <code class="docutils literal notranslate"><span class="pre">X-Trino-Session</span></code> header in subsequent client requests.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Role</span></code></p></td>
<td><p>Instructs the client to set <code class="docutils literal notranslate"><span class="pre">X-Trino-Role</span></code> request header to the catalog
role supplied by the <code class="docutils literal notranslate"><span class="pre">X-Trino-Set-Role</span></code> header in subsequent client
requests.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Added-Prepare</span></code></p></td>
<td><p>Instructs the client to add the name=value pair to the set of prepared
statements in the <code class="docutils literal notranslate"><span class="pre">X-Trino-Prepared-Statement</span></code> request header in subsequent
client requests.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Deallocated-Prepare</span></code></p></td>
<td><p>Instructs the client to remove the prepared statement whose name is the
value of the <code class="docutils literal notranslate"><span class="pre">X-Trino-Deallocated-Prepare</span></code> header from the client’s list of
prepared statements sent in the <code class="docutils literal notranslate"><span class="pre">X-Trino-Prepared-Statement</span></code> request header
in subsequent client requests.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Started-Transaction-Id</span></code></p></td>
<td><p>Provides the transaction ID that the client should pass back in the
<code class="docutils literal notranslate"><span class="pre">X-Trino-Transaction-Id</span></code> request header in subsequent requests.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">X-Trino-Clear-Transaction-Id</span></code></p></td>
<td><p>Instructs the client to clear the <code class="docutils literal notranslate"><span class="pre">X-Trino-Transaction-Id</span></code> request header in
subsequent requests.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="protocolheaders">
<h2 id="protocolheaders"><code class="docutils literal notranslate"><span class="pre">ProtocolHeaders</span></code><a class="headerlink" href="client-protocol.html#protocolheaders" title="Link to this heading">#</a></h2>
<p>Class <code class="docutils literal notranslate"><span class="pre">io.trino.client.ProtocolHeaders</span></code> in module <code class="docutils literal notranslate"><span class="pre">trino-client</span></code> in the
<code class="docutils literal notranslate"><span class="pre">client</span></code> directory of Trino source enumerates all the HTTP request and
response headers allowed by the Trino client REST API.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="event-listener.html" title="Event listener"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Event listener </span>
              </div>
            </a>
          
          
            <a href="../glossary.html" title="Glossary"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Glossary </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>