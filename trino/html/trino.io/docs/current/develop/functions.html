<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="functions.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Table functions" href="table-functions.html" />
    <link rel="prev" title="Types" href="types.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="functions.html#develop/functions" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Functions </label>
    
      <a href="functions.html#" class="md-nav__link md-nav__link--active">Functions</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="functions.html#plugin-implementation" class="md-nav__link">Plugin implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#scalar-function-implementation" class="md-nav__link">Scalar function implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#parametric-scalar-functions" class="md-nav__link">Parametric scalar functions</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#another-scalar-function-example" class="md-nav__link">Another scalar function example</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#aggregation-function-implementation" class="md-nav__link">Aggregation function implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#deprecated-function" class="md-nav__link">Deprecated function</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="functions.html#plugin-implementation" class="md-nav__link">Plugin implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#scalar-function-implementation" class="md-nav__link">Scalar function implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#parametric-scalar-functions" class="md-nav__link">Parametric scalar functions</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#another-scalar-function-example" class="md-nav__link">Another scalar function example</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#aggregation-function-implementation" class="md-nav__link">Aggregation function implementation</a>
        </li>
        <li class="md-nav__item"><a href="functions.html#deprecated-function" class="md-nav__link">Deprecated function</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="functions">
<h1 id="develop-functions--page-root">Functions<a class="headerlink" href="functions.html#develop-functions--page-root" title="Link to this heading">#</a></h1>
<section id="plugin-implementation">
<h2 id="plugin-implementation">Plugin implementation<a class="headerlink" href="functions.html#plugin-implementation" title="Link to this heading">#</a></h2>
<p>The function framework is used to implement SQL functions. Trino includes a
number of built-in functions. In order to implement new functions, you can
write a plugin that returns one or more functions from <code class="docutils literal notranslate"><span class="pre">getFunctions()</span></code>:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ExampleFunctionsPlugin</span>
<span class="w">        </span><span class="kd">implements</span><span class="w"> </span><span class="n">Plugin</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">Class</span><span class="o">&lt;?&gt;&gt;</span><span class="w"> </span><span class="n">getFunctions</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ImmutableSet</span><span class="p">.</span><span class="o">&lt;</span><span class="n">Class</span><span class="o">&lt;?&gt;&gt;</span><span class="n">builder</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">ExampleNullFunction</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">IsNullFunction</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">IsEqualOrNullFunction</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">ExampleStringFunction</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">ExampleAverageFunction</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Note that the <code class="docutils literal notranslate"><span class="pre">ImmutableSet</span></code> class is a utility class from Guava.
The <code class="docutils literal notranslate"><span class="pre">getFunctions()</span></code> method contains all of the classes for the functions
that we will implement below in this tutorial.</p>
<p>For a full example in the codebase, see either the <code class="docutils literal notranslate"><span class="pre">trino-ml</span></code> module for
machine learning functions or the <code class="docutils literal notranslate"><span class="pre">trino-teradata-functions</span></code> module for
Teradata-compatible functions, both in the <code class="docutils literal notranslate"><span class="pre">plugin</span></code> directory of the Trino
source.</p>
</section>
<section id="scalar-function-implementation">
<h2 id="scalar-function-implementation">Scalar function implementation<a class="headerlink" href="functions.html#scalar-function-implementation" title="Link to this heading">#</a></h2>
<p>The function framework uses annotations to indicate relevant information
about functions, including name, description, return type and parameter
types. Below is a sample function which implements <code class="docutils literal notranslate"><span class="pre">is_null</span></code>:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ExampleNullFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@ScalarFunction</span><span class="p">(</span><span class="s">"is_null"</span><span class="p">,</span><span class="w"> </span><span class="n">deterministic</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@Description</span><span class="p">(</span><span class="s">"Returns TRUE if the argument is NULL"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNull</span><span class="p">(</span>
<span class="w">            </span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">VARCHAR</span><span class="p">)</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="n">string</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">string</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The function <code class="docutils literal notranslate"><span class="pre">is_null</span></code> takes a single <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> argument and returns a
<code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code> indicating if the argument was <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. Note that the argument to
the function is of type <code class="docutils literal notranslate"><span class="pre">Slice</span></code>. <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> uses <code class="docutils literal notranslate"><span class="pre">Slice</span></code>, which is essentially
a wrapper around <code class="docutils literal notranslate"><span class="pre">byte[]</span></code>, rather than <code class="docutils literal notranslate"><span class="pre">String</span></code> for its native container type.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">deterministic</span></code> argument indicates that a function has no side effects and,
for subsequent calls with the same argument(s), the function returns the exact
same value(s).</p>
<p>In Trino, deterministic functions don’t rely on any changing state
and don’t modify any state. The <code class="docutils literal notranslate"><span class="pre">deterministic</span></code> flag is optional and defaults
to <code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
<p>For example, the function <a class="reference internal" href="../functions/array.html#shuffle" title="shuffle"><code class="xref py py-func docutils literal notranslate"><span class="pre">shuffle()</span></code></a> is non-deterministic, since it uses random
values. On the other hand, <a class="reference internal" href="../functions/datetime.html#now" title="now"><code class="xref py py-func docutils literal notranslate"><span class="pre">now()</span></code></a> is deterministic, because subsequent calls in a
single query return the same timestamp.</p>
<p>Any function with non-deterministic behavior is required to set <code class="docutils literal notranslate"><span class="pre">deterministic</span> <span class="pre">=</span> <span class="pre">false</span></code>
to avoid unexpected results.</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">@SqlType</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@SqlType</span></code> annotation is used to declare the return type and the argument
types. Note that the return type and arguments of the Java code must match
the native container types of the corresponding annotations.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">@SqlNullable</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@SqlNullable</span></code> annotation indicates that the argument may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. Without
this annotation the framework assumes that all functions return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if
any of their arguments are <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. When working with a <code class="docutils literal notranslate"><span class="pre">Type</span></code> that has a
primitive native container type, such as <code class="docutils literal notranslate"><span class="pre">BigintType</span></code>, use the object wrapper for the
native container type when using <code class="docutils literal notranslate"><span class="pre">@SqlNullable</span></code>. The method must be annotated with
<code class="docutils literal notranslate"><span class="pre">@SqlNullable</span></code> if it can return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> when the arguments are non-null.</p>
</li>
</ul>
</section>
<section id="parametric-scalar-functions">
<h2 id="parametric-scalar-functions">Parametric scalar functions<a class="headerlink" href="functions.html#parametric-scalar-functions" title="Link to this heading">#</a></h2>
<p>Scalar functions that have type parameters have some additional complexity.
To make our previous example work with any type we need the following:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@ScalarFunction</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"is_null"</span><span class="p">)</span>
<span class="nd">@Description</span><span class="p">(</span><span class="s">"Returns TRUE if the argument is NULL"</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kd">class</span> <span class="nc">IsNullFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@TypeParameter</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNullSlice</span><span class="p">(</span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@TypeParameter</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNullLong</span><span class="p">(</span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span><span class="w"> </span><span class="n">Long</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@TypeParameter</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNullDouble</span><span class="p">(</span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span><span class="w"> </span><span class="n">Double</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// ...and so on for each native container type</span>
<span class="p">}</span>
</pre></div>
</div>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">@TypeParameter</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@TypeParameter</span></code> annotation is used to declare a type parameter which can
be used in the argument types <code class="docutils literal notranslate"><span class="pre">@SqlType</span></code> annotation, or return type of the function.
It can also be used to annotate a parameter of type <code class="docutils literal notranslate"><span class="pre">Type</span></code>. At runtime, the engine
will bind the concrete type to this parameter. <code class="docutils literal notranslate"><span class="pre">@OperatorDependency</span></code> may be used
to declare that an additional function for operating on the given type parameter is needed.
For example, the following function will only bind to types which have an equals function
defined:</p>
</li>
</ul>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@ScalarFunction</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"is_equal_or_null"</span><span class="p">)</span>
<span class="nd">@Description</span><span class="p">(</span><span class="s">"Returns TRUE if arguments are equal or both NULL"</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kd">class</span> <span class="nc">IsEqualOrNullFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@TypeParameter</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isEqualOrNullSlice</span><span class="p">(</span>
<span class="w">            </span><span class="nd">@OperatorDependency</span><span class="p">(</span>
<span class="w">                    </span><span class="n">operator</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">OperatorType</span><span class="p">.</span><span class="na">EQUAL</span><span class="p">,</span>
<span class="w">                    </span><span class="n">returnType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">,</span>
<span class="w">                    </span><span class="n">argumentTypes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="s">"T"</span><span class="p">,</span><span class="w"> </span><span class="s">"T"</span><span class="p">})</span><span class="w"> </span><span class="n">MethodHandle</span><span class="w"> </span><span class="n">equals</span><span class="p">,</span>
<span class="w">            </span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="n">value1</span><span class="p">,</span>
<span class="w">            </span><span class="nd">@SqlNullable</span><span class="w"> </span><span class="nd">@SqlType</span><span class="p">(</span><span class="s">"T"</span><span class="p">)</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="n">value2</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">value1</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">value2</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">value1</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">value2</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="kt">boolean</span><span class="p">)</span><span class="w"> </span><span class="n">equals</span><span class="p">.</span><span class="na">invokeExact</span><span class="p">(</span><span class="n">value1</span><span class="p">,</span><span class="w"> </span><span class="n">value2</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// ...and so on for each native container type</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="another-scalar-function-example">
<h2 id="another-scalar-function-example">Another scalar function example<a class="headerlink" href="functions.html#another-scalar-function-example" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">lowercaser</span></code> function takes a single <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> argument and returns a
<code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, which is the argument converted to lower case:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ExampleStringFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@ScalarFunction</span><span class="p">(</span><span class="s">"lowercaser"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@Description</span><span class="p">(</span><span class="s">"Converts the string to alternating case"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">VARCHAR</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="nf">lowercaser</span><span class="p">(</span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">VARCHAR</span><span class="p">)</span><span class="w"> </span><span class="n">Slice</span><span class="w"> </span><span class="n">slice</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">argument</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">slice</span><span class="p">.</span><span class="na">toStringUtf8</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Slices</span><span class="p">.</span><span class="na">utf8Slice</span><span class="p">(</span><span class="n">argument</span><span class="p">.</span><span class="na">toLowerCase</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Note that for most common string functions, including converting a string to
lower case, the Slice library also provides implementations that work directly
on the underlying <code class="docutils literal notranslate"><span class="pre">byte[]</span></code>, which have much better performance. This function
has no <code class="docutils literal notranslate"><span class="pre">@SqlNullable</span></code> annotations, meaning that if the argument is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
the result will automatically be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> (the function will not be called).</p>
</section>
<section id="aggregation-function-implementation">
<h2 id="aggregation-function-implementation">Aggregation function implementation<a class="headerlink" href="functions.html#aggregation-function-implementation" title="Link to this heading">#</a></h2>
<p>Aggregation functions use a similar framework to scalar functions, but are
a bit more complex.</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code>:</p>
<p>All aggregation functions accumulate input rows into a state object; this
object must implement <code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code>. For simple aggregations, just
extend <code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code> into a new interface with the getters and setters
you want, and the framework will generate all the implementations and
serializers for you. If you need a more complex state object, you will need
to implement <code class="docutils literal notranslate"><span class="pre">AccumulatorStateFactory</span></code> and <code class="docutils literal notranslate"><span class="pre">AccumulatorStateSerializer</span></code>
and provide these via the <code class="docutils literal notranslate"><span class="pre">AccumulatorStateMetadata</span></code> annotation.</p>
</li>
</ul>
<p>The following code implements the aggregation function <code class="docutils literal notranslate"><span class="pre">avg_double</span></code> which computes the
average of a <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> column:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@AggregationFunction</span><span class="p">(</span><span class="s">"avg_double"</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">AverageAggregation</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@InputFunction</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">input</span><span class="p">(</span>
<span class="w">            </span><span class="n">LongAndDoubleState</span><span class="w"> </span><span class="n">state</span><span class="p">,</span>
<span class="w">            </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">DOUBLE</span><span class="p">)</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">state</span><span class="p">.</span><span class="na">setLong</span><span class="p">(</span><span class="n">state</span><span class="p">.</span><span class="na">getLong</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>
<span class="w">        </span><span class="n">state</span><span class="p">.</span><span class="na">setDouble</span><span class="p">(</span><span class="n">state</span><span class="p">.</span><span class="na">getDouble</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">value</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@CombineFunction</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">combine</span><span class="p">(</span>
<span class="w">            </span><span class="n">LongAndDoubleState</span><span class="w"> </span><span class="n">state</span><span class="p">,</span>
<span class="w">            </span><span class="n">LongAndDoubleState</span><span class="w"> </span><span class="n">otherState</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">state</span><span class="p">.</span><span class="na">setLong</span><span class="p">(</span><span class="n">state</span><span class="p">.</span><span class="na">getLong</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">otherState</span><span class="p">.</span><span class="na">getLong</span><span class="p">());</span>
<span class="w">        </span><span class="n">state</span><span class="p">.</span><span class="na">setDouble</span><span class="p">(</span><span class="n">state</span><span class="p">.</span><span class="na">getDouble</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">otherState</span><span class="p">.</span><span class="na">getDouble</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@OutputFunction</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">DOUBLE</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">output</span><span class="p">(</span><span class="n">LongAndDoubleState</span><span class="w"> </span><span class="n">state</span><span class="p">,</span><span class="w"> </span><span class="n">BlockBuilder</span><span class="w"> </span><span class="n">out</span><span class="p">)</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="kt">long</span><span class="w"> </span><span class="n">count</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">state</span><span class="p">.</span><span class="na">getLong</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">count</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">out</span><span class="p">.</span><span class="na">appendNull</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="kt">double</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">state</span><span class="p">.</span><span class="na">getDouble</span><span class="p">();</span>
<span class="w">            </span><span class="n">DOUBLE</span><span class="p">.</span><span class="na">writeDouble</span><span class="p">(</span><span class="n">out</span><span class="p">,</span><span class="w"> </span><span class="n">value</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">count</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The average has two parts: the sum of the <code class="docutils literal notranslate"><span class="pre">DOUBLE</span></code> in each row of the column
and the <code class="docutils literal notranslate"><span class="pre">LONG</span></code> count of the number of rows seen. <code class="docutils literal notranslate"><span class="pre">LongAndDoubleState</span></code> is an interface
which extends <code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code>:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">interface</span> <span class="nc">LongAndDoubleState</span>
<span class="w">        </span><span class="kd">extends</span><span class="w"> </span><span class="n">AccumulatorState</span>
<span class="p">{</span>
<span class="w">    </span><span class="kt">long</span><span class="w"> </span><span class="nf">getLong</span><span class="p">();</span>

<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">setLong</span><span class="p">(</span><span class="kt">long</span><span class="w"> </span><span class="n">value</span><span class="p">);</span>

<span class="w">    </span><span class="kt">double</span><span class="w"> </span><span class="nf">getDouble</span><span class="p">();</span>

<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">setDouble</span><span class="p">(</span><span class="kt">double</span><span class="w"> </span><span class="n">value</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p>As stated above, for simple <code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code> objects, it is sufficient to
just define the interface with the getters and setters, and the framework
will generate the implementation for you.</p>
<p>An in-depth look at the various annotations relevant to writing an aggregation
function follows:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">@InputFunction</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@InputFunction</span></code> annotation declares the function which accepts input
rows and stores them in the <code class="docutils literal notranslate"><span class="pre">AccumulatorState</span></code>. Similar to scalar functions
you must annotate the arguments with <code class="docutils literal notranslate"><span class="pre">@SqlType</span></code>.  Note that, unlike in the above
scalar example where <code class="docutils literal notranslate"><span class="pre">Slice</span></code> is used to hold <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code>, the primitive
<code class="docutils literal notranslate"><span class="pre">double</span></code> type is used for the argument to input. In this example, the input
function simply keeps track of the running count of rows (via <code class="docutils literal notranslate"><span class="pre">setLong()</span></code>)
and the running sum (via <code class="docutils literal notranslate"><span class="pre">setDouble()</span></code>).</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">@CombineFunction</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@CombineFunction</span></code> annotation declares the function used to combine two
state objects. This function is used to merge all the partial aggregation states.
It takes two state objects, and merges the results into the first one (in the
above example, just by adding them together).</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">@OutputFunction</span></code>:</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@OutputFunction</span></code> is the last function called when computing an
aggregation. It takes the final state object (the result of merging all
partial states) and writes the result to a <code class="docutils literal notranslate"><span class="pre">BlockBuilder</span></code>.</p>
</li>
<li><p>Where does serialization happen, and what is <code class="docutils literal notranslate"><span class="pre">GroupedAccumulatorState</span></code>?</p>
<p>The <code class="docutils literal notranslate"><span class="pre">@InputFunction</span></code> is usually run on a different worker from the
<code class="docutils literal notranslate"><span class="pre">@CombineFunction</span></code>, so the state objects are serialized and transported
between these workers by the aggregation framework. <code class="docutils literal notranslate"><span class="pre">GroupedAccumulatorState</span></code>
is used when performing a <code class="docutils literal notranslate"><span class="pre">GROUP</span> <span class="pre">BY</span></code> aggregation, and an implementation
will be automatically generated for you, if you don’t specify a
<code class="docutils literal notranslate"><span class="pre">AccumulatorStateFactory</span></code></p>
</li>
</ul>
</section>
<section id="deprecated-function">
<h2 id="deprecated-function">Deprecated function<a class="headerlink" href="functions.html#deprecated-function" title="Link to this heading">#</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">@Deprecated</span></code> annotation has to be used on any function that should no longer be
used. The annotation causes Trino to generate a warning whenever SQL statements
use a deprecated function. When a function is deprecated, the <code class="docutils literal notranslate"><span class="pre">@Description</span></code>
needs to be replaced with a note about the deprecation and the replacement function:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ExampleDeprecatedFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="nd">@Deprecated</span>
<span class="w">    </span><span class="nd">@ScalarFunction</span><span class="p">(</span><span class="s">"bad_function"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@Description</span><span class="p">(</span><span class="s">"(DEPRECATED) Use good_function() instead"</span><span class="p">)</span>
<span class="w">    </span><span class="nd">@SqlType</span><span class="p">(</span><span class="n">StandardTypes</span><span class="p">.</span><span class="na">BOOLEAN</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">bad_function</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="types.html" title="Types"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Types </span>
              </div>
            </a>
          
          
            <a href="table-functions.html" title="Table functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Table functions </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>