<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Example HTTP connector &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="example-http.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Example JDBC connector" href="example-jdbc.html" />
    <link rel="prev" title="Connectors" href="connectors.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="example-http.html#develop/example-http" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Example HTTP connector </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Example HTTP connector </label>
    
      <a href="example-http.html#" class="md-nav__link md-nav__link--active">Example HTTP connector</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="example-http.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#code" class="md-nav__link">Code</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#plugin-implementation" class="md-nav__link">Plugin implementation</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#connectorfactory-implementation" class="md-nav__link">ConnectorFactory implementation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="example-http.html#connector-exampleconnector" class="md-nav__link">Connector: ExampleConnector</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#metadata-examplemetadata" class="md-nav__link">Metadata: ExampleMetadata</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#split-manager-examplesplitmanager" class="md-nav__link">Split manager: ExampleSplitManager</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#record-set-provider-examplerecordsetprovider" class="md-nav__link">Record set provider: ExampleRecordSetProvider</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="example-http.html#installation" class="md-nav__link">Installation</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#code" class="md-nav__link">Code</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#plugin-implementation" class="md-nav__link">Plugin implementation</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#connectorfactory-implementation" class="md-nav__link">ConnectorFactory implementation</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="example-http.html#connector-exampleconnector" class="md-nav__link">Connector: ExampleConnector</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#metadata-examplemetadata" class="md-nav__link">Metadata: ExampleMetadata</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#split-manager-examplesplitmanager" class="md-nav__link">Split manager: ExampleSplitManager</a>
        </li>
        <li class="md-nav__item"><a href="example-http.html#record-set-provider-examplerecordsetprovider" class="md-nav__link">Record set provider: ExampleRecordSetProvider</a>
        </li></ul>
            </nav>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="example-http-connector">
<h1 id="develop-example-http--page-root">Example HTTP connector<a class="headerlink" href="example-http.html#develop-example-http--page-root" title="Link to this heading">#</a></h1>
<p>The Example HTTP connector has a simple goal: it reads comma-separated
data over HTTP. For example, if you have a large amount of data in a
CSV format, you can point the example HTTP connector at this data and
write a query to process it.</p>
<section id="installation">
<h2 id="installation">Installation<a class="headerlink" href="example-http.html#installation" title="Link to this heading">#</a></h2>
<p>The example HTTP connector plugin is optional and therefore not included in the
default <a class="reference internal" href="../installation/deployment.html"><span class="doc std std-doc">tarball</span></a> and the default <a class="reference internal" href="../installation/containers.html"><span class="doc std std-doc">Docker
image</span></a>.</p>
<p>Follow the <a class="reference internal" href="../installation/plugins.html#plugins-installation"><span class="std std-ref">plugin installation instructions</span></a> and
optionally use the <a class="reference external" href="https://github.com/trinodb/trino-packages">trino-packages
project</a> or manually download the
plugin archive <a class="reference external" href="https://repo1.maven.org/maven2/io/trino/trino-example-http/476/trino-example-http-476.zip">trino-example-http-476.zip</a>.</p>
</section>
<section id="code">
<h2 id="code">Code<a class="headerlink" href="example-http.html#code" title="Link to this heading">#</a></h2>
<p>The Example HTTP connector can be found in the <a class="reference external" href="https://github.com/trinodb/trino/tree/master/plugin/trino-example-http">trino-example-http</a>
directory within the Trino source tree.</p>
</section>
<section id="plugin-implementation">
<h2 id="plugin-implementation">Plugin implementation<a class="headerlink" href="example-http.html#plugin-implementation" title="Link to this heading">#</a></h2>
<p>The plugin implementation in the Example HTTP connector looks very
similar to other plugin implementations.  Most of the implementation is
devoted to handling optional configuration and the only function of
interest is the following:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="n">Iterable</span><span class="o">&lt;</span><span class="n">ConnectorFactory</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getConnectorFactories</span><span class="p">()</span>
<span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">ImmutableList</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">ExampleConnectorFactory</span><span class="p">());</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Note that the <code class="docutils literal notranslate"><span class="pre">ImmutableList</span></code> class is a utility class from Guava.</p>
<p>As with all connectors, this plugin overrides the <code class="docutils literal notranslate"><span class="pre">getConnectorFactories()</span></code> method
and returns an <code class="docutils literal notranslate"><span class="pre">ExampleConnectorFactory</span></code>.</p>
</section>
<section id="connectorfactory-implementation">
<h2 id="connectorfactory-implementation">ConnectorFactory implementation<a class="headerlink" href="example-http.html#connectorfactory-implementation" title="Link to this heading">#</a></h2>
<p>In Trino, the primary object that handles the connection between
Trino and a particular type of data source is the <code class="docutils literal notranslate"><span class="pre">Connector</span></code> object,
which are created using <code class="docutils literal notranslate"><span class="pre">ConnectorFactory</span></code>.</p>
<p>This implementation is available in the class <code class="docutils literal notranslate"><span class="pre">ExampleConnectorFactory</span></code>.
The first thing the connector factory implementation does is specify the
name of this connector. This is the same string used to reference this
connector in Trino configuration.</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="nf">getName</span><span class="p">()</span>
<span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="s">"example_http"</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The real work in a connector factory happens in the <code class="docutils literal notranslate"><span class="pre">create()</span></code>
method. In the <code class="docutils literal notranslate"><span class="pre">ExampleConnectorFactory</span></code> class, the <code class="docutils literal notranslate"><span class="pre">create()</span></code> method
configures the connector and then asks Guice to create the object.
This is the meat of the <code class="docutils literal notranslate"><span class="pre">create()</span></code> method without parameter validation
and exception handling:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="c1">// A plugin is not required to use Guice; it is just very convenient</span>
<span class="n">Bootstrap</span><span class="w"> </span><span class="n">app</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Bootstrap</span><span class="p">(</span>
<span class="w">        </span><span class="k">new</span><span class="w"> </span><span class="n">JsonModule</span><span class="p">(),</span>
<span class="w">        </span><span class="k">new</span><span class="w"> </span><span class="n">ExampleModule</span><span class="p">(</span><span class="n">catalogName</span><span class="p">));</span>

<span class="n">Injector</span><span class="w"> </span><span class="n">injector</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">app</span>
<span class="w">        </span><span class="p">.</span><span class="na">doNotInitializeLogging</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">setRequiredConfigurationProperties</span><span class="p">(</span><span class="n">requiredConfig</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">initialize</span><span class="p">();</span>

<span class="k">return</span><span class="w"> </span><span class="n">injector</span><span class="p">.</span><span class="na">getInstance</span><span class="p">(</span><span class="n">ExampleConnector</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
</pre></div>
</div>
<section id="connector-exampleconnector">
<h3 id="connector-exampleconnector">Connector: ExampleConnector<a class="headerlink" href="example-http.html#connector-exampleconnector" title="Link to this heading">#</a></h3>
<p>This class allows Trino to obtain references to the various services
provided by the connector.</p>
</section>
<section id="metadata-examplemetadata">
<h3 id="metadata-examplemetadata">Metadata: ExampleMetadata<a class="headerlink" href="example-http.html#metadata-examplemetadata" title="Link to this heading">#</a></h3>
<p>This class is responsible for reporting table names, table metadata,
column names, column metadata and other information about the schemas
that are provided by this connector. <code class="docutils literal notranslate"><span class="pre">ConnectorMetadata</span></code> is also called
by Trino to ensure that a particular connector can understand and
handle a given table name.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">ExampleMetadata</span></code> implementation delegates many of these calls to
<code class="docutils literal notranslate"><span class="pre">ExampleClient</span></code>, a class that implements much of the core functionality
of the connector.</p>
</section>
<section id="split-manager-examplesplitmanager">
<h3 id="split-manager-examplesplitmanager">Split manager: ExampleSplitManager<a class="headerlink" href="example-http.html#split-manager-examplesplitmanager" title="Link to this heading">#</a></h3>
<p>The split manager partitions the data for a table into the individual
chunks that Trino will distribute to workers for processing.
In the case of the Example HTTP connector, each table contains one or
more URIs pointing at the actual data. One split is created per URI.</p>
</section>
<section id="record-set-provider-examplerecordsetprovider">
<h3 id="record-set-provider-examplerecordsetprovider">Record set provider: ExampleRecordSetProvider<a class="headerlink" href="example-http.html#record-set-provider-examplerecordsetprovider" title="Link to this heading">#</a></h3>
<p>The record set provider creates a record set which in turn creates a
record cursor that returns the actual data to Trino.
<code class="docutils literal notranslate"><span class="pre">ExampleRecordCursor</span></code> reads data from a URI via HTTP. Each line
corresponds to a single row. Lines are split on comma into individual
field values which are then returned to Trino.</p>
</section>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="connectors.html" title="Connectors"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Connectors </span>
              </div>
            </a>
          
          
            <a href="example-jdbc.html" title="Example JDBC connector"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Example JDBC connector </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>