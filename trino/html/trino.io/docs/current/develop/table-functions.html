<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Table functions &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="table-functions.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="System access control" href="system-access-control.html" />
    <link rel="prev" title="Functions" href="functions.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="table-functions.html#develop/table-functions" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Table functions </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="tests.html" class="md-nav__link">Test writing guidelines</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Table functions </label>
    
      <a href="table-functions.html#" class="md-nav__link md-nav__link--active">Table functions</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="table-functions.html#table-function-declaration" class="md-nav__link">Table function declaration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#the-constructor" class="md-nav__link">The constructor</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#argument-types" class="md-nav__link">Argument types</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#scalar-arguments" class="md-nav__link">Scalar arguments</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#descriptor-arguments" class="md-nav__link">Descriptor arguments</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#table-arguments" class="md-nav__link">Table arguments</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#set-or-row-semantics" class="md-nav__link">Set or row semantics</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#prune-or-keep-when-empty" class="md-nav__link">Prune or keep when empty</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#pass-through-columns" class="md-nav__link">Pass-through columns</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#the-analyze-method" class="md-nav__link">The <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#table-function-execution" class="md-nav__link">Table function execution</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#access-control" class="md-nav__link">Access control</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="table-functions.html#table-function-declaration" class="md-nav__link">Table function declaration</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#the-constructor" class="md-nav__link">The constructor</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#argument-types" class="md-nav__link">Argument types</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#scalar-arguments" class="md-nav__link">Scalar arguments</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#descriptor-arguments" class="md-nav__link">Descriptor arguments</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#table-arguments" class="md-nav__link">Table arguments</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="table-functions.html#set-or-row-semantics" class="md-nav__link">Set or row semantics</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#prune-or-keep-when-empty" class="md-nav__link">Prune or keep when empty</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#pass-through-columns" class="md-nav__link">Pass-through columns</a>
        </li></ul>
            </nav>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#the-analyze-method" class="md-nav__link">The <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#table-function-execution" class="md-nav__link">Table function execution</a>
        </li>
        <li class="md-nav__item"><a href="table-functions.html#access-control" class="md-nav__link">Access control</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="table-functions">
<h1 id="develop-table-functions--page-root">Table functions<a class="headerlink" href="table-functions.html#develop-table-functions--page-root" title="Link to this heading">#</a></h1>
<p>Table functions return tables. They allow users to dynamically invoke custom
logic from within the SQL query. They are invoked in the <code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause of a
query, and the calling convention is similar to a scalar function call. For
description of table functions usage, see
<a class="reference internal" href="../functions/table.html"><span class="doc">table functions</span></a>.</p>
<p>Trino supports adding custom table functions. They are declared by connectors
through implementing dedicated interfaces.</p>
<section id="table-function-declaration">
<h2 id="table-function-declaration">Table function declaration<a class="headerlink" href="table-functions.html#table-function-declaration" title="Link to this heading">#</a></h2>
<p>To declare a table function, you need to implement <code class="docutils literal notranslate"><span class="pre">ConnectorTableFunction</span></code>.
Subclassing <code class="docutils literal notranslate"><span class="pre">AbstractConnectorTableFunction</span></code> is a convenient way to do it.
The connector’s <code class="docutils literal notranslate"><span class="pre">getTableFunctions()</span></code> method must return a set of your
implementations.</p>
<section id="the-constructor">
<h3 id="the-constructor">The constructor<a class="headerlink" href="table-functions.html#the-constructor" title="Link to this heading">#</a></h3>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">MyFunction</span>
<span class="w">        </span><span class="kd">extends</span><span class="w"> </span><span class="n">AbstractConnectorTableFunction</span>
<span class="p">{</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">MyFunction</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="kd">super</span><span class="p">(</span>
<span class="w">                </span><span class="s">"system"</span><span class="p">,</span>
<span class="w">                </span><span class="s">"my_function"</span><span class="p">,</span>
<span class="w">                </span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">                        </span><span class="n">ScalarArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                                </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"COLUMN_COUNT"</span><span class="p">)</span>
<span class="w">                                </span><span class="p">.</span><span class="na">type</span><span class="p">(</span><span class="n">INTEGER</span><span class="p">)</span>
<span class="w">                                </span><span class="p">.</span><span class="na">defaultValue</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="w">                                </span><span class="p">.</span><span class="na">build</span><span class="p">(),</span>
<span class="w">                        </span><span class="n">ScalarArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                                </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"ROW_COUNT"</span><span class="p">)</span>
<span class="w">                                </span><span class="p">.</span><span class="na">type</span><span class="p">(</span><span class="n">INTEGER</span><span class="p">)</span>
<span class="w">                                </span><span class="p">.</span><span class="na">build</span><span class="p">()),</span>
<span class="w">                </span><span class="n">GENERIC_TABLE</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The constructor takes the following arguments:</p>
<ul class="simple">
<li><p><strong>schema name</strong></p></li>
</ul>
<p>The schema name helps you organize functions, and it is used for function
resolution. When a table function is invoked, the right implementation is
identified by the catalog name, the schema name, and the function name.</p>
<p>The function can use the schema name, for example to use data from the
indicated schema, or ignore it.</p>
<ul class="simple">
<li><p><strong>function name</strong></p></li>
<li><p><strong>list of expected arguments</strong></p></li>
</ul>
<p>Three different types of arguments are supported: scalar arguments, descriptor
arguments, and table arguments. See <a class="reference internal" href="table-functions.html#tf-argument-types"><span class="std std-ref">Argument types</span></a> for details. You can
specify default values for scalar and descriptor arguments. The arguments with
specified default can be skipped during table function invocation.</p>
<ul class="simple">
<li><p><strong>returned row type</strong></p></li>
</ul>
<p>It describes the row type produced by the table function.</p>
<p>If a table function takes table arguments, it can additionally pass the columns
of the input tables to output using the <em>pass-through mechanism</em>. The returned
row type is supposed to describe only the columns produced by the function, as
opposed to the pass-through columns.</p>
<p>In the example, the returned row type is <code class="docutils literal notranslate"><span class="pre">GENERIC_TABLE</span></code>, which means that
the row type is not known statically, and it is determined dynamically based on
the passed arguments.</p>
<p>When the returned row type is known statically, you can declare it using:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="k">new</span><span class="w"> </span><span class="n">DescribedTable</span><span class="p">(</span><span class="n">descriptor</span><span class="p">)</span>
</pre></div>
</div>
<p>If a table function does not produce any columns, and it only outputs the
pass-through columns, use <code class="docutils literal notranslate"><span class="pre">ONLY_PASS_THROUGH</span></code> as the returned row type.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A table function must return at least one column. It can either be a proper
column, i.e. produced by the function, or a pass-through column.</p>
</div>
</section>
<section id="argument-types">
<span id="tf-argument-types"></span><h3 id="argument-types">Argument types<a class="headerlink" href="table-functions.html#argument-types" title="Link to this heading">#</a></h3>
<p>Table functions take three types of arguments:
<a class="reference internal" href="table-functions.html#tf-scalar-arguments"><span class="std std-ref">scalar arguments</span></a>,
<a class="reference internal" href="table-functions.html#tf-descriptor-arguments"><span class="std std-ref">descriptor arguments</span></a>, and
<a class="reference internal" href="table-functions.html#tf-table-arguments"><span class="std std-ref">table arguments</span></a>.</p>
<section id="scalar-arguments">
<span id="tf-scalar-arguments"></span><h4 id="scalar-arguments">Scalar arguments<a class="headerlink" href="table-functions.html#scalar-arguments" title="Link to this heading">#</a></h4>
<p>They can be of any supported data type. You can specify a default value.</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="n">ScalarArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"COLUMN_COUNT"</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">type</span><span class="p">(</span><span class="n">INTEGER</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">defaultValue</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
</pre></div>
</div>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="n">ScalarArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"ROW_COUNT"</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">type</span><span class="p">(</span><span class="n">INTEGER</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="descriptor-arguments">
<span id="tf-descriptor-arguments"></span><h4 id="descriptor-arguments">Descriptor arguments<a class="headerlink" href="table-functions.html#descriptor-arguments" title="Link to this heading">#</a></h4>
<p>Descriptors consist of fields with names and optional data types. They are a
convenient way to pass the required result row type to the function, or for
example inform the function which input columns it should use. You can specify
default values for descriptor arguments. Descriptor argument can be <code class="docutils literal notranslate"><span class="pre">null</span></code>.</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="n">DescriptorArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"SCHEMA"</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">defaultValue</span><span class="p">(</span><span class="kc">null</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="table-arguments">
<span id="tf-table-arguments"></span><h4 id="table-arguments">Table arguments<a class="headerlink" href="table-functions.html#table-arguments" title="Link to this heading">#</a></h4>
<p>A table function can take any number of input relations. It allows you to
process multiple data sources simultaneously.</p>
<p>When declaring a table argument, you must specify characteristics to determine
how the input table is processed. Also note that you cannot specify a default
value for a table argument.</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="n">TableArgumentSpecification</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">"INPUT"</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">rowSemantics</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">pruneWhenEmpty</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">passThroughColumns</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
</pre></div>
</div>
<section id="set-or-row-semantics">
<span id="tf-set-or-row-semantics"></span><h5 id="set-or-row-semantics">Set or row semantics<a class="headerlink" href="table-functions.html#set-or-row-semantics" title="Link to this heading">#</a></h5>
<p>Set semantics is the default for table arguments. A table argument with set
semantics is processed on a partition-by-partition basis. During function
invocation, the user can specify partitioning and ordering for the argument. If
no partitioning is specified, the argument is processed as a single partition.</p>
<p>A table argument with row semantics is processed on a row-by-row basis.
Partitioning or ordering is not applicable.</p>
</section>
<section id="prune-or-keep-when-empty">
<h5 id="prune-or-keep-when-empty">Prune or keep when empty<a class="headerlink" href="table-functions.html#prune-or-keep-when-empty" title="Link to this heading">#</a></h5>
<p>The <em>prune when empty</em> property indicates that if the given table argument is
empty, the function returns empty result. This property is used to optimize
queries involving table functions. The <em>keep when empty</em> property indicates
that the function should be executed even if the table argument is empty. The
user can override this property when invoking the function. Using the <em>keep
when empty</em> property can negatively affect performance when the table argument
is not empty.</p>
</section>
<section id="pass-through-columns">
<h5 id="pass-through-columns">Pass-through columns<a class="headerlink" href="table-functions.html#pass-through-columns" title="Link to this heading">#</a></h5>
<p>If a table argument has <em>pass-through columns</em>, all of its columns are passed
on output. For a table argument without this property, only the partitioning
columns are passed on output.</p>
</section>
</section>
</section>
<section id="the-analyze-method">
<h3 id="the-analyze-method">The <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method<a class="headerlink" href="table-functions.html#the-analyze-method" title="Link to this heading">#</a></h3>
<p>In order to provide all the necessary information to the Trino engine, the
class must implement the <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method. This method is called by the
engine during the analysis phase of query processing. The <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method
is also the place to perform custom checks on the arguments:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="n">TableFunctionAnalysis</span><span class="w"> </span><span class="nf">analyze</span><span class="p">(</span><span class="n">ConnectorSession</span><span class="w"> </span><span class="n">session</span><span class="p">,</span><span class="w"> </span><span class="n">ConnectorTransactionHandle</span><span class="w"> </span><span class="n">transaction</span><span class="p">,</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Argument</span><span class="o">&gt;</span><span class="w"> </span><span class="n">arguments</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="kt">long</span><span class="w"> </span><span class="n">columnCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">long</span><span class="p">)</span><span class="w"> </span><span class="p">((</span><span class="n">ScalarArgument</span><span class="p">)</span><span class="w"> </span><span class="n">arguments</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="s">"COLUMN_COUNT"</span><span class="p">)).</span><span class="na">getValue</span><span class="p">();</span>
<span class="w">    </span><span class="kt">long</span><span class="w"> </span><span class="n">rowCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">long</span><span class="p">)</span><span class="w"> </span><span class="p">((</span><span class="n">ScalarArgument</span><span class="p">)</span><span class="w"> </span><span class="n">arguments</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="s">"ROW_COUNT"</span><span class="p">)).</span><span class="na">getValue</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// custom validation of arguments</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">columnCount</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">columnCount</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">         </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">TrinoException</span><span class="p">(</span><span class="n">INVALID_FUNCTION_ARGUMENT</span><span class="p">,</span><span class="w"> </span><span class="s">"column_count must be in range [1, 3]"</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">rowCount</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">TrinoException</span><span class="p">(</span><span class="n">INVALID_FUNCTION_ARGUMENT</span><span class="p">,</span><span class="w"> </span><span class="s">"row_count must be positive"</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// determine the returned row type</span>
<span class="w">    </span><span class="n">List</span><span class="o">&lt;</span><span class="n">Descriptor</span><span class="p">.</span><span class="na">Field</span><span class="o">&gt;</span><span class="w"> </span><span class="n">fields</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">"col_a"</span><span class="p">,</span><span class="w"> </span><span class="s">"col_b"</span><span class="p">,</span><span class="w"> </span><span class="s">"col_c"</span><span class="p">).</span><span class="na">subList</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="kt">int</span><span class="p">)</span><span class="w"> </span><span class="n">columnCount</span><span class="p">).</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Descriptor</span><span class="p">.</span><span class="na">Field</span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">Optional</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">BIGINT</span><span class="p">)))</span>
<span class="w">            </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">toList</span><span class="p">());</span>

<span class="w">    </span><span class="n">Descriptor</span><span class="w"> </span><span class="n">returnedType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Descriptor</span><span class="p">(</span><span class="n">fields</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">TableFunctionAnalysis</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">returnedType</span><span class="p">(</span><span class="n">returnedType</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">handle</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">MyHandle</span><span class="p">(</span><span class="n">columnCount</span><span class="p">,</span><span class="w"> </span><span class="n">rowCount</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">analyze()</span></code> method returns a <code class="docutils literal notranslate"><span class="pre">TableFunctionAnalysis</span></code> object, which
comprises all the information required by the engine to analyze, plan, and
execute the table function invocation:</p>
<ul class="simple">
<li><p>The returned row type, specified as an optional <code class="docutils literal notranslate"><span class="pre">Descriptor</span></code>. It should be
passed if and only if the table function is declared with the
<code class="docutils literal notranslate"><span class="pre">GENERIC_TABLE</span></code> returned type.</p></li>
<li><p>Required columns from the table arguments, specified as a map of table
argument names to lists of column indexes.</p></li>
<li><p>Any information gathered during analysis that is useful during planning or
execution, in the form of a <code class="docutils literal notranslate"><span class="pre">ConnectorTableFunctionHandle</span></code>.
<code class="docutils literal notranslate"><span class="pre">ConnectorTableFunctionHandle</span></code> is a marker interface intended to carry
information throughout subsequent phases of query processing in a manner that
is opaque to the engine.</p></li>
</ul>
</section>
</section>
<section id="table-function-execution">
<h2 id="table-function-execution">Table function execution<a class="headerlink" href="table-functions.html#table-function-execution" title="Link to this heading">#</a></h2>
<p>There are two paths of execution available for table functions.</p>
<ol class="arabic simple">
<li><p>Pushdown to the connector</p></li>
</ol>
<p>The connector that provides the table function implements the
<code class="docutils literal notranslate"><span class="pre">applyTableFunction()</span></code> method. This method is called during the optimization
phase of query processing. It returns a <code class="docutils literal notranslate"><span class="pre">ConnectorTableHandle</span></code> and a list of
<code class="docutils literal notranslate"><span class="pre">ColumnHandle</span></code> s representing the table function result. The table function
invocation is then replaced with a <code class="docutils literal notranslate"><span class="pre">TableScanNode</span></code>.</p>
<p>This execution path is convenient for table functions whose results are easy to
represent as a <code class="docutils literal notranslate"><span class="pre">ConnectorTableHandle</span></code>, for example query pass-through. It
only supports scalar and descriptor arguments.</p>
<ol class="arabic simple" start="2">
<li><p>Execution by operator</p></li>
</ol>
<p>Trino has a dedicated operator for table functions. It can handle table
functions with any number of table arguments as well as scalar and descriptor
arguments. To use this execution path, you provide an implementation of a
processor.</p>
<p>If your table function has one or more table arguments, you must implement
<code class="docutils literal notranslate"><span class="pre">TableFunctionDataProcessor</span></code>. It processes pages of input data.</p>
<p>If your table function is a source operator (it does not have table arguments),
you must implement <code class="docutils literal notranslate"><span class="pre">TableFunctionSplitProcessor</span></code>. It processes splits. The
connector that provides the function must provide a <code class="docutils literal notranslate"><span class="pre">ConnectorSplitSource</span></code>
for the function. With splits, the task can be divided so that each split
represents a subtask.</p>
</section>
<section id="access-control">
<h2 id="access-control">Access control<a class="headerlink" href="table-functions.html#access-control" title="Link to this heading">#</a></h2>
<p>The access control for table functions can be provided both on system and
connector level. It is based on the fully qualified table function name,
which consists of the catalog name, the schema name, and the function name,
in the syntax of <code class="docutils literal notranslate"><span class="pre">catalog.schema.function</span></code>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="functions.html" title="Functions"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Functions </span>
              </div>
            </a>
          
          
            <a href="system-access-control.html" title="System access control"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> System access control </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>