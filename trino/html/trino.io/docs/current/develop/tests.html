<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Test writing guidelines &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="tests.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Connectors" href="connectors.html" />
    <link rel="prev" title="SPI overview" href="spi-overview.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="tests.html#develop/tests" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Test writing guidelines </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="spi-overview.html" class="md-nav__link">SPI overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Test writing guidelines </label>
    
      <a href="tests.html#" class="md-nav__link md-nav__link--active">Test writing guidelines</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="tests.html#conventions-and-recommendations" class="md-nav__link">Conventions and recommendations</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#guidelines" class="md-nav__link">Guidelines</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tests.html#focus-on-high-value-tests" class="md-nav__link">Focus on high value tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-combinatorial-tests" class="md-nav__link">Avoid combinatorial tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-product-tests" class="md-nav__link">Avoid product tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-creating-testing-abstractions" class="md-nav__link">Avoid creating testing abstractions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-data-providers-and-parametric-tests" class="md-nav__link">Avoid data providers and parametric tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-writing-stateful-test-classes" class="md-nav__link">Avoid writing stateful test classes</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#do-not-try-to-manage-memory" class="md-nav__link">Do not try to manage memory</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#use-simple-resource-initialization" class="md-nav__link">Use simple resource initialization</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#keep-test-setup-and-teardown-simple" class="md-nav__link">Keep test setup and teardown simple</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#ensure-testability-of-new-plugin-and-connector-features" class="md-nav__link">Ensure testability of new plugin and connector features</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#keep-focus-on-plugin-and-connector-tests" class="md-nav__link">Keep focus on plugin and connector tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-flaky-tests" class="md-nav__link">Avoid flaky tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-disabling-tests" class="md-nav__link">Avoid disabling tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-using-assumptions-abort" class="md-nav__link">Avoid using <code class="docutils literal notranslate"><span class="pre">Assumptions.abort()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-test-inheritance" class="md-nav__link">Avoid test inheritance</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-helper-assertions" class="md-nav__link">Avoid helper assertions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#examples" class="md-nav__link">Examples</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tests.html#concurrency-for-tests" class="md-nav__link">Concurrency for tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-manual-lifecycle-management" class="md-nav__link">Avoid manual lifecycle management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-fake-abstractions" class="md-nav__link">Avoid fake abstractions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-custom-parallelization" class="md-nav__link">Avoid custom parallelization</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-parameterized-tests" class="md-nav__link">Avoid parameterized tests</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connectors.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-http.html" class="md-nav__link">Example HTTP connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="example-jdbc.html" class="md-nav__link">Example JDBC connector</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="insert.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> and <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span> <span class="pre">AS</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="supporting-merge.html" class="md-nav__link">Supporting <code class="docutils literal notranslate"><span class="pre">MERGE</span></code></a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="types.html" class="md-nav__link">Types</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="table-functions.html" class="md-nav__link">Table functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="system-access-control.html" class="md-nav__link">System access control</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="password-authenticator.html" class="md-nav__link">Password authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="certificate-authenticator.html" class="md-nav__link">Certificate authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="header-authenticator.html" class="md-nav__link">Header authenticator</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="group-provider.html" class="md-nav__link">Group provider</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="event-listener.html" class="md-nav__link">Event listener</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client-protocol.html" class="md-nav__link">Trino client REST API</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="tests.html#conventions-and-recommendations" class="md-nav__link">Conventions and recommendations</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#guidelines" class="md-nav__link">Guidelines</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tests.html#focus-on-high-value-tests" class="md-nav__link">Focus on high value tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-combinatorial-tests" class="md-nav__link">Avoid combinatorial tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-product-tests" class="md-nav__link">Avoid product tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-creating-testing-abstractions" class="md-nav__link">Avoid creating testing abstractions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-data-providers-and-parametric-tests" class="md-nav__link">Avoid data providers and parametric tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-writing-stateful-test-classes" class="md-nav__link">Avoid writing stateful test classes</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#do-not-try-to-manage-memory" class="md-nav__link">Do not try to manage memory</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#use-simple-resource-initialization" class="md-nav__link">Use simple resource initialization</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#keep-test-setup-and-teardown-simple" class="md-nav__link">Keep test setup and teardown simple</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#ensure-testability-of-new-plugin-and-connector-features" class="md-nav__link">Ensure testability of new plugin and connector features</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#keep-focus-on-plugin-and-connector-tests" class="md-nav__link">Keep focus on plugin and connector tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-flaky-tests" class="md-nav__link">Avoid flaky tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-disabling-tests" class="md-nav__link">Avoid disabling tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-using-assumptions-abort" class="md-nav__link">Avoid using <code class="docutils literal notranslate"><span class="pre">Assumptions.abort()</span></code></a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-test-inheritance" class="md-nav__link">Avoid test inheritance</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-helper-assertions" class="md-nav__link">Avoid helper assertions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#examples" class="md-nav__link">Examples</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="tests.html#concurrency-for-tests" class="md-nav__link">Concurrency for tests</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-manual-lifecycle-management" class="md-nav__link">Avoid manual lifecycle management</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-fake-abstractions" class="md-nav__link">Avoid fake abstractions</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-custom-parallelization" class="md-nav__link">Avoid custom parallelization</a>
        </li>
        <li class="md-nav__item"><a href="tests.html#avoid-parameterized-tests" class="md-nav__link">Avoid parameterized tests</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="test-writing-guidelines">
<h1 id="develop-tests--page-root">Test writing guidelines<a class="headerlink" href="tests.html#develop-tests--page-root" title="Link to this heading">#</a></h1>
<p>The Trino project aims to provide high quality artifacts and features to all
users. A suite of tests used for development, continuous integration builds,
and releases is critical for achieving this goal.</p>
<p>Trino acts as an integration layer for many different data sources with the help
of different connector plugin, and includes support of other plugins as well.
This results in the need testing many different use cases with a number of
external systems. The Trino test suite limits the executed tests for any changes
in pull requests as much as possible, while any merges to the master branch
process all tests.</p>
<p>The following requirements and guidelines help contributors to create tests with
the following goals:</p>
<ul class="simple">
<li><p>Fast execution.</p></li>
<li><p>Low requirements in terms of hardware, software, and compute power, and
therefore low cost.</p></li>
<li><p>Reliable execution and test results across multiple invocations.</p></li>
<li><p>Complex enough to have enough coverage of the execution paths, while avoiding
unnecessary complexity.</p></li>
<li><p>Enable quick, iterative development on developer workstation.</p></li>
</ul>
<section id="conventions-and-recommendations">
<h2 id="conventions-and-recommendations">Conventions and recommendations<a class="headerlink" href="tests.html#conventions-and-recommendations" title="Link to this heading">#</a></h2>
<p>The following section details conventions and recommendations to follow when
creating new tests or refactoring existing test code. The preferred approaches
The existing codebase is a mixture of newer test code that adheres to these
guidelines and older legacy code. The legacy test code should not be used as
example for new tests, rather follow the guidelines in this document.</p>
<p>Also note that the guidelines are subject to change in a process of further
refinement and improvements from practical experience.</p>
<p>A number of requirements apply to all new tests, and any refactoring work of
existing tests:</p>
<ul class="simple">
<li><p>All tests must use JUnit 5.</p></li>
<li><p>All tests must use statically imported AssertJ assertions, typically from
<code class="docutils literal notranslate"><span class="pre">org.assertj.core.api.Assertions</span></code>.</p></li>
<li><p>Test class names must start with <code class="docutils literal notranslate"><span class="pre">Test</span></code>, for example <code class="docutils literal notranslate"><span class="pre">TestExample</span></code></p></li>
<li><p>Test classes should be defined as package-private and final.</p></li>
<li><p>Test method must start with <code class="docutils literal notranslate"><span class="pre">test</span></code>, for example <code class="docutils literal notranslate"><span class="pre">testExplain()</span></code></p></li>
<li><p>Test methods should be defined as package-private.</p></li>
<li><p>Tests must be written as unit tests, including tests that abstract production
infrastructure with TestContainers, when possible. Product or other
integration tests should be avoided. These tests typically rely on external
infrastructure, use a full Trino runtime, and therefore are often slower and
suffer from reliability issues.</p></li>
<li><p>Tests must not be duplicated across unit and product tests, or different
plugins and other integrations.</p></li>
</ul>
</section>
<section id="guidelines">
<h2 id="guidelines">Guidelines<a class="headerlink" href="tests.html#guidelines" title="Link to this heading">#</a></h2>
<p>In addition to the requirements, a number of specific guidelines help with
authoring new tests as well as refactoring existing tests.</p>
<section id="focus-on-high-value-tests">
<h3 id="focus-on-high-value-tests">Focus on high value tests<a class="headerlink" href="tests.html#focus-on-high-value-tests" title="Link to this heading">#</a></h3>
<p>Testing in Trino is extremely expensive, and slows down all development as they
take hours of compute time in a limited environment. For large expensive tests,
consider the value the test brings to Trino, and ensure the value is justified
by the cost. We effectively have a limited budget for testing, and CI tests
queue on most days, often for many hours, which reduces the overall project
velocity.</p>
</section>
<section id="avoid-combinatorial-tests">
<h3 id="avoid-combinatorial-tests">Avoid combinatorial tests<a class="headerlink" href="tests.html#avoid-combinatorial-tests" title="Link to this heading">#</a></h3>
<p>Prefer tests of items in isolation and test a few common combinations to verify
integrations are functional. Do not implement tests for all possible
combinations.</p>
</section>
<section id="avoid-product-tests">
<h3 id="avoid-product-tests">Avoid product tests<a class="headerlink" href="tests.html#avoid-product-tests" title="Link to this heading">#</a></h3>
<p>If you can create a unit test for a feature, use a unit test and avoid writing a
product test. Over time the aim is to remove the majority of product tests, and
avoiding new product tests helps to prevent the migration costs from growing.</p>
<p>Only use product tests in the following cases:</p>
<ul class="simple">
<li><p>Minimal, high level integration testing that uses a full server. For example,
this can verify that a plugin works correctly with the plugin classloader and
classpath.</p></li>
<li><p>When the test code needs to run in a specialized environment, such as a
container with Kerberos configured. Only run the minimum set of tests
necessary to verify this integration.</p></li>
</ul>
</section>
<section id="avoid-creating-testing-abstractions">
<h3 id="avoid-creating-testing-abstractions">Avoid creating testing abstractions<a class="headerlink" href="tests.html#avoid-creating-testing-abstractions" title="Link to this heading">#</a></h3>
<p>The following approaches should be avoided because the existing build tools and
frameworks provide sufficient capabilities:</p>
<ul class="simple">
<li><p>Creating custom dispatch frameworks for parallelizing test execution</p></li>
<li><p>Creating test-specific assertion frameworks</p></li>
<li><p>Creating custom parameterized test frameworks</p></li>
</ul>
</section>
<section id="avoid-data-providers-and-parametric-tests">
<h3 id="avoid-data-providers-and-parametric-tests">Avoid data providers and parametric tests<a class="headerlink" href="tests.html#avoid-data-providers-and-parametric-tests" title="Link to this heading">#</a></h3>
<p>Data providers and parametric tests add unnecessary complexity. Consider
focusing on high value tests and avoiding combinatorial tests, and the
following details:</p>
<ul class="simple">
<li><p>Most data providers are either trivially small, or generate massive
combinatorial, indiscriminate, data sets for testing.</p></li>
<li><p>Prefer to write explicit test cases for trivial cases like a boolean
parameter.</p></li>
<li><p>For small datasets, use a “for-each item in an inline list”.</p></li>
<li><p>For larger datasets, consider using a type safe enum class.</p></li>
<li><p>For large test datasets, discuss your use case with Trino maintainers to work
on a solution or other guidance.</p></li>
<li><p>Avoid multiple independent data providers in a test, including multiple nested
for loops or multiple data provider parameters.</p></li>
</ul>
</section>
<section id="avoid-writing-stateful-test-classes">
<h3 id="avoid-writing-stateful-test-classes">Avoid writing stateful test classes<a class="headerlink" href="tests.html#avoid-writing-stateful-test-classes" title="Link to this heading">#</a></h3>
<p>Stateful tests can lead to issues from on one test leaking into other tests,
especially when test runs are parallelized. As a result debugging and
troubleshooting test failures and maintenance of the tests is more difficult. If
possible these stateful test classes should be avoided.</p>
</section>
<section id="do-not-try-to-manage-memory">
<h3 id="do-not-try-to-manage-memory">Do not try to manage memory<a class="headerlink" href="tests.html#do-not-try-to-manage-memory" title="Link to this heading">#</a></h3>
<p>JUnit and the JVM take care of test life cycle and memory management. Avoid
manual steps such as nulling out fields in <code class="docutils literal notranslate"><span class="pre">@After</span></code> methods to “free memory”. It
is safe to assign memory intensive objects to final fields, as the class is
automatically dereferenced after the test run.</p>
</section>
<section id="use-simple-resource-initialization">
<h3 id="use-simple-resource-initialization">Use simple resource initialization<a class="headerlink" href="tests.html#use-simple-resource-initialization" title="Link to this heading">#</a></h3>
<p>Prefer resource initialization in constructors and tear them down in <code class="docutils literal notranslate"><span class="pre">@After</span></code>
methods if necessary. This approach, combined with not nulling fields, allows
the fields to be final and behave like any <code class="docutils literal notranslate"><span class="pre">Closeable</span></code> class in normal Java code
Consider using the Guava <code class="docutils literal notranslate"><span class="pre">Closer</span></code> class to simplify cleanup.</p>
</section>
<section id="keep-test-setup-and-teardown-simple">
<h3 id="keep-test-setup-and-teardown-simple">Keep test setup and teardown simple<a class="headerlink" href="tests.html#keep-test-setup-and-teardown-simple" title="Link to this heading">#</a></h3>
<p>Avoid the <code class="docutils literal notranslate"><span class="pre">@Before</span></code>/<code class="docutils literal notranslate"><span class="pre">@After</span></code> each test method style of setup and teardown.</p>
<ul class="simple">
<li><p>Prefer try-with-resources if natural</p></li>
<li><p>If necessary, use a shared initialization or cleanup method that is explicitly
called.</p></li>
<li><p>If you have a test that benefits from @Before/After methods, discuss the
approach with the maintainers to develop a solution and improve guidance.</p></li>
</ul>
</section>
<section id="ensure-testability-of-new-plugin-and-connector-features">
<h3 id="ensure-testability-of-new-plugin-and-connector-features">Ensure testability of new plugin and connector features<a class="headerlink" href="tests.html#ensure-testability-of-new-plugin-and-connector-features" title="Link to this heading">#</a></h3>
<p>New plugin/connector features should be testable using one of the testing
plugins (e.g., memory or null). There are existing features only tested in
plugins in Hive, and over time we expect coverage using the testing plugins</p>
</section>
<section id="keep-focus-on-plugin-and-connector-tests">
<h3 id="keep-focus-on-plugin-and-connector-tests">Keep focus on plugin and connector tests<a class="headerlink" href="tests.html#keep-focus-on-plugin-and-connector-tests" title="Link to this heading">#</a></h3>
<p>For plugins and specifically connector plugins, focus on the code unique to the
plugin. Do not add tests for core engine features. Plugins should be focused on
the correctness of the SPI implementation, and compatibility with external
systems.</p>
</section>
<section id="avoid-flaky-tests">
<h3 id="avoid-flaky-tests">Avoid flaky tests<a class="headerlink" href="tests.html#avoid-flaky-tests" title="Link to this heading">#</a></h3>
<p>Flaky tests are test that are not reliable. Multiple runs of the same test
result in inconsistent results. Typically the tests are successful, and then
rarely fail. Reasons for flakiness include reliance on external, unstable
systems, connections, and other hard to troubleshoot setups.</p>
<p>Existing flaky tests using the legacy TestNG library can be marked with the
<code class="docutils literal notranslate"><span class="pre">@Flaky</span></code> annotation temporarily to improve CI reliability until a fix is
implemented:</p>
<ul class="simple">
<li><p>Ideally the fix is to make the test reliable.</p></li>
<li><p>Rewrite the test to not rely on flakey infrastructure, including the practice
to avoid HDFS.</p></li>
<li><p>If necessary, add explicit retries, but be cognizant of resource usage.</p></li>
</ul>
<p>After a certain time period, if the test hasn’t been fixed, it should be
removed.</p>
<p>New tests with the <code class="docutils literal notranslate"><span class="pre">@Flaky</span></code> annotation can not be introduced, since new tests
must use JUnit. Rewrite the test to be stable or avoid the test altogether.</p>
</section>
<section id="avoid-disabling-tests">
<h3 id="avoid-disabling-tests">Avoid disabling tests<a class="headerlink" href="tests.html#avoid-disabling-tests" title="Link to this heading">#</a></h3>
<p>Prefer to remove a test instead of disabling it. Test code is maintained and
updated as the codebase changes, and inactive tests just waste time and effort.</p>
<p>Disabled tests can be removed at any time.</p>
</section>
<section id="avoid-using-assumptions-abort">
<h3 id="avoid-using-assumptions-abort">Avoid using <code class="docutils literal notranslate"><span class="pre">Assumptions.abort()</span></code><a class="headerlink" href="tests.html#avoid-using-assumptions-abort" title="Link to this heading">#</a></h3>
<p>The approach to use <code class="docutils literal notranslate"><span class="pre">Assumptions.abort()</span></code> to skip a test, especially deep in the
call stack, makes it difficult to debug tests failures. The <code class="docutils literal notranslate"><span class="pre">abort()</span></code> works by
throwing an exception, which can be caught by intervening code inadvertently,
leading to misleading stack traces and test failures.</p>
</section>
<section id="avoid-test-inheritance">
<h3 id="avoid-test-inheritance">Avoid test inheritance<a class="headerlink" href="tests.html#avoid-test-inheritance" title="Link to this heading">#</a></h3>
<p>Inheritance of tests creates unnecessary complexity. Keep tests simple and use
composition if necessary.</p>
</section>
</section>
<section id="avoid-helper-assertions">
<h2 id="avoid-helper-assertions">Avoid helper assertions<a class="headerlink" href="tests.html#avoid-helper-assertions" title="Link to this heading">#</a></h2>
<p>The required usage of AssertJ provides a rich set of assertions, that typically
makes custom helper assertions unnecessary. Custom assertions often make tests
harder to follow and debug.</p>
<p>If you decide a helper assertion is needed, consider the following details:</p>
<ul class="simple">
<li><p>Start the name with <code class="docutils literal notranslate"><span class="pre">assert</span></code>, for example <code class="docutils literal notranslate"><span class="pre">assertSomeLogicWorks</span></code></p></li>
<li><p>Prefer private and static</p></li>
</ul>
</section>
<section id="examples">
<h2 id="examples">Examples<a class="headerlink" href="tests.html#examples" title="Link to this heading">#</a></h2>
<p>The following examples showcase recommended and discourage practices.</p>
<section id="concurrency-for-tests">
<h3 id="concurrency-for-tests">Concurrency for tests<a class="headerlink" href="tests.html#concurrency-for-tests" title="Link to this heading">#</a></h3>
<p>Use <code class="docutils literal notranslate"><span class="pre">PER_CLASS</span></code> for instances because <code class="docutils literal notranslate"><span class="pre">QueryAssertions</span></code> is too expensive to
create per-method, and a allow parallel execution of tests with <code class="docutils literal notranslate"><span class="pre">CONCURRENT</span></code>:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@TestInstance</span><span class="p">(</span><span class="n">PER_CLASS</span><span class="p">)</span>
<span class="nd">@Execution</span><span class="p">(</span><span class="n">CONCURRENT</span><span class="p">)</span>
<span class="kd">final</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TestJoin</span>
<span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QueryAssertions</span><span class="w"> </span><span class="n">assertions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QueryAssertions</span><span class="p">();</span>

<span class="w">    </span><span class="nd">@AfterAll</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">teardown</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">assertions</span><span class="p">.</span><span class="na">close</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testXXX</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">assertions</span><span class="p">.</span><span class="na">query</span><span class="p">(</span>
<span class="w">            </span><span class="s">"""</span>
<span class="s">            ...</span>
<span class="s">            """</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">matches</span><span class="p">(</span><span class="s">"..."</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="avoid-manual-lifecycle-management">
<h3 id="avoid-manual-lifecycle-management">Avoid manual lifecycle management<a class="headerlink" href="tests.html#avoid-manual-lifecycle-management" title="Link to this heading">#</a></h3>
<p>Avoid managing the lifecycle of a Closeable like a connection with
<code class="docutils literal notranslate"><span class="pre">@BeforeEach</span></code>/<code class="docutils literal notranslate"><span class="pre">@AfterEach</span></code> to reduce overhead:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@TestInstance</span><span class="p">(</span><span class="n">PER_METHOD</span><span class="p">)</span>
<span class="kd">final</span><span class="w"> </span><span class="kd">class</span> <span class="nc">Test</span>
<span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Connection</span><span class="w"> </span><span class="n">connection</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@BeforeEach</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// WRONG: create this in the test method using try-with-resources</span>
<span class="w">        </span><span class="n">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">newConnection</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@AfterEach</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">teardown</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="n">connection</span><span class="p">.</span><span class="na">close</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">test</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="p">...</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Using a try with resources approach allows clean parallelization of tests and
includes automatic memory management:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kd">final</span><span class="w"> </span><span class="kd">class</span> <span class="nc">Test</span>
<span class="p">{</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testSomething</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">(</span><span class="n">Connection</span><span class="w"> </span><span class="n">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">newConnection</span><span class="p">();)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="p">...</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testSomethingElse</span><span class="p">()</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">(</span><span class="n">Connection</span><span class="w"> </span><span class="n">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">newConnection</span><span class="p">();)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="p">...</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="avoid-fake-abstractions">
<h2 id="avoid-fake-abstractions">Avoid fake abstractions<a class="headerlink" href="tests.html#avoid-fake-abstractions" title="Link to this heading">#</a></h2>
<p>Avoid using fake abstraction for tests.</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@DataProvider</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"data"</span><span class="p">)</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">test</span><span class="p">(</span><span class="kt">boolean</span><span class="w"> </span><span class="n">flag</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="c1">// WRONG: use separate test methods</span>
<span class="w">    </span><span class="n">assertEqual</span><span class="p">(</span>
<span class="w">        </span><span class="n">flag</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">...,</span>
<span class="w">        </span><span class="n">flag</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">...);</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Replace with simplified separate assertions:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">test</span><span class="p">()</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(...).</span><span class="na">isEqualTo</span><span class="p">(...);</span><span class="w"> </span><span class="c1">// case corresponding to flag == true</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(...).</span><span class="na">isEqualTo</span><span class="p">(...);</span><span class="w"> </span><span class="c1">// case corresponding to flag == false</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="avoid-custom-parallelization">
<h2 id="avoid-custom-parallelization">Avoid custom parallelization<a class="headerlink" href="tests.html#avoid-custom-parallelization" title="Link to this heading">#</a></h2>
<p>Do not develop a custom parallel test execution framework:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@Test</span><span class="p">(</span><span class="n">dataProvider</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"parallelTests"</span><span class="p">)</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">testParallel</span><span class="p">(</span><span class="n">Runnable</span><span class="w"> </span><span class="n">runnable</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">   </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">       </span><span class="n">parallelTestsSemaphore</span><span class="p">.</span><span class="na">acquire</span><span class="p">();</span>
<span class="w">   </span><span class="p">}</span>
<span class="w">   </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">InterruptedException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">       </span><span class="n">Thread</span><span class="p">.</span><span class="na">currentThread</span><span class="p">().</span><span class="na">interrupt</span><span class="p">();</span>
<span class="w">       </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RuntimeException</span><span class="p">(</span><span class="n">e</span><span class="p">);</span>
<span class="w">   </span><span class="p">}</span>
<span class="w">   </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">       </span><span class="n">runnable</span><span class="p">.</span><span class="na">run</span><span class="p">();</span>
<span class="w">   </span><span class="p">}</span>
<span class="w">   </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">       </span><span class="n">parallelTestsSemaphore</span><span class="p">.</span><span class="na">release</span><span class="p">();</span>
<span class="w">   </span><span class="p">}</span>
<span class="p">}</span>

<span class="nd">@DataProvider</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"parallelTests"</span><span class="p">,</span><span class="w"> </span><span class="n">parallel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">)</span>
<span class="n">Object</span><span class="o">[][]</span><span class="w"> </span><span class="nf">parallelTests</span><span class="p">()</span>
<span class="p">{</span>
<span class="w">   </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Object</span><span class="o">[][]</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testCreateTable"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testCreateTable</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testInsert"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testInsert</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testDelete"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testDelete</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testDeleteWithSubquery"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testDeleteWithSubquery</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testUpdate"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testUpdate</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testUpdateWithSubquery"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testUpdateWithSubquery</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testMerge"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testMerge</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testAnalyzeTable"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testAnalyzeTable</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testExplainAnalyze"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testExplainAnalyze</span><span class="p">),</span>
<span class="w">        </span><span class="n">parallelTest</span><span class="p">(</span><span class="s">"testRequestTimeouts"</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">testRequestTimeouts</span><span class="p">)</span>
<span class="w">   </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Leave parallelization to JUnit instead, and implement separate test methods
instead.</p>
</section>
<section id="avoid-parameterized-tests">
<h2 id="avoid-parameterized-tests">Avoid parameterized tests<a class="headerlink" href="tests.html#avoid-parameterized-tests" title="Link to this heading">#</a></h2>
<p>Do not create a custom parameterized test framework:</p>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="nd">@Test</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">testTinyint</span><span class="p">()</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">SqlDataTypeTest</span><span class="p">.</span><span class="na">create</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">addRoundTrip</span><span class="p">(...)</span>
<span class="w">        </span><span class="p">.</span><span class="na">addRoundTrip</span><span class="p">(...)</span>
<span class="w">        </span><span class="p">.</span><span class="na">addRoundTrip</span><span class="p">(...)</span>
<span class="w">        </span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">getQueryRunner</span><span class="p">(),</span><span class="w"> </span><span class="n">trinoCreateAsSelect</span><span class="p">(</span><span class="s">"test_tinyint"</span><span class="p">))</span>
<span class="w">        </span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">getQueryRunner</span><span class="p">(),</span><span class="w"> </span><span class="n">trinoCreateAndInsert</span><span class="p">(</span><span class="s">"test_tinyint"</span><span class="p">))</span>
<span class="w">        </span><span class="p">.</span><span class="na">addRoundTrip</span><span class="p">(...)</span>
<span class="w">        </span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">getQueryRunner</span><span class="p">(),</span><span class="w"> </span><span class="n">clickhouseQuery</span><span class="p">(</span><span class="s">"tpch.test_tinyint"</span><span class="p">));</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="spi-overview.html" title="SPI overview"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> SPI overview </span>
              </div>
            </a>
          
          
            <a href="connectors.html" title="Connectors"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Connectors </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>