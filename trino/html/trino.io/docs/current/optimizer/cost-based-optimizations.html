<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Cost-based optimizations &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="cost-based-optimizations.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Pushdown" href="pushdown.html" />
    <link rel="prev" title="Cost in EXPLAIN" href="cost-in-explain.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="cost-based-optimizations.html#optimizer/cost-based-optimizations" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Cost-based optimizations </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="statistics.html" class="md-nav__link">Table statistics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cost-in-explain.html" class="md-nav__link">Cost in EXPLAIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Cost-based optimizations </label>
    
      <a href="cost-based-optimizations.html#" class="md-nav__link md-nav__link--active">Cost-based optimizations</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cost-based-optimizations.html#join-enumeration" class="md-nav__link">Join enumeration</a>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#join-distribution-selection" class="md-nav__link">Join distribution selection</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cost-based-optimizations.html#capping-replicated-table-size" class="md-nav__link">Capping replicated table size</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#syntactic-join-order" class="md-nav__link">Syntactic join order</a>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#connector-implementations" class="md-nav__link">Connector implementations</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="pushdown.html" class="md-nav__link">Pushdown</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="adaptive-plan-optimizations.html" class="md-nav__link">Adaptive plan optimizations</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="cost-based-optimizations.html#join-enumeration" class="md-nav__link">Join enumeration</a>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#join-distribution-selection" class="md-nav__link">Join distribution selection</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="cost-based-optimizations.html#capping-replicated-table-size" class="md-nav__link">Capping replicated table size</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#syntactic-join-order" class="md-nav__link">Syntactic join order</a>
        </li>
        <li class="md-nav__item"><a href="cost-based-optimizations.html#connector-implementations" class="md-nav__link">Connector implementations</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="cost-based-optimizations">
<h1 id="optimizer-cost-based-optimizations--page-root">Cost-based optimizations<a class="headerlink" href="cost-based-optimizations.html#optimizer-cost-based-optimizations--page-root" title="Link to this heading">#</a></h1>
<p>Trino supports several cost based optimizations, described below.</p>
<section id="join-enumeration">
<h2 id="join-enumeration">Join enumeration<a class="headerlink" href="cost-based-optimizations.html#join-enumeration" title="Link to this heading">#</a></h2>
<p>The order in which joins are executed in a query can have a significant impact
on the query’s performance. The aspect of join ordering that has the largest
impact on performance is the size of the data being processed and transferred
over the network. If a join which produces a lot of data is performed early in
the query’s execution, then subsequent stages need to process large amounts of
data for longer than necessary, increasing the time and resources needed for
processing the query.</p>
<p>With cost-based join enumeration, Trino uses <a class="reference internal" href="statistics.html"><span class="doc">Table statistics</span></a>
provided by connectors to estimate the costs for different join orders and
automatically picks the join order with the lowest computed costs.</p>
<p>The join enumeration strategy is governed by the <code class="docutils literal notranslate"><span class="pre">join_reordering_strategy</span></code>
<a class="reference internal" href="../sql/set-session.html#session-properties-definition"><span class="std std-ref">session property</span></a>, with the
<code class="docutils literal notranslate"><span class="pre">optimizer.join-reordering-strategy</span></code> configuration property providing the
default value.</p>
<p>The possible values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> (default) - enable full automatic join enumeration</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ELIMINATE_CROSS_JOINS</span></code> - eliminate unnecessary cross joins</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NONE</span></code> - purely syntactic join order</p></li>
</ul>
<p>If you are using <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> join enumeration and statistics are not
available or a cost can not be computed for any other reason, the
<code class="docutils literal notranslate"><span class="pre">ELIMINATE_CROSS_JOINS</span></code> strategy is used instead.</p>
</section>
<section id="join-distribution-selection">
<h2 id="join-distribution-selection">Join distribution selection<a class="headerlink" href="cost-based-optimizations.html#join-distribution-selection" title="Link to this heading">#</a></h2>
<p>Trino uses a hash-based join algorithm. For each join operator, a hash table
must be created from one join input, referred to as the build side. The other
input, called the probe side, is then iterated on. For each row, the hash table
is queried to find matching rows.</p>
<p>There are two types of join distributions:</p>
<ul class="simple">
<li><p>Partitioned: each node participating in the query builds a hash table from
only a fraction of the data</p></li>
<li><p>Broadcast: each node participating in the query builds a hash table from all
the data. The data is replicated to each node.</p></li>
</ul>
<p>Each type has advantages and disadvantages. Partitioned joins require
redistributing both tables using a hash of the join key. These joins can be much
slower than broadcast joins, but they allow much larger joins overall. Broadcast
joins are faster if the build side is much smaller than the probe side. However,
broadcast joins require that the tables on the build side of the join after
filtering fit in memory on each node, whereas distributed joins only need to fit
in distributed memory across all nodes.</p>
<p>With cost-based join distribution selection, Trino automatically chooses whether
to use a partitioned or broadcast join. With cost-based join enumeration, Trino
automatically chooses which sides are probe and build.</p>
<p>The join distribution strategy is governed by the <code class="docutils literal notranslate"><span class="pre">join_distribution_type</span></code>
session property, with the <code class="docutils literal notranslate"><span class="pre">join-distribution-type</span></code> configuration property
providing the default value.</p>
<p>The valid values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> (default) - join distribution type is determined automatically
for each join</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BROADCAST</span></code> - broadcast join distribution is used for all joins</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PARTITIONED</span></code> - partitioned join distribution is used for all join</p></li>
</ul>
<section id="capping-replicated-table-size">
<h3 id="capping-replicated-table-size">Capping replicated table size<a class="headerlink" href="cost-based-optimizations.html#capping-replicated-table-size" title="Link to this heading">#</a></h3>
<p>The join distribution type is automatically chosen when the join reordering
strategy is set to <code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code> or when the join distribution type is set to
<code class="docutils literal notranslate"><span class="pre">AUTOMATIC</span></code>. In both cases, it is possible to cap the maximum size of the
replicated table with the <code class="docutils literal notranslate"><span class="pre">join-max-broadcast-table-size</span></code> configuration
property or with the <code class="docutils literal notranslate"><span class="pre">join_max_broadcast_table_size</span></code> session property. This
allows you to improve cluster concurrency and prevent bad plans when the
cost-based optimizer misestimates the size of the joined tables.</p>
<p>By default, the replicated table size is capped to 100MB.</p>
</section>
</section>
<section id="syntactic-join-order">
<h2 id="syntactic-join-order">Syntactic join order<a class="headerlink" href="cost-based-optimizations.html#syntactic-join-order" title="Link to this heading">#</a></h2>
<p>If not using cost-based optimization, Trino defaults to syntactic join ordering.
While there is no formal way to optimize queries for this case, it is possible
to take advantage of how Trino implements joins to make them more performant.</p>
<p>Trino uses in-memory hash joins. When processing a join statement, Trino loads
the right-most table of the join into memory as the build side, then streams the
next right-most table as the probe side to execute the join. If a query has
multiple joins, the result of this first join stays in memory as the build side,
and the third right-most table is then used as the probe side, and so on for
additional joins. In the case where join order is made more complex, such as
when using parentheses to specify specific parents for joins, Trino may execute
multiple lower-level joins at once, but each step of that process follows the
same logic, and the same applies when the results are ultimately joined
together.</p>
<p>Because of this behavior, it is optimal to syntactically order joins in your SQL
queries from the largest tables to the smallest, as this minimizes memory usage.</p>
<p>As an example, if you have a small, medium, and large table and are using left
joins:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
<span class="w">  </span><span class="o">*</span>
<span class="k">FROM</span>
<span class="w">  </span><span class="n">large_table</span><span class="w"> </span><span class="n">l</span>
<span class="w">  </span><span class="k">LEFT</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">medium_table</span><span class="w"> </span><span class="n">m</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">l</span><span class="p">.</span><span class="n">user_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">m</span><span class="p">.</span><span class="n">user_id</span>
<span class="w">  </span><span class="k">LEFT</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">small_table</span><span class="w"> </span><span class="n">s</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">s</span><span class="p">.</span><span class="n">user_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">l</span><span class="p">.</span><span class="n">user_id</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This means of optimization is not a feature of Trino. It is an artifact of
how joins are implemented, and therefore this behavior may change without
notice.</p>
</div>
</section>
<section id="connector-implementations">
<h2 id="connector-implementations">Connector implementations<a class="headerlink" href="cost-based-optimizations.html#connector-implementations" title="Link to this heading">#</a></h2>
<p>In order for the Trino optimizer to use the cost based strategies,
the connector implementation must provide <a class="reference internal" href="statistics.html"><span class="doc">Table statistics</span></a>.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="cost-in-explain.html" title="Cost in EXPLAIN"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Cost in EXPLAIN </span>
              </div>
            </a>
          
          
            <a href="pushdown.html" title="Pushdown"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Pushdown </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>