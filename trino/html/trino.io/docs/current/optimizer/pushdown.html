<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="../_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="../_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="../_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="../_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Pushdown &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="../_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="../_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="../_static/trino.css@v=b5fc78e7.css" />
    <script src="../_static/jquery.js@v=5d32c60e"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="../_static/documentation_options.js@v=febf07ea"></script>
    <script src="../_static/doctools.js@v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="../_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="../_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="pushdown.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Adaptive plan optimizations" href="adaptive-plan-optimizations.html" />
    <link rel="prev" title="Cost-based optimizations" href="cost-based-optimizations.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="pushdown.html#optimizer/pushdown" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="../_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Pushdown </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="../search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="../_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../../versions.json",
        target_loc = "../../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="../_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="../index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="../overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../optimizer.html" class="md-nav__link">Query optimizer</a>
      <ul class="md-nav__list"> 
    <li class="md-nav__item">
    
    
      <a href="statistics.html" class="md-nav__link">Table statistics</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cost-in-explain.html" class="md-nav__link">Cost in EXPLAIN</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="cost-based-optimizations.html" class="md-nav__link">Cost-based optimizations</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Pushdown </label>
    
      <a href="pushdown.html#" class="md-nav__link md-nav__link--active">Pushdown</a>
      
        
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pushdown.html#predicate-pushdown" class="md-nav__link">Predicate pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#projection-pushdown" class="md-nav__link">Projection pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#dereference-pushdown" class="md-nav__link">Dereference pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#aggregation-pushdown" class="md-nav__link">Aggregation pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pushdown.html#limitations" class="md-nav__link">Limitations</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#join-pushdown" class="md-nav__link">Join pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#limit-pushdown" class="md-nav__link">Limit pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#top-n-pushdown" class="md-nav__link">Top-N pushdown</a>
        </li>
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="adaptive-plan-optimizations.html" class="md-nav__link">Adaptive plan optimizations</a>
      
    
    </li></ul>
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../glossary.html" class="md-nav__link">Glossary</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="../release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <label class="md-nav__title" for="__toc">Contents</label>
    <ul class="md-nav__list" data-md-scrollfix="">
        <li class="md-nav__item"><a href="pushdown.html#predicate-pushdown" class="md-nav__link">Predicate pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#projection-pushdown" class="md-nav__link">Projection pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#dereference-pushdown" class="md-nav__link">Dereference pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#aggregation-pushdown" class="md-nav__link">Aggregation pushdown</a><nav class="md-nav">
                <ul class="md-nav__list">
        <li class="md-nav__item"><a href="pushdown.html#limitations" class="md-nav__link">Limitations</a>
        </li></ul>
            </nav>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#join-pushdown" class="md-nav__link">Join pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#limit-pushdown" class="md-nav__link">Limit pushdown</a>
        </li>
        <li class="md-nav__item"><a href="pushdown.html#top-n-pushdown" class="md-nav__link">Top-N pushdown</a>
        </li>
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="pushdown">
<h1 id="optimizer-pushdown--page-root">Pushdown<a class="headerlink" href="pushdown.html#optimizer-pushdown--page-root" title="Link to this heading">#</a></h1>
<p>Trino can push down the processing of queries, or parts of queries, into the
connected data source. This means that a specific predicate, aggregation
function, or other operation, is passed through to the underlying database or
storage system for processing.</p>
<p>The results of this pushdown can include the following benefits:</p>
<ul class="simple">
<li><p>Improved overall query performance</p></li>
<li><p>Reduced network traffic between Trino and the data source</p></li>
<li><p>Reduced load on the remote data source</p></li>
</ul>
<p>These benefits often result in significant cost reduction.</p>
<p>Support for pushdown is specific to each connector and the relevant underlying
database or storage system.</p>
<section id="predicate-pushdown">
<span id="id1"></span><h2 id="predicate-pushdown">Predicate pushdown<a class="headerlink" href="pushdown.html#predicate-pushdown" title="Link to this heading">#</a></h2>
<p>Predicate pushdown optimizes row-based filtering. It uses the inferred filter,
typically resulting from a condition in a <code class="docutils literal notranslate"><span class="pre">WHERE</span></code> clause to omit unnecessary
rows. The processing is pushed down to the data source by the connector and then
processed by the data source.</p>
<p>If predicate pushdown for a specific clause is successful, the <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code> plan
for the query does not include a <code class="docutils literal notranslate"><span class="pre">ScanFilterProject</span></code> operation for that
clause.</p>
</section>
<section id="projection-pushdown">
<span id="id2"></span><h2 id="projection-pushdown">Projection pushdown<a class="headerlink" href="pushdown.html#projection-pushdown" title="Link to this heading">#</a></h2>
<p>Projection pushdown optimizes column-based filtering. It uses the columns
specified in the <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> clause and other parts of the query to limit access
to these columns. The processing is pushed down to the data source by the
connector and then the data source only reads and returns the necessary
columns.</p>
<p>If projection pushdown is successful, the <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code> plan for the query only
accesses the relevant columns in the <code class="docutils literal notranslate"><span class="pre">Layout</span></code> of the <code class="docutils literal notranslate"><span class="pre">TableScan</span></code> operation.</p>
</section>
<section id="dereference-pushdown">
<span id="id3"></span><h2 id="dereference-pushdown">Dereference pushdown<a class="headerlink" href="pushdown.html#dereference-pushdown" title="Link to this heading">#</a></h2>
<p>Projection pushdown and dereference pushdown limit access to relevant columns,
except dereference pushdown is more selective. It limits access to only read the
specified fields within a top level or nested <code class="docutils literal notranslate"><span class="pre">ROW</span></code> data type.</p>
<p>For example, consider a table in the Hive connector that has a <code class="docutils literal notranslate"><span class="pre">ROW</span></code> type
column with several fields. If a query only accesses one field, dereference
pushdown allows the file reader to read only that single field within the row.
The same applies to fields of a row nested within the top level row. This can
result in significant savings in the amount of data read from the storage
system.</p>
</section>
<section id="aggregation-pushdown">
<span id="id4"></span><h2 id="aggregation-pushdown">Aggregation pushdown<a class="headerlink" href="pushdown.html#aggregation-pushdown" title="Link to this heading">#</a></h2>
<p>Aggregation pushdown can take place provided the following conditions are satisfied:</p>
<ul class="simple">
<li><p>If aggregation pushdown is generally supported by the connector.</p></li>
<li><p>If pushdown of the specific function or functions is supported by the connector.</p></li>
<li><p>If the query structure allows pushdown to take place.</p></li>
</ul>
<p>You can check if pushdown for a specific query is performed by looking at the
<a class="reference internal" href="../sql/explain.html"><span class="doc">EXPLAIN plan</span></a> of the query. If an aggregate function is successfully
pushed down to the connector, the explain plan does <strong>not</strong> show that <code class="docutils literal notranslate"><span class="pre">Aggregate</span></code> operator.
The explain plan only shows the operations that are performed by Trino.</p>
<p>As an example, we loaded the TPC-H data set into a PostgreSQL database and then
queried it using the PostgreSQL connector:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">regionkey</span><span class="p">,</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">nation</span>
<span class="k">GROUP</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">regionkey</span><span class="p">;</span>
</pre></div>
</div>
<p>You can get the explain plan by prepending the above query with <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span>
<span class="k">SELECT</span><span class="w"> </span><span class="n">regionkey</span><span class="p">,</span><span class="w"> </span><span class="k">count</span><span class="p">(</span><span class="o">*</span><span class="p">)</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">nation</span>
<span class="k">GROUP</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">regionkey</span><span class="p">;</span>
</pre></div>
</div>
<p>The explain plan for this query does not show any <code class="docutils literal notranslate"><span class="pre">Aggregate</span></code> operator with
the <code class="docutils literal notranslate"><span class="pre">count</span></code> function, as this operation is now performed by the connector. You
can see the <code class="docutils literal notranslate"><span class="pre">count(*)</span></code> function as part of the PostgreSQL <code class="docutils literal notranslate"><span class="pre">TableScan</span></code>
operator. This shows you that the pushdown was successful.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Fragment 0 [SINGLE]
    Output layout: [regionkey_0, _generated_1]
    Output partitioning: SINGLE []
    Output[regionkey, _col1]
    │   Layout: [regionkey_0:bigint, _generated_1:bigint]
    │   Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: ?}
    │   regionkey := regionkey_0
    │   _col1 := _generated_1
    └─ RemoteSource[1]
            Layout: [regionkey_0:bigint, _generated_1:bigint]

Fragment 1 [SOURCE]
    Output layout: [regionkey_0, _generated_1]
    Output partitioning: SINGLE []
    TableScan[postgresql:tpch.nation tpch.nation columns=[regionkey:bigint:int8, count(*):_generated_1:bigint:bigint] groupingSets=[[regionkey:bigint:int8]], gro
        Layout: [regionkey_0:bigint, _generated_1:bigint]
        Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: 0B}
        _generated_1 := count(*):_generated_1:bigint:bigint
        regionkey_0 := regionkey:bigint:int8
</pre></div>
</div>
<p>A number of factors can prevent a push-down:</p>
<ul class="simple">
<li><p>adding a condition to the query</p></li>
<li><p>using a different aggregate function that cannot be pushed down into the connector</p></li>
<li><p>using a connector without pushdown support for the specific function</p></li>
</ul>
<p>As a result, the explain plan shows the <code class="docutils literal notranslate"><span class="pre">Aggregate</span></code> operation being performed
by Trino. This is a clear sign that now pushdown to the remote data source is not
performed, and instead Trino performs the aggregate processing.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Fragment 0 [SINGLE]
    Output layout: [regionkey, count]
    Output partitioning: SINGLE []
    Output[regionkey, _col1]
    │   Layout: [regionkey:bigint, count:bigint]
    │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
    │   _col1 := count
    └─ RemoteSource[1]
           Layout: [regionkey:bigint, count:bigint]

Fragment 1 [HASH]
    Output layout: [regionkey, count]
    Output partitioning: SINGLE []
    Aggregate(FINAL)[regionkey]
    │   Layout: [regionkey:bigint, count:bigint]
    │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
    │   count := count("count_0")
    └─ LocalExchange[HASH][$hashvalue] ("regionkey")
       │   Layout: [regionkey:bigint, count_0:bigint, $hashvalue:bigint]
       │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
       └─ RemoteSource[2]
              Layout: [regionkey:bigint, count_0:bigint, $hashvalue_1:bigint]

Fragment 2 [SOURCE]
    Output layout: [regionkey, count_0, $hashvalue_2]
    Output partitioning: HASH [regionkey][$hashvalue_2]
    Project[]
    │   Layout: [regionkey:bigint, count_0:bigint, $hashvalue_2:bigint]
    │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
    │   $hashvalue_2 := combine_hash(bigint '0', COALESCE("$operator$hash_code"("regionkey"), 0))
    └─ Aggregate(PARTIAL)[regionkey]
       │   Layout: [regionkey:bigint, count_0:bigint]
       │   count_0 := count(*)
       └─ TableScan[tpch:nation:sf0.01, grouped = false]
              Layout: [regionkey:bigint]
              Estimates: {rows: 25 (225B), cpu: 225, memory: 0B, network: 0B}
              regionkey := tpch:regionkey
</pre></div>
</div>
<section id="limitations">
<h3 id="limitations">Limitations<a class="headerlink" href="pushdown.html#limitations" title="Link to this heading">#</a></h3>
<p>Aggregation pushdown does not support a number of more complex statements:</p>
<ul class="simple">
<li><p>complex grouping operations such as <code class="docutils literal notranslate"><span class="pre">ROLLUP</span></code>, <code class="docutils literal notranslate"><span class="pre">CUBE</span></code>, or <code class="docutils literal notranslate"><span class="pre">GROUPING</span> <span class="pre">SETS</span></code></p></li>
<li><p>expressions inside the aggregation function call: <code class="docutils literal notranslate"><span class="pre">sum(a</span> <span class="pre">*</span> <span class="pre">b)</span></code></p></li>
<li><p>coercions: <code class="docutils literal notranslate"><span class="pre">sum(integer_column)</span></code></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#aggregate-function-ordering-during-aggregation"><span class="std std-ref">aggregations with ordering</span></a></p></li>
<li><p><a class="reference internal" href="../functions/aggregate.html#aggregate-function-filtering-during-aggregation"><span class="std std-ref">aggregations with filter</span></a></p></li>
</ul>
</section>
</section>
<section id="join-pushdown">
<span id="id5"></span><h2 id="join-pushdown">Join pushdown<a class="headerlink" href="pushdown.html#join-pushdown" title="Link to this heading">#</a></h2>
<p>Join pushdown allows the connector to delegate the table join operation to the
underlying data source. This can result in performance gains, and allows Trino
to perform the remaining query processing on a smaller amount of data.</p>
<p>The specifics for the supported pushdown of table joins varies for each data
source, and therefore for each connector.</p>
<p>However, there are some generic conditions that must be met in order for a join
to be pushed down:</p>
<ul class="simple">
<li><p>all predicates that are part of the join must be possible to be pushed down</p></li>
<li><p>the tables in the join must be from the same catalog</p></li>
</ul>
<p>You can verify if pushdown for a specific join is performed by looking at the
<a class="reference internal" href="../sql/explain.html"><span class="doc">EXPLAIN</span></a>  plan of the query. The explain plan does not
show a <code class="docutils literal notranslate"><span class="pre">Join</span></code> operator, if the join is pushed down to the data source by the
connector:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="k">c</span><span class="p">.</span><span class="n">custkey</span><span class="p">,</span><span class="w"> </span><span class="n">o</span><span class="p">.</span><span class="n">orderkey</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">orders</span><span class="w"> </span><span class="n">o</span><span class="w"> </span><span class="k">JOIN</span><span class="w"> </span><span class="n">customer</span><span class="w"> </span><span class="k">c</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">c</span><span class="p">.</span><span class="n">custkey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">o</span><span class="p">.</span><span class="n">custkey</span><span class="p">;</span>
</pre></div>
</div>
<p>The following plan results from the PostgreSQL connector querying TPC-H
data in a PostgreSQL database. It does not show any <code class="docutils literal notranslate"><span class="pre">Join</span></code> operator as a
result of the successful join push down.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Fragment 0 [SINGLE]
    Output layout: [custkey, orderkey]
    Output partitioning: SINGLE []
    Output[custkey, orderkey]
    │   Layout: [custkey:bigint, orderkey:bigint]
    │   Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: ?}
    └─ RemoteSource[1]
           Layout: [orderkey:bigint, custkey:bigint]

Fragment 1 [SOURCE]
    Output layout: [orderkey, custkey]
    Output partitioning: SINGLE []
    TableScan[postgres:Query[SELECT l."orderkey" AS "orderkey_0", l."custkey" AS "custkey_1", r."custkey" AS "custkey_2" FROM (SELECT "orderkey", "custkey" FROM "tpch"."orders") l INNER JOIN (SELECT "custkey" FROM "tpch"."customer") r O
        Layout: [orderkey:bigint, custkey:bigint]
        Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: 0B}
        orderkey := orderkey_0:bigint:int8
        custkey := custkey_1:bigint:int8
</pre></div>
</div>
<p>It is typically beneficial to push down a join. Pushing down a join can also
increase the row count compared to the size of the input to the join. This
may impact performance.</p>
</section>
<section id="limit-pushdown">
<span id="id6"></span><h2 id="limit-pushdown">Limit pushdown<a class="headerlink" href="pushdown.html#limit-pushdown" title="Link to this heading">#</a></h2>
<p>A <a class="reference internal" href="../sql/select.html#limit-clause"><span class="std std-ref">LIMIT or FETCH FIRST clause</span></a> reduces the number of returned records for a statement.
Limit pushdown enables a connector to push processing of such queries of
unsorted record to the underlying data source.</p>
<p>A pushdown of this clause can improve the performance of the query and
significantly reduce the amount of data transferred from the data source to
Trino.</p>
<p>Queries include sections such as <code class="docutils literal notranslate"><span class="pre">LIMIT</span> <span class="pre">N</span></code> or <code class="docutils literal notranslate"><span class="pre">FETCH</span> <span class="pre">FIRST</span> <span class="pre">N</span> <span class="pre">ROWS</span></code>.</p>
<p>Implementation and support is connector-specific since different data sources have varying capabilities.</p>
</section>
<section id="top-n-pushdown">
<span id="topn-pushdown"></span><h2 id="top-n-pushdown">Top-N pushdown<a class="headerlink" href="pushdown.html#top-n-pushdown" title="Link to this heading">#</a></h2>
<p>The combination of a <a class="reference internal" href="../sql/select.html#limit-clause"><span class="std std-ref">LIMIT or FETCH FIRST clause</span></a> with an <a class="reference internal" href="../sql/select.html#order-by-clause"><span class="std std-ref">ORDER BY clause</span></a> creates
a small set of records to return out of a large sorted dataset. It relies on the
order to determine which records need to be returned, and is therefore quite
different to optimize compared to a <a class="reference internal" href="pushdown.html#limit-pushdown"><span class="std std-ref">Limit pushdown</span></a>.</p>
<p>The pushdown for such a query is called a Top-N pushdown, since the operation is
returning the top N rows. It enables a connector to push processing of such
queries to the underlying data source, and therefore significantly reduces the
amount of data transferred to and processed by Trino.</p>
<p>Queries include sections such as <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span> <span class="pre">...</span> <span class="pre">LIMIT</span> <span class="pre">N</span></code> or <code class="docutils literal notranslate"><span class="pre">ORDER</span> <span class="pre">BY</span> <span class="pre">...</span> <span class="pre">FETCH</span> <span class="pre">FIRST</span> <span class="pre">N</span> <span class="pre">ROWS</span></code>.</p>
<p>Implementation and support is connector-specific since different data sources
support different SQL syntax and processing.</p>
<p>For example, you can find two queries to learn how to identify Top-N pushdown behavior in the following section.</p>
<p>First, a concrete example of a Top-N pushdown query on top of a PostgreSQL database:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">name</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">postgresql</span><span class="p">.</span><span class="k">public</span><span class="p">.</span><span class="n">company</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">id</span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span>
</pre></div>
</div>
<p>You can get the explain plan by prepending the above query with <code class="docutils literal notranslate"><span class="pre">EXPLAIN</span></code>:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">EXPLAIN</span><span class="w"> </span><span class="k">SELECT</span><span class="w"> </span><span class="n">id</span><span class="p">,</span><span class="w"> </span><span class="n">name</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">postgresql</span><span class="p">.</span><span class="k">public</span><span class="p">.</span><span class="n">company</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">id</span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Fragment 0 [SINGLE]
    Output layout: [id, name]
    Output partitioning: SINGLE []
    Stage Execution Strategy: UNGROUPED_EXECUTION
    Output[id, name]
    │   Layout: [id:integer, name:varchar]
    │   Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: ?}
    └─ RemoteSource[1]
           Layout: [id:integer, name:varchar]

Fragment 1 [SOURCE]
    Output layout: [id, name]
    Output partitioning: SINGLE []
    Stage Execution Strategy: UNGROUPED_EXECUTION
    TableScan[postgresql:public.company public.company sortOrder=[id:integer:int4 ASC NULLS LAST] limit=5, grouped = false]
        Layout: [id:integer, name:varchar]
        Estimates: {rows: ? (?), cpu: ?, memory: 0B, network: 0B}
        name := name:varchar:text
        id := id:integer:int4
</pre></div>
</div>
<p>Second, an example of a Top-N query on the <code class="docutils literal notranslate"><span class="pre">tpch</span></code> connector which does not support
Top-N pushdown functionality:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">custkey</span><span class="p">,</span><span class="w"> </span><span class="n">name</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">tpch</span><span class="p">.</span><span class="n">sf1</span><span class="p">.</span><span class="n">customer</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">custkey</span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span>
</pre></div>
</div>
<p>The related query plan:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Fragment 0 [SINGLE]
    Output layout: [custkey, name]
    Output partitioning: SINGLE []
    Stage Execution Strategy: UNGROUPED_EXECUTION
    Output[custkey, name]
    │   Layout: [custkey:bigint, name:varchar(25)]
    │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
    └─ TopN[5 by (custkey ASC NULLS LAST)]
       │   Layout: [custkey:bigint, name:varchar(25)]
       └─ LocalExchange[SINGLE] ()
          │   Layout: [custkey:bigint, name:varchar(25)]
          │   Estimates: {rows: ? (?), cpu: ?, memory: ?, network: ?}
          └─ RemoteSource[1]
                 Layout: [custkey:bigint, name:varchar(25)]

Fragment 1 [SOURCE]
    Output layout: [custkey, name]
    Output partitioning: SINGLE []
    Stage Execution Strategy: UNGROUPED_EXECUTION
    TopNPartial[5 by (custkey ASC NULLS LAST)]
    │   Layout: [custkey:bigint, name:varchar(25)]
    └─ TableScan[tpch:customer:sf1.0, grouped = false]
           Layout: [custkey:bigint, name:varchar(25)]
           Estimates: {rows: 150000 (4.58MB), cpu: 4.58M, memory: 0B, network: 0B}
           custkey := tpch:custkey
           name := tpch:name
</pre></div>
</div>
<p>In the preceding query plan, the Top-N operation <code class="docutils literal notranslate"><span class="pre">TopN[5</span> <span class="pre">by</span> <span class="pre">(custkey</span> <span class="pre">ASC</span> <span class="pre">NULLS</span> <span class="pre">LAST)]</span></code>
is being applied in the <code class="docutils literal notranslate"><span class="pre">Fragment</span> <span class="pre">0</span></code> by Trino and not by the source database.</p>
<p>Note that, compared to the query executed on top of the <code class="docutils literal notranslate"><span class="pre">tpch</span></code> connector,
the explain plan of the query applied on top of the <code class="docutils literal notranslate"><span class="pre">postgresql</span></code> connector
is missing the reference to the operation <code class="docutils literal notranslate"><span class="pre">TopN[5</span> <span class="pre">by</span> <span class="pre">(id</span> <span class="pre">ASC</span> <span class="pre">NULLS</span> <span class="pre">LAST)]</span></code>
in the <code class="docutils literal notranslate"><span class="pre">Fragment</span> <span class="pre">0</span></code>.
The absence of the <code class="docutils literal notranslate"><span class="pre">TopN</span></code> Trino operator in the <code class="docutils literal notranslate"><span class="pre">Fragment</span> <span class="pre">0</span></code> from the query plan
demonstrates that the query benefits of the Top-N pushdown optimization.</p>
</section>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="cost-based-optimizations.html" title="Cost-based optimizations"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Cost-based optimizations </span>
              </div>
            </a>
          
          
            <a href="adaptive-plan-optimizations.html" title="Adaptive plan optimizations"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Adaptive plan optimizations </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="../_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>