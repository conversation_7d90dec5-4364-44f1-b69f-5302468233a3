<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="lang:clipboard.copy" content="Copy to clipboard">
  <meta name="lang:clipboard.copied" content="Copied to clipboard">
  <meta name="lang:search.language" content="en">
  <meta name="lang:search.pipeline.stopwords" content="True">
  <meta name="lang:search.pipeline.trimmer" content="True">
  <meta name="lang:search.result.none" content="No matching documents">
  <meta name="lang:search.result.one" content="1 matching document">
  <meta name="lang:search.result.other" content="# matching documents">
  <meta name="lang:search.tokenizer" content="[\s\-]+">

  
    <link href="https://fonts.gstatic.com/" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto+Mono:400,500,700|Roboto:300,400,400i,700&display=fallback" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Alata&family=Open+Sans:ital,wght%400,300;0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
      body,
      input {
        font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif
      }

      code,
      kbd,
      pre {
        font-family: "Roboto Mono", "Courier New", Courier, monospace
      }
    </style>
  

  <link rel="stylesheet" href="_static/stylesheets/application.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-palette.css"/>
  <link rel="stylesheet" href="_static/stylesheets/application-fixes.css"/>
  
  <link rel="stylesheet" href="_static/fonts/material-icons.css"/>
  
  <meta name="theme-color" content="2196f3">
  <script src="_static/javascripts/modernizr.js"></script>
  
  
  
    <title>Glossary &#8212; Trino 476 Documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css@v=bbebba6e.css" />
    <link rel="stylesheet" type="text/css" href="_static/material.css@v=79c92029.css" />
    <link rel="stylesheet" type="text/css" href="_static/copybutton.css@v=76b2166b.css" />
    <link rel="stylesheet" type="text/css" href="_static/trino.css@v=b5fc78e7.css" />
    <script src="_static/jquery.js@v=5d32c60e"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js@v=2cd50e6c"></script>
    <script src="_static/documentation_options.js@v=febf07ea"></script>
    <script src="_static/doctools.js@v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js@v=dc90522c"></script>
    <script src="_static/clipboard.min.js@v=a7894cd8"></script>
    <script src="_static/copybutton.js@v=f281be69"></script>
    <link rel="canonical" href="glossary.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Appendix" href="appendix.html" />
    <link rel="prev" title="Trino client REST API" href="develop/client-protocol.html" />
  
   

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RJ94STKPJ5"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RJ94STKPJ5');
      gtag('config', 'UA-133457846-1');
      gtag('config', 'AW-1036784065');
      window._linkedin_data_partner_ids = ['2842796'];
    </script>
    <script async src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script>
  </head>
  <body dir=ltr
        data-md-color-primary= data-md-color-accent=>
  
  <svg class="md-svg">
    <defs data-children-count="0">
      
      <svg xmlns="http://www.w3.org/2000/svg" width="416" height="448" viewBox="0 0 416 448" id="__github"><path fill="currentColor" d="M160 304q0 10-3.125 20.5t-10.75 19T128 352t-18.125-8.5-10.75-19T96 304t3.125-20.5 10.75-19T128 256t18.125 8.5 10.75 19T160 304zm160 0q0 10-3.125 20.5t-10.75 19T288 352t-18.125-8.5-10.75-19T256 304t3.125-20.5 10.75-19T288 256t18.125 8.5 10.75 19T320 304zm40 0q0-30-17.25-51T296 232q-10.25 0-48.75 5.25Q229.5 240 208 240t-39.25-2.75Q130.75 232 120 232q-29.5 0-46.75 21T56 304q0 22 8 38.375t20.25 25.75 30.5 15 35 7.375 37.25 1.75h42q20.5 0 37.25-1.75t35-7.375 30.5-15 20.25-25.75T360 304zm56-44q0 51.75-15.25 82.75-9.5 19.25-26.375 33.25t-35.25 21.5-42.5 11.875-42.875 5.5T212 416q-19.5 0-35.5-.75t-36.875-3.125-38.125-7.5-34.25-12.875T37 371.5t-21.5-28.75Q0 312 0 260q0-59.25 34-99-6.75-20.5-6.75-42.5 0-29 12.75-54.5 27 0 47.5 9.875t47.25 30.875Q171.5 96 212 96q37 0 70 8 26.25-20.5 46.75-30.25T376 64q12.75 25.5 12.75 54.5 0 21.75-6.75 42 34 40 34 99.5z"/></svg>
      
    </defs>
  </svg>
  
  <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer">
  <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search">
  <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
  <a href="glossary.html#glossary" tabindex="1" class="md-skip"> Skip to content </a>
  <header class="md-header" data-md-component="header">
    <nav class="md-header-nav md-grid">
        <div class="md-flex navheader">
            <div class="md-flex__cell md-flex__cell--shrink">
                <a href="https://trino.io/" title="Trino"
                   class="md-header-nav__button md-logo">
                    
                    <!-- https://github.com/bashtage/sphinx-material/issues/136 -->
                    <img src="_static/trino.svg" height="26"
                         alt="Trino 476 Documentation logo">
                    
                </a>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch">
                <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
                    <span class="md-header-nav__topic">Trino 476 Documentation</span>
                    <span class="md-header-nav__topic"> Glossary </span>
                </div>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
                <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
                
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" action="search.html" method="get" name="search">
      <input type="text" class="md-search__input" name="q" placeholder="Search"
             autocapitalize="off" autocomplete="off" spellcheck="false"
             data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>

            </div>
            
            <div class="md-flex__cell md-flex__cell--shrink">
                <div class="md-header-nav__source">
                    <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
                </div>
            </div>
            
            
  
  <script src="_static/javascripts/version_dropdown.js"></script>
  <script>
    var json_loc = "../versions.json",
        target_loc = "../",
        text = "Versions";
    $( document ).ready( add_version_dropdown(json_loc, target_loc, text));
  </script>
  

        </div>
    </nav>
</header>

  
  <div class="md-container">
    
    
    <!-- empty -->
    <main class="md-main">
      <div class="md-main__inner md-grid" data-md-component="container">
        
          <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                <nav class="md-nav md-nav--primary" data-md-level="0">
    <label class="md-nav__title md-nav__title--site" for="__drawer">
        <a href="https://trino.io/" title="Trino" class="md-nav__button md-logo">
            
            <img src="_static/trino.svg" alt=" logo" width="48" height="48">
            
        </a>
        <a href="index.html"
           title="Trino 476 Documentation">Trino 476 Documentation</a>
    </label>
    <div class="md-nav__source">
        <a href="https://github.com/trinodb/trino" title="Go to repository" class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="28" height="28">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>
  
  <div class="md-source__repository">
    Trino
  </div>
</a>
    </div>
    
    

  
  <ul class="md-nav__list">
    <li class="md-nav__item">
    
    
      <a href="overview.html" class="md-nav__link">Overview</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="installation.html" class="md-nav__link">Installation</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="client.html" class="md-nav__link">Clients</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="security.html" class="md-nav__link">Security</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="admin.html" class="md-nav__link">Administration</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="optimizer.html" class="md-nav__link">Query optimizer</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="connector.html" class="md-nav__link">Connectors</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="object-storage.html" class="md-nav__link">Object storage</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="functions.html" class="md-nav__link">Functions and operators</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="udf.html" class="md-nav__link">User-defined functions</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="language.html" class="md-nav__link">SQL language</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="sql.html" class="md-nav__link">SQL statement syntax</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="develop.html" class="md-nav__link">Developer guide</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    <label class="md-nav__link md-nav__link--active" for="__toc"> Glossary </label>
    
      <a href="glossary.html#" class="md-nav__link md-nav__link--active">Glossary</a>
      
        
<nav class="md-nav md-nav--secondary">
    <ul class="md-nav__list" data-md-scrollfix="">
    </ul>
</nav>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="appendix.html" class="md-nav__link">Appendix</a>
      
    
    </li>
    <li class="md-nav__item">
    
    
      <a href="release.html" class="md-nav__link">Release notes</a>
      
    
    </li>
  </ul>
  

</nav>
              </div>
            </div>
          </div>
          <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
            <div class="md-sidebar__scrollwrap">
              <div class="md-sidebar__inner">
                
<nav class="md-nav md-nav--secondary">
    <ul class="md-nav__list" data-md-scrollfix="">
    </ul>
</nav>
              </div>
            </div>
          </div>
        
        <div class="md-content">
          <article class="md-content__inner md-typeset" role="main">
            
  <section id="glossary">
<h1 id="glossary--page-root">Glossary<a class="headerlink" href="glossary.html#glossary--page-root" title="Link to this heading">#</a></h1>
<p>The glossary contains a list of key Trino terms and definitions.</p>
<dl class="simple myst" id="glosscatalog">
<dt>Catalog</dt><dd><p>Catalogs define and name a configuration for connecting to a data source,
allowing users to query the connected data. Each catalog’s configuration
specifies a <a class="reference internal" href="glossary.html#glossconnector"><span class="std std-ref">connector</span></a> to define which data source
the catalog connects to. For more information about catalogs, see
<a class="reference internal" href="overview/concepts.html#trino-concept-catalog"><span class="std std-ref">Catalog</span></a>.</p>
</dd>
</dl>
<dl class="simple myst" id="glosscert">
<dt>Certificate</dt><dd><p>A public key <a class="reference external" href="https://wikipedia.org/wiki/Public_key_certificate">certificate</a> issued by a <a class="reference internal" href="glossary.html#glossca"><span class="std std-ref">CA</span></a>, sometimes abbreviated as cert, that verifies the ownership of a
server’s private keys. Certificate format is specified in the <a class="reference external" href="https://wikipedia.org/wiki/X.509">X.509</a> standard.</p>
</dd>
</dl>
<dl class="simple myst" id="glossca">
<dt>Certificate Authority (CA)</dt><dd><p>A trusted organization that signs and issues certificates. Its signatures
can be used to verify the validity of <a class="reference internal" href="glossary.html#glosscert"><span class="std std-ref">certificates</span></a>.</p>
</dd>
<dt>Cluster</dt><dd><p>A Trino cluster provides the resources to run queries against numerous data
sources. Clusters define the number of nodes, the configuration for the JVM
runtime, configured data sources, and others aspects. For more information,
see <a class="reference internal" href="overview/concepts.html#trino-concept-cluster"><span class="std std-ref">Cluster</span></a>.</p>
</dd>
</dl>
<dl class="simple myst" id="glossconnector">
<dt>Connector</dt><dd><p>Translates data from a data source into Trino schemas, tables, columns,
rows, and data types. A <a class="reference internal" href="connector.html"><span class="doc">connector</span></a> is specific to a data
source, and is used in <a class="reference internal" href="glossary.html#glosscatalog"><span class="std std-ref">catalog</span></a> configurations to
define what data source the catalog connects to. A connector is one of many
types of <a class="reference internal" href="glossary.html#glossplugin"><span class="std std-ref">plugins</span></a></p>
</dd>
</dl>
<dl class="simple myst" id="glosscontainer">
<dt>Container</dt><dd><p>A lightweight virtual package of software that contains libraries, binaries,
code, configuration files, and other dependencies needed to deploy an
application. A running container does not include an operating system,
instead using the operating system of the host machine. To learn more,
read about <a class="reference external" href="https://kubernetes.io/docs/concepts/containers/">containers</a>
in the Kubernetes documentation.</p>
</dd>
</dl>
<dl class="simple myst" id="glossdatasource">
<dt>Data source</dt><dd><p>A system from which data is retrieved - for example, PostgreSQL or Iceberg
on S3 data. In Trino, users query data sources with <a class="reference internal" href="glossary.html#glosscatalog"><span class="std std-ref">catalogs</span></a> that connect to each source. See
<a class="reference internal" href="overview/concepts.html#trino-concept-data-source"><span class="std std-ref">Data source</span></a> for more information.</p>
</dd>
</dl>
<dl class="simple myst" id="glossdatavirtualization">
<dt>Data virtualization</dt><dd><p><a class="reference external" href="https://wikipedia.org/wiki/Data_virtualization">Data virtualization</a> is a
method of abstracting an interaction with multiple <a class="reference internal" href="glossary.html#glossdatasource"><span class="std std-ref">heterogeneous data sources</span></a>, without needing to know the distributed nature
of the data, its format, or any other technical details involved in
presenting the data.</p>
</dd>
</dl>
<dl class="simple myst" id="glossgzip">
<dt>gzip</dt><dd><p><a class="reference external" href="https://wikipedia.org/wiki/Gzip">gzip</a> is a compression format and
software that compresses and decompresses files. This format is used several
ways in Trino, including deployment and compressing files in <a class="reference internal" href="glossary.html#glossobjectstorage"><span class="std std-ref">object storage</span></a>. The most common extension for gzip-compressed
files is <code class="docutils literal notranslate"><span class="pre">.gz</span></code>.</p>
</dd>
</dl>
<dl class="simple myst" id="glosshdfs">
<dt>HDFS</dt><dd><p><a class="reference external" href="https://wikipedia.org/wiki/Apache_Hadoop#HDFS">Hadoop Distributed Filesystem (HDFS)</a> is a scalable <a class="reference internal" href="glossary.html#glossopensource"><span class="std std-ref">open source</span></a> filesystem that was one of the earliest
distributed big data systems created to store large amounts of data for the
<a class="reference external" href="https://wikipedia.org/wiki/Apache_Hadoop">Hadoop ecosystem</a>.</p>
</dd>
</dl>
<dl class="simple myst" id="glossjks">
<dt>Java KeyStore (JKS)</dt><dd><p>The system of public key cryptography supported as one part of the Java
security APIs. The legacy JKS system recognizes keys and <a class="reference internal" href="glossary.html#glosscert"><span class="std std-ref">certificates</span></a> stored in <em>keystore</em> files, typically with the <code class="docutils literal notranslate"><span class="pre">.jks</span></code>
extension, and by default relies on a system-level list of <a class="reference internal" href="glossary.html#glossca"><span class="std std-ref">CAs</span></a> in <em>truststore</em> files installed as part of the current Java
installation.</p>
</dd>
<dt>Key</dt><dd><p>A cryptographic key specified as a pair of public and private strings
generally used in the context of <a class="reference internal" href="glossary.html#glosstls"><span class="std std-ref">TLS</span></a> to secure public
network traffic.</p>
</dd>
</dl>
<dl class="simple myst" id="glosslb">
<dt>Load Balancer (LB)</dt><dd><p>Software or a hardware device that sits on a network edge and accepts
network connections on behalf of servers behind that wall, distributing
traffic across network and server infrastructure to balance the load on
networked services.</p>
</dd>
</dl>
<dl class="simple myst" id="glossobjectstorage">
<dt>Object storage</dt><dd><p><a class="reference external" href="https://en.wikipedia.org/wiki/Object_storage">Object storage</a> is a file
storage mechanism. Examples of compatible object stores include the
following:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://aws.amazon.com/s3">Amazon S3</a></p></li>
<li><p><a class="reference external" href="https://cloud.google.com/storage">Google Cloud Storage</a></p></li>
<li><p><a class="reference external" href="https://azure.microsoft.com/en-us/products/storage/blobs">Azure Blob Storage</a></p></li>
<li><p><a class="reference external" href="https://min.io/">MinIO</a> and other S3-compatible stores</p></li>
<li><p><a class="reference internal" href="glossary.html#glosshdfs"><span class="std std-ref">HDFS</span></a></p></li>
</ul>
</dd>
</dl>
<dl class="simple myst" id="glossopensource">
<dt>Open-source</dt><dd><p>Typically refers to <a class="reference external" href="https://wikipedia.org/wiki/Open-source_software">open-source software</a>. which is software that
has the source code made available for others to see, use, and contribute
to. Allowed usage varies depending on the license that the software is
licensed under. Trino is licensed under the <a class="reference external" href="https://wikipedia.org/wiki/Apache_License">Apache license</a>, and is therefore maintained
by a community of contributors from all across the globe.</p>
</dd>
</dl>
<dl class="simple myst" id="glosspem">
<dt>PEM file format</dt><dd><p>A format for storing and sending cryptographic keys and certificates. PEM
format can contain both a key and its certificate, plus the chain of
certificates from authorities back to the root <a class="reference internal" href="glossary.html#glossca"><span class="std std-ref">CA</span></a>, or back
to a CA vendor’s intermediate CA.</p>
</dd>
</dl>
<dl class="simple myst" id="glosspkcs12">
<dt>PKCS #12</dt><dd><p>A binary archive used to store keys and certificates or certificate chains
that validate a key. <a class="reference external" href="https://wikipedia.org/wiki/PKCS_12">PKCS #12</a> files
have <code class="docutils literal notranslate"><span class="pre">.p12</span></code> or <code class="docutils literal notranslate"><span class="pre">.pfx</span></code> extensions. This format is a less popular
alternative to <a class="reference internal" href="glossary.html#glosspem"><span class="std std-ref">PEM</span></a>.</p>
</dd>
</dl>
<dl class="simple myst" id="glossplugin">
<dt>Plugin</dt><dd><p>A bundle of code implementing the Trino <a class="reference internal" href="develop/spi-overview.html"><span class="doc std std-doc">Service Provider Interface
(SPI)</span></a>. that is used to add new functionality.
More information is available in <a class="reference internal" href="installation/plugins.html"><span class="doc std std-doc">Plugins</span></a>.</p>
</dd>
<dt>Presto and PrestoSQL</dt><dd><p>The old name for Trino. To learn more about the name change to Trino, read
<a class="reference external" href="https://wikipedia.org/wiki/Trino_(SQL_query_engine)#History">the history</a>.</p>
</dd>
<dt>Query federation</dt><dd><p>A type of <a class="reference internal" href="glossary.html#glossdatavirtualization"><span class="std std-ref">data virtualization</span></a> that provides a
common access point and data model across two or more heterogeneous data
sources. A popular data model used by many query federation engines is
translating different data sources to <a class="reference internal" href="glossary.html#glosssql"><span class="std std-ref">SQL</span></a> tables.</p>
</dd>
</dl>
<dl class="simple myst" id="glossssl">
<dt>Secure Sockets Layer (SSL)</dt><dd><p>Now superseded by <a class="reference internal" href="glossary.html#glosstls"><span class="std std-ref">TLS</span></a>, but still recognized as the term
for what TLS does.</p>
</dd>
</dl>
<dl class="simple myst" id="glosssql">
<dt>Structured Query Language (SQL)</dt><dd><p>The standard language used with relational databases. For more information,
see <a class="reference internal" href="language.html"><span class="doc">SQL</span></a>.</p>
</dd>
</dl>
<dl class="simple myst" id="glosstarball">
<dt>Tarball</dt><dd><p>A common abbreviation for <a class="reference external" href="https://wikipedia.org/wiki/Tar_(computing)">TAR file</a>, which is a common software
distribution mechanism. This file format is a collection of multiple files
distributed as a single file, commonly compressed using <a class="reference internal" href="glossary.html#glossgzip"><span class="std std-ref">gzip</span></a> compression.</p>
</dd>
</dl>
<dl class="simple myst" id="glosstls">
<dt>Transport Layer Security (TLS)</dt><dd><p><a class="reference external" href="https://wikipedia.org/wiki/Transport_Layer_Security">TLS</a> is a security
protocol designed to provide secure communications over a network. It is the
successor to <a class="reference internal" href="glossary.html#glossssl"><span class="std std-ref">SSL</span></a>, and used in many applications like
HTTPS, email, and Trino. These security topics use the term TLS to refer to
both TLS and SSL.</p>
</dd>
</dl>
</section>


          </article>
        </div>
      </div>
    </main>
  </div>
  <footer class="md-footer">
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
          
            <a href="develop/client-protocol.html" title="Trino client REST API"
               class="md-flex md-footer-nav__link md-footer-nav__link--prev"
               rel="prev">
              <div class="md-flex__cell md-flex__cell--shrink">
                <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
              </div>
              <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
                <span class="md-flex__ellipsis">
                  <span
                      class="md-footer-nav__direction"> Previous </span> Trino client REST API </span>
              </div>
            </a>
          
          
            <a href="appendix.html" title="Appendix"
               class="md-flex md-footer-nav__link md-footer-nav__link--next"
               rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title"><span
                class="md-flex__ellipsis"> <span
                class="md-footer-nav__direction"> Next </span> Appendix </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink"><i
                class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          
        </a>
        
      </nav>
    </div>
    <div class="md-footer-meta md-typeset">
      <div class="md-footer-meta__inner md-grid">
        <div class="md-footer-copyright">
        </div>
      </div>
    </div>
  </footer>
  <script src="_static/javascripts/application.js"></script>
  <script>app.initialize({version: "1.0.4", url: {base: ".."}})</script>
  </body>
</html>