请对 skynet-platform 这个项目，进行分析，

要求如下：
1、为所有的类添加相关的注释说明，包括 每个函数的参数说明（英文）
2、从整体考虑你认为需要重构优化的地方，给出优化方案，并进行修改优化，直到测试编译测试验证通过为止。
3、你认为比较重点的类和方法，编写单元测试用例，进行测试，直到测试编译测试验证通过为止。
4、对 类似以下的  @ConfigurationProperties  @Value 标识的属性，进行统一抽取，按照模块编写一个配置说明文档，Markdown 格式。 

    ```java
    @Bean
    @ConfigurationProperties("skynet.platform")
    public PlatformProperties platformProperties() throws Exception {
        return new PlatformProperties();
    }
    
    @Value("${skynet.fetch.server.status.timeout.seconds:5}")
    private int timeout;
    ```
    
按照模块输出 配置说明文档，

------------
------------

2. Java项目README.md 
请对当前 Java 工程项目进行全面分析，并生成一份结构化的报告，输出到项目根目录下的 `README.md` 文件中。报告应包含以下内容：

1. **技术架构**  
   - 描述项目的整体技术架构，包括使用的框架（如 Spring Boot、Hibernate 等）、设计模式（如 MVC、微服务等）以及核心技术栈（如 Java 版本、数据库类型、缓存机制等）。  
   - 说明各模块的职责及其之间的依赖关系，提供简要的架构图（使用 Mermaid 或 ASCII 格式表示，嵌入 Markdown）。  

2. **主要功能**  
   - 列出项目的主要功能模块，简要描述每个模块的核心业务逻辑。  
   - 说明功能实现的关键技术点或算法（如数据处理、并发处理等）。  

3. **业务处理逻辑分析**  
   - 深入分析项目中核心业务逻辑的实现，重点关注关键 Java 类或方法的处理流程。  
   - 描述主要业务逻辑的实现方式，包括数据流转、条件判断、循环结构及异常处理等。  
   - 识别并总结代码中的关键算法或复杂逻辑（如排序、搜索、状态机等），并说明其业务场景和优化点。  
   - 提供代码片段（如有必要）以示例说明核心业务逻辑，嵌入 Markdown 代码块。  

4. **主要对外接口**  
   - 提供项目暴露的主要对外接口（如 REST API、gRPC、消息队列等）的详细说明。  
   - 包括接口的类型、路径、参数、返回值及使用场景，以表格形式呈现。  

5. **系统配置**  
   - 描述项目的运行环境要求（如 JDK 版本、操作系统、依赖的中间件等）。  
   - 列出关键配置文件（如 `application.properties` 或 `application.yml`）中的重要参数及其作用。  
   - 提供项目启动和部署的简要步骤。  

**输出要求**：  
- 报告内容使用 Markdown 格式，结构清晰，标题层次分明。  
- 语言简洁专业，适合技术人员阅读。  
- 确保生成的文件覆盖所有要求的内容，并保存至项目根目录下的 `README.md`。  
- 业务逻辑分析部分应突出代码实现的核心点，避免过多冗余描述，必要时引用具体类名或方法名以提高可读性。

@src 


4. 代码优化
```
请分析并优化 `skynet-boot-context` Java 项目。
需要检查并优化的内容包括所有 Java 业务逻辑类、所有单元测试类。
请找出可以改进的地方（如性能、代码质量、依赖管理、测试有效性等），**直接给出优化后的代码**。
最后，请提供一份简要的优化报告，说明你做了哪些主要的修改及其原因。
```


------------
------------

```
请对 `skynet-pandora-brave` Java 项目执行代码优化。
**具体要求：**
1.  **全面扫描：** 分析项目中的 `pom.xml` 文件、所有 Java 源代码（包括主代码和单元测试代码）。
2.  **识别并优化：** 找出所有需要优化的地方，包括但不限于：
    * **依赖优化：** `pom.xml` 中的依赖版本、冗余、冲突、安全问题等。
    * **代码优化：** Java 类中的性能瓶颈、代码坏味、冗余代码、不合理的实现、资源未关闭、并发风险、可读性差等问题。
    * **测试优化：** 单元测试的有效性、覆盖率、可维护性等。
3.  **直接修改：** **请直接修改识别出的问题点**，提供优化后的 `pom.xml` 内容、Java 类文件内容和单元测试文件内容。
4.  **输出报告：** 提供一份优化报告，清晰说明：
    * 分析了哪些文件/方面。
    * 进行了哪些具体的优化修改（最好有修改前后的对比或清晰的变更描述）。
    * 做出这些优化的原因或预期效果。

**请开始执行分析和优化，并直接给出优化后的代码和最终的优化报告。**

```

------------
------------


5. 代码深度优化（推荐）
```
请扮演一位经验丰富的 Java 开发专家和代码审查员。我将提供的 Java 代码，请你对其进行审查和优化，重点关注以下方面：

1.  规范化变量命名:
    - 确保所有局部变量、实例变量、静态变量和参数名都遵循 Java 的标准命名约定（例如，使用小驼峰命名法 lowerCamelCase）。
    - 变量名应清晰、简洁且具有描述性，能够准确反映其用途。

2.  规范化 Private 方法命名:
    - 确保所有 `private` 方法名同样遵循 Java 的标准命名约定（小驼峰命名法 lowerCamelCase）。
    - 方法名应清晰地描述该方法执行的操作。

3.  线程安全 (Thread Safety):
    - 分析代码中是否存在潜在的线程安全问题，特别是在并发环境中访问共享可变状态时。
    - 如果发现问题，请提出修改建议或直接应用修复，例如使用 `synchronized` 关键字、`volatile` 关键字、`java.util.concurrent` 包中的原子类或并发集合等。
    - 请简要说明为什么需要进行线程安全方面的修改。

4.  日志规范化 (Logging Standardization):
    - 检查代码中的日志记录实践。
    - 如果使用了日志框架（如 SLF4j、Logback、Log4j2），请确保其使用方式一致。
    - 在关键路径、重要事件、方法入口/出口（可选）以及错误处理逻辑中添加适当的日志记录语句。
    - 使用恰当的日志级别（如 DEBUG, INFO, WARN, ERROR）。
    - 确保日志信息清晰、包含必要的上下文信息。

5.  空指针判断 (Null Pointer Checks):
    - 识别可能导致 `NullPointerException` 的潜在风险点。
    - 在访问可能为 `null` 的对象的成员（方法或字段）之前，添加必要的空指针检查（例如 `if (variable != null)`）。
    - 在合适的情况下，考虑使用 `java.util.Optional` 来更好地处理可能为空的值。

6.  性能优化 (Performance Optimization):
    - 集合使用: 检查是否选择了最合适的集合类型（如 `ArrayList` vs `LinkedList`）。检查集合初始化容量是否合理，避免不必要的扩容。
    - 字符串处理: 避免在循环中直接使用 `+` 或 `+=` 连接字符串，优先考虑使用 `StringBuilder`。
    - 循环效率: 检查循环逻辑，看是否有可优化的空间（如减少循环次数、避免在循环内重复计算或对象创建）。
    - I/O 操作: 检查是否有不必要的 I/O 操作或低效的读写方式。

7.  资源管理 (Resource Management):
    - Try-with-resources: 确保实现了 `java.lang.AutoCloseable` 接口的资源（如文件流、数据库连接、网络连接等）被正确地使用 `try-with-resources` 语句管理，以保证资源总是被关闭，即使发生异常。

8.  代码可读性与维护性 (Readability & Maintainability):
    - 消除魔法数值 (Magic Numbers): 将代码中硬编码的数值或字符串字面量替换为有意义的常量（`final static`）。
    - 减少代码重复 (DRY Principle): 识别重复的代码块，并将其提取到独立的、可复用的方法中。
    - 简化复杂性: 对于过长或逻辑过于复杂的方法（高圈复杂度），建议进行拆分，使其更易于理解和测试。
    - 注释: 添加必要的注释来解释复杂的逻辑、设计决策或 TODO 项，同时移除无用或过时的注释。

9.  错误处理 (Error Handling):
    - 精确异常捕获: 避免捕获过于宽泛的异常（如 `Exception` 或 `Throwable`），应尽可能捕获具体的异常类型。
    - 避免空的 Catch 块: 检查是否存在空的 `catch` 块，如果确实不需要处理异常，应添加注释说明原因或记录日志。
    - 异常链: 在重新抛出异常时，保留原始异常信息（cause），便于问题追踪。

10. 现代化 Java 实践 (Modern Java Practices):
    - Lambda 表达式与方法引用: 在适用的地方（如匿名内部类、函数式接口），使用 Lambda 表达式或方法引用简化代码。
    - Stream API: 对于集合操作，考虑使用 Stream API 替代传统的 `for` 循环，以提高代码的表达力和简洁性（注意评估性能影响）。
    - 接口静态/默认方法: 利用 Java 8 及以后版本接口的新特性，减少工具类或抽象类的使用。

输出要求:

- 提供优化后的完整 Java 代码（class 内部的日志和注释要全部是英文，类注释要中文）。
- （可选但推荐）提供一份变更摘要，解释所做的主要修改及其原因，特别是关于性能、资源管理、线程安全和空指针处理的改动。

现在，请处理以下 Java 代码：
```

------------
------------

6. 类注释（推荐）

```
# 类注释助手
## 角色定义：
请扮演一位经验丰富的 Java 开发专家和代码分析审查员。为提供的若干类（Class）代码生成或修订一份高质量、信息全面的类级别文档注释。

## 核心要求：
请基于提供的所有类代码，执行以下文档注释生成与完善任务：

1.深入分析与功能总结：
    - 分析： 仔细研究该类的核心功能、设计目的、关键职责、主要工作流程以及它在系统或模块中的预期角色。
    - 总结： 用简洁精炼的语言概括其核心功能和主要职责。

2.编写详细中文描述：
    - 基于上述分析，撰写一段详尽的、使用**简体中文**的描述性文字。
    - 内容应清晰阐述：
        - 这个类**是做什么的**（What）。
        - 它的主要**工作机制或流程**是怎样的（How）。
        - 其关键的**特性或功能点**有哪些。

3.整理使用注意事项（简体中文）：
    - 识别并列出使用该类时需要特别留意的关键点。请使用清晰的列表格式（例如项目符号列表）。
    - 至少应涵盖以下方面（如果适用）：
        - 前置条件： 如需初始化的依赖项、配置要求等。
        - 潜在风险与副作用： 可能导致的问题、状态变更等。
        - 线程安全： 该类是否线程安全，以及在并发环境下的使用建议。
        - 性能考量： 性能敏感点、资源消耗情况。
        - 错误处理： 异常抛出情况、推荐的错误处理方式。
        - 最佳实践/推荐用法： 建议的使用模式或避免的反模式。

4.提供调用示例代码：
    - 根据类的典型应用场景，提供 1 到 3 个具体的代码示例片段。
    - 示例应清晰展示：
        - 如何**实例化**该类（构造函数的正确使用）。
        - 如何调用其**关键方法**或访问其**重要属性/字段**。
    - 代码示例应简洁明了，并附带简短的**简体中文注释**以说明其意图。
    - 确保示例代码格式适合在目标文档注释格式（如 Javadoc 的 `<pre><code>...</code></pre>`）中进行**语法高亮**显示。

5.（可选但推荐）描述监控指标：
    - 如果该类涉及关键操作或状态，可简要说明与其相关的、有助于理解系统运行状态的**监控指标**（例如，处理速率、错误计数、队列长度等）。使用**简体中文**描述。

6.处理现有注释与元数据：
    - 如果类已存在文档注释：
        - 请用新生成的、包含上述所有要求（要点 1-5）的完整内容**彻底替换**原有的注释主体描述文本。
        - **【关键保留项】**：**必须**检查并**完整保留**原始注释中已存在的标准元数据标签及其对应值（例如 `@author`, `@version`, `@since`, `@date`, `@param`, `@return`, `@throws` 等）。将这些保留的标签适当地附加在新注释内容的末尾或标准位置。
        - 如果 <AUTHOR> @since 未空，自动从git中获取当前文件的用户名和当前项目的版本号，（如果获取不到，默认 @author默认 `lyhu`，就忽略）
    - 如果类没有文档注释：
        - 请创建一个新的文档注释块，并包含上述所有要求（要点 1-5）的内容。如果合适，可以添加标准的元数据标签（如 `@since`）。

7.格式与位置：
    - 生成的完整文档注释块必须放置在 `class` 关键字声明行的**正上方**。
    - 注释格式必须严格遵循目标编程语言的**标准规范**（例如 Java 的 Javadoc 或 Python 的 Docstring）。
    - 在 Javadoc 等支持 HTML 的格式中，可适当使用 HTML 标签（如 `<p>`, `<ul>`, `<li>`, `<pre>`, `<code>`）来增强描述、列表和代码示例的可读性。

## 最终输出：
- 请返回包含更新后或新增的、符合上述所有要求的**完整类源代码**。确保文档注释已正确放置并格式化。
- 完成用户提供的所有类文件的优化补充修改。
```


------------
------------


8. Java代码优化
```
你是一个资深Java工程师，请对提供的 Java 后端代码进行优化(运行环境JDK21)，具体要求如下：
- 线程方面：优先考虑使用虚拟线程，并且修复代码中存在的线程安全问题。
- 注释方面：在类上添加调用示例。若已有调用示例，需检查其是否与类的功能一致，使用是否正确，如有问题则重新修订。
- 日志方面：纠正注释说明语法，使其符合英文语法规范。
- 参数方面：在 public 方法中使用 Assert.notNull 进行参数空指针检查。
- 变量与方法方面：规范内部变量名；若对外方法名不符合规范，对原方法添加 @Deprecated 注解，并新增符合规范的方法或属性；可调整变量定义顺序。
- 逻辑方面：分析代码处理逻辑的合理性，若不合理则进行优化，确保代码简洁，并添加注释说明优化原因。
```